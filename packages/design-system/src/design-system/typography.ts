/**
 * Design System - Typography
 *
 * Defines typography related styles and components for consistent text treatment
 * across the application.
 */

import { cn } from '../utils'
import { typography } from './tokens'

// Text size variants
export const textSizeVariants = {
  xs: 'text-xs',
  sm: 'text-sm',
  base: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
  '2xl': 'text-2xl',
  '3xl': 'text-3xl',
  '4xl': 'text-4xl',
} as const

// Font weight variants
export const fontWeightVariants = {
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold',
} as const

// Line height variants
export const lineHeightVariants = {
  none: 'leading-none',
  tight: 'leading-tight',
  normal: 'leading-normal',
  relaxed: 'leading-relaxed',
} as const

// Text alignment variants
export const textAlignVariants = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right',
  justify: 'text-justify',
} as const

// Tracking (letter spacing) variants
export const trackingVariants = {
  tighter: 'tracking-tighter',
  tight: 'tracking-tight',
  normal: 'tracking-normal',
  wide: 'tracking-wide',
  wider: 'tracking-wider',
} as const

// Predefined heading variants
export const headingVariants = {
  h1: cn('scroll-m-20 text-4xl font-bold tracking-tight', 'lg:text-5xl'),
  h2: cn('scroll-m-20 text-3xl font-semibold tracking-tight', 'first:mt-0'),
  h3: cn('scroll-m-20 text-2xl font-semibold tracking-tight'),
  h4: cn('scroll-m-20 text-xl font-semibold tracking-tight'),
  h5: cn('scroll-m-20 text-lg font-semibold tracking-tight'),
  h6: cn('scroll-m-20 text-base font-semibold tracking-tight'),
} as const

// Predefined text variants
export const textVariants = {
  // Default body text
  default: 'text-base leading-normal',

  // Lead/intro paragraph
  lead: 'text-xl leading-relaxed',

  // Large text
  large: 'text-lg font-normal',

  // Small text
  small: 'text-sm font-normal leading-tight',

  // Muted text (lower emphasis)
  muted: 'text-sm text-muted-foreground',

  // Subtle text (for metadata, timestamps)
  subtle: 'text-xs text-muted-foreground',
} as const

// Link styles
export const linkVariants = {
  default: 'text-primary underline-offset-4 hover:underline',
  subtle: 'text-muted-foreground hover:text-foreground underline-offset-4 hover:underline',
  nav: 'text-foreground hover:text-primary transition-colors',
} as const

// Helper function to combine typography variants
export function createTypographyStyle(options: {
  size?: keyof typeof textSizeVariants
  weight?: keyof typeof fontWeightVariants
  lineHeight?: keyof typeof lineHeightVariants
  align?: keyof typeof textAlignVariants
  tracking?: keyof typeof trackingVariants
  additionalClasses?: string
}) {
  const {
    size = 'base',
    weight = 'normal',
    lineHeight = 'normal',
    align = 'left',
    tracking = 'normal',
    additionalClasses = '',
  } = options

  return cn(
    textSizeVariants[size],
    fontWeightVariants[weight],
    lineHeightVariants[lineHeight],
    textAlignVariants[align],
    trackingVariants[tracking],
    additionalClasses
  )
}

// Typography styles mapping
export const typographyStyles = {
  headings: headingVariants,
  text: textVariants,
  links: linkVariants,

  // Helper for creating custom typography
  create: createTypographyStyle,

  // Direct access to variants
  sizes: textSizeVariants,
  weights: fontWeightVariants,
  lineHeights: lineHeightVariants,
  alignments: textAlignVariants,
  tracking: trackingVariants,
}

export default typographyStyles
