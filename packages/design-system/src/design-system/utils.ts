/**
 * Design System - Utilities
 *
 * Helper functions and utilities for the design system.
 */

import { ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

// Utility to merge class names safely with Tailwind
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs))
}

// Color manipulation utilities
export const colorUtils = {
  // Add alpha to a color
  withAlpha: (color: string, alpha: number): string => {
    // For OKLCH colors
    if (color.startsWith('oklch')) {
      const matches = color.match(/oklch\((.*?)\s+(.*?)\s+(.*?)\)/)
      if (matches && matches.length === 4) {
        return `oklch(${matches[1]} ${matches[2]} ${matches[3]} / ${alpha})`
      }
    }

    // For hex colors
    if (color.startsWith('#')) {
      // Convert hex to rgba
      const r = parseInt(color.slice(1, 3), 16)
      const g = parseInt(color.slice(3, 5), 16)
      const b = parseInt(color.slice(5, 7), 16)
      return `rgba(${r}, ${g}, ${b}, ${alpha})`
    }

    // For RGB colors
    if (color.startsWith('rgb')) {
      // Replace alpha if it already exists
      if (color.startsWith('rgba')) {
        return color.replace(
          /rgba\((.*?),\s*(.*?),\s*(.*?),\s*(.*?)\)/,
          `rgba($1, $2, $3, ${alpha})`
        )
      }
      // Add alpha if it doesn't exist
      return color.replace(/rgb\((.*?),\s*(.*?),\s*(.*?)\)/, `rgba($1, $2, $3, ${alpha})`)
    }

    return color
  },

  // Check if a color is dark or light (useful for conditional text colors)
  isDark: (color: string): boolean => {
    // For OKLCH, we can use the first value (lightness)
    if (color.startsWith('oklch')) {
      const matches = color.match(/oklch\((.*?)\s+/)
      if (matches && matches.length === 2) {
        const lightness = parseFloat(matches[1])
        return lightness < 0.5
      }
    }

    // For hex colors
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16)
      const g = parseInt(color.slice(3, 5), 16)
      const b = parseInt(color.slice(5, 7), 16)

      // Calculate relative luminance (per WCAG)
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
      return luminance < 0.5
    }

    return false
  },

  // Get a contrasting color (white or black) based on a background color
  getContrastColor: (bgColor: string): string => {
    return colorUtils.isDark(bgColor) ? '#FFFFFF' : '#000000'
  },
}

// Media query generation utilities
export const mediaQueryUtils = {
  // Generate a media query string for min-width
  minWidth: (breakpoint: string): string => {
    return `@media (min-width: ${breakpoint})`
  },

  // Generate a media query string for max-width
  maxWidth: (breakpoint: string): string => {
    return `@media (max-width: ${breakpoint})`
  },

  // Generate a media query string for a range
  between: (minBreakpoint: string, maxBreakpoint: string): string => {
    return `@media (min-width: ${minBreakpoint}) and (max-width: ${maxBreakpoint})`
  },
}

// Animation utilities
export const animationUtils = {
  // Generate a keyframe string for animations
  keyframes: (name: string, frames: Record<string, string>): string => {
    const keyframeString = Object.entries(frames)
      .map(([key, value]) => `${key} { ${value} }`)
      .join('\n')

    return `@keyframes ${name} {
      ${keyframeString}
    }`
  },

  // Create an animation string
  animation: (
    name: string,
    duration: string,
    timingFunction: string,
    delay: string = '0s',
    iterationCount: string = '1'
  ): string => {
    return `${name} ${duration} ${timingFunction} ${delay} ${iterationCount}`
  },
}

// Spacing helpers
export const spacingUtils = {
  // Convert rem values to pixels
  remToPx: (rem: string): number => {
    const value = parseFloat(rem.replace('rem', ''))
    const baseFontSize = 16 // Default browser font size
    return value * baseFontSize
  },

  // Convert pixels to rem
  pxToRem: (px: number): string => {
    const baseFontSize = 16 // Default browser font size
    return `${px / baseFontSize}rem`
  },
}

// Validators for design system props
export const validators = {
  // Validate a color value against our color palette
  isValidColor: (color: string): boolean => {
    // Implementation would check against our design system colors
    // This is a placeholder implementation
    return true
  },

  // Validate a spacing value
  isValidSpacing: (spacing: string): boolean => {
    // Implementation would check against our spacing scale
    // This is a placeholder implementation
    return true
  },
}

// Export all utilities
export const designUtils = {
  cn,
  color: colorUtils,
  mediaQuery: mediaQueryUtils,
  animation: animationUtils,
  spacing: spacingUtils,
  validators,
}

export default designUtils
