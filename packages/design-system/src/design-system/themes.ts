/**
 * Design System - Themes
 *
 * This file defines the theme configurations for the application.
 * It extends the tokens with theme-specific variations.
 */

import { tokens } from './tokens'

// Theme variants
export type ThemeVariant = 'light' | 'dark' | 'system'

// Components theme configurations
export const componentThemes = {
  // Common component patterns
  patterns: {
    // Section headers with icons
    sectionHeader: {
      container: 'flex items-center space-x-4',
      iconContainer: 'h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center',
      icon: 'h-4 w-4 text-primary',
      content: {
        title: 'text-lg font-medium',
        description: 'text-sm text-muted-foreground',
      },
    },

    // Cards
    card: {
      base: 'rounded-lg border p-4',
      hover: 'hover:bg-primary/5',
      content: {
        label: 'text-base font-medium',
        description: 'text-sm text-muted-foreground',
      },
    },

    // Form elements
    form: {
      field: {
        spacing: 'space-y-4',
        label:
          'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
      },
    },

    // Interactive elements
    interactive: {
      primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
      outline: 'bg-primary/10 border-primary/20 hover:bg-primary/20 hover:border-primary/30',
      ghost: 'hover:bg-primary/10 text-foreground',
      link: 'text-primary underline-offset-4 hover:underline',
    },
  },

  // Component-specific themes
  components: {
    // Button variants
    button: {
      primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'text-primary underline-offset-4 hover:underline',
      destructive: 'bg-destructive text-white font-medium hover:bg-destructive/90 border-none',
    },

    // Switch
    switch: {
      active: 'data-[state=checked]:bg-primary data-[state=checked]:hover:bg-primary/90',
    },

    // Tabs
    tabs: {
      base: 'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1',
      tab: 'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm',
    },

    // Alerts
    alert: {
      default: 'bg-background text-foreground',
      destructive:
        'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',
      info: 'border-primary/50 text-primary dark:border-primary [&>svg]:text-primary',
      warning: 'border-warning/50 text-warning dark:border-warning [&>svg]:text-warning',
      success: 'border-success/50 text-success dark:border-success [&>svg]:text-success',
    },
  },
}

// Common spacing patterns
export const spacingPatterns = {
  section: 'space-y-6',
  container: 'space-y-8',
  itemGap: 'space-x-4',
  formFields: 'space-y-4',
  labelGap: 'space-y-1',
  padding: {
    button: 'px-4 py-2',
    card: 'p-4',
    section: 'p-6',
  },
  margins: {
    card: 'my-6',
    button: 'mt-8',
  },
}

// Animation variants
export const animationVariants = {
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } },
  },
  slideUp: {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.4 } },
  },
  slideIn: {
    hidden: { x: -20, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: { duration: 0.3 } },
  },
  scale: {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { scale: 1, opacity: 1, transition: { duration: 0.3 } },
  },
}

// Complete theme configuration that combines tokens and components
export const themeConfig = {
  tokens,
  components: componentThemes,
  spacing: spacingPatterns,
  animations: animationVariants,
}

// Helper function to get theme values by path
export function getThemeValue(path: string): any {
  return path
    .split('.')
    .reduce(
      (obj, key) => (obj && typeof obj === 'object' ? obj[key] : undefined),
      themeConfig as any
    )
}
