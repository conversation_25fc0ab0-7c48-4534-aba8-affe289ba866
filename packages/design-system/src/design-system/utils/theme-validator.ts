'use client'

import { useTheme } from 'next-themes'
import { tokens } from '../tokens'
import { themeConfig } from '../themes'

// Required token categories that should exist in the theme
const REQUIRED_TOKEN_CATEGORIES = [
  'colors',
  'typography',
  'spacing',
  'borderRadius',
  'shadows',
  'transitions',
]

// Required semantic color tokens that should exist for proper theme functionality
const REQUIRED_SEMANTIC_COLORS = [
  'primary',
  'secondary',
  'background',
  'foreground',
  'muted',
  'accent',
  'card',
  'border',
  'destructive',
]

// Configuration errors type
export type ThemeValidationError = {
  type: 'missing' | 'invalid' | 'warning'
  message: string
  token?: string
  suggestions?: string[]
}

/**
 * Validates theme tokens structure and ensures all required tokens are present
 * @returns Array of validation errors or empty array if valid
 */
export function validateThemeTokens(): ThemeValidationError[] {
  const errors: ThemeValidationError[] = []

  // Check for required token categories
  REQUIRED_TOKEN_CATEGORIES.forEach(category => {
    if (!tokens[category]) {
      errors.push({
        type: 'missing',
        message: `Missing required token category: ${category}`,
        token: category,
      })
    }
  })

  // Hardcoded semantic colors check since we can't directly import the config
  // We're checking based on standard semantic color naming
  const availableColors = Object.keys(tokens.colors?.light || {}).concat(
    Object.keys(tokens.colors?.dark || {})
  )

  REQUIRED_SEMANTIC_COLORS.forEach(color => {
    if (!availableColors.includes(color)) {
      errors.push({
        type: 'missing',
        message: `Missing required semantic color in tokens: ${color}`,
        token: color,
      })
    }
  })

  // Check CSS variable definitions in :root
  if (typeof document !== 'undefined') {
    const computedStyle = getComputedStyle(document.documentElement)
    const cssVars = ['--background', '--foreground', '--primary', '--card', '--border']

    cssVars.forEach(variable => {
      const value = computedStyle.getPropertyValue(variable)
      if (!value) {
        errors.push({
          type: 'missing',
          message: `Missing CSS variable: ${variable} in :root`,
          token: variable,
        })
      }
    })
  }

  return errors
}

/**
 * Check for duplicate color values across the theme to identify potential consolidation
 * @returns Object mapping duplicate colors to their token paths
 */
export function findDuplicateColorValues() {
  const colorValues = new Map<string, string[]>()
  const colorTokens = { ...tokens.colors.light, ...tokens.colors.dark, ...tokens.colors.brand }

  // Function to recursively find all color values
  const findColors = (obj: any, path: string[] = []) => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = [...path, key]

      if (
        typeof value === 'string' &&
        (value.startsWith('#') ||
          value.startsWith('rgb') ||
          value.startsWith('hsl') ||
          value.startsWith('oklch'))
      ) {
        const normalizedValue = value.toLowerCase()
        const existingPaths = colorValues.get(normalizedValue) || []
        colorValues.set(normalizedValue, [...existingPaths, currentPath.join('.')])
      } else if (typeof value === 'object' && value !== null) {
        findColors(value, currentPath)
      }
    }
  }

  findColors(colorTokens)

  // Filter to only include duplicates
  const duplicates: Record<string, string[]> = {}
  for (const [color, paths] of colorValues.entries()) {
    if (paths.length > 1) {
      duplicates[color] = paths
    }
  }

  return duplicates
}

/**
 * Hook to validate theme configuration at runtime
 * @returns Theme validation results
 */
export function useThemeValidator() {
  const { theme, systemTheme, setTheme } = useTheme()

  // Get the current effective theme
  const currentTheme = theme === 'system' ? systemTheme : theme

  // Validate the current theme
  const errors = validateThemeTokens()
  const duplicateColors = findDuplicateColorValues()

  // Check if the theme is valid according to WCAG contrast requirements
  const hasContrastIssues = checkContrastIssues()

  return {
    isValid: errors.length === 0 && !hasContrastIssues,
    errors,
    duplicateColors,
    currentTheme,
    hasContrastIssues,
    fixIssues: () => {
      // Reset to default theme if there are issues
      if (errors.length > 0 || hasContrastIssues) {
        setTheme('system')
        return 'Reset to system theme with Emynent Default'
      }
      return 'No issues to fix'
    },
  }
}

/**
 * Check if there are WCAG contrast issues with the current theme
 * @returns Boolean indicating if contrast issues exist
 */
function checkContrastIssues(): boolean {
  if (typeof document === 'undefined') return false

  const computedStyle = getComputedStyle(document.documentElement)

  const backgroundColor = computedStyle.getPropertyValue('--background').trim()
  const foregroundColor = computedStyle.getPropertyValue('--foreground').trim()

  if (!backgroundColor || !foregroundColor) return false

  // Simple luminance check for contrast (more sophisticated implementation would use WCAG algorithms)
  const getLuminance = (color: string): number => {
    // This is a simplified version - a real implementation should convert to RGB and compute accurately
    // For demonstration purposes only
    return color.includes('dark') ? 0.1 : 0.9
  }

  const bgLuminance = getLuminance(backgroundColor)
  const fgLuminance = getLuminance(foregroundColor)

  const contrast = Math.max(bgLuminance, fgLuminance) / Math.min(bgLuminance, fgLuminance)

  // WCAG AA requires 4.5:1 for normal text
  return contrast < 4.5
}

/**
 * Check if a theme is properly registered in the system
 * @param themeName The name of the theme to check
 * @returns Boolean indicating if the theme is valid
 */
export function isValidTheme(themeName: string): boolean {
  const availableThemes = ['light', 'dark', 'system', 'emynent-default', 'slate', 'mint']
  return availableThemes.includes(themeName)
}
