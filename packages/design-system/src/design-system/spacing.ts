/**
 * Design System - Spacing
 *
 * Defines spacing-related patterns and utilities for maintaining
 * consistent spacing throughout the application.
 */

import { cn } from '../utils'
import { spacing } from './tokens'

// Common container size variants
export const containerSizeVariants = {
  sm: 'max-w-screen-sm',
  md: 'max-w-screen-md',
  lg: 'max-w-screen-lg',
  xl: 'max-w-screen-xl',
  '2xl': 'max-w-screen-2xl',
  full: 'max-w-full',
} as const

// Margin variants
export const marginVariants = {
  none: 'm-0',
  xs: 'm-1',
  sm: 'm-2',
  md: 'm-4',
  lg: 'm-6',
  xl: 'm-8',
  '2xl': 'm-10',
  auto: 'm-auto',
} as const

// Padding variants
export const paddingVariants = {
  none: 'p-0',
  xs: 'p-1',
  sm: 'p-2',
  md: 'p-4',
  lg: 'p-6',
  xl: 'p-8',
  '2xl': 'p-10',
} as const

// Gap variants for flex and grid
export const gapVariants = {
  none: 'gap-0',
  xs: 'gap-1',
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8',
  '2xl': 'gap-10',
} as const

// Space between variants (for vertical spacing)
export const spaceBetweenVariants = {
  none: 'space-y-0',
  xs: 'space-y-1',
  sm: 'space-y-2',
  md: 'space-y-4',
  lg: 'space-y-6',
  xl: 'space-y-8',
  '2xl': 'space-y-10',
} as const

// Common layout patterns
export const layoutPatterns = {
  // Center content horizontally
  centerX: 'mx-auto',

  // Center content both horizontally and vertically
  center: 'flex items-center justify-center',

  // Standard page container
  pageContainer: cn('container mx-auto px-4 md:px-6', 'max-w-7xl'),

  // Section container
  sectionContainer: cn('py-8 md:py-12 lg:py-16', 'space-y-8'),

  // Card container
  cardContainer: cn('rounded-lg border bg-card p-4 md:p-6', 'shadow-sm'),

  // Form field container
  formField: cn('space-y-2'),

  // Grid layouts
  grid: {
    cols1: 'grid grid-cols-1 gap-4',
    cols2: 'grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6',
    cols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6',
    cols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6',
  },

  // Flex layouts
  flex: {
    row: 'flex flex-row items-center',
    col: 'flex flex-col',
    between: 'flex items-center justify-between',
    center: 'flex items-center justify-center',
    start: 'flex items-start',
    end: 'flex items-end',
  },

  // Responsive spacers
  spacer: {
    sm: 'h-4 md:h-6',
    md: 'h-8 md:h-12',
    lg: 'h-12 md:h-16',
  },
}

// Helper function to create a spacing utility
export function createSpacing(options: {
  margin?: keyof typeof marginVariants
  padding?: keyof typeof paddingVariants
  gap?: keyof typeof gapVariants
  spaceBetween?: keyof typeof spaceBetweenVariants
  additionalClasses?: string
}) {
  const { margin, padding, gap, spaceBetween, additionalClasses = '' } = options

  return cn(
    margin && marginVariants[margin],
    padding && paddingVariants[padding],
    gap && gapVariants[gap],
    spaceBetween && spaceBetweenVariants[spaceBetween],
    additionalClasses
  )
}

// Export all spacing utilities
export const spacingSystem = {
  // Predefined spacing patterns
  layouts: layoutPatterns,

  // Container sizes
  containers: containerSizeVariants,

  // Spacing variants
  margin: marginVariants,
  padding: paddingVariants,
  gap: gapVariants,
  spaceBetween: spaceBetweenVariants,

  // Helper function
  create: createSpacing,
}

export default spacingSystem
