#!/usr/bin/env tsx

// Load environment variables FIRST before any other imports
import { config } from 'dotenv'
import { join } from 'path'

// Load environment variables from .env.local and .env files
const envLocalPath = join(process.cwd(), '.env.local')
const envPath = join(process.cwd(), '.env')

try {
  config({ path: envLocalPath })
  config({ path: envPath })

  console.log('🔧 Environment variables loaded:', {
    hasNextAuthSecret: !!process.env.NEXTAUTH_SECRET,
    secretLength: process.env.NEXTAUTH_SECRET?.length || 0,
    nodeEnv: process.env.NODE_ENV || 'development',
    envFilesChecked: [envLocalPath, envPath],
  })
} catch (error) {
  console.warn('⚠️ Could not load some environment files:', error.message)
}

import { createServer, Server } from 'http'
import { DesignSystemWebSocketServer } from '../src/lib/websocket/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import { promises as fs } from 'fs'

const execAsync = promisify(exec)

// Path to write the port information
const PORT_FILE_PATH = join(process.cwd(), '.websocket-port')

async function findAvailablePort(startPort: number = 8080): Promise<number> {
  const maxAttempts = 10

  for (let i = 0; i < maxAttempts; i++) {
    const port = startPort + i

    try {
      // Check if port is in use
      const isPortInUse = await checkPortInUse(port)

      if (!isPortInUse) {
        console.log(`✅ Port ${port} is available`)
        return port
      }

      console.log(`⚠️ Port ${port} is busy, trying next port...`)

      // Try to kill process on this port if it's one of our development ports
      if (port >= 8080 && port <= 8084) {
        console.log(`🔄 Attempting to free up port ${port}...`)
        await killProcessOnPort(port)

        // Re-check if port is now available
        const stillInUse = await checkPortInUse(port)
        if (!stillInUse) {
          console.log(`✅ Port ${port} is now available after cleanup`)
          return port
        }
      }
    } catch (error) {
      console.error(`❌ Error checking port ${port}:`, error)
    }
  }

  throw new Error(
    `❌ Could not find an available port after ${maxAttempts} attempts starting from ${startPort}`
  )
}

async function checkPortInUse(port: number): Promise<boolean> {
  try {
    const { stdout } = await execAsync(`lsof -ti:${port}`, { timeout: 5000 })
    return stdout.trim().length > 0
  } catch (error: any) {
    // lsof returns exit code 1 when no process is found on the port
    if (error.code === 1) {
      return false
    }
    console.error(`Error checking port ${port}:`, error.message)
    return false
  }
}

async function killProcessOnPort(port: number): Promise<void> {
  try {
    console.log(`🔪 Attempting to kill processes on port ${port}...`)

    // Get PIDs using the port
    const { stdout: pids } = await execAsync(`lsof -ti:${port}`, { timeout: 5000 })

    if (pids.trim()) {
      const pidList = pids.trim().split('\n')
      console.log(`📋 Found PIDs on port ${port}:`, pidList)

      // Get process info before killing
      for (const pid of pidList) {
        try {
          const { stdout: processInfo } = await execAsync(`ps -p ${pid} -o pid,ppid,command`, {
            timeout: 3000,
          })
          console.log(`📊 Process info for PID ${pid}:\n${processInfo}`)
        } catch (error) {
          console.log(`⚠️ Could not get info for PID ${pid}`)
        }
      }

      // Kill the processes
      await execAsync(`kill -9 ${pids.trim().split('\n').join(' ')}`, { timeout: 5000 })
      console.log(`✅ Successfully killed processes on port ${port}`)

      // Wait a moment for the port to be freed
      await new Promise(resolve => setTimeout(resolve, 1000))
    } else {
      console.log(`ℹ️ No processes found on port ${port}`)
    }
  } catch (error: any) {
    if (error.code === 1) {
      // No process found, which is fine
      console.log(`ℹ️ No processes to kill on port ${port}`)
    } else {
      console.error(`❌ Error killing processes on port ${port}:`, error.message)
    }
  }
}

async function writePortToFile(port: number): Promise<void> {
  try {
    await fs.writeFile(PORT_FILE_PATH, port.toString(), 'utf-8')
    console.log(`📝 Wrote WebSocket port ${port} to ${PORT_FILE_PATH}`)
  } catch (error) {
    console.error(`❌ Failed to write port to file:`, error)
  }
}

async function removePortFile(): Promise<void> {
  try {
    await fs.unlink(PORT_FILE_PATH)
    console.log(`🗑️ Cleaned up port file ${PORT_FILE_PATH}`)
  } catch (error) {
    // File might not exist, which is fine
  }
}

async function startServer(): Promise<void> {
  let server: Server | null = null
  let wsServer: DesignSystemWebSocketServer | null = null

  try {
    console.log('🚀 Starting WebSocket server...')

    // Find an available port
    const port = await findAvailablePort(8080)
    console.log(`📡 Using port: ${port}`)

    // Write port to file for client discovery
    await writePortToFile(port)

    // Create HTTP server
    server = createServer((req, res) => {
      // Handle health check endpoint
      if (req.url === '/health' && req.method === 'GET') {
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(
          JSON.stringify({
            status: 'healthy',
            port: port,
            uptime: process.uptime(),
            timestamp: new Date().toISOString(),
            connections: wsServer ? wsServer.getStats().totalConnections : 0,
          })
        )
        return
      }

      // Check if this is a WebSocket upgrade request
      const isWebSocketUpgrade =
        req.headers.upgrade?.toLowerCase() === 'websocket' &&
        req.headers.connection?.toLowerCase().includes('upgrade')

      if (isWebSocketUpgrade) {
        // Let the WebSocket server handle this - don't respond here
        console.log('🔌 WebSocket upgrade request detected, passing to WebSocket server')
        return
      }

      // Handle other HTTP requests with 404
      res.writeHead(404, { 'Content-Type': 'text/plain' })
      res.end('Not Found')
    })

    // Create and attach WebSocket server
    wsServer = new DesignSystemWebSocketServer(server)

    // CRITICAL: Start the WebSocket server (this was missing!)
    await wsServer.start()

    // Start listening
    await new Promise<void>((resolve, reject) => {
      server!.listen(port, (error?: Error) => {
        if (error) {
          reject(error)
        } else {
          resolve()
        }
      })
    })

    console.log(`✅ WebSocket server running on port ${port}`)
    console.log(`🔗 WebSocket URL: ws://localhost:${port}`)
    console.log(`🏥 Health check: http://localhost:${port}/health`)

    // Setup health checks with reduced frequency to avoid log noise
    let healthCheckInterval: NodeJS.Timeout | null = null
    let healthCheckCount = 0

    const runHealthCheck = async () => {
      healthCheckCount++

      try {
        const response = await fetch(`http://localhost:${port}/health`)
        if (response.ok) {
          // Only log every 10th health check to reduce noise
          if (healthCheckCount % 10 === 0) {
            const data = await response.json()
            console.log(`💚 Health check #${healthCheckCount} passed:`, data)
          }
        } else {
          console.warn(`⚠️ Health check failed with status: ${response.status}`)
        }
      } catch (error) {
        console.error(`❌ Health check error:`, error)
      }
    }

    // Initial health check after a short delay
    setTimeout(runHealthCheck, 2000)

    // Regular health checks every 30 seconds
    healthCheckInterval = setInterval(runHealthCheck, 30000)

    // Graceful shutdown handlers
    const shutdown = async (signal: string) => {
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`)

      // Clear health check interval
      if (healthCheckInterval) {
        clearInterval(healthCheckInterval)
        healthCheckInterval = null
      }

      // Close WebSocket server
      if (wsServer) {
        try {
          await wsServer.stop()
          console.log('✅ WebSocket server closed')
        } catch (error) {
          console.error('❌ Error closing WebSocket server:', error)
        }
      }

      // Close HTTP server
      if (server) {
        await new Promise<void>(resolve => {
          server!.close(() => {
            console.log('✅ HTTP server closed')
            resolve()
          })
        })
      }

      // Clean up port file
      await removePortFile()

      console.log('👋 WebSocket server shutdown complete')
      process.exit(0)
    }

    // Handle various shutdown signals
    process.on('SIGINT', () => shutdown('SIGINT'))
    process.on('SIGTERM', () => shutdown('SIGTERM'))
    process.on('SIGUSR1', () => shutdown('SIGUSR1'))
    process.on('SIGUSR2', () => shutdown('SIGUSR2'))

    // Handle uncaught exceptions
    process.on('uncaughtException', error => {
      console.error('❌ Uncaught Exception:', error)
      shutdown('uncaughtException')
    })

    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
      shutdown('unhandledRejection')
    })
  } catch (error) {
    console.error('❌ Failed to start WebSocket server:', error)

    // Clean up port file on failure
    await removePortFile()

    if (server) {
      server.close()
    }

    // Provide troubleshooting information
    console.log('\n🔧 Troubleshooting tips:')
    console.log('1. Check if another process is using the port range 8080-8089')
    console.log('2. Run: npm run websocket:health')
    console.log('3. Try manually killing processes: npm run websocket:stop')
    console.log('4. Check the logs above for specific error details')

    process.exit(1)
  }
}

// Start the server
startServer().catch(error => {
  console.error('❌ Fatal error starting WebSocket server:', error)
  process.exit(1)
})
