#!/usr/bin/env node

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs'
import { join, relative, resolve, extname } from 'path'
import { fileURLToPath } from 'url'

const __dirname = fileURLToPath(new URL('.', import.meta.url))
const projectRoot = resolve(__dirname, '..')

/**
 * Simple dependency analyzer to find actually used files
 */
class DependencyAnalyzer {
  constructor() {
    this.usedFiles = new Set()
    this.entryPoints = [
      'src/app/layout.tsx',
      'src/app/page.tsx',
      'src/app/(protected)/layout.tsx',
      'src/app/onboarding/page.tsx',
      'src/app/(protected)/dashboard/page.tsx',
      'src/app/(protected)/settings/page.tsx',
      'src/app/signin/page.tsx',
      'middleware.ts',
      'next.config.js',
      'tailwind.config.ts',
    ]
  }

  // Extract basic import patterns
  extractImports(content) {
    const imports = []
    const patterns = [
      /import.*?from\s+['"]([^'"]+)['"]/g,
      /import\s*\(\s*['"]([^'"]+)['"]/g,
      /@\/([^'"]*)/g,
    ]

    patterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(content)) !== null) {
        imports.push(match[1])
      }
    })

    return imports
  }

  // Get all files recursively
  getAllFiles(dir, extensions = ['.tsx', '.ts', '.jsx', '.js', '.css', '.scss', '.json']) {
    const files = []

    try {
      const items = readdirSync(resolve(projectRoot, dir))

      for (const item of items) {
        if (item.startsWith('.') || item === 'node_modules') continue

        const itemPath = join(dir, item)
        const fullPath = resolve(projectRoot, itemPath)

        try {
          const stat = statSync(fullPath)

          if (stat.isDirectory()) {
            files.push(...this.getAllFiles(itemPath, extensions))
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            files.push(itemPath)
          }
        } catch (e) {}
      }
    } catch (e) {}

    return files
  }

  // Simple usage check
  isFileUsed(filePath, allFiles) {
    const filename = filePath.split('/').pop()
    const nameWithoutExt = filename.replace(/\.(tsx?|jsx?|css|scss)$/, '')

    // Check if any other file imports this one
    for (const otherFile of allFiles) {
      if (otherFile === filePath) continue

      try {
        const content = readFileSync(resolve(projectRoot, otherFile), 'utf-8')

        // Simple checks for usage
        if (
          content.includes(filename) ||
          content.includes(nameWithoutExt) ||
          content.includes(filePath) ||
          content.includes(filePath.replace('src/', '@/'))
        ) {
          return true
        }
      } catch (e) {}
    }

    return false
  }

  // Analyze and generate report
  analyze() {
    console.log('🔍 Starting dependency analysis...\n')

    // Get all source files
    const allSourceFiles = this.getAllFiles('src')
    const allFiles = [...allSourceFiles, ...this.entryPoints]

    console.log(`📊 Found ${allSourceFiles.length} source files`)

    // Mark entry points as used
    this.entryPoints.forEach(entry => {
      try {
        if (statSync(resolve(projectRoot, entry)).isFile()) {
          this.usedFiles.add(entry)
        }
      } catch (e) {}
    })

    // Check usage for each file
    for (const file of allSourceFiles) {
      // Skip if already marked as used
      if (this.usedFiles.has(file)) continue

      // Check if it's an entry point or commonly used file
      if (
        file.includes('layout') ||
        file.includes('page.tsx') ||
        file.includes('globals.css') ||
        file.includes('index')
      ) {
        this.usedFiles.add(file)
        continue
      }

      // Check if used by other files
      if (this.isFileUsed(file, allFiles)) {
        this.usedFiles.add(file)
      }
    }

    const unusedFiles = allSourceFiles.filter(file => !this.usedFiles.has(file))

    // Categorize unused files
    const categories = {
      safe: [],
      review: [],
      duplicates: [],
      assets: [],
      tests: [],
    }

    for (const file of unusedFiles) {
      const filename = file.toLowerCase()

      if (
        filename.includes('test') ||
        filename.includes('spec') ||
        filename.includes('.test.') ||
        filename.includes('.spec.')
      ) {
        categories.tests.push(file)
      } else if (
        filename.includes('unused') ||
        filename.includes('old') ||
        filename.includes('backup') ||
        filename.includes('temp') ||
        filename.includes('example') ||
        filename.includes('demo')
      ) {
        categories.safe.push(file)
      } else if (
        file.includes('duplicate') ||
        filename.includes('copy') ||
        filename.includes('duplicate')
      ) {
        categories.duplicates.push(file)
      } else if (
        ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.css', '.scss'].some(ext =>
          filename.endsWith(ext)
        )
      ) {
        categories.assets.push(file)
      } else {
        categories.review.push(file)
      }
    }

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalSourceFiles: allSourceFiles.length,
        usedFiles: this.usedFiles.size,
        unusedFiles: unusedFiles.length,
      },
      categories,
      usedFiles: Array.from(this.usedFiles).sort(),
      recommendations: this.generateRecommendations(categories),
    }

    return report
  }

  generateRecommendations(categories) {
    return {
      phase1_safe: categories.safe.concat(categories.tests),
      phase2_duplicates: categories.duplicates,
      phase3_assets: categories.assets.filter(f => !f.includes('public/')),
      phase4_review: categories.review,
    }
  }
}

// Run analysis
try {
  const analyzer = new DependencyAnalyzer()
  const report = analyzer.analyze()

  // Write report
  writeFileSync(
    resolve(projectRoot, 'cleanup-dependency-report.json'),
    JSON.stringify(report, null, 2)
  )

  console.log('\n📊 Analysis Complete!')
  console.log(`✅ Used files: ${report.summary.usedFiles}`)
  console.log(`📦 Unused files: ${report.summary.unusedFiles}`)
  console.log(`🔒 Safe to archive: ${report.recommendations.phase1_safe.length}`)
  console.log(`🔄 Duplicates: ${report.recommendations.phase2_duplicates.length}`)
  console.log(`🖼️ Assets: ${report.recommendations.phase3_assets.length}`)
  console.log(`⚠️ Need review: ${report.recommendations.phase4_review.length}`)
  console.log('\n📄 Full report saved to: cleanup-dependency-report.json')
} catch (error) {
  console.error('❌ Analysis failed:', error.message)
  process.exit(1)
}
