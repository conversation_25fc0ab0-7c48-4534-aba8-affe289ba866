#!/usr/bin/env tsx

import { config } from 'dotenv'
import net from 'net'

// Load environment variables
config({ path: '.env.local' })

const DEFAULT_PORTS = [8080, 8081, 8082, 8083, 8084]

/**
 * WebSocket Health Check and Diagnostics Tool
 * Helps diagnose port conflicts and connection issues
 */

interface PortStatus {
  port: number
  available: boolean
  process?: string
  error?: string
}

async function checkPortAvailability(port: number): Promise<PortStatus> {
  return new Promise(resolve => {
    const server = net.createServer()

    server.listen(port, () => {
      server.close(() => {
        resolve({ port, available: true })
      })
    })

    server.on('error', (err: any) => {
      if (err.code === 'EADDRINUSE') {
        resolve({
          port,
          available: false,
          error: 'Port already in use',
          process: 'Unknown process',
        })
      } else if (err.code === 'EACCES') {
        resolve({
          port,
          available: false,
          error: 'Permission denied (try port > 1024)',
          process: 'System reserved',
        })
      } else {
        resolve({
          port,
          available: false,
          error: err.message,
        })
      }
    })
  })
}

async function getProcessUsingPort(port: number): Promise<string> {
  try {
    const { exec } = await import('child_process')
    return new Promise(resolve => {
      exec(`lsof -ti:${port}`, (error, stdout, stderr) => {
        if (error || stderr) {
          resolve('Unknown process')
        } else {
          const pid = stdout.trim()
          if (pid) {
            exec(`ps -p ${pid} -o comm=`, (psError, psStdout) => {
              resolve(psError ? `PID ${pid}` : psStdout.trim())
            })
          } else {
            resolve('No process found')
          }
        }
      })
    })
  } catch (error) {
    return 'Could not check process'
  }
}

async function checkWebSocketServer(): Promise<void> {
  try {
    const response = await fetch('http://localhost:8080/health', {
      method: 'GET',
      timeout: 5000,
    })

    if (response.ok) {
      const data = await response.json()
      console.log('✅ WebSocket server is responding:', data)
    } else {
      console.log('⚠️ WebSocket server responded with status:', response.status)
    }
  } catch (error: any) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ WebSocket server is not running (connection refused)')
    } else {
      console.log('❌ Cannot reach WebSocket server:', error.message)
    }
  }
}

async function checkNextJsServer(): Promise<void> {
  try {
    const response = await fetch('http://localhost:3000/api/websocket/design-system', {
      method: 'GET',
      timeout: 5000,
    })

    if (response.ok) {
      const data = await response.json()
      console.log('✅ Next.js WebSocket API is responding:', data)
    } else {
      console.log('⚠️ Next.js server responded with status:', response.status)
    }
  } catch (error: any) {
    console.log('❌ Cannot reach Next.js server:', error.message)
  }
}

async function main() {
  console.log('🔍 WebSocket Health Check & Diagnostics')
  console.log('=====================================\n')

  // Check port availability
  console.log('📊 Checking port availability...')
  for (const port of DEFAULT_PORTS) {
    const status = await checkPortAvailability(port)
    if (status.available) {
      console.log(`✅ Port ${port}: Available`)
    } else {
      const process = await getProcessUsingPort(port)
      console.log(`❌ Port ${port}: ${status.error} (Process: ${process})`)
    }
  }

  console.log('\n🔍 Checking running servers...')

  // Check WebSocket server
  console.log('\n📡 WebSocket Server (port 8080):')
  await checkWebSocketServer()

  // Check Next.js server
  console.log('\n⚡ Next.js Server (port 3000):')
  await checkNextJsServer()

  console.log('\n📋 Process Information:')
  try {
    const { exec } = await import('child_process')
    exec('ps aux | grep -E "(node|tsx|next)" | grep -v grep', (error, stdout) => {
      if (stdout) {
        console.log('Running Node.js processes:')
        console.log(stdout)
      } else {
        console.log('No Node.js processes found')
      }
    })
  } catch (error) {
    console.log('Could not check running processes')
  }

  console.log('\n💡 Troubleshooting Tips:')
  console.log('1. Stop all running processes: npm run websocket:stop')
  console.log('2. Kill processes using ports: kill $(lsof -ti:8080)')
  console.log('3. Start with fresh ports: npm run websocket:start -- 8081')
  console.log('4. Use dev:full instead of dev:all to avoid conflicts')
  console.log('5. Check for multiple terminal sessions running dev commands')
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Health check failed:', error)
    process.exit(1)
  })
}

export { checkPortAvailability, getProcessUsingPort, checkWebSocketServer }
