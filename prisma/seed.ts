import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create test company
  const company = await prisma.company.upsert({
    where: { id: 'company-123' },
    update: {},
    create: {
      id: 'company-123',
      name: 'Test Company',
      domains: ['test.com'],
      currentPlan: 'ENTERPRISE',
      subscriptionStatus: 'ACTIVE',
      maxUsers: 1000,
      allowedEmailDomains: ['test.com'],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  })

  console.log('✅ Created company:', company.name)

  // Create test users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { id: 'user-123' },
      update: {},
      create: {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'SUPERADMIN',
        companyId: company.id,
        password: await hash('password', 12),
        emailVerified: new Date(),
        onboardingCompleted: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    }),
    prisma.user.upsert({
      where: { id: 'user-456' },
      update: {},
      create: {
        id: 'user-456',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'ADMIN',
        companyId: company.id,
        password: await hash('password', 12),
        emailVerified: new Date(),
        onboardingCompleted: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    }),
    prisma.user.upsert({
      where: { id: 'user-789' },
      update: {},
      create: {
        id: 'user-789',
        email: '<EMAIL>',
        name: 'Employee User',
        role: 'EMPLOYEE',
        companyId: company.id,
        password: await hash('password', 12),
        emailVerified: new Date(),
        onboardingCompleted: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    }),
  ])

  console.log('✅ Created users:', users.length)

  // Create realistic analytics events over the past 30 days
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

  const eventTypes = [
    'page_view',
    'component_usage',
    'button_click',
    'form_submit',
    'theme_change',
    'navigation',
    'search',
    'export',
    'performance',
    'error',
    'session_start',
    'session_end',
    'user_signup',
    'user_login',
  ]

  const componentIds = [
    'button-primary',
    'button-secondary',
    'input-text',
    'input-email',
    'card-default',
    'modal-dialog',
    'dropdown-menu',
    'navigation-bar',
    'sidebar-menu',
    'chart-line',
    'chart-bar',
    'table-data',
    'form-contact',
    'badge-status',
    'tooltip-info',
  ]

  const analyticsEvents = []

  // Generate events for each day over the past 30 days
  for (let day = 0; day < 30; day++) {
    const dayDate = new Date(thirtyDaysAgo.getTime() + day * 24 * 60 * 60 * 1000)

    // Generate 50-200 events per day with realistic patterns
    const eventsPerDay = Math.floor(Math.random() * 150) + 50

    for (let i = 0; i < eventsPerDay; i++) {
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
      const userId = users[Math.floor(Math.random() * users.length)].id
      const componentId =
        Math.random() > 0.3 ? componentIds[Math.floor(Math.random() * componentIds.length)] : null

      // Add some realistic time distribution (more activity during work hours)
      const hour = Math.floor(Math.random() * 24)
      const minute = Math.floor(Math.random() * 60)
      const eventTime = new Date(dayDate)
      eventTime.setHours(hour, minute)

      // Create realistic metadata based on event type
      let metadata = {}
      switch (eventType) {
        case 'performance':
          metadata = {
            loadTime: Math.floor(Math.random() * 500) + 100, // 100-600ms
            renderTime: Math.floor(Math.random() * 100) + 10, // 10-110ms
            memoryUsage: Math.floor(Math.random() * 50) + 20, // 20-70MB
          }
          break
        case 'page_view':
          metadata = {
            page: ['/dashboard', '/settings', '/analytics', '/profile'][
              Math.floor(Math.random() * 4)
            ],
            referrer: Math.random() > 0.5 ? 'direct' : 'internal',
            sessionDuration: Math.floor(Math.random() * 300) + 30, // 30-330 seconds
          }
          break
        case 'component_usage':
          metadata = {
            action: ['render', 'interact', 'focus', 'blur'][Math.floor(Math.random() * 4)],
            props: { variant: 'primary', size: 'medium' },
          }
          break
        case 'button_click':
          metadata = {
            buttonText: ['Save', 'Cancel', 'Submit', 'Delete', 'Edit'][
              Math.floor(Math.random() * 5)
            ],
            position: { x: Math.floor(Math.random() * 1000), y: Math.floor(Math.random() * 800) },
          }
          break
        case 'error':
          metadata = {
            errorType: ['validation', 'network', 'runtime'][Math.floor(Math.random() * 3)],
            errorMessage: 'Sample error message',
            stack: 'Error stack trace...',
          }
          break
        default:
          metadata = {
            value: Math.floor(Math.random() * 100),
            category: 'general',
          }
      }

      analyticsEvents.push({
        eventType,
        userId,
        companyId: company.id,
        componentId,
        metadata,
        timestamp: eventTime,
      })
    }
  }

  // Insert analytics events in batches
  console.log(`🔄 Creating ${analyticsEvents.length} analytics events...`)

  const batchSize = 1000
  for (let i = 0; i < analyticsEvents.length; i += batchSize) {
    const batch = analyticsEvents.slice(i, i + batchSize)
    await prisma.analyticsEvent.createMany({
      data: batch,
    })
    console.log(
      `✅ Created batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(analyticsEvents.length / batchSize)}`
    )
  }

  // Create some analytics aggregates for performance
  const aggregates = []
  for (let day = 0; day < 30; day++) {
    const dayDate = new Date(thirtyDaysAgo.getTime() + day * 24 * 60 * 60 * 1000)

    aggregates.push({
      metricType: 'daily_page_views',
      metricName: 'page_views',
      value: Math.floor(Math.random() * 1000) + 500,
      companyId: company.id,
      timeframe: 'daily',
      timestamp: dayDate,
      metadata: {
        breakdown: {
          '/dashboard': Math.floor(Math.random() * 300) + 100,
          '/settings': Math.floor(Math.random() * 200) + 50,
          '/analytics': Math.floor(Math.random() * 150) + 25,
        },
      },
    })

    aggregates.push({
      metricType: 'daily_unique_users',
      metricName: 'unique_users',
      value: Math.floor(Math.random() * 50) + 20,
      companyId: company.id,
      timeframe: 'daily',
      timestamp: dayDate,
      metadata: {
        newUsers: Math.floor(Math.random() * 10) + 1,
        returningUsers: Math.floor(Math.random() * 40) + 15,
      },
    })
  }

  await prisma.analyticsAggregate.createMany({
    data: aggregates,
  })

  console.log('✅ Created analytics aggregates:', aggregates.length)

  // Create some context events for behavioral analytics
  const contextEvents = []
  for (let i = 0; i < 500; i++) {
    const userId = users[Math.floor(Math.random() * users.length)].id
    const eventTime = new Date(thirtyDaysAgo.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)

    contextEvents.push({
      eventType: ['page_view', 'feature_use', 'interaction'][Math.floor(Math.random() * 3)],
      userId,
      companyId: company.id,
      sessionId: `session-${Math.floor(Math.random() * 100)}`,
      eventData: {
        page: '/dashboard',
        feature: 'theme_toggle',
        duration: Math.floor(Math.random() * 300) + 30,
      },
      timestamp: eventTime,
      durationMs: Math.floor(Math.random() * 5000) + 1000,
    })
  }

  await prisma.contextEvent.createMany({
    data: contextEvents,
  })

  console.log('✅ Created context events:', contextEvents.length)

  console.log('🎉 Database seed completed successfully!')
}

main()
  .catch(e => {
    console.error('❌ Seed failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
