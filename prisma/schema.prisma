generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expires   DateTime
  used      Boolean  @default(false)
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
  @@index([token])
  @@index([expires])
}

model Company {
  id                     String                    @id @default(cuid())
  name                   String
  domains                String[]
  createdAt              DateTime                  @default(now())
  updatedAt              DateTime                  @updatedAt
  allowedEmailDomains    String[]
  isActive               Boolean                   @default(true)
  subscriptionStatus     SubscriptionStatus        @default(TRIAL)
  subscriptionStartDate  DateTime                  @default(now())
  subscriptionEndDate    DateTime?
  maxUsers               Int                       @default(5)
  currentPlan            String                    @default("TRIAL")
  billingEmail           String?
  billingName            String?
  billingAddress         String?
  featureFlags           FeatureFlag[]
  jobRoles               JobRole[]
  subDepartments         SubDepartment[]
  departments            TeamDepartment[]
  analyticsEvents        AnalyticsEvent[]
  designSystemComponents ComponentValidationRule[]
  designComponents       DesignSystemComponent[]
  users                  User[]
  FocusTask              FocusTask[]
  Goal                   Goal[]
  Skill                  Skill[]

  @@index([domains])
  @@index([subscriptionStatus])
  @@index([subscriptionEndDate])
}

model User {
  id                      String                   @id @default(cuid())
  email                   String                   @unique
  name                    String?
  role                    Role                     @default(EMPLOYEE)
  companyId               String?
  title                   String?
  jobTitle                String?
  department              String?
  subDepartment           String?
  phoneNumber             String?
  themeMode               String                   @default("system")
  colorScheme             String                   @default("emynent-light")
  emailNotifications      Boolean                  @default(true)
  inAppNotifications      Boolean                  @default(true)
  weeklyDigest            Boolean                  @default(false)
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  password                String?
  emailVerified           DateTime?
  onboardingCompleted     Boolean                  @default(false)
  contextData             Json?                    @default("{}")
  lastContextUpdate       DateTime?                @default(now())
  userPreferenceId        String?
  accounts                Account[]
  auditLogs               AuditLog[]
  customPermissions       CustomPermission[]
  sessions                Session[]
  analyticsEvents         AnalyticsEvent[]
  componentCollaborations ComponentCollaboration[]
  userActivities          UserActivity[]
  userProfile             UserProfile?
  company                 Company?                 @relation(fields: [companyId], references: [id])
  userPreference          UserPreference?          @relation(fields: [userPreferenceId], references: [id])
  FocusTask               FocusTask[]
  Goal                    Goal[]
  Skill                   Skill[]
  SkillAssessment         SkillAssessment[]
  TimeBlock               TimeBlock[]
  EnergyLevel             EnergyLevel[]
  dailyCheckIns           DailyCheckIn[]
  weeklyReviews           WeeklyReview[]
  achievementLogs         AchievementLog[]
  reflectionInsights      ReflectionInsight[]

  @@unique([email, companyId])
  @@index([companyId])
  @@index([email])
  @@index([lastContextUpdate])
  @@map("users")
}

model JobRole {
  id         String   @id @default(cuid())
  name       String
  department String?
  count      Int      @default(1)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  companyId  String
  company    Company  @relation(fields: [companyId], references: [id])

  @@unique([name, department, companyId])
  @@index([companyId])
}

model TeamDepartment {
  id        String   @id @default(cuid())
  name      String
  count     Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  companyId String
  company   Company  @relation(fields: [companyId], references: [id])

  @@unique([name, companyId])
  @@index([companyId])
}

model SubDepartment {
  id           String   @id @default(cuid())
  name         String
  description  String?
  departmentId String
  isActive     Boolean  @default(true)
  adminId      String?
  companyId    String
  createdBy    String
  createdAt    DateTime @default(now())
  updatedBy    String?
  updatedAt    DateTime @updatedAt
  company      Company  @relation(fields: [companyId], references: [id])

  @@unique([name, departmentId, companyId])
  @@index([companyId])
}

model SubDepartmentAuditLog {
  id                String   @id @default(cuid())
  subDepartmentId   String
  subDepartmentName String
  departmentId      String
  departmentName    String
  userId            String
  userName          String
  action            String
  details           String
  timestamp         DateTime @default(now())
  companyId         String

  @@index([companyId])
}

model CSRFToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([expiresAt])
}

model SubscriptionAuditLog {
  id          String             @id @default(cuid())
  companyId   String
  companyName String
  oldStatus   SubscriptionStatus
  newStatus   SubscriptionStatus
  updatedBy   String
  details     String
  createdAt   DateTime           @default(now())

  @@index([companyId])
  @@index([createdAt])
}

model AuditLog {
  id           String   @id @default(cuid())
  userId       String
  action       String
  details      Json?
  resourceType String?
  resourceId   String?
  ipAddress    String?
  createdAt    DateTime @default(now())
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([action])
  @@index([resourceType, resourceId])
}

model FeatureFlag {
  companyId String
  key       String
  enabled   Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  company   Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@id([companyId, key])
  @@index([companyId])
  @@index([key])
}

model CustomPermission {
  id         String   @id @default(cuid())
  userId     String
  permission String
  granted    Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, permission])
  @@index([userId])
  @@index([permission])
}

model UserContext {
  id             String   @id @default(cuid())
  userId         String   @unique
  role           String?
  companyId      String?
  preferences    Json?
  recentActions  Json?
  historicalData Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([userId])
  @@index([companyId])
}

model CoachingHistory {
  id             String    @id @default(cuid())
  userId         String
  recommendation String?
  completed      Boolean   @default(false)
  context        Json?
  createdAt      DateTime  @default(now())
  completedAt    DateTime?

  @@index([userId])
  @@index([completed])
}

model OnboardingData {
  id          String   @id @default(cuid())
  userId      String   @unique
  data        Json
  lastStep    String?
  lastUpdated DateTime @default(now()) @updatedAt
  createdAt   DateTime @default(now())

  @@index([userId])
}

model DesignSystemComponent {
  id             String                    @id @default(cuid())
  componentId    String                    @unique
  name           String
  category       String
  version        String
  description    String?
  isActive       Boolean                   @default(true)
  isPublished    Boolean                   @default(false)
  companyId      String
  styling        Json
  interactions   Json
  dataContract   Json
  metadata       Json
  versioning     Json
  validation     Json?
  createdAt      DateTime                  @default(now())
  updatedAt      DateTime                  @updatedAt
  createdBy      String
  lastModifiedBy String
  branches       ComponentBranch[]
  changelog      ComponentChangelogEntry[]
  examples       ComponentExample[]
  metrics        ComponentMetrics[]
  versions       ComponentVersion[]
  company        Company                   @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("design_system_components")
}

model ComponentVersion {
  id            String                @id @default(cuid())
  versionNumber String
  componentId   String
  componentData Json
  changeSummary String?
  changelog     String?
  isStable      Boolean               @default(false)
  publishedAt   DateTime?
  publishedBy   String?
  createdAt     DateTime              @default(now())
  createdBy     String
  component     DesignSystemComponent @relation(fields: [componentId], references: [id], onDelete: Cascade)

  @@unique([componentId, versionNumber])
  @@map("component_versions")
}

model ComponentBranch {
  id             String                @id @default(cuid())
  name           String
  componentId    String
  basedOnVersion String
  branchData     Json
  description    String?
  merged         Boolean               @default(false)
  mergedAt       DateTime?
  mergedBy       String?
  createdAt      DateTime              @default(now())
  createdBy      String
  component      DesignSystemComponent @relation(fields: [componentId], references: [id], onDelete: Cascade)

  @@unique([componentId, name])
  @@map("component_branches")
}

model ComponentExample {
  id          String                @id @default(cuid())
  name        String
  description String
  code        String
  preview     String?
  componentId String
  isActive    Boolean               @default(true)
  order       Int                   @default(0)
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt
  createdBy   String
  component   DesignSystemComponent @relation(fields: [componentId], references: [id], onDelete: Cascade)

  @@map("component_examples")
}

model ComponentChangelogEntry {
  id          String                @id @default(cuid())
  version     String
  componentId String
  changes     Json
  breaking    Boolean               @default(false)
  author      String
  createdAt   DateTime              @default(now())
  component   DesignSystemComponent @relation(fields: [componentId], references: [id], onDelete: Cascade)

  @@map("component_changelog")
}

model ComponentMetrics {
  id             String                @id @default(cuid())
  componentId    String
  renderTime     Float
  bundleSize     Int
  memoryUsage    Float
  updateTime     Float
  validationTime Float
  timestamp      DateTime              @default(now())
  component      DesignSystemComponent @relation(fields: [componentId], references: [id], onDelete: Cascade)

  @@map("component_metrics")
}

model ComponentValidationRule {
  id          String   @id @default(cuid())
  name        String   @unique
  category    String
  severity    String
  description String
  validator   String
  enabled     Boolean  @default(true)
  companyId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String
  company     Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("component_validation_rules")
}

model ComponentCollaboration {
  id             String    @id @default(cuid())
  componentId    String
  userId         String
  action         String
  startedAt      DateTime  @default(now())
  endedAt        DateTime?
  isActive       Boolean   @default(true)
  cursorPosition Json?
  selection      Json?
  changes        Json?
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([componentId, userId])
  @@map("component_collaboration")
}

model ComponentHotSwapLog {
  id          String   @id @default(cuid())
  componentId String
  oldVersion  String
  newVersion  String
  success     Boolean
  swapTime    Float
  errors      Json?
  triggeredBy String
  triggeredAt DateTime @default(now())

  @@map("component_hotswap_logs")
}

model ContextEvent {
  id         String   @id @default(cuid())
  userId     String
  companyId  String
  eventType  String
  eventData  Json     @default("{}")
  pageUrl    String?
  sessionId  String?
  timestamp  DateTime @default(now())
  durationMs Int?

  @@index([userId])
  @@index([timestamp])
  @@index([eventType])
  @@index([sessionId])
  @@index([companyId])
  @@map("context_events")
}

model BehavioralPattern {
  id              String   @id @default(cuid())
  userId          String
  patternType     String
  patternData     Json     @default("{}")
  confidenceScore Float    @default(0.0)
  detectedAt      DateTime @default(now())
  lastUpdated     DateTime @updatedAt

  @@unique([userId, patternType])
  @@index([userId])
  @@index([patternType])
  @@map("behavioral_patterns")
}

model AIInsight {
  id              String    @id @default(cuid())
  userId          String
  insightType     String
  insightContent  String
  confidenceScore Float     @default(0.0)
  contextSnapshot Json      @default("{}")
  generatedAt     DateTime  @default(now())
  expiresAt       DateTime?
  status          String    @default("active")

  @@index([userId])
  @@index([status])
  @@index([expiresAt])
  @@index([insightType])
  @@map("ai_insights")
}

model AnalyticsEvent {
  id          String   @id @default(cuid())
  eventType   String
  userId      String
  companyId   String?
  componentId String?
  action      String?
  section     String?
  subsection  String?
  sessionId   String?
  deviceType  String?
  duration    Int?
  metadata    Json     @default("{}")
  timestamp   DateTime @default(now())
  company     Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([companyId])
  @@index([eventType])
  @@index([timestamp])
  @@index([componentId])
  @@index([action])
  @@index([section])
  @@index([sessionId])
  @@map("analytics_events")
}

model AnalyticsAggregate {
  id         String   @id @default(cuid())
  companyId  String
  metricType String
  metricName String
  value      Float
  metadata   Json     @default("{}")
  timeframe  String
  timestamp  DateTime @default(now())

  @@index([companyId])
  @@index([metricType])
  @@index([timestamp])
  @@index([timeframe])
  @@map("analytics_aggregates")
}

model UserPersonalizationProfile {
  id              String   @id @default(cuid())
  userId          String   @unique
  preferences     Json     @default("{}")
  behaviorProfile Json     @default("{}")
  recommendations Json     @default("[]")
  uiConfiguration Json     @default("{}")
  learningData    Json     @default("{}")
  lastUpdated     DateTime @updatedAt
  createdAt       DateTime @default(now())

  @@index([userId])
  @@map("user_personalization_profiles")
}

model EnterpriseSettings {
  id                   String   @id @default(cuid())
  companyId            String   @unique
  securityPolicies     Json     @default("{}")
  complianceSettings   Json     @default("{}")
  billingConfiguration Json     @default("{}")
  featurePermissions   Json     @default("{}")
  customBranding       Json     @default("{}")
  integrationSettings  Json     @default("{}")
  auditConfiguration   Json     @default("{}")
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  @@index([companyId])
  @@map("enterprise_settings")
}

model SystemMetrics {
  id         String   @id @default(cuid())
  metricType String
  metricName String
  value      Float
  unit       String?
  tags       Json     @default("{}")
  source     String
  severity   String?
  timestamp  DateTime @default(now())

  @@index([metricType])
  @@index([timestamp])
  @@index([severity])
  @@index([source])
  @@map("system_metrics")
}

model OptimizationRule {
  id        String   @id @default(cuid())
  name      String
  category  String
  condition Json
  action    Json
  priority  Int      @default(0)
  enabled   Boolean  @default(true)
  companyId String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([category])
  @@index([enabled])
  @@index([companyId])
  @@map("optimization_rules")
}

model SystemAlert {
  id             String    @id @default(cuid())
  alertType      String
  severity       String
  title          String
  description    String
  source         String
  metadata       Json      @default("{}")
  status         String    @default("active")
  acknowledgedBy String?
  acknowledgedAt DateTime?
  resolvedBy     String?
  resolvedAt     DateTime?
  createdAt      DateTime  @default(now())

  @@index([alertType])
  @@index([severity])
  @@index([status])
  @@index([createdAt])
  @@map("system_alerts")
}

model UserSetting {
  id        String   @id @default(cuid())
  userId    String
  section   String
  key       String
  value     Json     @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, section, key])
  @@index([userId])
  @@index([section])
  @@map("user_settings")
}

model CompanySettings {
  id                  String   @id @default(cuid())
  companyId           String   @unique
  name                String?
  logo                String?
  primaryColor        String?
  secondaryColor      String?
  maxUsers            Int?
  defaultLocale       String?
  supportEmail        String?
  adminEmails         String[]
  allowedEmailDomains String[]
  isActive            Boolean  @default(true)
  settings            Json     @default("{}")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@index([companyId])
  @@map("company_settings")
}

model SettingsChangeLog {
  id         String   @id @default(cuid())
  userId     String
  changeType String
  field      String
  oldValue   String?
  newValue   String?
  createdAt  DateTime @default(now())

  @@index([userId])
  @@index([createdAt])
  @@map("settings_change_logs")
}

model UserActivity {
  id           String   @id @default(cuid())
  userId       String
  action       String
  resourceId   String?
  resourceType String?
  metadata     Json     @default("{}")
  ipAddress    String?
  userAgent    String?
  createdAt    DateTime @default(now())
  User         User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([createdAt])
  @@index([action])
  @@map("user_activities")
}

model UserPreference {
  id                  String   @id @default(cuid())
  userId              String   @unique
  theme               String?
  themeMode           String?
  themeCustomColors   String?
  colorScheme         String?
  locale              String?
  timezone            String?
  preferences         Json     @default("{}")
  navigationAnalytics Json     @default("{}")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  User                User[]

  @@index([userId])
  @@map("user_preferences")
}

model UserProfile {
  id                  String   @id @default(cuid())
  userId              String   @unique
  onboardingCompleted Boolean  @default(false)
  profilePicture      String?
  bio                 String?
  department          String?
  position            String?
  skills              Json     @default("[]")
  interests           Json     @default("[]")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("user_profiles")
}

model FocusTask {
  id               String    @id @default(cuid())
  title            String
  description      String?
  status           String    @default("pending")
  priority         String    @default("medium")
  dueDate          DateTime?
  completedAt      DateTime?
  estimatedMinutes Int?
  actualMinutes    Int?
  tags             String[]
  timeBlocks       Json      @default("[]")
  userId           String
  companyId        String
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  order            Int       @default(0)
  user             User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  company          Company   @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([companyId])
  @@index([status])
  @@index([priority])
  @@index([dueDate])
  @@index([createdAt])
  @@map("focus_tasks")
}

model Goal {
  id                String             @id @default(cuid())
  title             String
  description       String?
  type              GoalType           @default(OBJECTIVE)
  status            GoalStatus         @default(NOT_STARTED)
  progress          Float              @default(0.0)
  targetValue       Float?
  currentValue      Float?
  unit              String?
  category          GoalCategory       @default(PERSONAL)
  timeframe         GoalTimeframe      @default(QUARTERLY)
  startDate         DateTime           @default(now())
  targetDate        DateTime?
  completedAt       DateTime?
  priority          String             @default("medium")
  isPublic          Boolean            @default(false)
  tags              String[]
  metadata          Json               @default("{}")
  userId            String
  companyId         String
  parentGoalId      String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  company           Company            @relation(fields: [companyId], references: [id], onDelete: Cascade)
  parentGoal        Goal?              @relation("GoalHierarchy", fields: [parentGoalId], references: [id])
  subGoals          Goal[]             @relation("GoalHierarchy")
  keyResults        KeyResult[]
  goalUpdates       GoalUpdate[]
  goalCollaborators GoalCollaborator[]

  @@index([userId])
  @@index([companyId])
  @@index([status])
  @@index([timeframe])
  @@index([category])
  @@index([parentGoalId])
  @@index([targetDate])
  @@index([createdAt])
  @@map("goals")
}

model KeyResult {
  id           String    @id @default(cuid())
  title        String
  description  String?
  targetValue  Float
  currentValue Float     @default(0.0)
  unit         String
  status       String    @default("not_started")
  dueDate      DateTime?
  completedAt  DateTime?
  goalId       String
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  goal         Goal      @relation(fields: [goalId], references: [id], onDelete: Cascade)

  @@index([goalId])
  @@index([status])
  @@index([dueDate])
  @@map("key_results")
}

model GoalUpdate {
  id          String   @id @default(cuid())
  content     String
  progress    Float?
  attachments Json?    @default("[]")
  goalId      String
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  goal        Goal     @relation(fields: [goalId], references: [id], onDelete: Cascade)

  @@index([goalId])
  @@index([userId])
  @@index([createdAt])
  @@map("goal_updates")
}

model GoalCollaborator {
  id         String    @id @default(cuid())
  role       String    @default("viewer")
  goalId     String
  userId     String
  invitedBy  String
  acceptedAt DateTime?
  createdAt  DateTime  @default(now())
  goal       Goal      @relation(fields: [goalId], references: [id], onDelete: Cascade)

  @@unique([goalId, userId])
  @@index([goalId])
  @@index([userId])
  @@map("goal_collaborators")
}

enum Role {
  EMPLOYEE
  MANAGER
  DIRECTOR
  ADMIN
  SUPERADMIN
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  TRIAL
}

enum GoalType {
  OBJECTIVE
  KEY_RESULT
  MILESTONE
  HABIT
}

enum GoalStatus {
  NOT_STARTED
  IN_PROGRESS
  ON_TRACK
  AT_RISK
  COMPLETED
  CANCELLED
}

enum GoalCategory {
  PERSONAL
  PROFESSIONAL
  LEARNING
  HEALTH
  FINANCIAL
  TEAM
  COMPANY
}

enum GoalTimeframe {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
  CUSTOM
}

enum SkillCategory {
  TECHNICAL
  SOFT_SKILLS
  LEADERSHIP
  COMMUNICATION
  ANALYTICAL
  CREATIVE
  DOMAIN_SPECIFIC
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

model Skill {
  id           String            @id @default(cuid())
  name         String
  description  String?
  category     SkillCategory     @default(TECHNICAL)
  currentLevel SkillLevel        @default(BEGINNER)
  targetLevel  SkillLevel?
  importance   Int               @default(1) // 1-5 scale
  lastAssessed DateTime?
  notes        String?
  tags         String[]
  userId       String
  companyId    String
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  user         User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  company      Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  assessments  SkillAssessment[]

  @@index([userId])
  @@index([companyId])
  @@index([category])
  @@index([currentLevel])
  @@index([lastAssessed])
  @@map("skills")
}

model SkillAssessment {
  id             String     @id @default(cuid())
  skillId        String
  userId         String
  score          Float // 0-100 percentage
  level          SkillLevel
  assessmentDate DateTime   @default(now())
  assessmentType String     @default("self") // self, peer, manager, 360
  notes          String?
  feedback       Json?      @default("{}")
  nextSteps      String?
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  skill          Skill      @relation(fields: [skillId], references: [id], onDelete: Cascade)
  user           User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([skillId])
  @@index([userId])
  @@index([assessmentDate])
  @@index([level])
  @@map("skill_assessments")
}

model TimeBlock {
  id                 String   @id @default(cuid())
  title              String
  description        String?
  startTime          DateTime
  endTime            DateTime
  type               String // 'focus', 'meeting', 'break', 'administrative', 'creative', 'learning'
  energyLevel        String? // 'low', 'medium', 'high'
  userId             String
  source             String   @default("manual") // 'manual', 'calendar', 'ai_suggested'
  completed          Boolean  @default(false)
  actualProductivity Float? // 0-1 scale
  tags               String[]
  metadata           Json     @default("{}")
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([startTime])
  @@index([endTime])
  @@index([type])
  @@index([source])
  @@index([completed])
  @@index([createdAt])
  @@map("time_blocks")
}

model EnergyLevel {
  id        String   @id @default(cuid())
  level     Int // 1-5 scale
  mood      String
  context   String?
  factors   String[] // e.g., ['caffeine', 'sleep', 'exercise']
  timestamp DateTime @default(now())
  userId    String
  metadata  Json     @default("{}")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([timestamp])
  @@index([level])
  @@index([mood])
  @@map("energy_levels")
}

model Analytics {
  id          String   @id @default(cuid())
  eventType   String
  eventAction String
  entityId    String?
  userId      String?
  metadata    Json     @default("{}")
  timestamp   DateTime @default(now())

  @@index([eventType])
  @@index([eventAction])
  @@index([userId])
  @@index([timestamp])
  @@map("analytics")
}

// Personal Reflection System Models
model DailyCheckIn {
  id            String   @id @default(cuid())
  date          String // YYYY-MM-DD format for easy querying
  mood          String
  priorities    String
  energyLevel   Int // 1-10 scale
  challenges    String?
  achievements  String?
  learnings     String?
  gratitude     String?
  tomorrowFocus String?
  userId        String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([userId])
  @@index([date])
  @@index([createdAt])
  @@map("daily_check_ins")
}

model WeeklyReview {
  id                 String   @id @default(cuid())
  weekStartDate      DateTime
  weekEndDate        DateTime
  accomplishments    String
  challenges         String
  lessons            String
  nextWeekGoals      String
  moodTrend          String?
  energyTrend        String?
  satisfactionRating Int      @default(0) // 1-10 scale
  improvementAreas   String?
  userId             String
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, weekStartDate])
  @@index([userId])
  @@index([weekStartDate])
  @@index([createdAt])
  @@map("weekly_reviews")
}

model AchievementLog {
  id           String   @id @default(cuid())
  title        String
  description  String
  impactLevel  String // 'low', 'medium', 'high'
  category     String // 'project', 'learning', 'leadership', 'personal', 'team'
  dateAchieved DateTime @default(now())
  tags         String[]
  linkedGoals  String[] // Goal IDs that this achievement relates to
  reflection   String?
  userId       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([dateAchieved])
  @@index([category])
  @@index([impactLevel])
  @@index([createdAt])
  @@map("achievement_logs")
}

model ReflectionInsight {
  id            String    @id @default(cuid())
  insightType   String // 'pattern', 'recommendation', 'trend'
  content       String
  confidence    Float     @default(0.0) // 0-1 scale
  dataPoints    Json      @default("[]") // Array of data sources that contributed to this insight
  isActive      Boolean   @default(true)
  userId        String
  generatedAt   DateTime  @default(now())
  lastValidated DateTime?
  validatedBy   String? // 'ai', 'user', 'system'
  feedbackScore Int? // User feedback: 1-5 scale
  metadata      Json      @default("{}")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([insightType])
  @@index([generatedAt])
  @@index([isActive])
  @@index([confidence])
  @@map("reflection_insights")
}
