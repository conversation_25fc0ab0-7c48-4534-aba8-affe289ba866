# 🏗️ EMYNENT FOLDER STRUCTURE ENFORCER

**MANDATORY: <PERSON>ursor must validate against this structure before any code changes**

## 🚨 CRITICAL RULES - NO EXCEPTIONS

### **1. ALWAYS CHECK CURRENT STRUCTURE FIRST**
```bash
# Before any file creation/modification, run:
find . -maxdepth 3 -type f -name "*.md" -o -name "*.json" -o -name "*.ts" -o -name "*.js" | grep -E "(config|env|readme)" | head -20
```

### **2. ROOT LEVEL FILES - STRICT ALLOWLIST**
**ONLY these files belong in project root:**
```
✅ ALLOWED:
- package.json, package-lock.json
- tsconfig.json, next.config.js, tailwind.config.ts, postcss.config.js, vitest.config.ts
- middleware.ts, next-env.d.ts
- .gitignore, .prettierrc, .prettierignore, .eslintrc.json
- README.md, CHANGELOG.md
- .DS_Store (auto-generated, ignore)

❌ FORBIDDEN IN ROOT:
- .env.* files (must go to config/environments/)
- CLEANUP_*.md files (must go to docs/ or delete)
- Any config files except the allowed list
- Temporary debug files
- Any .roo directory or similar
```

### **3. CONFIGURATION HIERARCHY**
```
config/
├── environments/          # ALL environment files here
│   ├── .env.example
│   ├── .env.local
│   └── .env.production
├── typescript/           # TS configs
├── testing/             # Test configs  
├── linting/             # ESLint configs
└── build/               # Build configs
```

### **4. DOCUMENTATION HIERARCHY**
```
docs/
├── architecture/        # Technical docs
├── api/                # API documentation
├── deployment/         # Deployment guides
└── cleanup/            # Cleanup logs (move existing cleanup files here)
```

## 🔍 MANDATORY VALIDATION PROCESS

### **Before Creating ANY File:**
1. **Check target directory exists and is appropriate**
2. **Verify no duplicates exist elsewhere**
3. **Confirm follows naming conventions**
4. **Update this enforcer if new patterns emerge**

### **File Placement Rules:**
```typescript
const FILE_PLACEMENT_RULES = {
  '.env*': 'config/environments/',
  '*config*.ts|js': 'config/[category]/', 
  'README.md': 'docs/[category]/ OR root (only main)',
  'CLEANUP*.md': 'docs/cleanup/ OR delete',
  '*.test.*': 'tests/[category]/',
  'components/*': 'src/components/[domain]/',
  'api/*': 'src/app/api/[feature]/',
  'pages/*': 'src/app/[route]/',
  'utilities/*': 'src/lib/[category]/',
  'types/*': 'src/types/',
  'docs/*': 'docs/[category]/',
  'scripts/*': 'scripts/',
  'tasks/*': 'tasks/',
  'infra/*': 'infra/',
  'archive/*': 'archive/ (deprecated code only)'
}
```

## 🎯 CURSOR COMMANDS FOR STRUCTURE VALIDATION

### **1. Pre-Change Validation**
```bash
# Run before any file operations
npm run structure:validate || echo "Structure validation needed"
```

### **2. File Creation Protocol**
```typescript
// ALWAYS use this pattern:
const targetPath = validateFilePlacement(fileName, fileType, domain);
if (!targetPath.isValid) {
  throw new Error(`Invalid placement: ${targetPath.suggestion}`);
}
```

### **3. Cleanup Protocol**
```bash
# When you see violations:
1. Identify misplaced files
2. Create proper directory structure  
3. Move files to correct locations
4. Update imports/references
5. Remove empty directories
6. Update documentation
```

## 🧹 IMMEDIATE CLEANUP NEEDED

### **Current Violations to Fix:**
1. **Move cleanup files**: `CLEANUP_READY.md`, `FINAL_CLEANUP_READY.md` → `docs/cleanup/`
2. **Remove deprecated**: `.roo/` directory 
3. **Organize configs**: Move any scattered config files to `config/[category]/`
4. **Environment files**: Ensure all `.env*` files are in `config/environments/`
5. **Remove duplicates**: Check for duplicate configs in multiple locations

### **Forbidden Directories to Remove:**
- `.roo/` - deprecated
- Any `temp*/` or `debug*/` directories
- Backup directories older than 30 days

## 📋 CURSOR INTEGRATION CHECKLIST

**For EVERY code change, Cursor must:**

- [ ] Check if file placement follows structure rules
- [ ] Verify no duplicates exist
- [ ] Update imports if files are moved
- [ ] Maintain alphabetical organization within directories
- [ ] Follow naming conventions (kebab-case for directories, PascalCase for components)
- [ ] Update related documentation
- [ ] Clean up empty directories after moves

## 🚀 STRUCTURE MAINTENANCE COMMANDS

```bash
# Daily structure health check
npm run structure:health-check

# Auto-fix common violations  
npm run structure:auto-fix

# Generate structure report
npm run structure:report
```

---

**⚠️ WARNING**: Any pull request that violates this structure will be rejected. Cursor must validate against this document before suggesting any file operations.

**✅ SUCCESS CRITERIA**: When followed precisely, this structure ensures:
- Zero configuration conflicts
- Clear separation of concerns  
- Easy navigation for developers
- Consistent project organization
- No duplicate or misplaced files 