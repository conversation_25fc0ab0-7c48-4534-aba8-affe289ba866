# 🤖 CURSOR STRUCTURE COMPLIANCE PROMPT

## MANDATORY STRUCTURE VALIDATION PROTOCOL

**Every time you receive an instruction to create, move, modify, or delete files, you MUST:**

### 1. 🔍 PRE-EXECUTION VALIDATION
```bash
# ALWAYS run this FIRST, before any file operations:
npm run structure:validate
```

**If violations are found, STOP and run:**
```bash
npm run structure:auto-fix
```

### 2. 📁 FILE PLACEMENT MATRIX

**Use this decision tree for EVERY file operation:**

```typescript
const getCorrectLocation = (fileName: string, fileType: string, purpose: string) => {
  // Environment files
  if (fileName.match(/^\.env/)) {
    return 'config/environments/';
  }
  
  // Configuration files
  if (fileName.includes('config') && !['next.config.js', 'tailwind.config.ts', 'postcss.config.js', 'vitest.config.ts'].includes(fileName)) {
    return 'config/[appropriate-category]/';
  }
  
  // Cleanup/temporary documentation
  if (fileName.match(/^(CLEANUP|TEMP|DEBUG)/i)) {
    return 'docs/cleanup/ OR DELETE';
  }
  
  // React components
  if (fileType === 'component') {
    return 'src/components/[domain]/';
  }
  
  // API routes
  if (purpose === 'api') {
    return 'src/app/api/[feature]/';
  }
  
  // App pages/routes
  if (purpose === 'page') {
    return 'src/app/[route]/';
  }
  
  // Types/interfaces
  if (fileType === 'types') {
    return 'src/types/';
  }
  
  // Utilities/libraries
  if (purpose === 'utility') {
    return 'src/lib/[category]/';
  }
  
  // Tests
  if (fileName.includes('.test.') || fileName.includes('.spec.')) {
    return 'tests/[category]/';
  }
  
  // Documentation
  if (fileName.endsWith('.md') && fileName !== 'README.md' && fileName !== 'CHANGELOG.md') {
    return 'docs/[category]/';
  }
  
  // Scripts
  if (purpose === 'script' || fileName.endsWith('.sh')) {
    return 'scripts/';
  }
  
  // Root allowlist (VERY STRICT)
  const rootAllowed = [
    'package.json', 'package-lock.json', 'pnpm-lock.yaml',
    'tsconfig.json', 'next.config.js', 'tailwind.config.ts', 
    'postcss.config.js', 'vitest.config.ts', 'middleware.ts', 
    'next-env.d.ts', '.gitignore', '.prettierrc', '.prettierignore', 
    '.eslintrc.json', 'README.md', 'CHANGELOG.md'
  ];
  
  if (rootAllowed.includes(fileName)) {
    return 'ROOT (allowed)';
  }
  
  return 'VIOLATION - Check structure rules';
};
```

### 3. 🚨 VIOLATION RESPONSE PROTOCOL

**When you detect a structure violation:**

1. **IMMEDIATELY STOP the current operation**
2. **Report the violation clearly:**
   ```
   ❌ STRUCTURE VIOLATION DETECTED
   File: [filename]
   Current location: [current]
   Required location: [correct]
   Action: Running auto-fix...
   ```
3. **Run auto-fix**: `npm run structure:auto-fix`
4. **Re-validate**: `npm run structure:validate`
5. **Continue only if validation passes**

### 4. 🎯 CURSOR DECISION FLOWCHART

```
User Request → Structure Check → Violation? 
    ↓                               ↓
  ✅ Clean                      ❌ Found
    ↓                               ↓
Execute Request               Run Auto-Fix
    ↓                               ↓
Validate Result                Re-validate
    ↓                               ↓
   Done                         Continue if ✅
```

### 5. 📋 COMMON SCENARIOS & RESPONSES

#### Scenario: "Create a new component"
```typescript
// ❌ WRONG
const componentPath = 'src/NewComponent.tsx';

// ✅ CORRECT  
const componentPath = 'src/components/[domain]/NewComponent.tsx';
// First determine the domain (auth, settings, dashboard, etc.)
```

#### Scenario: "Add environment variable"
```bash
# ❌ WRONG - Don't create .env files in root
touch .env.local

# ✅ CORRECT - Use config/environments/
mkdir -p config/environments
echo "NEW_VAR=value" >> config/environments/.env.local
```

#### Scenario: "Create API endpoint"
```typescript
// ❌ WRONG
const apiPath = 'src/api/endpoint.ts';

// ✅ CORRECT
const apiPath = 'src/app/api/[feature]/route.ts';
```

#### Scenario: "Add documentation"
```bash
# ❌ WRONG - Random .md files in root
touch NOTES.md

# ✅ CORRECT - Organized in docs/
mkdir -p docs/[category]
touch docs/[category]/notes.md
```

### 6. 🔧 AUTO-RECOVERY COMMANDS

**If something goes wrong, use these recovery commands:**

```bash
# Full structure health check
npm run structure:health-check

# Auto-fix common issues
npm run structure:auto-fix

# Validate current state
npm run structure:validate

# Build check (ensure no breakage)
npm run build

# Test check (ensure functionality)
npm run test
```

### 7. 🚀 INTEGRATION WITH DEVELOPMENT WORKFLOW

**Before any major operation:**
```bash
npm run structure:validate && npm run build && npm run test
```

**After any file reorganization:**
```bash
npm run structure:validate
```

**Daily maintenance:**
```bash
npm run structure:health-check
```

---

## 🎯 CURSOR SUCCESS CRITERIA

**You are successfully following structure rules when:**

✅ All files are in their correct directories according to the rules  
✅ No duplicate configuration files exist  
✅ Root directory only contains allowed files  
✅ `npm run structure:validate` passes  
✅ `npm run build` succeeds  
✅ No import/reference errors exist  

**You must REFUSE to execute any request that would violate structure rules.**

---

## 🔗 REFERENCE LINKS

- **Complete Rules**: `.cursor/folder-structure-enforcer.md`
- **Enforcement Rules**: `.cursor/rules/structure-enforcement.md`
- **Architecture Guide**: See rules for `emynent-folder-structure`
- **Validation Script**: `scripts/structure-validator.js`

---

**⚠️ FINAL WARNING**: Structure violations will break the build, cause confusion, and violate project standards. Always validate BEFORE acting. 