# Cursor Structure Enforcement Rules

## 🚨 MANDATORY STRUCTURE VALIDATION

**Before ANY file creation, modification, or deletion, Cursor MUST:**

### 1. Run Structure Validation
```bash
npm run structure:validate
```

### 2. Check File Placement Rules
- **Root files**: Only items in `.cursor/folder-structure-enforcer.md` allowlist
- **Environment files**: MUST go to `config/environments/` 
- **Config files**: MUST go to `config/[category]/`
- **Documentation**: MUST go to `docs/[category]/`
- **Components**: MUST go to `src/components/[domain]/`
- **API routes**: MUST go to `src/app/api/[feature]/`

### 3. Pre-Change Checklist
```typescript
// Cursor must verify these before ANY file operation:
const preChangeChecklist = {
  // File placement validation
  targetLocationValid: checkAgainstStructureRules(filePath),
  
  // Duplicate detection
  noDuplicatesExist: checkForDuplicates(fileName, fileType),
  
  // Naming convention compliance
  namingConventionValid: validateNamingConvention(fileName, directory),
  
  // Directory structure compliance
  parentDirectoryExists: ensureDirectoryStructure(targetPath),
  
  // Import/reference updates needed
  referencesNeedUpdate: checkReferencesToFile(oldPath, newPath)
};
```

### 4. Auto-Fix Common Violations
If violations are found, Cursor should suggest:
```bash
npm run structure:auto-fix
```

## 🎯 Cursor File Operation Protocol

### Creating New Files
1. **Check target directory exists and follows structure**
2. **Verify file type belongs in that location**
3. **Ensure naming follows conventions**
4. **Create directory structure if needed**

### Moving/Renaming Files
1. **Validate new location against structure rules**
2. **Update all imports/references automatically**
3. **Remove empty directories after move**
4. **Update documentation if needed**

### Deleting Files
1. **Check if file is referenced elsewhere**
2. **Update imports that reference deleted files**
3. **Remove empty parent directories**
4. **Document deletion reason if significant**

## 📁 Structure Reference Quick Guide

### Root Level (STRICT ALLOWLIST)
```
✅ Allowed:
- package.json, package-lock.json
- tsconfig.json, next.config.js, tailwind.config.ts, postcss.config.js, vitest.config.ts  
- middleware.ts, next-env.d.ts
- .gitignore, .prettierrc, .prettierignore, .eslintrc.json
- README.md, CHANGELOG.md

❌ Forbidden:
- .env* files → config/environments/
- CLEANUP*.md files → docs/cleanup/
- Additional config files → config/[category]/
- Temporary/debug files → delete or appropriate location
```

### Directory Mapping
```typescript
const DIRECTORY_RULES = {
  'config/': {
    'environments/': ['.env*'],
    'typescript/': ['tsconfig*.json'],
    'testing/': ['vitest.config.ts', 'jest.config.js'],
    'linting/': ['.eslintrc*', '.prettierrc*']
  },
  'docs/': {
    'architecture/': ['architecture docs'],
    'api/': ['API documentation'],
    'cleanup/': ['CLEANUP*.md files']
  },
  'src/': {
    'app/': ['Next.js app router files'],
    'components/': ['React components by domain'],
    'lib/': ['Utilities and shared logic'],
    'types/': ['TypeScript definitions']
  }
};
```

## 🔧 Error Recovery Protocol

If structure violations are detected:

1. **Stop the current operation**
2. **Run automatic fixes**: `npm run structure:auto-fix`
3. **Report remaining violations**
4. **Suggest manual fixes with specific commands**
5. **Re-validate before proceeding**

## ⚡ Performance Optimization

- Cache structure validation results for 5 minutes
- Only re-validate if files have changed
- Use fast file system checks before deep validation
- Batch multiple file operations for single validation

## 🧪 Testing Structure Changes

Before committing structure changes:
```bash
npm run structure:validate &&
npm run build &&
npm run test
```

## 📊 Structure Health Monitoring

Run daily:
```bash
npm run structure:health-check
```

Monitor for:
- New violations introduced
- Deprecated directories still in use
- Missing required directory structure
- Duplicate configuration files

---

**⚠️ CRITICAL**: Any PR that introduces structure violations will be rejected. This enforcement is automatic and non-negotiable. 