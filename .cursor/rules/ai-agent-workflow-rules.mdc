---
description:
globs:
alwaysApply: false
---
# 🤖 AI Agent Workflow & Development Rules

**Universal guidelines for AI-assisted development across all IDEs and agents**

> This document establishes workflow standards for AI agents (Claude, GPT, Copilot, etc.) working in any development environment (VS Code, Cursor, PyCharm, IntelliJ, etc.)

## 📝 Related Documentation
- **Development Standards**: See @ai-agent-development-rules.md for coding principles
- **Testing Framework**: See @emynent-testing.mdc for quality requirements
- **Project Rules**: See @emynent-project-rules.mdc for project-specific standards

---

## 🎯 Universal AI Agent Behavior

### **Context-Awareness Marker**
- **Rule**: Begin every response with appropriate context indicator (🤖, 🛠️, 📝, etc.) to confirm understanding
- **Purpose**: Establishes clear communication and adherence to workflow preferences

### **Multi-IDE Compatibility**
- **Consistent Standards**: Follow same coding patterns regardless of IDE/editor
- **Tool-Agnostic Approach**: Focus on principles, not specific tool features
- **Universal Shortcuts**: Use industry-standard patterns that work across environments
- **Cross-Platform Support**: Ensure solutions work on Windows, macOS, and Linux

---

## 🔄 Development Workflow (Strictly Enforced)

### **1. Understand & Breakdown**
- **Comprehend Clearly**: Fully understand each request or task before proceeding
- **Break Down Tasks**: Divide complex requests into small, actionable subtasks
- **Clarify Ambiguity**: Ask specific questions before implementation when requirements are unclear
- **Validate Understanding**: Confirm interpretation matches user expectations

### **2. Thoughtful Implementation**
- **Focus on Request**: Address only the specific task requested
- **Avoid Scope Creep**: Never modify unrelated areas without explicit instruction
- **Preserve Patterns**: Maintain existing architecture and patterns unless instructed otherwise
- **Prevent Duplication**: Search existing codebase before creating new solutions
- **Clean Implementation**: Remove scaffolding, mocks, or experimental code
- **Validate Functionality**: Ensure all existing features continue working

### **3. Error Handling & Scope Management**
- **Clear Error Messages**: Provide specific, actionable error information
- **Suggest Alternatives**: Offer multiple solutions when implementation fails
- **Respect Boundaries**: Stay within designated folders and file structures
- **Feature Flag Awareness**: Honor existing feature toggles and flags
- **Future-Ready**: Prepare for planned features without implementing them

### **4. Rigorous Quality Assurance**
- **Test Everything**: Unit, integration, and E2E tests for all changes
- **Performance Validation**: Ensure speed requirements are met
- **Accessibility Compliance**: Verify WCAG 2.1 AA standards
- **Security Review**: Check for vulnerabilities and security issues
- **Cross-Browser Testing**: Validate functionality across browsers
- **Documentation Updates**: Keep docs current with changes

### **5. Completion & Validation**
- **Functionality Verification**: Confirm implementation matches original intent
- **Code Quality**: Ensure maintainable, scalable, well-documented code
- **Standard Compliance**: Follow established conventions and patterns
- **Impact Assessment**: Document changes and affected areas
- **User Confirmation**: Verify solution meets user expectations

---

## 📋 Task Master Integration

### **Primary Interaction Methods**
1. **MCP Tools (Recommended)**: For AI agents and integrated environments
2. **CLI Commands**: Direct terminal commands when MCP unavailable
3. **IDE Extensions**: Tool-specific integrations when available

### **Standard Development Process**
```mermaid
graph TD
    A[Start Session] --> B[Get Current Status]
    B --> C[Identify Next Task]
    C --> D[Analyze Complexity]
    D --> E[Break Down if Complex]
    E --> F[Implement with Tests]
    F --> G[Mark Complete]
    G --> H[Update Dependencies]
    H --> I[Document Changes]
```

### **Core Workflow Commands**
```bash
# Task Management (adapt to your environment)
task-master list                    # View all tasks
task-master next                    # Get next task to work on
task-master show <id>               # View specific task details
task-master expand --id=<id>        # Break down complex tasks
task-master set-status --id=<id> --status=done  # Mark complete
task-master update --from=<id> --prompt="..."   # Update future tasks
```

### **Implementation Drift Handling**
- **Multiple Tasks**: Use `update` for multiple future tasks when implementation changes
- **Single Task**: Use `update_task` for specific task modifications  
- **Progress Logging**: Use `update_subtask` for detailed implementation notes
- **Research Mode**: Include `--research` flag for AI-backed updates

---

## 📝 Universal Rule Management

### **Required Rule Structure**
```markdown
---
description: Clear, one-line description of what the rule enforces
globs: path/to/files/*.ext, other/path/**/*
alwaysApply: boolean
---

- **Main Points in Bold**
  - Sub-points with details
  - Examples and explanations
```

### **File References**
- **Use @filename**: Reference other rule files with @filename syntax
- **Code References**: Reference actual code files when providing examples
- **Cross-References**: Link related rules and documentation

### **Code Examples**
```typescript
// ✅ DO: Show good examples with clear explanations
const goodExample = () => {
  // Clear, self-documenting code
  return processUserInput(sanitizedData)
}

// ❌ DON'T: Show anti-patterns with explanations
const badExample = () => {
  // Unclear, unmaintainable code
  return doStuff(x)
}
```

### **Best Practices**
- **Bullet Points**: Use for clarity and scanability
- **Concise Descriptions**: Keep explanations brief but complete
- **Practical Examples**: Include both positive and negative examples
- **Real Code**: Reference actual codebase over theoretical examples
- **Consistent Formatting**: Maintain uniform structure across rules

---

## 🏗️ Architecture & Standards Compliance

### **Folder Structure Adherence**
**Follow established patterns** (see @emynent-folder-structure.mdc):
- `/src/app/` → Routes and layouts
- `/src/components/` → UI components by domain
- `/src/controllers/` → API request handlers
- `/src/services/` → Business logic
- `/src/lib/` → Utilities and shared logic
- `/src/types/` → TypeScript definitions
- `/tests/` → Test files
- `/domains/` → Domain-driven modules
- `/packages/` → Shared libraries

### **Component Development Standards**
- **Design System**: Source from established component libraries
- **Duplicate Prevention**: Search existing components before creating new ones
- **Accessibility**: Build WCAG 2.1 AA compliance from start
- **Performance**: Optimize for speed requirements
- **Testing**: Include comprehensive test coverage

### **API Development Standards**
- **Controller Pattern**: Separate request handling from business logic
- **Service Layer**: Keep business logic pure and testable
- **Validation**: Use strong typing and schema validation
- **Database**: Follow established ORM patterns
- **Documentation**: Maintain API documentation

---

## ⚡ Performance & Security Standards

### **Performance Requirements**
| Metric | Target | Validation Method |
|--------|--------|------------------|
| **API Response** | <100ms | Load testing with k6 |
| **UI Interactions** | <200ms | Browser performance tools |
| **Page Load** | <1.5s initial, <500ms navigation | Lighthouse |
| **Bundle Size** | <500KB | Build analysis |
| **Accessibility** | WCAG 2.1 AA | axe-core validation |

### **Security Standards**
- **OWASP Compliance**: Follow Top 10 security guidelines
- **Input Validation**: Sanitize all user inputs
- **Authentication**: Implement proper session management
- **Authorization**: Enforce role-based access control
- **Data Protection**: Encrypt sensitive information
- **Audit Logging**: Track security-relevant actions

---

## 🔧 Tool-Specific Adaptations

### **VS Code / Cursor**
- **Extensions**: Leverage IDE-specific AI extensions
- **Shortcuts**: Use built-in keyboard shortcuts
- **Debugging**: Utilize integrated debugging tools
- **Git Integration**: Use built-in source control

### **JetBrains (PyCharm, IntelliJ, WebStorm)**
- **AI Assistant**: Integrate with JetBrains AI
- **Refactoring**: Use powerful refactoring tools
- **Code Analysis**: Leverage static analysis features
- **Database Tools**: Use integrated database support

### **Command Line / Terminal**
- **CLI Tools**: Master essential command-line tools
- **Scripting**: Create reusable automation scripts
- **Package Management**: Use appropriate package managers
- **Process Management**: Handle background processes

### **Cloud IDEs (GitHub Codespaces, GitLab, etc.)**
- **Remote Development**: Optimize for cloud environments
- **Collaboration**: Use shared development features
- **Resource Management**: Be mindful of compute limits
- **Sync & Backup**: Ensure work is properly saved

---

## 🔄 Continuous Improvement Process

### **Pattern Recognition**
- **Monitor Patterns**: Identify repeated implementations across files
- **Common Errors**: Track frequent error patterns for rule creation
- **Success Metrics**: Measure effectiveness of established patterns
- **Feedback Integration**: Incorporate user feedback into workflow improvements

### **Rule Evolution**
- **Regular Updates**: Review and update rules based on experience
- **New Patterns**: Add rules when 3+ files use same pattern
- **Deprecation**: Remove outdated rules and patterns
- **Cross-Reference**: Maintain consistency across related rules

### **Quality Metrics**
- **Code Quality**: Track maintainability and technical debt
- **Performance**: Monitor speed and resource usage
- **Security**: Measure vulnerability detection and prevention
- **User Satisfaction**: Assess developer experience and productivity

---

## 📊 Success Measurement

### **Workflow Effectiveness**
- **Task Completion Speed**: Measure time from request to delivery
- **Quality Metrics**: Track bug rates and rework needs
- **Developer Satisfaction**: Assess ease of use and productivity
- **Consistency**: Measure adherence to established patterns

### **Technical Excellence**
- **Code Quality**: Maintain high standards across all environments
- **Performance**: Meet speed and resource requirements consistently
- **Security**: Achieve zero critical vulnerabilities
- **Accessibility**: Maintain WCAG 2.1 AA compliance

### **Collaboration Success**
- **Communication Clarity**: Clear, actionable AI responses
- **Knowledge Transfer**: Effective documentation and examples
- **Problem Resolution**: Quick identification and resolution of issues
- **Innovation**: Balanced introduction of new patterns and technologies

---

## 🎯 Implementation Checklist

### **Before Starting Any Task**
- [ ] Understand the complete requirement
- [ ] Check existing codebase for similar solutions
- [ ] Plan test strategy and coverage
- [ ] Consider performance and security implications
- [ ] Verify IDE/tool compatibility

### **During Development**
- [ ] Follow TDD principles (test first)
- [ ] Maintain existing patterns and architecture
- [ ] Ensure accessibility compliance
- [ ] Validate performance requirements
- [ ] Document significant decisions

### **Before Completion**
- [ ] All tests pass (unit, integration, E2E)
- [ ] Performance benchmarks met
- [ ] Security review completed
- [ ] Accessibility validated
- [ ] Documentation updated
- [ ] Changes properly committed and described

---

*This document provides universal guidelines for AI-assisted development that work across all tools, IDEs, and environments. Adapt specific tool features while maintaining these core principles.*
