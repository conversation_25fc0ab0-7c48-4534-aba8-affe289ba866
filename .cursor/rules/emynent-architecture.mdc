---
description: 
globs: 
alwaysApply: true
---
# Architecture Rules for Emynent (MVP)

## Overview
These rules define the architecture principles for Emynent's **completed client-server architecture** using Next.js 15+, React 19, Prisma 6.5.0, and Tailwind CSS 4.0.12. The migration has been successfully completed, ensuring maintainability, performance, security, and zero-regression across all authenticated and public routes, while preparing for full context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel) using Gen AI to enable personalized insights, recommendations, and coaching based on performance reviews, OKRs, skill gaps, user preferences, and recent actions. Gen AI integration is deferred post-MVP to ensure a stable launch, with placeholders for future implementation.

**Status: MIGRATION COMPLETED ✅** - This reflects the current, working client-server architecture as of January 2025.

- **Related Rules**:
  - [Emynent Coding Rules](mdc:emynent-coding-rules.mdc)
  - [Emynent Design Preferences](mdc:emynent-design.mdc)
  - [Cursor Workflow Preferences](mdc:cursor-workflow-preference.mdc)
  - [Emynent <PERSON>ope](mdc:emynent-scope.mdc)
  - [Emynent Tech Stack](mdc:emynent-tech-stack.mdc)
  - [emynent-testing.mdc](mdc:.cursor/rules/emynent-testing.mdc)

## Goals
- ✅ **Client-server separation**: Successfully implemented without regressions
- ✅ **Persistent global layout**: `navbar` and `sidebar` preserved and functional
- ✅ **Functionality retention**: All features operational in new architecture
- ✅ **Scalability foundation**: Ready for long-term growth and white-labeling
- ✅ **Context-awareness preparation**: Placeholders for Gen AI integration ready

## Current Folder Structure (Implemented)
```
/src
  /app                   → App routes, RSC layout, entry points ✅
  /components            → Shared UI organized by domain ✅
    /ai                  → Gen AI-driven UI components ✅
    /context-aware       → Context-aware UI components ✅
    /analytics           → Analytics components ✅
    /auth                → Authentication UI ✅
    /collaboration       → Collaboration UI ✅
    /design-system       → Design system components ✅
    /intelligence        → Intelligence UI ✅
    /layout              → Layout components ✅
    /onboarding          → Onboarding components ✅
    /personalization     → Personalization UI ✅
    /settings            → Settings components ✅
    /shared              → Generic shared components ✅
    /superadmin          → Superadmin UI ✅
    /theme               → Theme-related components ✅
    /ui                  → Base UI components ✅
    /websocket           → WebSocket components ✅
  /controllers           → API request/response handling ✅
    /auth-controller.ts     → Authentication logic ✅
    /user-controller.ts     → User management ✅
    /theme-controller.ts    → Theme management ✅
    /context.ts             → Context-aware handlers ✅
    /gen-ai.ts              → Gen AI controllers ✅
    /ai-recommendations.ts  → AI recommendation logic ✅
    /behavioral-analytics.ts → Analytics controllers ✅
  /services              → Business logic layer (no side effects) ✅
    /auth-service.ts        → Authentication services ✅
    /user-service.ts        → User services ✅
    /theme-service.ts       → Theme services ✅
    /onboarding-service.ts  → Onboarding logic ✅
    /settings-service.ts    → Settings management ✅
    /rbac-service.ts        → Role-based access control ✅
    /redis-service.ts       → Redis caching ✅
    /session-service.ts     → Session management ✅
    /context-service.ts     → Context-aware services ✅
    /ai-recommendations.ts  → AI recommendation services ✅
    /behavioral-analytics.ts → Analytics services ✅
    /intelligence/          → Intelligence services ✅
  /lib                   → Reusable logic (auth, theme, design-system) ✅
    /auth/                  → Authentication utilities ✅
    /design-system/         → Design system utilities ✅
    /analytics/             → Analytics utilities ✅
    /collaboration/         → Collaboration utilities ✅
    /context/               → Context utilities ✅
    /hooks/                 → Custom React hooks ✅
    /intelligence/          → Intelligence utilities ✅
    /personalization/       → Personalization utilities ✅
    /services/              → Service utilities ✅
    /utils/                 → General utilities ✅
    /validators/            → Zod validation schemas ✅
    /websocket/             → WebSocket utilities ✅
  /hooks                 → Custom React hooks ✅
  /types                 → Shared enums, interfaces ✅
  /tests                 → Unit, integration, E2E tests ✅
```

## Architecture Implementation Status
- **Controllers**: ✅ 7 controllers handling HTTP requests and validation
- **Services**: ✅ 13+ services providing stateless business logic
- **RESTful API**: ✅ Feature-based organization, typed APIs operational
- **Zod Validation**: ✅ Shared validators in `/lib/validators` implemented
- **Error Handling**: ✅ Structured responses with sonner 1.5.0 toast alerts
- **API Documentation**: Ready for `/docs/api-spec.md` updates
- **Contextual Data**: ✅ `UserContext` and `CoachingHistory` models in Prisma schema

## Gen AI Integration (Placeholders Ready)
- **API Integration**: ✅ Placeholder controllers in `/controllers/gen-ai.ts` and `/controllers/context.ts`
- **Recommendation Service**: ✅ Placeholder in `/services/ai-recommendations.ts` 
- **Context Service**: ✅ Placeholder in `/services/context-service.ts`
- **Feature Flags**: ✅ `contextAwareness` flag implemented and used throughout codebase
- **Scalability**: ✅ Stateless design ready for horizontal scaling

## Theme & Layout (Operational)
- ✅ **next-themes 0.4.6**: Light/dark/system theme management working
- ✅ **Database Storage**: User preferences stored with Redis caching (1hr TTL)
- ✅ **Fallback Logic**: System mode + Emynent Default theme with toast alerts
- ✅ **Persistent Layout**: `app/(protected)/layout.tsx` with navbar/sidebar functional
- ✅ **Onboarding Protection**: Routes preserved and validated

## Authentication & Security (Operational)
- ✅ **Role Validation**: All protected routes validate via session/`authGuard`
- ✅ **Middleware**: Authentication, role preloading, and security headers
- ✅ **Feature Flags**: `hasFeature()` system extensively implemented
- ✅ **Multi-tenancy**: Company-scoped operations throughout codebase
- ✅ **Rate Limiting**: Applied to APIs using Redis
- ✅ **Input Sanitization**: XSS, CSRF, SQL injection protection

## Performance (Meeting Targets)
- ✅ **API Response Time**: <300ms for critical endpoints
- ✅ **Redis Caching**: 1hr for roles, 10min for feature flags implemented
- ✅ **Cache Invalidation**: On role/flag updates with Sentry logging
- ✅ **Lazy Loading**: Charts, modals, and theme previews optimized

## Testing & Monitoring (Operational)
- ✅ **Visual Testing**: Playwright snapshots for themes and layouts
- ✅ **Error Tracking**: Sentry and LogRocket integration
- ✅ **Performance Monitoring**: API response times and rendering metrics
- ✅ **Security Logging**: Sanitized logging with hashed identifiers

## Architecture Implementation Validation Checklist ✅

### **Core Architecture**
✅ Client-server separation implemented and operational  
✅ Controllers layer handling HTTP requests (7 controllers active)  
✅ Services layer providing business logic (13+ services active)  
✅ RESTful API with feature-based organization operational  
✅ Zod validation schemas implemented in `/lib/validators`  

### **Layout & UX**
✅ Sidebar and navbar persist across all protected routes  
✅ All themes load correctly with fallback logic operational  
✅ `app/(protected)/layout.tsx` provides consistent layout  
✅ Onboarding routes preserved and validated  
✅ No hydration mismatches in production  

### **Security & Auth**
✅ Role and session checks pass on client and server  
✅ Feature flags control rollout with `hasFeature()` system  
✅ Multi-tenancy with company-scoped operations  
✅ Rate limiting and input sanitization operational  
✅ Middleware enforcing authentication and security headers  

### **Performance & Caching**
✅ API errors logged and user-facing alerts displayed  
✅ Redis caching operational (1hr roles, 10min flags)  
✅ Performance targets met (API <300ms, initial load <2.5s)  
✅ Cache invalidation working on role/flag updates  

### **Gen AI Preparation**
✅ `UserContext` and `CoachingHistory` models defined in Prisma  
✅ Context-aware controllers and services implemented as placeholders  
✅ `contextAwareness` feature flag implemented and disabled by default  
✅ Context-aware components ready in `/src/components/context-aware`  
✅ Gen AI placeholders feature-flagged throughout codebase  

### **Testing & Monitoring**
✅ Playwright snapshots operational for visual regression  
✅ Sentry and LogRocket monitoring active  
✅ Error handling with structured responses and fallbacks  
✅ Security logging with sanitized, hashed identifiers  

---

This architecture specification reflects the **current, operational client-server implementation** for Emynent, with all migration goals achieved and Gen AI placeholders ready for future implementation.
