{"name": "emynent", "version": "1.0.0", "description": "Career and skills progression platform for growth-stage companies", "main": "index.js", "scripts": {"dev": "next dev -p 3005", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:websocket": "vitest run tests/websocket/", "test:websocket:watch": "vitest --watch tests/websocket/", "test:websocket:integration": "vitest run tests/websocket/websocket-integration.test.ts", "test:websocket:verbose": "TEST_VERBOSE=true vitest run tests/websocket/", "test:websocket:ci": "CI=true vitest run tests/websocket/ --reporter=verbose", "test:all": "vitest run", "test:all:ci": "CI=true vitest run --reporter=verbose", "deploy:staging": "chmod +x scripts/deploy-staging.sh && DEPLOYMENT_TARGET=gcp ./scripts/deploy-staging.sh", "deploy:staging:vercel": "DEPLOYMENT_TARGET=vercel npm run deploy:staging", "deploy:staging:gcp": "DEPLOYMENT_TARGET=gcp npm run deploy:staging", "deploy:staging:aws": "DEPLOYMENT_TARGET=aws npm run deploy:staging", "deploy:production": "chmod +x scripts/deploy-production.sh && DEPLOYMENT_TARGET=gcp ./scripts/deploy-production.sh", "deploy:production:vercel": "DEPLOYMENT_TARGET=vercel npm run deploy:production", "deploy:production:gcp": "DEPLOYMENT_TARGET=gcp npm run deploy:production", "deploy:production:aws": "DEPLOYMENT_TARGET=aws npm run deploy:production", "deploy:production:auto": "AUTO_APPROVE=true npm run deploy:production", "deploy:help": "echo '🚀 Deployment Commands:' && echo '  deploy:staging        - Deploy to staging (build only)' && echo '  deploy:staging:vercel - Deploy to Vercel staging' && echo '  deploy:staging:gcp    - Deploy to GCP staging' && echo '  deploy:staging:aws    - Deploy to AWS staging' && echo '  deploy:production     - Deploy to production (build only)' && echo '  deploy:production:*   - Deploy to specific provider' && echo '  deploy:production:auto - Auto-approve production deployment'", "websocket:start": "tsx scripts/start-websocket-server.ts start", "websocket:stop": "tsx scripts/start-websocket-server.ts stop", "websocket:status": "tsx scripts/start-websocket-server.ts status", "websocket:health": "tsx scripts/websocket-health-check.ts", "websocket:restart": "npm run websocket:stop && sleep 2 && npm run websocket:start", "dev:clean": "npm run websocket:stop && npm run dev", "dev:full": "concurrently \"npm run dev\" \"npm run websocket:start\"", "dev:all": "concurrently \"npm run dev\" \"npm run websocket:start\" \"npx prisma studio --port 5555\"", "dev:safe": "npm run websocket:health && npm run dev:full", "structure:validate": "node scripts/structure-validator.js validate", "structure:auto-fix": "node scripts/structure-validator.js auto-fix", "structure:health-check": "node scripts/structure-validator.js health-check", "structure:report": "node scripts/structure-validator.js validate", "cleanup:summary": "tsx scripts/quick-cleanup-summary.ts", "cleanup:verify": "tsx scripts/safe-cleanup-verification.ts", "cleanup:execute": "tsx scripts/safe-cleanup-verification.ts --execute", "cleanup:help": "echo '🧹 Archive Cleanup Commands:' && echo '  cleanup:summary  - Show what will be cleaned' && echo '  cleanup:verify   - Dry run verification' && echo '  cleanup:execute  - Perform actual cleanup' && echo '  See docs/archive-cleanup-plan.md for details'", "cleanup:comprehensive": "chmod +x scripts/comprehensive-cleanup.sh && ./scripts/comprehensive-cleanup.sh", "cleanup:structure": "echo '🏗️  Comprehensive Structure Cleanup:' && echo '  cleanup:comprehensive - Full folder reorganization' && echo '  See docs/COMPREHENSIVE_CLEANUP_ANALYSIS.md for details'", "db:seed": "tsx prisma/seed.ts", "db:test:setup": "DATABASE_URL=postgresql://postgres:password@localhost:5432/emynent_test npx prisma migrate deploy", "db:test:reset": "DATABASE_URL=postgresql://postgres:password@localhost:5432/emynent_test npx prisma migrate reset --force", "test:setup": "npm run db:test:setup", "test:teardown": "npm run db:test:reset", "type-check": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@auth/core": "^0.39.1", "@auth/prisma-adapter": "^2.9.1", "@emotion/is-prop-valid": "^1.3.1", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@sendgrid/mail": "^8.1.5", "@types/chokidar": "^2.1.7", "@types/ws": "^8.18.1", "@upstash/redis": "^1.35.0", "bcryptjs": "^3.0.2", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie": "^1.0.2", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "framer-motion": "^12.19.2", "glob": "^11.0.3", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "^15.3.4", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "prisma": "^6.10.1", "react": "^19.1.0", "react-best-gradient-color-picker": "^3.0.14", "react-colorful": "^5.6.1", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-responsive": "^10.0.1", "recharts": "^3.0.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "ws": "^8.18.2", "zod": "^3.25.67"}, "devDependencies": {"@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4.1.11", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/cookie": "^1.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.6", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/sendgrid": "^4.3.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "node-mocks-http": "^1.17.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "engines": {"node": ">=20.0.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}}