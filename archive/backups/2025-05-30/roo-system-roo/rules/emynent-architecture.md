---
description: 
globs: 
alwaysApply: true
---
# Architecture Rules for Emynent (MVP)

## Overview
These rules define the architecture principles and migration safeguards for Emynent’s move from a monolithic to a client-server architecture using Next.js 15+, React 19, Prisma 6.5.0, and Tailwind CSS 4.0.12. They ensure maintainability, performance, security, and zero-regression across all authenticated and public routes, while preparing for full context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel) using Gen AI to enable personalized insights, recommendations, and coaching based on performance reviews, OKRs, skill gaps, user preferences, and recent actions. Gen AI integration is deferred post-MVP to ensure a stable launch, with placeholders for future implementation.

- **Related Artifacts**:
  - Emynent Coding Rules: `artifact_id: 844ed034-3539-445b-acdc-da23f3336a60`
  - Emynent Design Preferences: `artifact_id: 7f0fba54-f330-4bd9-85fb-597f42724ae4`
  - Emynent Workflow Preferences: `artifact_id: f63727a6-2ed2-4f7a-902b-c1a97cb1ca62`
  - Emynent Scope: `artifact_id: 2a65be92-45bc-4f2d-bdf6-3a410ba2b056`
  - Emynent Tech Stack: `artifact_id: e17ac116-3104-4573-bf08-5797b549fb0f`

## Goals
- Enable client-server separation without regressions.
- Preserve persistent global layout (`navbar`, `sidebar`).
- Retain all functionality during and after migration.
- Support long-term scalability and white-labeling.
- Prepare for full context-awareness across all entry points using Gen AI to provide personalized insights, recommendations, and coaching, ensuring scalability for dynamic adaptation to user context (e.g., role, preferences, recent actions).

## Folder Structure (Enforced)
```
/src
  /app                   → App routes, RSC layout, entry points
  /components            → Shared UI (non-feature specific)
    /ai                  → Gen AI-driven UI components (e.g., `AiInsightBanner.tsx`)
    /context-aware       → Context-aware UI components (e.g., `DynamicDashboard.tsx`)
  /features              → Feature-bound UI and logic
  /lib                   → Reusable logic (auth, theme, design-system)
    /gen-ai-utils.ts     → Placeholder for Gen AI utility functions
  /controllers           → API request/response handling
    /gen-ai.ts           → Placeholder for Gen AI API controller
    /context.ts          → Placeholder for context-aware API controller
  /services              → Business logic layer (no side effects)
    /ai-recommendations.ts → Placeholder for Gen AI recommendation service
    /context-service.ts  → Placeholder for context-aware service
  /types                 → Shared enums, interfaces
  /tests                 → Unit, integration, E2E tests
  /scripts               → One-off migrations/tools (must be cleaned up)
```

## Migration Safety Rules
- All migrated features must:
  - Preserve existing behavior and UI state.
  - Be gated behind `hasFeature(companyId, featureKey)`, including `contextAwareness` for Gen AI-driven features.
  - Be validated with Playwright E2E and snapshot tests.
  - Not remove SSR fallback unless the flag is fully enabled.
- Legacy implementations must be:
  - Marked with `@deprecated` and logged in `/docs/migration-log.md`.
  - Removed only after client-server migration has passed CI and staging checks.
- **Feature Flag Rollback Plan**:
  - If a feature causes issues post-migration, disable the flag via Superadmin Panel, revert to legacy implementation, and log the rollback in `/docs/migration-log.md`. Ensure zero downtime by preloading legacy code paths.
- **Context-Awareness Placeholder Validation**:
  - Ensure context-aware placeholders (e.g., `/controllers/context.ts`) are disabled by default and validated with E2E tests to confirm no regressions.

## Client-Server Architecture
- **Controllers**: Handle HTTP requests, perform validation, return structured responses.
- **Services**: Stateless business logic, reusable across server and tests.
- **RESTful API**: All client data-fetching must use typed APIs, not direct DB queries.
- **Zod Validation**: Validate request/response schemas using shared validators in `/lib/validators`, including context-aware data (e.g., `userPreferences`).
- **Error Handling**:
  - On API/controller failures (e.g., DB timeout, validation error), return a structured error response (e.g., `{ error: "Failed to fetch data", status: 500 }`).
  - Display a user-facing toast alert using sonner 1.5.0 (e.g., “Unable to load data, please try again”).
  - Fallback to cached data (Redis) or default states (e.g., empty list, default theme) to prevent UI breakages.
  - Plan for context-aware API failures (e.g., “Context service unavailable”) with fallback to default UI state.
- **API Documentation**:
  - Document all API endpoints, schemas, and usage in `/docs/api-spec.md` (e.g., `GET /api/users/:id`, request/response schemas, auth requirements).
  - Include placeholder documentation for context-aware APIs (e.g., `GET /api/context/:userId`).
- **Contextual Data for Gen AI (Placeholder)**:
  - Define a placeholder Prisma schema in `/prisma/schema.prisma` for `UserContext` (e.g., `model UserContext { id Int @id @default(autoincrement()) userId Int role String preferences Json? recentActions Json? historicalData Json? /* performanceReview Text?, okrId Int?, skillGaps Json? */ }`) and `CoachingHistory` (e.g., `model CoachingHistory { id Int @id @default(autoincrement()) userId Int recommendations Json? createdAt DateTime }`) for future Gen AI use.
  - Create a placeholder service in `/services/context-service.ts` with comments (e.g., `// Aggregate user context for Gen AI-driven personalization`).

## Gen AI Integration (Future-Proofing, Post-MVP)
- **API Integration (Placeholder)**:
  - Add a placeholder controller in `/controllers/gen-ai.ts` (e.g., `export async function GET() { return { data: "Gen AI coming soon" }; }`) for future API integration.
  - Plan to cache Gen AI responses in Redis (1hr TTL, `companyId:userId:<requestHash>` key) and handle failures with sonner 1.5.0 toast alerts.
  - Include a placeholder controller in `/controllers/context.ts` for context-aware API endpoints (e.g., `GET /api/context/:userId` to fetch user context).
- **Recommendation and Coaching Service (Placeholder)**:
  - Plan a service in `/services/ai-recommendations.ts` to process Gen AI outputs into insights (e.g., “Complete this course”) and store in `CoachingHistory` model.
  - Plan a service in `/services/context-service.ts` to aggregate user context (e.g., role, preferences, recent actions) for Gen AI personalization.
- **Scalability (Placeholder)**:
  - Plan rate-limiting (e.g., 10 requests/min per user) and latency monitoring (< 1s) for Gen AI and context-aware APIs.
  - Prepare for horizontal scaling by designing stateless controllers and services, supporting load balancing across multiple nodes.

## Theme Persistence & Layout
- Use `next-themes` 0.4.6 for light/dark/system theme management.
- Store user preference in DB, cache in Redis (1hr TTL).
- Fallback: System mode + Emynent Default theme, with a sonner 1.5.0 toast alert: “Theme preferences could not be loaded.”
- Preserve layout using `app/(protected)/layout.tsx`.
- Never break or remove `navbar` and `sidebar` unless explicitly scoped.
- **Onboarding Protection**:
  - Ensure onboarding routes (`onboarding/page.tsx`) are preserved and validated post-migration with Playwright E2E tests, maintaining logic and UX per Design Preferences (artifact_id: 7f0fba54-f330-4bd9-85fb-597f42724ae4).
- **Gen AI and Context-Awareness UX (Placeholder)**:
  - Plan to integrate Gen AI-driven UI elements (e.g., `AiInsightBanner.tsx`) and context-aware components (e.g., `DynamicDashboard.tsx`) in `/components/ai` and `/components/context-aware`, aligning with the design system (v1.0) and supporting white-labeled themes.
  - Prepare for context-aware theming (e.g., suggesting themes based on user preferences) with placeholders in `/components/context-aware`.

## Role & Auth Enforcement
- All protected routes/components must:
  - Validate role via session or `authGuard`.
  - Render fallback or redirect if unauthenticated/unauthorized.
  - Use middleware to preload role + session context, including user context for future personalization.

## Hydration & RSC
- Use RSCs wherever possible.
- Wrap all client components in `<Suspense fallback>`.
- Avoid hydration mismatches by syncing theme, session, and layout state.
- Validate hydration on slow 3G simulation in Playwright or Lighthouse.

## Multi-Tenancy
- All APIs, controllers, and services must be `companyId`-scoped.
- Cache keys in Redis must use `companyId:<value>` prefix.
- Routes must not leak tenant data between sessions or API calls.
- Plan for context-aware multi-tenancy by scoping user context to `companyId` (e.g., `companyId:userId:context`).

## Security Rules
- **Rate Limiting**: Apply rate limiting to all public APIs (e.g., 100 requests/min per IP) using Redis to prevent abuse.
- **Input Sanitization**: Sanitize all inputs in controllers to prevent XSS, CSRF, and SQL injection, per Coding Rules (artifact_id: 844ed034-3539-445b-acdc-da23f3336a60).
- **Secure Headers**: Set secure HTTP headers (e.g., CSP, X-Frame-Options) in Next.js middleware to mitigate common vulnerabilities.
- **Gen AI and Context-Awareness Data Privacy (Placeholder)**:
  - Plan to anonymize sensitive data (e.g., performance reviews, user preferences) and require user consent for Gen AI features in `/app/settings.tsx`.
  - Use hashed identifiers for context-aware data before sending to Gen AI APIs.

## Performance Targets
- Initial load (mobile 4G): < 2.5s.
- API response time: < 300ms for critical endpoints.
- Context-aware rendering: < 300ms for dynamic UI updates (e.g., personalized dashboards).
- Use Redis caching (1hr for roles, 10min for feature flags, 1hr for context data).
- Lazy-load charts, modals, and theme previews.
- **Cache Invalidation**:
  - Invalidate Redis cache on user role updates, feature flag changes, or context updates (e.g., on DB write, publish to Redis channel to clear cache).
  - Fallback to DB query if cache fails, logging the failure in Sentry.
- **CI Validation**:
  - Validate performance targets in CI using Lighthouse (e.g., initial load < 2.5s, API < 300ms, context-aware rendering < 300ms) for all PRs affecting critical paths.

## Logging and Monitoring
- Log API errors, performance metrics (e.g., API response time), and migration events in Sentry and LogRocket.
- Never log sensitive data (e.g., JWTs, emails, IPs); use hashed identifiers per Coding Rules (artifact_id: 844ed034-3539-445b-acdc-da23f3336a60).
- Monitor initial load, API response times, and context-aware rendering times in staging and production, alerting on thresholds (e.g., API > 300ms, context rendering > 300ms).
- Log Gen AI API errors, response times, and context-aware processing times in Sentry (post-MVP).

## Visual Testing
- Snapshot test theme appearance, layout, and onboarding.
- Use Playwright for visual regression.
- Validate all breakpoints (`sm`, `md`, `lg`).
- Run in CI for all PRs that affect layout or themes.
- **Gen AI and Context-Awareness Testing (Placeholder)**:
  - Plan E2E tests for Gen AI and context-aware features (e.g., personalized dashboards, context-driven settings) using mocked API responses, validating failure scenarios (e.g., context service downtime).

## Version Control
- Use semantic versioning for architectural changes (e.g., `v1.0` for MVP) and document in `/docs/architecture-changelog.md`.
- Resolve merge conflicts with `git rebase` and document in PR descriptions.
## Final Pre-Migration Architecture Validation Checklist
✅ Sidebar and navbar persist post-migration  
✅ All themes load correctly with fallback logic  
✅ Role and session checks pass on client and server  
✅ Feature flags control rollout cleanly  
✅ Legacy logic is deprecated and tracked  
✅ No hydration mismatch  
✅ No tenant data leakage  
✅ SSR fallback works if feature is disabled  
✅ All pages inherit from `app/(protected)/layout.tsx`  
✅ Playwright snapshots pass in CI  
✅ API errors are logged and user-facing alerts are displayed  
✅ Performance targets (initial load < 2.5s, API < 300ms, context-aware rendering < 300ms) pass in CI  
✅ Onboarding routes (`onboarding/page.tsx`) are preserved and validated  
✅ Placeholder for `UserContext` and `CoachingHistory` models is defined  
✅ Placeholder for Gen AI and context-aware controllers and services is implemented  
✅ Context-aware placeholders are feature-flagged and disabled by default  
✅ Context-aware API documentation is prepared in `/docs/api-spec.md`  

---

This architecture specification governs how Emynent migrates safely to a client-server model while preserving UX, performance, and stability, with placeholders for full context-awareness and Gen AI integration post-MVP.
