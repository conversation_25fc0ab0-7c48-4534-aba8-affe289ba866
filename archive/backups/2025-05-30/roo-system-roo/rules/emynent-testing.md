---
description: 
globs: 
alwaysApply: true
---
# Emynent Test Rules for Roo Code AI

This document defines all testing requirements for Roo Code AI and engineering contributors when developing, migrating, or modifying Emynent functionality. It ensures zero regressions, accessibility compliance, layout preservation, and confidence across all flows, while preparing for full context-awareness across all entry points using Gen AI post-MVP.

---

## 🔍 Test Philosophy

* **Prevent Regressions**: Tests must catch any visual, behavioral, or structural regressions.
* **Preserve Functionality**: All existing behavior must remain intact across themes, layouts, and workflows.
* **Validate by Role and Route**: Every test must account for user roles, protected routes, onboarding status, and theming.
* **Automate Feedback**: Tests should be integrated into CI to flag issues early.
* **Roo Code-aware**: Roo Code-generated changes must trigger corresponding test updates or coverage.
* **No Orphaned Artifacts**: Mock data, scaffolding, or test hacks used for coverage must be cleaned up.
* **Context-Awareness Preparation**: Tests should prepare for future context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel), ensuring dynamic adaptation to user context (e.g., role, company, preferences, recent actions, historical data).

---

## ✅ Coverage Requirements

| Area                          | Requirement                                                       |
| ----------------------------- | ----------------------------------------------------------------- |
| Unit Tests (Vitest)           | ✓ 80%+ coverage on `/services`, `/controllers`, `/lib/validators` |
| Integration Tests             | ✓ Cover API + Zod validation + form logic                         |
| End-to-End Tests (Playwright) | ✓ Flows: onboarding, settings, dashboard, themes, Superadmin Panel |
| Accessibility (a11y)          | ✓ WCAG 2.1 AA via axe-core, Lighthouse, screen reader             |
| Visual Snapshots              | ✓ All layouts, themes, onboarding, navigation states              |
| Responsive Testing            | ✓ Validate at sm / md / lg Tailwind breakpoints                   |
| Feature Flag Testing          | ✓ ON/OFF state coverage for all gated features, including `contextAwareness` placeholder |
| Context-Awareness (Placeholder) | ✓ Plan tests for context-aware rendering across all entry points, validating failure scenarios (e.g., context service downtime) |

---

## 👩‍💼 Role-Based Testing

Test each route and page by the following roles:

* Employee
* Manager
* Director
* Admin
* Superadmin (incl. impersonation, developer persona)

Each role must:

* Access only its permitted routes.
* Render the correct layout and theme.
* Trigger no auth errors or 404s.
* **Context-Awareness Preparation**: Plan for context-aware rendering based on user context (e.g., role-specific dashboards showing recent actions), with placeholder tests gated behind `hasFeature(companyId, "contextAwareness")`.

---

## 🏢 Theming Test Matrix

All UI and flows must work with:
* Light Mode (with each pre-defined color theme)
* Dark Mode (with each pre-defined color theme)
* System Mode fallback
* Custom Theme (e.g. Red Bull override)

Use Playwright snapshots to validate each mode + theme pair.
Ensure tokens from `tailwind.config.ts` apply correctly.
Validate theme fallback to `Emynent Default` if invalid or inaccessible.

---

## ⚖️ Testing Stack

| Type        | Tool                                   |
| ----------- | -------------------------------------- |
| Unit        | Vitest 3.1.1                           |
| Integration | Vitest + Prisma                        |
| E2E         | Playwright 1.51.1                      |
| a11y        | axe-core + Lighthouse + NVDA/VoiceOver |
| Visual      | Playwright Snapshots                   |
| Load        | k6 (1,000 concurrent users)            |

---

## 🔧 Local Developer Commands

```bash
pnpm test                       # Run Vitest unit + integration
npx playwright test             # Run E2E tests
npx playwright test --update-snapshots  # Re-record visual snapshots
npx lighthouse-ci               # Run Lighthouse for a11y and performance
npx k6 run tests/load.js        # Run load tests with k6
```

---

## 📦 Required Test Files & Location

* All test files must be placed under `/tests` or colocated as `.test.ts[x]` or `.spec.ts[x]`
* Shared components: `/tests/components`
* Context-aware tests: `/tests/context-aware` (placeholder for future Gen AI-driven tests)
* Each service/controller/component/page must ship with at least one test
* Group tests by domain where possible

---

## 🔄 CI Integration

All PRs must:

* ✅ Pass Vitest suite
* ✅ Pass Playwright tests
* ✅ Update snapshots if layout or theme changes
* ✅ Report a11y violations (via axe-core, Lighthouse)
* ✅ Include logs or screenshots for failed test runs
* ✅ Store snapshots + test artifacts as part of CI run

---

## 🧹 Test Maintenance Rules

* ❌ Do NOT leave mock/stub test code in production branches
* ✅ Remove failing snapshots if layout has *explicitly* changed
* ✅ Log added/removed tests in `/docs/testing-changelog.md`
* ✅ Remove unused mocks or fixtures after testing
* ❌ Never comment out broken tests—fix or remove with justification

---

## 🛑 When a Test Fails

* Block merge until the issue is fixed
* Debug using test logs and Playwright screenshots
* Ensure resolution without reducing coverage or bypassing assertions
* Notify tech/product lead if failure is systemic (e.g., hydration bug, routing loop, SSR mismatch)

---

## 📋 Summary Checklists

### Roo Code Code PR Must Include:

* ✅ Unit test (if logic added/changed)
* ✅ Integration or E2E test (if route/API/UX changes)
* ✅ Snapshot test (if layout or theme changes)
* ✅ Accessibility validation (for interactive components)
* ✅ PR description summarizing test coverage + impacted areas
* ✅ Reference to affected test files or updated snapshots
* ✅ Placeholder tests for context-aware features (e.g., `/tests/context-aware`)

### PR Will Be Rejected If:

* ❌ No tests included for major changes
* ❌ Theme/layout/UX/role regression is untested
* ❌ Disabled, stubbed, or incomplete tests left in PR
* ❌ Any failing test is ignored or bypassed

---

## 📊 Metrics Validation

Ensure anonymized product metrics are still collected and functional:

* Theme preference stored in DB and Redis (1hr TTL)
* Superadmin actions logged
* Dashboard + onboarding usage counters preserved
* **Context-Awareness Metrics (Placeholder)**: Plan to track context-aware usage metrics (e.g., personalized dashboard views, context-driven settings changes) post-MVP

---

## 🧪 Manual QA (if automation blocked)

* Record Loom walkthrough
* Log test steps in `/docs/manual-qa.md`
* Flag test gaps in PR description
* Include context-aware scenarios (e.g., role-based dashboard personalization) in manual testing plans
* Get approval from project lead before merging

---

## ✏️ Roo Code AI Responsibilities

Roo Code must:

* Generate colocated tests for new features, components, or layouts
* Snapshot all layout, navigation, theme changes
* Test every route by role (Employee, Manager, etc.)
* Ensure onboarding logic, layout inheritance, and theming are never broken
* Validate breakpoints and responsive behavior
* Explicitly mark what was tested in PR (e.g., `✅ E2E for /settings/team, responsive snapshots updated`)
* Ensure that migrating to client-server architecture does not break routing, layouts, or theming logic
* Default to `Emynent Default` theme if custom theme fails accessibility check
* Generate placeholder tests for context-aware features across all entry points, ensuring failure scenarios are covered

---

## 🖥️ Browser Compatibility

* Test on latest versions of Chrome, Firefox, Safari, and Edge
* Ensure context-aware UI components (placeholder) render consistently across browsers
* Use Playwright for cross-browser E2E testing

---

## ⚡ Performance Testing

* Validate Web Vitals (LCP < 2.5s, FCP < 1.5s, CLS < 0.1) using Vercel Analytics
* Test API response times <100ms for critical endpoints using k6
* Plan for context-aware performance testing (e.g., context processing <300ms) post-MVP, ensuring Redis caching (1hr TTL) and lazy-loading are effective

---

## 🔒 Security Testing

* Test rate limiting (100 requests/min per user, 10 deletions/hour for Superadmin) using k6
* Validate XSS, CSRF, and SQL injection protections in API endpoints
* Ensure sensitive data (e.g., JWTs, emails, IPs) is not logged in Sentry or LogRocket
* Plan for context-aware data privacy testing (e.g., anonymization of user context data) post-MVP

---

This document governs all test expectations and test enforcement for the Emynent platform. All feature additions, architectural changes, and Roo Code-generated work must meet the criteria listed here to maintain design, layout, role-based access, accessibility, and performance standards, while preparing for full context-awareness across all entry points.

Always test with clarity, preserve what works, and validate what changes.
