---
description: 
globs: 
alwaysApply: true
---
# Emynent MVP <PERSON> and Roo Code Rules

## Project Overview
Emynent is a proprietary web application designed to manage career progression and skill frameworks in fast-growing companies. The MVP is an internal pilot to validate core functionalities, built using Next.js 15+ (App Router), Prisma, NextAuth.js, and a Tailwind + shadcn/ui design system, with placeholders for future generative AI (Gen AI) integration to enable full context-awareness across all entry points, providing personalized insights, recommendations, and coaching.

## Business Purpose
Emynent enables structured career frameworks and skills progression, fostering:
- Self-assessment and skill development for employees
- Manager oversight for training needs
- Strategic visibility for department leads

## Goals and Objectives (<PERSON> Scope)
- **Career Visibility**: Clear career progression paths for users
- **Skill Management**: Structured tracking and development of skills
- **RBAC Security**: Role-based access control for data protection
- **Consistent UX/UI**: Professional, intuitive user experience
- **Multi-tenancy**: Company-level data isolation across routes and resources
- **Client-Server Architecture**: Modular, scalable refactor from monolithic structure
- **Full Context-Awareness Preparation**: Include placeholders for future Gen AI integration to support full context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel), adapting dynamically to user context (e.g., role, company, preferences, recent actions, historical data), with active implementation deferred post-MVP.

## MVP Exit Criteria
- No regressions; all features fully functional
- Superadmin access to dashboard and panel via Settings
- Authentication and tenant isolation enforced in production
- All scoped documentation complete and accessible
- No critical QA or usability bugs
- WCAG 2.1 AA accessibility verified via Lighthouse/axe
- All E2E tests pass in CI

## Non-Goals (MVP)
- No microservices or multi-repo conversion
- No RBAC schema changes unless scoped
- No UX redesigns or new navigation patterns
- No HR tool integrations
- No new design systems or forks
- No internationalization (i18n) or localization
- No active Gen AI implementation for context-awareness, insights, recommendations, or coaching, though placeholders are included for future integration across all entry points (e.g., expanded `UserContext` schema, context-aware controllers).

## User Roles and Permissions
| Role       | Inherits From | Pages Accessed                                  |
|------------|---------------|-------------------------------------------------|
| Employee   | —             | Dashboard, Settings                             |
| Manager    | Employee      | Team dashboard                                  |
| Director   | Manager       | Department dashboard                            |
| Admin      | Director      | Org settings, User management                   |
| Superadmin | Admin         | All + Superadmin Panel + Developer Persona Mode |

## Superadmin Panel Scope
### Access
- **Dashboard**: Superadmins land on the standard dashboard
- **Panel Access**: Available only via `/settings/superadmin` (not in top nav)
- **Layout**: Reuses global sidebar, theme settings, and design fidelity
- **Developer Mode**: Switch between role views (Employee, Manager, Director, Admin)
- **Persona Switching**: View-only impersonation of users, with developer-mode banner

### Panel Modules
- **Design System Manager**:
  - Live-editable component library (charts, inputs, forms, typography)
  - Updates global usage via shared imports
  - Supports previews and AI enhancements (e.g., AI-generated design suggestions post-MVP)
  - Versioned changes with rollback, stored in database
- **Company Management**:
  - Add/edit/delete companies
  - Assign admins, set subscription limits, update plan status
- **Email Validation**:
  - Whitelist domains, resend verification, override logic
- **Feature Flags**:
  - Toggle features per company (e.g., AI, modules)
  - Stored in database, cached in Redis
  - Evaluated via `hasFeature(companyId, featureKey)`
  - Include placeholder for `contextAwareness` flag to enable future full context-awareness across all entry points

### Audit Logging
- Log all Superadmin actions (timestamp, actor, action, target)
- Actions include company edits, feature toggles, impersonation, design system changes
- Stored in database, accessible via Superadmin Panel (Superadmin-only)
- 90-day retention, exportable, queryable by timestamp/actor/action
## Engineering Requirements
- Enforce access via middleware and client guards
- Show fallback UI for component-level errors
- Use client-server architecture with RESTful APIs (OpenAPI-documented)
- Reuse global sidebar, layout, and theme system for Superadmin panel
- Structure logic in `/lib/superadmin` and `/features/superadmin`
- **Database Schema**:
  - Tables: `User`, `Company`, `Role`, `FeatureFlag`, `AuditLog`
  - Prisma migrations in `/prisma/migrations`
  - Seed data for local development
  - Placeholder models: Expanded `UserContext` (e.g., `model UserContext { id Int @id @default(autoincrement()) /* role String?, companyId Int?, preferences Json?, recentActions Json?, historicalData Json? */ }`) and `CoachingHistory` (e.g., `model CoachingHistory { id Int @id @default(autoincrement()) /* userId Int, recommendation String?, completed Boolean? */ }`) for future Gen AI-driven context-awareness across all entry points
- **Local Development**:
  - Docker Compose for PostgreSQL and Redis
  - `.env.example` for environment variables
  - Setup documented in `/docs/development.md`
  - Seed data maintained via `/prisma/seed.ts`
- **Data Migration**:
  - Confirm with stakeholders if existing user data needs migration
  - If none, use fresh database
  - If needed, develop `/prisma/migrate-data.ts`, validate integrity, minimize downtime (<1 hour)
  - Document in `/docs/migration.md`
- **Scalability**:
  - Support 1,000 concurrent users per company
  - Optimize queries for <100ms response
  - Cache data (e.g., feature flags, roles) in Redis
- **Offline Behavior**:
  - Show cached dashboard with offline warning
  - Disable write operations when offline
- **Analytics**:
  - Track feature usage (e.g., dashboard views, skill updates) per company
  - Store anonymized metrics in database, accessible via Superadmin Panel
- **Scheduled Jobs**:
  - Use node-cron for tasks (e.g., data cleanup, notifications)
  - Document in `/docs/jobs.md`
- **Additional Libraries**:
  - Framer Motion: Animations and transitions
  - Lucide React: Icon library
  - Sonner: Toast notifications
  - React Hook Form: Form state management
  - React Day Picker: Date selection
  - React Best Gradient Color Picker, React Colorful: Color selection

## Security
- Protect against XSS, CSRF, SQL injection
- Encrypt PII at rest (AES-256)
- Follow OWASP Top 10 guidelines
- Avoid hardcoding secrets; use environment variables
- **Rate Limiting**:
  - API endpoints: 100 requests/min per user (express-rate-limit)
  - Superadmin actions: 10 deletions/hour, 50 impersonations/hour (Redis)
- **API Tokens**: JWT authentication, rotated every 24 hours
- **Data Privacy**: GDPR/CCPA-compliant PII handling, export/deletion options
- **Content Security Policy**: Strict CSP in Next.js middleware
- **Session Management**: 30-minute timeouts, manual logout in Settings
- **Authentication**: NextAuth.js with Google OAuth, bcryptjs for credentials
- **Context-Awareness Data Privacy (Placeholder)**:
  - Plan to anonymize sensitive user data (e.g., preferences, recent actions, historical data) and require user consent for Gen AI-driven context-awareness in `/app/settings.tsx` post-MVP

## Error Handling & Monitoring
- Use Sentry for error tracking, LogRocket for session replays
- Log API errors centrally
- Implement `try/catch` with clear error messages
- **Fallbacks**:
  - Cache Sentry/LogRocket data locally if services fail
  - Use Vercel Analytics as backup

## Dependencies
Pin all dependencies in `package.json`:
- **Core**: Next.js ^15.2.2, React ^19.0.0, React DOM ^19.0.0, TypeScript ^5.x
- **Frontend**:
  - Radix UI ^1.1.x – ^2.2.x
  - shadcn/ui ^0.0.4
  - Tailwind CSS ^4.0.12
  - tailwind-merge ^3.0.2
  - tailwindcss-animate ^1.0.7
  - class-variance-authority ^0.7.1
  - clsx ^2.1.1
  - next-themes ^0.4.6
  - lucide-react ^0.479.0
  - framer-motion ^12.5.0
  - sonner ^2.0.1
  - react-hook-form ^7.54.2
  - react-day-picker ^8.10.1
  - react-best-gradient-color-picker ^3.0.14
  - react-colorful ^5.6.1
- **Backend**:
  - next-auth ^5.0.0-beta.25
  - @auth/core ^0.38.0
  - @auth/prisma-adapter ^2.8.0
  - Prisma ^6.5.0
  - Zod ^3.24.2
  - bcryptjs ^3.0.2
  - express-rate-limit ^7.5.0
  - node-cron ^3.0.3
- **Testing**:
  - Vitest ^3.1.1
  - Playwright ^1.51.1
  - @testing-library/react ^14.3.1
  - @testing-library/jest-dom, user-event, jsdom ^26.0.0
- **Tooling**:
  - ESLint ^9, eslint-config-next ^15.2.2-canary.7
  - tsx ^4.19.3
  - Husky ^9.1.7
- **Monitoring**:
  - Sentry ^7.0.0
  - LogRocket ^2.0.0
  - Vercel Analytics
- Use npm (locked); evaluate pnpm for future migration

## UX/UI Requirements
- Deliver polished UI with Tailwind + shadcn/ui
- **Onboarding**:
  - In-app tooltips for first-time users (Dashboard, Settings)
  - Guided tour for Superadmin Panel
- **Feedback Collection**:
  - In-app feedback form in Settings
  - Store feedback in database, accessible via Superadmin Panel
- **Context-Awareness Preparation**:
  - Plan for future context-aware UX across all entry points (e.g., personalized onboarding flows, dynamic dashboard widgets, context-driven settings, Superadmin Panel adjustments)
  - Use placeholders in `/src/components/context-aware` to prepare for Gen AI-driven personalization, ensuring alignment with the design system (v1.0)

## Engineering Principles
- No duplicated components or logic
- Use shared Zod schemas for validation
- Follow modular folder structure:
  ```
  /app
  /features
  /lib (design-system, auth, prisma, validators)
  /controllers
  /services
  /types
  /tests
  ```
- Role-based rendering and route protection via middleware/guards
- Backend logic follows controller-service pattern
- Shared components in `lib/design-system/components`

## Roo Code AI Rules
### Code Generation
- **Generate code in** `/lib` or `/features` for reusability
- **Avoid modifying** `node_modules` or auto-generated files
- **Adhere to tech stack**:
  - Use Next.js 15.2.2, React 19.0.0, TypeScript 5.x
  - Use shadcn/ui, Tailwind CSS 4.0.12, Radix UI components
  - Use Prisma 6.5.0, Zod 3.24.2, next-auth 5.0.0-beta.25
- **Validate code**:
  - Run ESLint and TypeScript checks
  - Ensure OWASP Top 10 compliance
  - Include JSDoc for complex/reusable code
- **Follow structure**:
  - Controller-service pattern for backend
  - Shared components in `lib/design-system/components`
  - RESTful APIs with OpenAPI documentation
- **Optimize** for performance and WCAG 2.1 AA accessibility

### Testing
- **Generate tests**:
  - Vitest for unit/integration (80% coverage for services/controllers)
  - Playwright for E2E (cover Superadmin navigation, role redirects, etc.)
- **Provide stubs** for new components/services
- **Validate edge cases** (e.g., network failures, invalid inputs)
- **Document** setup in `/docs/testing.md`
- **Context-Awareness Testing (Placeholder)**:
  - Plan E2E tests for future context-awareness across all entry points (e.g., personalized dashboard rendering) using mocked responses, validating failure scenarios

### Debugging
- **Analyze** Sentry logs and suggest fixes
- **Provide** debug snippets for common issues (e.g., Prisma query failures)
- **Ensure** error messages are clear and actionable

### Code Review
- **Generate** PR descriptions summarizing changes
- **Respond** to reviewer comments with explanations or adjustments
- **Validate** changes against MVP scope

### Scope Adherence
- **Reject** tasks in Non-Goals (e.g., microservices, i18n, active context-awareness implementation)
- **Prompt** for clarification if input is ambiguous
- **Explain** refactoring benefits and scope
- **Avoid** regressions or unapproved dependency changes

### Error Handling
- If code fails validation, suggest alternatives or request clarification
- Ensure `try/catch` and fallback UI for all generated code

## Testing Requirements
- **Unit Tests**: 80% coverage for services/controllers (Vitest)
- **Integration Tests**: Cover API endpoints, Prisma queries (Vitest)
- **E2E Tests (Playwright)**:
  - Superadmin navigation
  - Theme selection and persistence
  - Role-based redirects and dashboard access
  - Component fallback and validation states
  - Mobile responsiveness (iPhone SE, Pixel 6, iPad Air)
  - Edge cases (network failures, invalid inputs)
- **Accessibility Tests**: Lighthouse, axe for WCAG 2.1 AA
- **Responsive Design**: Test Tailwind breakpoints (sm, md, lg, xl)
- **Usability Testing**:
  - Validate Superadmin Panel with 5 internal users
- **Browser Compatibility**:
  - Test on latest Chrome, Firefox, Safari, Edge
- **Load Testing**:
  - Basic load tests on staging for critical APIs (k6)
  - Validate 1,000 concurrent users, <100ms query response
- **Testing Framework**:
  - Vitest for unit/integration (fast, co-located)
  - Playwright for E2E (cross-browser)
  - Document in `/docs/testing.md`

## Performance Expectations
- First load <2.5s on 4G mobile
- Lazy-load non-critical dashboard/Superadmin sections
- No blocking loads for charts/async data
- Superadmin operations (e.g., impersonation) <1s
- **Benchmarking**: Web Vitals (LCP, FCP, CLS) via Vercel Analytics
- **Context-Awareness Performance (Placeholder)**:
  - Plan for future context-awareness to maintain API response times <300ms, using Redis caching (1hr TTL) for context data

## Documentation Required
| Area                       | Owner         | Format            | Location                     |
|----------------------------|---------------|-------------------|------------------------------|
| RBAC & Roles               | Security Team | Markdown          | `/docs/rbac.md`              |
| API Endpoints & Schema     | Backend Team  | Markdown + OpenAPI| `/docs/api.md`               |
| Multi-tenancy              | Architecture  | Diagram + MD      | `/docs/tenancy.md`           |
| Design System Architecture | Design Team   | MD + Preview      | `/docs/design.md`            |
| Superadmin Panel Spec      | Platform Lead | Notion            | Notion > Features            |
| Migration Plan             | Engineering   | Markdown          | `/docs/migration.md`         |
| Local Development Setup    | Engineering   | Markdown          | `/docs/development.md`       |
| Backup & Recovery          | Engineering   | Markdown          | `/docs/recovery.md`          |
| Testing Framework          | Engineering   | Markdown          | `/docs/testing.md`           |
| Scheduled Jobs             | Engineering   | Markdown          | `/docs/jobs.md`              |

- All documentation versioned (Git/Notion), RBAC-controlled
- Migration Plan includes legacy code deprecation and data migration (if needed)

## Code Quality & Naming Conventions
- Enforce ESLint, Prettier, TypeScript strict mode
- Naming:
  - Pages: kebab-case (e.g., `dashboard-overview.tsx`)
  - Components: PascalCase (e.g., `ChartPreview.tsx`)
  - Folders: lowercase-hyphen (e.g., `feature-flags/`)
  - Types/interfaces: CamelCase (e.g., `CompanyPlan`)
- **Documentation**:
  - JSDoc for public functions/complex logic
  - Inline comments for non-obvious code
  - API endpoints with request/response schemas

## Deployment Requirements
- **CI/CD**: GitHub Actions for testing, linting, deployment
- **Environments**: Staging, Production on Vercel
- **Monitoring**: Vercel Analytics, Sentry, LogRocket
- **Branching**:
  - `main`: Production
  - `develop`: Integration
  - `feature/*`: Feature work
  - PRs require 1 reviewer, passing CI
- **Backup & Recovery**:
  - Daily PostgreSQL backups to Vercel Storage
  - 30-day retention
  - Document in `/docs/recovery.md`
- **Environment Variables**:
  - Manage via Vercel dashboard (staging/production)
  - Use `.env.local`, synced with `.env.example`
- **Support & Incident Management**:
  - GitHub Issues for bugs
  - Notion board for incident assignment
  - SLA: Critical bugs resolved in 24 hours
- **Deployment Rollback**:
  - Vercel native rollback for failed deployments
  - Document in `/docs/recovery.md`

## Glossary
| Term            | Description                                     |
|-----------------|-------------------------------------------------|
| RBAC            | Role-Based Access Control                       |
| Zod             | Schema validation for frontend & backend        |
| Prisma          | ORM for PostgreSQL                              |
| SSR             | Server-Side Rendering (React Server Components) |
| Feature Flags   | Controlled feature toggles by company           |
| Middleware      | Route protection logic (auth, tenant)           |
| Roo Code AI       | AI assistant supporting development             |
| Design System   | Central component library (shadcn/ui-based)     |
| Gen AI          | Generative AI for context-aware insights, recommendations, and coaching |
| UserContext     | Placeholder model for storing comprehensive user context (e.g., role, preferences, recent actions, historical data) |
| Context-Awareness | Dynamic adaptation of the app across all entry points based on user context, leveraging Gen AI post-MVP |

## Future Roadmap (Post-MVP)
- Advanced permission groups and toggles
- Public API with scopes/tokens
- HRIS integrations (Leapsome, BambooHR)
- Mobile apps (iOS/Android)
- Recognition and engagement tools
- BI dashboards and analytics
- LMS and performance review modules
- Org chart, directory, birthday/anniversary alerts
- i18n support for global locales (e.g., en, fr, de)
- **Gen AI Integration (Q3-Q4 2025)**:
  - Full context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel), adapting dynamically to user context (e.g., role, company, preferences, recent actions, historical data)
  - Personalized insights, recommendations, and coaching using Gen AI APIs (e.g., xAI)

---

**Note**: This document defines the Emynent MVP scope and Roo Code AI rules. All layouts, workflows, and UX must be preserved unless explicitly scoped.
