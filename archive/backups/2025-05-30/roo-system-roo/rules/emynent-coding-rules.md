---
description: 
globs: 
alwaysApply: true
---
# Emynent Coding Rules (MVP)

## Overview
These rules guide development for the Emynent MVP, ensuring robust, maintainable code during the migration to a client-server architecture. They incorporate best practices from the `awesome-cursorrules` repository (https://github.com/PatrickJS/awesome-cursorrules) and stakeholder suggestions to enhance Roo Code AI’s code generation, focusing on clarity, modularity, and prevention of duplicates. The rules also prepare for future Gen AI integration to enable context-aware insights, recommendations, and coaching across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel), with implementation deferred post-MVP. Roo Code must adhere to these rules, aligning with the tech stack (`/docs/tech-stack.md`) and scope (`/docs/scope.md`).

## General Engineering Principles
- **Prioritize Clarity**: Write self-documenting code using descriptive names (e.g., `isLoading`, `handleSubmit`). Avoid clever abstractions unless complexity justifies them.
- **Prevent Duplication**:
  - Search `/lib`, `/features`, `/services`, `/controllers`, and `/src/components` for existing solutions before writing new code.
  - Log reused components or functions in PR descriptions (e.g., “Reused `Button` from `/lib/design-system`”).
- **Preserve Functionality**: Retain all existing features during migration. Validate against production behavior using E2E tests.
- **Avoid Scope Creep**: Implement only ticket/task-related changes. New features or deletions require stakeholder approval.
- **Remove Deprecated Code**: Replace old patterns/libraries completely, ensuring no parallel implementations. Document in `/docs/migration-log.md`.
- **Respect Environment Context**:
  - Handle dev, test, and prod environments distinctly (e.g., separate logging, seed data).
  - Use `.env.local` for local development; never commit sensitive `.env` files.
- **Keep Files Modular**:
  - Refactor files >300 lines or functions >50 lines into single-responsibility modules.
  - Group related logic in `/features` or `/services`.
- **No Mock Data in Production**:
  - Use mocks only in `/tests`.
  - Require written approval for fake data in non-test paths.
- **Manage Scripts Responsibly**:
  - Place temporary utilities in `/scripts`, not core folders.
  - Remove scripts after use and document in `/docs/scripts.md`.
- **Prepare for Gen AI and Context-Awareness (Placeholder)**:
  - Add placeholder utilities in `/lib` (e.g., `/lib/gen-ai-utils.ts`) and `/services` (e.g., `/services/ai-recommendations.ts`) with comments for future Gen AI integration (e.g., `// Process performance reviews, OKRs, skill gaps with context-aware logic`).
  - Plan for context-aware logic across all entry points, using placeholders gated behind `hasFeature(companyId, "contextAwareness")`.

## Architectural Rules
- **Enforce Client-Server Separation**:
  - Use RESTful APIs with controller-service pattern in `/controllers` and `/services`.
  - Refactor monolithic code without altering functionality, validating with Playwright E2E tests.
  - Document migrations in `/docs/migration.md`.
- **Follow Folder Structure**:
  ```
  /src/
    /app                    → App routes, layouts
    /components             → Shared UI components
      /ai                   → Placeholder for Gen AI-driven UI components (e.g., `AiInsightBanner.tsx`, `ContextAwareDashboard.tsx`)
    /features               → Feature-specific UI and logic
    /lib                    → Shared utilities (auth, design-system, validators)
      /gen-ai-utils.ts      → Placeholder for Gen AI utility functions with context-aware processing
    /controllers            → API request/response logic
      /gen-ai.ts            → Placeholder for Gen AI API controller with context-aware endpoints
    /services               → Pure business logic
      /ai-recommendations.ts → Placeholder for Gen AI recommendation service with context-aware logic
    /types                  → Shared enums, interfaces
    /tests                  → Unit, integration, E2E tests
    /scripts                → Temporary utilities
  ```
  - Organize components by feature (e.g., `/src/components/forms`, `/src/components/layout`).
- **Use Shared Components**:
  - Source UI components from `/lib/design-system/components` or `/src/components`.
  - Check for existing components in `/src/components` and `/lib` before creating new ones.
- **Validate with Zod**:
  - Use shared Zod schemas in `/lib/validators` for frontend and backend, including context-aware data (e.g., `userPreferences`).
  - Never bypass validation for API inputs or form data.
- **Reuse Prisma Queries**:
  - Abstract database calls into reusable services in `/services`.
  - Avoid duplicating queries in `/controllers`.
- **Implement Feature Flags**:
  - Use `hasFeature(companyId, featureKey)` for conditional logic, including `contextAwareness` for Gen AI-driven features.
  - Store flags in database, cache in Redis (10min TTL).
  - Experimental features (e.g., AI-driven enhancements) must be gated behind Superadmin-only flags, off by default.
- **Optimize for Next.js 15 and React 19**:
  - Favor React Server Components (RSC) over client components. Minimize `use client` directives.
  - Use `useActionState` for form state management, avoiding deprecated `useFormState`.
  - Wrap client components in `Suspense` with fallbacks.
- **Performance Optimization**:
  - Ensure API responses <100ms using Redis caching.
  - Lazy-load non-critical components (e.g., charts, modals) with dynamic imports.
  - Optimize images using WebP format and lazy-loading.
  - Plan for context-aware processing to maintain rendering times <300ms, using Redis caching (1hr TTL) for context data.
- **Prepare for Gen AI API Integration (Placeholder)**:
  - Plan a controller in `/controllers/gen-ai.ts` (e.g., `export async function GET() { return { data: "Gen AI coming soon" }; }`) for future API integration with context-aware endpoints.
  - Plan to cache Gen AI responses in Redis (1hr TTL) and handle failures with sonner 1.5.0 toast alerts.

## Security and Logging
- **Secure Logging**:
  - Never log sensitive data (JWTs, passwords, emails, IPs, or internal IDs).
  - Use hashed identifiers for tracking in Sentry or LogRocket.
  - Use Sentry for sanitized logging.
  - Plan to anonymize Gen AI-related data (e.g., performance reviews, skill gaps) and context-aware data (e.g., user preferences) before logging post-MVP.
- **Audit Superadmin Actions**:
  - Log all actions (impersonation, flag toggling, company edits) in `AuditLog` model.
  - Ensure 90-day retention, queryable by timestamp/actor/action.
- **Rate Limiting and Caching**:
  - Apply API rate limits (100 requests/min per user) using `express-rate-limit`.
  - Limit Superadmin actions (10 deletions/hour, 50 impersonations/hour) with Redis.
  - Use TTLs: 1hr for role caches, 10min for feature flags.
  - Plan rate limiting for Gen AI API calls (e.g., 10 requests/min per user) and context-aware requests post-MVP using Redis.
- **Secure API Design**:
  - Sanitize inputs to prevent XSS, CSRF, and SQL injection.
  - Use JWTs rotated every 24 hours, stored securely.
  - Implement strict Content Security Policy (CSP) in Next.js middleware.
  - Plan for context-aware data privacy (e.g., anonymization of user context) in API responses post-MVP.

## Testing Rules
- **Mandate Test Coverage**:
  - 80% unit test coverage for `/services` and `/controllers` using Vitest.
  - E2E tests with Playwright for navigation, RBAC flows, visual validation, and context-aware workflows.
  - Snapshot tests for UI components in `/lib/design-system/components`.
- **Write Tests Concurrently**:
  - Include test cases in feature PRs, colocated as `*.test.ts` or in `/tests`.
  - Cover edge cases (e.g., network failures, invalid inputs, context service downtime).
- **Prefer Stubs Over Mocks**:
  - Use realistic test data and in-memory stores (e.g., Prisma in-memory).
  - Minimize mocking libraries to reduce test fragility.
- **Validate Migrations**:
  - Write E2E tests to compare migrated API endpoints against legacy behavior.
  - Include `legacy.test.ts` or snapshot baselines for refactored implementations.
  - Document test baselines in `/docs/migration.md`.
  - Run tests in CI before merging migration PRs.
- **Test Accessibility**:
  - Run Lighthouse and axe for WCAG 2.1 AA compliance on UI components.
- **Gen AI and Context-Awareness Testing (Placeholder)**:
  - Plan E2E tests for future Gen AI features (e.g., insight generation, context-aware recommendations) in `/tests/gen-ai` using mocked API responses.
  - Validate Gen AI and context-aware failure scenarios (e.g., API downtime, invalid context data) to ensure fallback UX.
  - Test context-aware rendering across all entry points (e.g., personalized dashboards).

## Code Style and Conventions
- **Enforce Standards**:
  - Use ESLint, Prettier, and TypeScript strict mode.
  - All CI checks (ESLint, Vitest, Playwright) must pass before PR merging. No overrides permitted without written stakeholder approval.
  - Validate code in CI pipelines.
- **Naming Conventions**:
  - Files: kebab-case (e.g., `dashboard-overview.tsx`)
  - Folders: lowercase-hyphen (e.g., `feature-flags`)
  - Components: PascalCase (e.g., `ChartPreview`)
  - Types/Interfaces: CamelCase (e.g., `CompanyPlan`)
  - Event handlers: Prefix with `handle` (e.g., `handleClick`).
- **TypeScript Usage**:
  - Use interfaces over types for component props and data structures.
  - Avoid enums; use const maps instead.
  - Use `satisfies` operator for type validation where applicable.
- **Document Code**:
  - Use JSDoc for public functions and complex logic in `/lib` and `/services`.
  - Add inline comments for workarounds, temporary patterns, migration notes, and context-aware placeholders.
- **Write Clean PRs**:
  - Provide detailed PR descriptions, linking to tickets and documenting migration impacts.
  - Group related changes (e.g., UI, API, tests) logically.
  - Remove `console.log`, unused code, and temporary comments before merging.
- **Minimal Code Changes**:
  - Modify only task-related code, avoiding unrelated changes.
  - Use single-chunk edits per file instead of multi-step instructions.
- **Error Handling**:
  - Implement try-catch blocks for all async operations, including Gen AI placeholders.
  - Provide user-friendly error messages via sonner 1.5.0 (e.g., “Context service unavailable”).
  - Plan for context-aware error handling (e.g., fallback to default context) post-MVP.

## Roo Code-Specific Rules
- **Generate in Correct Folders**:
  - Output code only to `/src/components`, `/features`, `/lib`, `/controllers`, `/services`, `/types`, `/tests`, or `/scripts`.
  - Include placeholders for Gen AI and context-awareness in `/src/components/ai`, `/lib/gen-ai-utils.ts`, `/controllers/gen-ai.ts`, and `/services/ai-recommendations.ts`.
  - Validate folder placement against architecture rules.
- **Prevent Duplication**:
  - Search `/src/components`, `/lib`, `/features`, `/services`, and `/controllers` for existing code before generating.
  - Provide a diff highlighting new code and confirming no duplicates.
  - Log reused elements in PR descriptions (e.g., “Reused `Button` from `/lib/design-system`”).
- **Avoid Breaking Changes**:
  - Validate code with TypeScript, ESLint, and Vitest/Playwright tests.
  - Run E2E tests to ensure migrated functionality matches legacy behavior.
  - Document migration changes in `/docs/migration-log.md`.
- **Adhere to Tech Stack**:
  - Use approved tools/versions (e.g., Next.js 15.2.2, Prisma 6.5.0, Vitest 3.1.1) per `/docs/tech-stack.md`.
  - Do not introduce deprecated libraries (e.g., Jest, old Next.js APIs).
  - Plan for Gen AI dependency management (e.g., xAI API client) post-MVP, documented in `/docs/tech-stack.md`.
- **Respect MVP Scope**:
  - Reject tasks outside scope (e.g., microservices, i18n, active Gen AI implementation) per `/docs/scope.md`.
  - Prompt for clarification if input risks scope creep.
- **Fail Gracefully**:
  - If generation fails validation, suggest alternatives or reduce scope.
  - Notify user with clear error messages (e.g., “Duplicate component found in `/lib`”).
- **Support Migration**:
  - Generate code that refactors monolithic logic into controller-service pattern.
  - Propose migrations with before/after tests (e.g., `legacy.test.ts`) to validate functionality.
  - Document migrated modules in `/docs/migration.md`.
- **Enhance PRs**:
  - Generate PR descriptions summarizing changes, reused code, and migration impacts.
  - Respond to reviewer comments with explanations or adjustments.
- **Single-Chunk Edits**:
  - Provide all file edits in a single chunk, avoiding multi-step instructions.
- **No Unnecessary Changes**:
  - Do not suggest whitespace, unrelated code removals, or unrequested optimizations.
- **Handle Developer Mode**:
  - When generating UI or tests for Developer Mode (Persona Switching), include a persistent “Developer View” banner.
  - Ensure state resets on logout or refresh to prevent leaks.
- **Gen AI and Context-Awareness Placeholder Generation**:
  - Generate placeholder files for Gen AI and context-awareness (e.g., `/controllers/gen-ai.ts`, `/services/ai-recommendations.ts`) with comments (e.g., `// Placeholder for Gen AI and context-aware integration across all entry points`).
  - Ensure placeholders align with folder structure and do not introduce active functionality.
- **Version Control**:
  - Use semantic versioning for shared libraries (e.g., `/lib/design-system`) and document in `package.json`.
  - Resolve merge conflicts with `git rebase` and document in PR descriptions.

## Guidelines
- **Validate Against Scope**: Cross-reference changes with `/docs/scope.md` to ensure MVP alignment.
- **Log Migration Changes**: Document migration modifications in `/docs/migration-log.md`.
- **Test Thoroughly**: Run full test suite (Vitest, Playwright) before merging to catch regressions, including context-aware placeholders.
- **Review Carefully**: Require human review for Roo Code-generated PRs to verify no duplicates, unintended deletions, or context-aware implementation errors.
- **Document Reusability**: Update `/docs/development.md` with reusable components, services, or utilities, including Gen AI and context-aware placeholders.
- **Reference Awesome-Roo Coderules**: Follow best practices from https://github.com/PatrickJS/awesome-cursorrules for Next.js, React, and TypeScript.- **Code Review Process**:
  - Require at least one developer and one designer review for context-aware placeholder PRs.
  - Resolve conflicts within 48 hours and document resolutions in `/docs/review-log.md`.
- **Dependency Management**:
  - Lock dependencies in `package.json` with npm or pnpm.
  - Evaluate new Gen AI dependencies post-MVP, ensuring compatibility with existing stack.

## Performance Metrics
- Ensure API endpoints maintain <100ms response time under load (1,000 concurrent users).
- Target <300ms rendering for context-aware UI updates, validated with k6 and Vercel Analytics.
- Optimize bundle size <500KB for production builds, using WebP and lazy-loading.
