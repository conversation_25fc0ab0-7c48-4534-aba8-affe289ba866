---
description: 
globs: 
alwaysApply: true
---
# Workflow Preferences for Roo Code

## 🤖 Context-Awareness Marker
- **Rule**: Begin every response and generated code snippet with the "🤖" icon to confirm understanding and adherence to these workflow preferences.

## Development Workflow (Strictly Enforced)
When approaching any instruction or request, adhere to these steps to ensure high-quality, scalable, reliable, and well-tested code with an exceptional user experience:

### 1. Understand & Breakdown
- Clearly comprehend each request or task.
- Break tasks into small, actionable subtasks for clarity and manageability.
- Clarify ambiguity before implementation by prompting the user for details.

### 2. Thoughtful Implementation
- Focus strictly on the requested task.
- Never modify unrelated areas or introduce global changes unless explicitly scoped.
- Maintain existing patterns and architecture unless instructed otherwise.
- Clean up any test scaffolding, mock data, or experimental components used during development.
- Prevent duplication. Reuse existing logic, components, and styles by searching `/src/components`, `/lib`, `/features`, `/services`, and `/controllers`.
- Preserve all existing functionality. Validate before and after code migration using E2E tests.
- Do not introduce or suggest new tools, technologies, or design paradigms without explicit instruction.
- **Error Handling**: If implementation fails (e.g., failing tests, invalid inputs), notify the user with a clear error message (e.g., “Cannot apply theme: invalid color contrast”), suggest alternatives, and default to safe patterns (e.g., Emynent Default theme). Plan for context-aware failures (e.g., “Context service unavailable”) with fallback to default UI state.
- **Folder Placement**: All generated code must reside only in `/src/components`, `/features`, `/lib`, `/controllers`, `/services`, `/types`, `/tests`, or `/scripts`. Never place logic in `/pages` unless explicitly scoped to route-level layouts (per Emynent Coding Rules, artifact_id: 844ed034-3539-445b-acdc-da23f3336a60).
- **Feature Flag Awareness**: Do not conditionally render new logic unless a `hasFeature(companyId, featureKey)` check is present, stored in Redis with a 10min TTL (per Emynent Coding Rules, artifact_id: 844ed034-3539-445b-acdc-da23f3336a60).
- **Full Context-Awareness Preparation (Placeholder)**:
  - Prepare the app for full context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel) by generating placeholder files for future Gen AI integration (e.g., `/controllers/context.ts`, `/services/context-service.ts`, `/src/components/context-aware/*`).
  - Define a comprehensive `UserContext` model placeholder to capture user data (e.g., role, company, preferences, recent actions, historical data) for dynamic adaptation.
  - Ensure placeholders are feature-flagged with `hasFeature(companyId, "contextAwareness")` and disabled by default, aligning with the deferred approach (per Emynent Scope, artifact_id: 2a65be92-45bc-4f2d-bdf6-3a410ba2b056).
  - Include comments in placeholders (e.g., `// Future Gen AI integration for context-aware personalization`) to guide post-MVP implementation.
### 3. Rigorous Testing
- Unit tests for all business logic using Vitest 3.1.1.
- Integration tests for API interactions and component behavior.
- E2E tests using Playwright for workflows, keyboard navigation, and layout validation.
- Accessibility testing with axe-core, Lighthouse, NVDA/VoiceOver to ensure WCAG 2.1 AA compliance.
- Visual regression testing for layout/UI changes (e.g., with Playwright screenshots).
- **Context-Awareness Testing (Placeholder)**:
  - Plan E2E tests for future context-awareness across all entry points (e.g., personalized dashboard rendering, context-driven settings) in `/tests/context-aware` using mocked API responses.
  - Validate failure scenarios (e.g., context service downtime, invalid context data) to ensure fallback UX (e.g., default dashboard content).
  - Test performance of context-aware rendering (<300ms) using k6 and Vercel Analytics.

### 4. Validation & Completion
- Confirm functionality matches both the original intent and user expectations.
- Ensure code is:
  - Maintainable
  - Scalable
  - Well-documented with meaningful comments where appropriate
- Adhere strictly to code style, naming conventions, and folder structure per Emynent Coding Rules (artifact_id: 844ed034-3539-445b-acdc-da23f3336a60).
- Include a detailed PR description:
  - Summary of changes
  - Affected areas
  - Reused components or services
  - Validation steps
  - Add a comment: "🧪 No regression introduced" after confirming snapshot and E2E test completion.
- **Documentation**: Update project documentation for architectural changes in `/docs/migration-log.md`, reusable components/services in `/docs/development.md`, context-awareness placeholders in `/docs/context-awareness-plan.md`, and performance/security plans in `/docs/context-performance-security.md`.


### 12. **Prisma / DB Schema**
- Roo Code must:
  - Use strict typing and modular schema design.
  - Avoid raw SQL unless approved.
  - Sync with `zod` validation models.

### 13. **Auto-Refactor Restrictions**
- Roo Code must not:
  - Auto-refactor without confirmation.
  - Proceed unless test coverage and visual diff confirm no regression.

### 14. **Developer Documentation**
- Roo Code must:
  - Update internal docs for new APIs and components.
  - Update Storybook (if applicable).

### 15. **i18n Readiness**
- Roo Code must:
  - Avoid hardcoded user-facing text.
  - Use centralized i18n wrappers for content.

### 16. **Fail-Safe or Abort Logic**
- If Roo Code encounters a conflict or unclear task, it must:
  - Abort and request clarification.
  - Never guess or hallucinate workarounds.

## UX Guidelines
- **Navigation**: Preserve persistent sidebar + navbar across authenticated pages, inheriting from `app/(protected)/layout.tsx`.
- **Clarity**: Keep interactions simple, expected, and user-centric.
- **Refinement**: Maintain a polished, elegant UI through consistent spacing, typography, and animation per Emynent Design Preferences (artifact_id: 7f0fba54-f330-4bd9-85fb-597f42724ae4).
- **Accessibility**: Every route, flow, and UI element must meet WCAG 2.1 AA standards, validated with axe-core, Lighthouse, and NVDA/VoiceOver.
- **Responsiveness**: Verify across breakpoints (`sm`, `md`, `lg`) using Tailwind CSS 4.0.12 responsive prefixes.
- **Context-Awareness UX (Placeholder)**:
  - Plan for future context-aware UI elements across all entry points (e.g., personalized onboarding flows, dynamic dashboard widgets, context-driven Superadmin Panel) in `/src/components/context-aware`.
  - Ensure alignment with the design system (v1.0) and white-labeled themes (e.g., Red Bull colors), preparing for Gen AI-driven personalization.
  - Use subtle animations (via Framer Motion 12.5.0) to guide users through context-aware transitions (e.g., dynamic widget updates).

## Additional Workflow Rules
- **No Orphaned Code**: Remove unused components, dead code, or debug scripts.
- **Test Coverage**: Never merge untested major logic or workflows; ensure 80% unit test coverage for `/services` and `/controllers`.
- **Minimal Scope**: Keep changes small, isolated, and clear (per awesome-cursorrules, code-guidelines-cursorrules-prompt-file).
- **No Hex in UI**: All colors must reference design tokens from `tailwind.config.ts`.
- **No Breakages**: Layout, theming, and routing must remain intact post-implementation.
- **Theme Persistence**: Never regress the theme logic when migrating to client-server architecture, ensuring database/Redis storage (1hr TTL) and fallback to system mode with Emynent Default. If Redis or DB fails during theme load, fallback to system mode + Emynent Default, and display a toast alert using sonner 1.5.0: “Theme preferences could not be loaded.”
- **Theme Customization Workflow**:
  - When applying or creating custom themes (e.g., Red Bull colors), extend `tailwind.config.ts`, validate WCAG 2.1 AA compliance with axe-core, log in `/docs/design-system-changelog.md`, and confirm with Playwright snapshots.
  - Notify user if customization fails (e.g., “Custom color #FF0000 fails contrast ratio”).
- **Performance Optimization**:
  - Minimize bundle size by ensuring Tailwind’s purge removes unused styles, validated in production builds.
  - Optimize API calls in `/controllers` using Redis caching (1hr TTL for roles, 10min for feature flags).
  - Validate Web Vitals (LCP < 2.5s, FCP < 1.5s, CLS < 0.1) using Vercel Analytics to ensure performance targets are met.
  - Plan for context-aware rendering to maintain <300ms update time, using lazy-loading for dynamic elements.
- **Security**:
  - Sanitize all inputs to prevent XSS, CSRF, and SQL injection.
  - Never log sensitive data (e.g., JWTs, emails, IPs) in Sentry or LogRocket; use hashed identifiers.
  - Apply rate limiting to API endpoints (100 requests/min per user) and Superadmin actions (10 deletions/hour, 50 impersonations/hour) using Redis.
  - Plan rate limiting for context-aware API calls (e.g., 10 requests/min per user) post-MVP.
- **Context-Awareness Placeholder Workflow**:
  - Generate placeholder files for context-awareness across all entry points (e.g., `/controllers/context.ts`, `/services/context-service.ts`) with comments indicating future use (e.g., `// Aggregate user context for personalization`).
  - Ensure placeholders are feature-flagged with `hasFeature(companyId, "contextAwareness")` and disabled by default.
- **Context-Awareness Data Privacy (Placeholder)**:
  - Plan to anonymize sensitive user data before sending to Gen AI APIs post-MVP, using hashed identifiers to ensure privacy across all context-aware features.
- **Version Control**:
  - Use semantic versioning for shared libraries (e.g., `/lib/design-system`) and document in `package.json`.
  - Resolve merge conflicts with `git rebase` and document in PR descriptions.

## Collaboration Expectations
- Work via direct code contribution or screenshot previews.
- Avoid introducing features, tools, or code paths outside the Emynent scope unless asked (per artifact_id: 2a65be92-45bc-4f2d-bdf6-3a410ba2b056).
- All test or demo logic must be removed prior to final PR delivery.
- Changes must validate against the current layout, navigation, theme, and onboarding architecture.
- **Cross-Team Validation**:
  - Ensure changes are reviewed by at least one developer and one designer to validate functionality and UX alignment.
  - Document cross-team feedback in `/docs/design-feedback.md`.
  - For context-awareness placeholders, seek feedback on structure, performance, and security implications.
- **Feedback Loop**:
  - Respond to PR comments with explanations or adjustments, logging feedback in `/docs/design-feedback.md`.
  - Iterate on designs or code based on developer/designer input to ensure alignment.
  - For context-awareness placeholders, seek feedback on placeholder structure and documentation, ensuring alignment with post-MVP goals for full app context-awareness.
- **Training and Onboarding for Roo Code**:
  - Train Roo Code on context-aware patterns by referencing `/docs/context-awareness-plan.md` and Emynent Design Preferences (artifact_id: 7f0fba54-f330-4bd9-85fb-597f42724ae4).
  - Onboard Roo Code to prioritize performance and security in context-aware implementations, using mock scenarios (e.g., context service downtime).

## Optional Context: Emynent Project
- Emynent is a career and skills progression platform for growth-stage companies.
- The MVP is internally piloted, with strict design, UX, and testing principles.
- The app aims to be fully context-aware across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel), adapting dynamically to user context (e.g., role, company, preferences, recent actions, historical data). This context-awareness will leverage Gen AI for personalized insights, recommendations, and coaching, deferred to post-MVP (Q3-Q4 2025).
- **Related Artifacts**:
  - Emynent Coding Rules: .roo/rules/emynent-coding-rules.md
  - Emynent Design Preferences: .roo/rules/emynent-design.md
  - Emynent Scope: .roo/rules/emynent-scope.md
  - Emynent Tech Stack: .roo/rules/emynent-tech-stack.md

---

This document governs how Roo Code operates when working on the Emynent project. All functionality must be preserved. No regressions, deviations, or scope expansions are permitted unless explicitly approved.
