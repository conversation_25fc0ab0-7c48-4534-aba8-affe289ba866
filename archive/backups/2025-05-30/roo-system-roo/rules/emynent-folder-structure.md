---
description: 
globs: 
alwaysApply: false
---
# 🧠 Emynent Project Folder Structure Guide (AI-First, Clean, and Scalable)

This document defines the **AI-native**, **domain-driven**, and **scalable** folder structure for the Emynent project, optimized for a single-repository client-server architecture post-migration (May 2025). It supports **multi-IDE environments** (Roo Code, PyCharm, VSCode) and **coding assistants** (Argument Code, Roo Code), ensuring seamless understanding of the codebase.

---

## 🌳 Root Directory Layout

```bash
/emynent
├── src/                 # Core application source (client-server split)
├── domains/             # Business and product domains
├── packages/            # Shared design, types, utils, AI logic
├── infra/               # Infra-as-code, CI/CD, containerization
├── scripts/             # Project automation, dev tools, AI helpers
├── tests/               # Global test configurations and mocks
├── archive/             # Archived legacy modules
├── docs/                # Project documentation
│   ├── migration-log.md       # Migration changes
│   ├── architecture-rules.md  # Architecture guidelines
│   ├── client-server-migration-rules.md  # Migration guidelines
│   ├── adr/                  # Architecture Decision Records
│   │   ├── 001-monorepo.md
│   │   ├── 002-client-server.md
│   │   └── 003-theming.md
│   ├── security/             # Security documentation
│   │   ├── best-practices.md
│   │   └── checklist.md
│   └── workflows/            # Cross-domain workflows
│       ├── user-onboarding-to-billing.md
│       └── tenant-management.md
├── public/              # Static assets (Next.js)
├── environments/        # Environment configurations
│   ├── .env.development
│   ├── .env.test
│   ├── .env.production
│   └── schema.ts       # Zod schema for env validation
├── .editorconfig        # Cross-IDE formatting (indent_size=2, charset=utf-8)
├── .nvmrc               # Node version (18.17.0 for Next.js 15+)
├── package.json         # Root dependencies with workspaces config
├── turbo.json           # Turborepo config for optimized builds
├── pnpm-workspace.yaml  # PNPM workspace config
├── tsconfig.json        # Shared TS config
├── next.config.mjs      # Next.js configuration (ESM)
└── README.md            # Project overview, IDE/assistant setup
🧠 AI Agents Start Here: Use context-map.json in each folder for grounding. Avoid /archive/ unless instructed. Log changes in /docs/migration-log.md.

🧩 /src — Core Application (Client-Server Split)
bash

Copy
/src
├── app/                 # Next.js App Router (client-side routes)
│   ├── (protected)/
│   │   ├── layout.tsx   # Persistent layout (navbar, sidebar, theming)
│   │   ├── dashboard/   # Dashboard page
│   │   ├── settings/    # Settings panel (Appearance for theming)
│   │   │   └── page.tsx
│   │   ├── onboarding/  # Conversational onboarding journey
│   │   │   └── page.tsx
│   │   └── login/       # Login page
│   │       └── page.tsx
│   ├── api/             # Server-side API routes (Next.js)
│   │   ├── v1/          # API version 1
│   │   │   ├── openapi/ # OpenAPI specifications
│   │   │   │   └── auth.yaml
│   │   │   ├── contracts/  # API contract definitions
│   │   │   ├── auth/
│   │   │   │   └── login/
│   │   │   │       └── route.ts  # POST /api/v1/auth/login
│   │   │   ├── onboarding/
│   │   │   │   └── progress/
│   │   │   │       └── route.ts  # POST /api/v1/onboarding/progress
│   │   │   └── middlewares/ # API middlewares (security, error handling)
│   │   └── v2/          # API version 2 (future)
│   └── layout.tsx       # Root layout
├── components/          # Reusable UI blocks
│   ├── context-aware/   # Context-aware UI (e.g., OnboardingFlow.tsx)
│   ├── onboarding/      # Onboarding components (e.g., ChatInterface.tsx)
│   ├── auth/            # Auth UI (e.g., LoginForm.tsx)
│   ├── shared/          # Generic UI (e.g., Button.tsx)
│   └── themes/          # App-specific theme overrides
├── context/             # React contexts
│   ├── AuthContextProvider.tsx  # Authentication context
│   └── ThemeContext.tsx         # Theming context
├── store/               # Global state management
│   ├── slices/          # Feature-specific state slices
│   │   ├── auth/
│   │   ├── onboarding/
│   │   └── theme/
│   └── hooks/           # Custom store hooks
├── hooks/               # Custom React logic (e.g., useAuth.ts, useTheme.ts)
├── controllers/         # API request handlers
│   ├── auth.ts          # Auth handlers
│   ├── onboarding.ts    # Onboarding handlers
│   └── context.ts       # Context-aware handlers (placeholder)
├── services/            # Stateless business logic
│   ├── auth-service.ts  # Auth logic
│   ├── onboarding-service.ts  # Onboarding logic
│   └── ai-recommendations.ts  # Gen AI recommendations
├── lib/                 # Utilities
│   ├── theme-utils.ts   # Theme persistence (next-themes, Redis)
│   └── validators/      # Zod schemas
├── types/               # TS types (e.g., UserContext.ts)
│   └── i18n/            # Internationalization types
├── features/            # Feature flags management
│   ├── auth-flags.ts
│   │   └── isLoginEnabled.ts
│   └── onboarding-flags.ts
└── README.md            # App structure overview
🧠 /domains — Domain-Driven Feature Modules
bash

Copy
/domains
├── auth/
│   ├── client/          # UI: LoginForm.tsx
│   ├── server/          # Logic: login.handler.ts
│   ├── shared/          # Types: AuthRole.ts
│   ├── __tests__/       # Domain-specific tests
│   │   ├── client/
│   │   ├── server/
│   │   └── integration/
│   ├── journeys.md      # Login, logout flows
│   ├── context-map.json # Entrypoints: /api/v1/auth/login, AuthContextProvider.tsx
│   └── README.md
├── onboarding/
│   ├── client/          # UI: OnboardingSteps.tsx
│   ├── server/          # Logic: progress.handler.ts
│   ├── shared/          # Types: OnboardingProgress.ts
│   ├── __tests__/       # Domain-specific tests
│   │   ├── client/
│   │   ├── server/
│   │   └── integration/
│   ├── journeys.md      # Conversational onboarding flow
│   ├── context-map.json # Entrypoints: /onboarding, /api/v1/onboarding/progress
│   └── README.md
├── settings/
│   ├── client/          # UI: AppearancePanel.tsx
│   ├── server/          # Logic: theme.handler.ts
│   ├── shared/          # Types: ThemeConfig.ts
│   ├── __tests__/       # Domain-specific tests
│   │   ├── client/
│   │   ├── server/
│   │   └── integration/
│   ├── journeys.md      # Theme selection flow
│   ├── context-map.json # Entrypoints: /settings
│   └── README.md
├── billing/
├── users/
└── tenant/
📦 /packages — Shared Libraries
bash

Copy
/packages
├── design-system/
│   ├── src/
│   │   ├── components/  # Shared UI: Button.tsx, Modal.tsx
│   │   ├── themes/      # Theme tokens
│   │   └── accessibility/  # a11y-checks.ts, guidelines.md
│   ├── design-system.config.ts  # Component props, variants
│   └── tailwind.config.ts
├── ai-agents/
│   ├── context/         # JSON maps
│   ├── prompts/         # Gen AI templates
│   ├── embeddings/      # Code embeddings for semantic search_files
│   └── codegen/         # Templates for AI code generation
├── telemetry/
│   ├── src/
│   │   ├── metrics/     # Performance metrics (frontend/backend)
│   │   └── logging/     # Centralized logging
├── reusables/           # Pre-built templates: CardTemplate.tsx
├── utils/               # Helpers: formatDate.ts
├── types/               # Global TS interfaces: UserContext.ts
│   └── i18n/            # Internationalization types
└── README.md
⚙️ /infra — Infrastructure
bash
Copy
/infra
├── ci/                  # GitHub Actions workflows
├── terraform/           # Infra modules
├── docker/              # Dockerfiles
├── environments/        # .env.dev, .env.prod
└── README.md            # Performance monitoring setup
🧪 /tests — Global Testing Config
bash

Copy
/tests
├── mocks/               # Mocks: mockUser.ts
├── fixtures/            # Test data: testOnboardingData.json
├── theme-snapshots/     # Theme variant snapshots
├── a11y/                # Accessibility tests
├── global-setup.ts      # Vitest bootstrap
├── vitest.config.ts
├── playwright.config.ts
└── README.md
🧹 /scripts — Automation and Tooling
bash

Copy
/scripts
├── dev/
│   ├── setup.sh          # Initial developer setup
│   ├── seed-db.ts        # Seed databases with test data
│   ├── generate-types.ts # Generate TypeScript types from APIs
│   └── visualize-deps.ts # Generate dependency graphs (dependency-cruiser)
├── ai/
│   ├── context-update.ts # Update AI context maps
│   └── vector-index.ts   # Generate embeddings for code search_files
├── scaffold.ts          # Generate domain boilerplate
├── cleanup.ts           # Move orphaned files to /archive
├── refresh-context.ts   # Update context-map.json
├── debug-tools.ts       # Debug helpers
└── README.md
📦 /archive — Soft-Deleted Modules
bash

Copy
/archive
├── legacy_monolith/     # Pre-migration code
├── unused_endpoints/
└── README.md
✨ AI + Developer Journey Design
Each domain and app includes:

README.md: Purpose, key files.
context-map.json: Entrypoints for AI (e.g., /api/v1/auth/login).
journeys.md: Flows (e.g., login → dashboard).
AI Agent Instructions:

Prioritize context-map.json.
Avoid /archive/ unless instructed.
Log changes in /docs/migration-log.md.
🧭 Navigation Guide (Human + AI)
Task	File Path
Fix login error	/src/context/AuthContextProvider.tsx
Debug API login	/src/app/api/v1/auth/login/route.ts
Debug theming	/src/app/(protected)/layout.tsx
Fix Appearance panel	/domains/settings/client/AppearancePanel.tsx
Restore onboarding	/domains/onboarding/client/OnboardingSteps.tsx
Debug onboarding API	/src/app/api/v1/onboarding/progress/route.ts
Review flow	/domains/onboarding/journeys.md
Update state slice	/src/store/slices/auth/
Generate API types	/scripts/dev/generate-types.ts
Visualize dependencies	/scripts/dev/visualize-deps.ts
Review security guidelines	/docs/security/best-practices.md
Monitor performance	/packages/telemetry/src/metrics/
✅ Summary
This structure is:

Client-Server Ready: Reflects the single-repo migration.
Design System Compliant: Supports consistency, theming, accessibility, and reusability.
AI-Friendly: Optimized for Roo Code, PyCharm, and Argument Code with enhanced AI capabilities.
Issue-Focused: Addresses login, theming, and onboarding.
Improved: Enhanced with dependency visualization, API versioning, performance monitoring, security guidelines, and cross-domain workflows.



Review this folder structure and let me know your thoughts. Also, advise what's missing.
