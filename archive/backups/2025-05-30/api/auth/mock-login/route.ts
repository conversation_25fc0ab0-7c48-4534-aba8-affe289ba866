import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  // Only available in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 404 });
  }

  // Create a simple mock session cookie - this matches what NextAuth expects
  const mockToken = {
    name: 'Admin User',
    email: '<EMAIL>',
    picture: null,
    sub: 'mock-user-id',
    role: 'ADMIN',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours
    jti: 'mock-jwt-id'
  };

  // Encode as base64
  const tokenStr = Buffer.from(JSON.stringify(mockToken)).toString('base64');

  // Set session cookie directly
  cookies().set({
    name: 'next-auth.session-token',
    value: tokenStr,
    httpOnly: true,
    path: '/',
    maxAge: 60 * 60 * 24, // 1 day
  });

  // Redirect to our mock dashboard page
  return NextResponse.redirect(new URL('/mock-dashboard', request.url));
}

export async function POST(request: NextRequest) {
  return GET(request);
} 