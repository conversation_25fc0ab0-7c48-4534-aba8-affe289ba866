"use client"

// This is a mock implementation for auth to use in development and testing
// It bypasses the need for Prisma in client components

export const mockSession = {
  user: {
    id: "mock-user-id",
    name: "Mock User",
    email: "<EMAIL>",
    role: "EMPLOYEE",
    image: "https://avatars.githubusercontent.com/u/1234567",
  },
};

export const mockAuth = async () => {
  // Return mock session for development/testing purposes
  return mockSession;
};

export const mockSignIn = async () => {
  console.log("Mock sign in called");
  return { ok: true };
};

export const mockSignOut = async () => {
  console.log("Mock sign out called");
  return { ok: true };
}; 