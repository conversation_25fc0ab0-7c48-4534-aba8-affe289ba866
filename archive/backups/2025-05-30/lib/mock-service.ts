/**
 * Mock Service
 * 
 * This module provides mock implementations for services when actual database
 * or Redis services aren't available. This is useful for development and testing.
 */

// Mock feature flags
const FEATURE_FLAGS = {
  'contextAwareness': false,
  'advancedAnalytics': true,
  'betaFeatures': false,
  'newDashboard': true,
};

// Mock user data
const USERS = [
  {
    id: '1',
    name: 'Demo User',
    email: '<EMAIL>',
    role: 'ADMIN',
    companyId: '1',
    settings: {
      theme: 'system',
      notifications: true,
      emailDigest: 'weekly'
    }
  },
  {
    id: '2',
    name: 'Test Employee',
    email: '<EMAIL>',
    role: 'EMPLOYEE',
    companyId: '1',
    settings: {
      theme: 'dark',
      notifications: false,
      emailDigest: 'daily'
    }
  }
];

// Mock companies
const COMPANIES = [
  {
    id: '1',
    name: 'Emynent Test Company',
    domain: 'example.com',
    plan: 'enterprise',
    settings: {
      defaultTheme: 'emynent-default',
      logoUrl: 'https://placehold.co/150',
      brandColor: '#1E40AF'
    }
  }
];

// Mock user contexts for future Gen AI integration
const USER_CONTEXTS = {
  '1': {
    role: 'ADMIN',
    companyId: '1',
    preferences: {
      theme: 'system',
      dashboardLayout: 'compact'
    },
    recentActions: [
      { type: 'viewed_profile', timestamp: new Date().toISOString() },
      { type: 'updated_settings', timestamp: new Date().toISOString() }
    ],
    historicalData: {
      loginCount: 42,
      lastActive: new Date().toISOString()
    }
  }
};

/**
 * Check if a feature is enabled for a company
 */
export function hasFeature(companyId: string, featureKey: string): boolean {
  console.log(`[MOCK] Checking feature ${featureKey} for company ${companyId}`);
  return FEATURE_FLAGS[featureKey] || false;
}

/**
 * Get a user's settings
 */
export async function getUserSettings(userId: string) {
  console.log(`[MOCK] Getting settings for user ${userId}`);
  const user = USERS.find(u => u.id === userId);
  return user?.settings || null;
}

/**
 * Update a user's settings
 */
export async function updateUserSettings(userId: string, settings: any) {
  console.log(`[MOCK] Updating settings for user ${userId}`, settings);
  const userIndex = USERS.findIndex(u => u.id === userId);
  
  if (userIndex >= 0) {
    USERS[userIndex].settings = {
      ...USERS[userIndex].settings,
      ...settings
    };
    return USERS[userIndex].settings;
  }
  
  return null;
}

/**
 * Get company details
 */
export async function getCompany(companyId: string) {
  console.log(`[MOCK] Getting company ${companyId}`);
  return COMPANIES.find(c => c.id === companyId) || null;
}

/**
 * Update company settings
 */
export async function updateCompany(companyId: string, data: any) {
  console.log(`[MOCK] Updating company ${companyId}`, data);
  const companyIndex = COMPANIES.findIndex(c => c.id === companyId);
  
  if (companyIndex >= 0) {
    COMPANIES[companyIndex] = {
      ...COMPANIES[companyIndex],
      ...data,
      settings: {
        ...COMPANIES[companyIndex].settings,
        ...data.settings
      }
    };
    return COMPANIES[companyIndex];
  }
  
  return null;
}

/**
 * Get user by ID
 */
export async function getUser(userId: string) {
  console.log(`[MOCK] Getting user ${userId}`);
  return USERS.find(u => u.id === userId) || null;
}

/**
 * Get user context for Gen AI integration (placeholder)
 */
export async function getUserContext(userId: string) {
  console.log(`[MOCK] Getting context for user ${userId}`);
  return USER_CONTEXTS[userId] || null;
}

/**
 * Update user context (placeholder)
 */
export async function updateUserContext(userId: string, contextData: any) {
  console.log(`[MOCK] Updating context for user ${userId}`, contextData);
  USER_CONTEXTS[userId] = {
    ...USER_CONTEXTS[userId],
    ...contextData
  };
  
  return USER_CONTEXTS[userId];
}

/**
 * Check if services are available
 */
export function areServicesAvailable() {
  return {
    database: false,
    redis: false
  };
}

/**
 * Mock analytics tracking
 */
export async function trackEvent(userId: string, event: string, data: any) {
  console.log(`[MOCK] Tracking event ${event} for user ${userId}`, data);
  return { success: true };
}

/**
 * Mock notification service
 */
export async function getNotifications(userId: string) {
  console.log(`[MOCK] Getting notifications for user ${userId}`);
  return [
    { id: '1', title: 'Welcome to Emynent', read: false, createdAt: new Date().toISOString() },
    { id: '2', title: 'New feature available', read: true, createdAt: new Date().toISOString() }
  ];
}

/**
 * Mark notification as read
 */
export async function markNotificationRead(notificationId: string) {
  console.log(`[MOCK] Marking notification ${notificationId} as read`);
  return { success: true };
} 