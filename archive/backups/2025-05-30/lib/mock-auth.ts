/**
 * Mock Authentication
 * 
 * This module provides mock authentication functions for development and testing
 * when real authentication services are not available.
 */

import { cookies } from 'next/headers';

// Mock session data for development
const MOCK_SESSION = {
  user: {
    id: '1',
    name: 'Demo User',
    email: '<EMAIL>',
    image: 'https://placehold.co/150',
    role: 'ADMIN'
  },
  expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7).toISOString() // 7 days
};

/**
 * Mock function to get the current session
 */
export async function getMockSession() {
  // Check if mock auth is enabled
  if (process.env.USE_MOCKS !== 'true') {
    return null;
  }
  
  // Check if session cookie exists
  const cookieStore = cookies();
  const hasMockSession = cookieStore.has('mock_session');
  
  return hasMockSession ? MOCK_SESSION : null;
}

/**
 * Mock function to create a session
 */
export async function createMockSession() {
  return MOCK_SESSION;
}

/**
 * Create a mock authentication handler for API routes
 */
export function withMockAuth(handler: Function) {
  return async (req: Request, ...args: any[]) => {
    // Add mock user headers for testing
    if (process.env.USE_MOCKS === 'true') {
      const headers = new Headers(req.headers);
      headers.set('x-user-id', '1');
      headers.set('x-user-role', 'ADMIN');
      headers.set('x-company-id', '1');
      
      // Create a new request with the updated headers
      const newReq = new Request(req.url, {
        method: req.method,
        headers,
        body: req.body,
        cache: req.cache,
        credentials: req.credentials,
        integrity: req.integrity,
        keepalive: req.keepalive,
        mode: req.mode,
        redirect: req.redirect,
        referrer: req.referrer,
        referrerPolicy: req.referrerPolicy,
        signal: req.signal,
      });
      
      return handler(newReq, ...args);
    }
    
    return handler(req, ...args);
  };
} 