import { cookies } from 'next/headers';

// This is a server-side mock implementation for auth to use in development and testing
// It provides the same mock data but can be used from server components

export const mockSession = {
  user: {
    id: "mock-user-id",
    name: "<PERSON><PERSON> User",
    email: "<EMAIL>",
    role: "ADMIN", // Changed to ADMIN to test admin features
    image: "https://avatars.githubusercontent.com/u/1234567",
  },
};

export async function getMockSession() {
  // Return mock session for development/testing purposes
  return mockSession;
}

// Server-side mock authentication check
export async function mockAuth() {
  // In a real implementation, you would check cookies, session store, etc.
  const cookieStore = cookies();
  const mockAuthCookie = cookieStore.get('mock-auth');
  
  // Only return a session if the mock-auth cookie is present
  // This simulates an authenticated user
  if (mockAuthCookie?.value === 'true' || process.env.NODE_ENV === 'development') {
    return mockSession;
  }
  
  // Return null if not authenticated, just like the real auth() function
  return null;
} 