@tailwind base;
@tailwind components;
@tailwind utilities;

@import './theme-styles.css';

@layer base {
  :root {
    /* Base Elements */
    --background: #fcfcfc;
    --foreground: #333333;
    --card: #ffffff;
    --card-foreground: #333333;
    --popover: #ffffff;
    --popover-foreground: #333333;

    /* Component Colors - Purple (Default) */
    --primary: #8957e5;
    --primary-foreground: #ffffff;
    --secondary: #6e40c9;
    --secondary-foreground: #ffffff;
    --accent: #b388ff;
    --accent-foreground: #333333;

    /* Supporting Colors */
    --muted: #f5f5f5;
    --muted-foreground: #737373;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #e5e5e5;
    --input: #e5e5e5;
    --ring: #8957e5;

    /* Sidebar Harmonization */
    --sidebar: #f8f8f8;
    --sidebar-foreground: #333333;
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);

    /* Gradient with Color Harmony */
    --gradient: linear-gradient(to right, var(--primary), var(--accent));
    --radius: 0.625rem;
  }

  .dark {
    /* Base Elements */
    --background: #0d1117;
    --foreground: #f0f6fc;
    --card: #161b22;
    --card-foreground: #f0f6fc;
    --popover: #161b22;
    --popover-foreground: #f0f6fc;

    /* Component Colors - Purple (Default) */
    --primary: #8957e5;
    --primary-foreground: #ffffff;
    --secondary: #6e40c9;
    --secondary-foreground: #ffffff;
    --accent: #b388ff;
    --accent-foreground: #ffffff;

    /* Supporting Colors */
    --muted: #21262d;
    --muted-foreground: #8b949e;
    --destructive: #f85149;
    --destructive-foreground: #ffffff;
    --border: #30363d;
    --input: #30363d;
    --ring: #8957e5;

    /* Sidebar Harmonization */
    --sidebar: #0d1117;
    --sidebar-foreground: #f0f6fc;
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);

    /* Gradient with Color Harmony */
    --gradient: linear-gradient(to right, var(--primary), var(--accent));
  }
}

/* Global base styles */
* {
  border-color: var(--border);
}

body {
  color: var(--foreground);
  background: var(--background);
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
}
