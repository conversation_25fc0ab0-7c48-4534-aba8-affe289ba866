import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
  console.log('🔍 Debug health check started')

  try {
    // Step 1: Test basic response
    console.log('✅ Step 1: Basic response test')

    // Step 2: Test imports
    console.log('✅ Step 2: Testing imports...')

    const { prisma } = await import('@/lib/prisma')
    console.log('✅ Step 2a: Prisma imported')

    const { checkDatabaseHealth } = await import('@/lib/database-health')
    console.log('✅ Step 2b: Database health imported')

    // Step 3: Test database connection
    console.log('✅ Step 3: Testing database...')
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Step 3: Database query successful:', result)

    // Step 4: Test health check function
    console.log('✅ Step 4: Testing health check function...')
    const healthStatus = await checkDatabaseHealth()
    console.log('✅ Step 4: Health check completed:', healthStatus.status)

    return NextResponse.json({
      status: 'healthy',
      steps: 4,
      database: 'connected',
      healthCheck: healthStatus.status,
      timestamp: new Date(),
    })
  } catch (error) {
    console.error('❌ Debug health check failed at step:', error)

    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date(),
      },
      { status: 503 }
    )
  }
}
