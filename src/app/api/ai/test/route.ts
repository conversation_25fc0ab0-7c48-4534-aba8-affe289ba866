import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

/**
 * GET /api/ai/test
 * Health check endpoint for AI services
 */
export async function GET(request: NextRequest) {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token?.sub) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Basic AI service health check
    return NextResponse.json(
      {
        status: 'healthy',
        aiServicesAvailable: true,
        timestamp: Date.now(),
        message: 'AI services are operational',
      },
      { status: 200 }
    )
  } catch {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') console.error('Error in AI health check:', error)
    }
    return NextResponse.json(
      {
        status: 'error',
        aiServicesAvailable: false,
        error: 'AI services temporarily unavailable',
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/ai/test
 * Test AI functionality with a simple request
 */
export async function POST(request: NextRequest) {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token?.sub) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { testType = 'basic' } = body

    // Simulate AI processing
    const testResult = {
      testType,
      status: 'success',
      result: 'AI test completed successfully',
      processingTime: Math.floor(Math.random() * 100) + 50, // 50-150ms
      timestamp: Date.now(),
    }

    return NextResponse.json(testResult, { status: 200 })
  } catch {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') console.error('Error in AI test:', error)
    }
    return NextResponse.json(
      {
        status: 'error',
        error: 'AI test failed',
      },
      { status: 500 }
    )
  }
}
