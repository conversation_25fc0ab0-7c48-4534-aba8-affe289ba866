import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { z } from 'zod'

// Validation schema for the request
const NavigationRecommendationsSchema = z.object({
  userId: z.string(),
  provider: z.string().default('openai'),
  context: z.object({
    userId: z.string(),
    role: z.string().optional(),
    recentlyVisited: z.array(z.string()).optional(),
    currentPath: z.string().optional(),
  }),
})

// Mock AI recommendations for navigation
function generateMockRecommendations(context: any) {
  const baseRecommendations = [
    {
      id: 'focus-today',
      type: 'navigation',
      priority: 'high',
      message: 'Complete your daily focus review',
      actionType: 'navigate',
      actionData: { href: '/focus/today' },
      confidence: 0.9,
    },
    {
      id: 'team-collaborate',
      type: 'navigation',
      priority: 'medium',
      message: 'Check team updates and collaboration requests',
      actionType: 'navigate',
      actionData: { href: '/team' },
      confidence: 0.8,
    },
    {
      id: 'skills-assessment',
      type: 'navigation',
      priority: 'medium',
      message: 'Update your skills assessment',
      actionType: 'navigate',
      actionData: { href: '/grow/skills' },
      confidence: 0.7,
    },
  ]

  // Customize based on role
  if (context.role === 'MANAGER') {
    baseRecommendations.unshift({
      id: 'team-dashboard',
      type: 'navigation',
      priority: 'high',
      message: 'Review team performance dashboard',
      actionType: 'navigate',
      actionData: { href: '/team/dashboard' },
      confidence: 0.95,
    })
  }

  return baseRecommendations
}

export async function POST(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = NavigationRecommendationsSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validation.error.errors },
        { status: 400 }
      )
    }

    const { userId, provider, context } = validation.data

    // Verify user can only request their own recommendations
    if (userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // For MVP, return mock recommendations
    // In production, this would call actual AI service
    const recommendations = generateMockRecommendations(context)

    return NextResponse.json({
      success: true,
      recommendations,
      provider,
      timestamp: new Date().toISOString(),
    })
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('Navigation recommendations error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({ error: 'Method not allowed. Use POST instead.' }, { status: 405 })
}
