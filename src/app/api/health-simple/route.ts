import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
  try {
    console.log('🔍 Starting health check...')

    // Test basic database connectivity
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Database query successful:', result)

    // Test basic table access
    const userCount = await prisma.user.count()
    console.log('✅ User count:', userCount)

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date(),
      database: 'connected',
      userCount,
    })
  } catch (error) {
    console.error('❌ Health check failed:', error)

    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      },
      { status: 503 }
    )
  }
}
