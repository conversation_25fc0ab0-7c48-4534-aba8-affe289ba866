import { handlers } from '@/lib/auth/config'

if (process.env.NODE_ENV === 'development') console.log('🔐 NextAuth route handler initialized')

// Use the pre-exported handlers from config to avoid double initialization
export const { GET, POST } = handlers

// Handle all other HTTP methods
export async function PUT(req: Request) {
  return Response.json({ error: 'Method not allowed' }, { status: 405 })
}

export async function PATCH(req: Request) {
  return Response.json({ error: 'Method not allowed' }, { status: 405 })
}

export async function DELETE(req: Request) {
  return Response.json({ error: 'Method not allowed' }, { status: 405 })
}
