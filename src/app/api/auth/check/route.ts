import { NextRequest, NextResponse } from 'next/server'
import { validateAndRefreshSession } from '@/lib/auth/session'

/**
 * GET /api/auth/check
 * Simple endpoint to check if authentication is working
 */
export async function GET(request: NextRequest) {
  try {
    // Get session validation result
    const validationResult = await validateAndRefreshSession(request)

    if (!validationResult.isValid || !validationResult.token) {
      return NextResponse.json(
        {
          authenticated: false,
          message: 'Not authenticated',
        },
        { status: 401 }
      )
    }

    // Return basic authentication info
    return NextResponse.json(
      {
        authenticated: true,
        message: 'Authentication valid',
        user: {
          id: validationResult.token.id,
          email: validationResult.token.email,
          role: validationResult.token.role,
        },
      },
      { status: 200 }
    )
  } catch {
    // console.error('Auth check error:', error)
    return NextResponse.json(
      {
        authenticated: false,
        message: 'Authentication error',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
