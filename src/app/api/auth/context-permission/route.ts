import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getSessionFromCookies, validateAccessToken } from '@/lib/auth/session'
import { rbacService } from '@/services/rbac-service'
import { hasFeature } from '@/lib/features'

// Context permission request schema
const contextPermissionSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  resource: z.string().min(1, 'Resource is required'),
  action: z.string().min(1, 'Action is required'),
  contextData: z
    .object({
      entityId: z.string().optional(),
      entityType: z.string().optional(),
      sensitivityLevel: z.number().min(0).max(10).optional(),
      isBulkOperation: z.boolean().optional(),
      userRole: z.string().optional(),
      customContext: z.record(z.any()).optional(),
    })
    .optional(),
})

/**
 * POST /api/auth/context-permission
 * Performs context-aware permission checks for authorization
 * This is a placeholder for future Gen AI-driven authorization
 */
export async function POST(request: NextRequest) {
  try {
    // Validate session first
    const session = getSessionFromCookies(request)

    if (!session?.accessToken) {
      return NextResponse.json(
        {
          allowed: false,
          message: 'No valid session',
        },
        { status: 401 }
      )
    }

    const tokenResult = await validateAccessToken(session.accessToken)

    if (!tokenResult?.id) {
      return NextResponse.json(
        {
          allowed: false,
          message: 'Invalid access token',
        },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = contextPermissionSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          allowed: false,
          message: 'Invalid request format',
          errors: validation.error.format(),
        },
        { status: 400 }
      )
    }

    const { userId, resource, action, contextData } = validation.data

    // Security check: User can only check permissions for themselves
    // except for special cases like impersonation
    if (userId !== tokenResult.id && tokenResult.role !== 'SUPERADMIN') {
      return NextResponse.json(
        {
          allowed: false,
          message: 'Cannot check permissions for another user',
        },
        { status: 403 }
      )
    }

    // Check if context-aware features are enabled
    const companyId = tokenResult.companyId
    const contextAwarenessEnabled = companyId
      ? await hasFeature(companyId, 'contextAwareness')
      : false

    if (!contextAwarenessEnabled) {
      // Fall back to basic permission check if context-awareness is not enabled
      const result = await rbacService.checkPermission(userId, resource, action)
      return NextResponse.json({
        allowed: result.allowed,
        message: result.message,
      })
    }

    // Context-aware permission check with the user's context data
    // In the future, this would integrate with Gen AI to analyze the context
    // and make more sophisticated authorization decisions
    const result = await rbacService.checkContextPermission(userId, resource, action, contextData)

    return NextResponse.json({
      allowed: result.allowed,
      message: result.message,
      // Future Gen AI integration could provide explanation for the decision
      explanation: null,
      // Future Gen AI integration could provide confidence score
      confidence: null,
    })
  } catch {
    // console.error('Context permission API error:', error)
    return NextResponse.json(
      {
        allowed: false,
        message: 'Internal server error during permission check',
      },
      { status: 500 }
    )
  }
}
