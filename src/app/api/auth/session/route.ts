import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export const dynamic = 'force-dynamic'

/**
 * GET /api/auth/session
 * Returns the current session information and user data
 */
export async function GET(request: NextRequest) {
  try {
    // Get the token from the request using NextAuth
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (token) {
      return NextResponse.json({
        user: {
          name: token.name,
          email: token.email,
          role: token.role,
          id: token.sub,
        },
      })
    } else {
      // No valid session
      return NextResponse.json({})
    }
  } catch {
    // console.error('Error getting session:', error)
    return NextResponse.json({})
  }
}
