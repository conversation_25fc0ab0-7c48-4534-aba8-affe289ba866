import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { prisma } from '@/lib/prisma'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

/**
 * POST /api/auth/record-activity
 * Records a user activity for context-awareness
 */
export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting - use checkRateLimit instead of rateLimit middleware
    const rateLimitResult = await checkRateLimit(req, 'USER_ACTIVITY')
    if (!rateLimitResult.success) {
      return createRateLimitResponse(rateLimitResult, 'USER_ACTIVITY')
    }

    // Get user from session - add secret parameter
    const token = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET,
    })
    if (!token || !token.sub) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse activity data
    const { activity, details } = await req.json()

    // Validate input
    if (!activity) {
      return NextResponse.json({ error: 'Activity is required' }, { status: 400 })
    }

    // Record user activity with tenant awareness
    await prisma.userActivity.create({
      data: {
        userId: token.sub,
        action: activity, // Required field based on Prisma schema
        resourceType: 'USER_ACTIVITY',
        metadata: {
          details: details || null,
          timestamp: new Date().toISOString(),
          // Include company ID for tenant isolation if available
          ...(token.companyId ? { companyId: token.companyId as string } : {}),
        },
      },
    })

    return NextResponse.json({ success: true })
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('Error recording user activity:', error)
    return NextResponse.json({ error: 'Failed to record activity' }, { status: 500 })
  }
}
