import { NextRequest, NextResponse } from 'next/server'
import { PasswordResetService } from '@/services/password-reset-service'
import { z } from 'zod'

// Request validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

/**
 * POST /api/auth/forgot-password
 * <PERSON><PERSON> forgot password requests
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request body
    const validation = forgotPasswordSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: validation.error.errors[0].message,
        },
        { status: 400 }
      )
    }

    const { email } = validation.data

    // Get client info for security tracking
    const ipAddress =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Process forgot password request
    const result = await PasswordResetService.forgotPassword({
      email,
      ipAddress,
      userAgent,
    })

    // Return success status regardless of whether email exists (security)
    return NextResponse.json(result, {
      status: result.success ? 200 : 400,
    })
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('Error in forgot-password API:', error)

    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while processing your request. Please try again later.',
      },
      { status: 500 }
    )
  }
}

/**
 * OPTIONS request for CORS preflight
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
