import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { isTestEnvironment } from '@/lib/utils'
import { auth } from '@/lib/auth/config'

// Initialize Prisma client
const prisma = new PrismaClient()

/**
 * This route is for testing purposes only.
 * It allows setting a user's session directly for E2E tests.
 */
export async function GET(request: NextRequest) {
  // Ensure this endpoint only works in test environments
  if (!isTestEnvironment()) {
    return new NextResponse('Forbidden: This endpoint is only available in test environments', {
      status: 403,
    })
  }

  const searchParams = request.nextUrl.searchParams
  const userId = searchParams.get('userId')
  const role = searchParams.get('role')

  if (!userId && !role) {
    return new NextResponse('Bad Request: Missing userId or role parameter', {
      status: 400,
    })
  }

  try {
    let user

    // Find user by ID if provided
    if (userId) {
      user = await prisma.user.findUnique({
        where: { id: userId },
      })
    }
    // Or find/create a test user with the specified role
    else if (role) {
      // Find or create a test user with the specified role
      const timestamp = Date.now()
      const email = `test-${role}-${timestamp}@example.com`

      user = await prisma.user.create({
        data: {
          id: uuidv4(),
          email,
          name: `Test ${role.charAt(0).toUpperCase() + role.slice(1)}`,
          role: role as string,
          onboardingCompleted: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      })
    }

    if (!user) {
      return new NextResponse('Not Found: User not found', {
        status: 404,
      })
    }

    // In a real implementation, we would use next-auth's internal APIs to create a session
    // For testing purposes, we'll just return information about the user
    // The actual session creation would happen in tests with proper NextAuth mocking

    // Return the user ID for reference
    return NextResponse.json({
      success: true,
      userId: user.id,
      role: user.role,
      onboardingCompleted: user.onboardingCompleted,
      message: 'For testing only - use NextAuth session mocking in tests to create actual sessions',
    })
  } catch {
    // console.error('Error in test login:', error);
    return new NextResponse('Internal Server Error: Failed to create test session', {
      status: 500,
    })
  }
}
