import { NextRequest, NextResponse } from 'next/server'
import { PasswordResetService } from '@/services/password-reset-service'
import { z } from 'zod'

// Request validation schema
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    ),
})

const validateTokenSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
})

/**
 * POST /api/auth/reset-password
 * Handle password reset with token
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request body
    const validation = resetPasswordSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: validation.error.errors[0].message,
        },
        { status: 400 }
      )
    }

    const { token, newPassword } = validation.data

    // Get client info for security tracking
    const ipAddress =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Process password reset
    const result = await PasswordResetService.resetPassword({
      token,
      newPassword,
      ipAddress,
      userAgent,
    })

    return NextResponse.json(result, {
      status: result.success ? 200 : 400,
    })
  } catch {
    if (process.env.NODE_ENV === 'development') console.error('Error in reset-password API:', error)

    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while resetting your password. Please try again later.',
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/auth/reset-password?token=...
 * Validate reset token without using it
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    // Validate query parameter
    const validation = validateTokenSchema.safeParse({ token })
    if (!validation.success) {
      return NextResponse.json(
        {
          valid: false,
          message: 'Invalid reset token',
        },
        { status: 400 }
      )
    }

    // Validate token
    const result = await PasswordResetService.validateResetToken(validation.data.token)

    return NextResponse.json({
      valid: result.valid,
      email: result.email,
    })
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('Error validating reset token:', error)

    return NextResponse.json(
      {
        valid: false,
        message: 'An error occurred while validating the reset token',
      },
      { status: 500 }
    )
  }
}

/**
 * OPTIONS request for CORS preflight
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
