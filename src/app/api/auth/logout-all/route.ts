import { NextRequest, NextResponse } from 'next/server'
import { getSessionFromCookies, validateAccessToken } from '@/lib/auth/session'
import { logoutAllSessions } from '@/services/session-service'

/**
 * POST /api/auth/logout-all
 * Invalidates all user sessions across all devices and clears current cookies
 */
export async function POST(request: NextRequest) {
  try {
    // Get session from cookies
    const session = getSessionFromCookies(request)

    if (!session || !session.accessToken) {
      // No valid session, just clear cookies
      const response = NextResponse.json({ success: true }, { status: 200 })

      response.cookies.set({
        name: 'access_token',
        value: '',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 0,
        path: '/',
      })

      response.cookies.set({
        name: 'refresh_token',
        value: '',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 0,
        path: '/',
      })

      return response
    }

    // Validate the token to get user ID
    const validationResult = await validateAccessToken(session.accessToken)

    if (!validationResult.valid) {
      // Invalid token, just clear cookies
      const response = NextResponse.json({ success: true }, { status: 200 })

      response.cookies.set({
        name: 'access_token',
        value: '',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 0,
        path: '/',
      })

      response.cookies.set({
        name: 'refresh_token',
        value: '',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 0,
        path: '/',
      })

      return response
    }

    // Invalidate all sessions for the user
    await logoutAllSessions(validationResult.payload.sub, request)

    // Prepare response with cleared cookies
    const response = NextResponse.json({ success: true }, { status: 200 })

    // Clear access token cookie
    response.cookies.set({
      name: 'access_token',
      value: '',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 0,
      path: '/',
    })

    // Clear refresh token cookie
    response.cookies.set({
      name: 'refresh_token',
      value: '',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 0,
      path: '/',
    })

    return response
  } catch {
    // console.error('Logout all API error:', error)

    // Even on error, attempt to clear cookies
    const response = NextResponse.json(
      { success: false, message: 'Error during logout' },
      { status: 500 }
    )

    response.cookies.set({
      name: 'access_token',
      value: '',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 0,
      path: '/',
    })

    response.cookies.set({
      name: 'refresh_token',
      value: '',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 0,
      path: '/',
    })

    return response
  }
}
