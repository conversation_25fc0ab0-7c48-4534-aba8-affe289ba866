/**
 * Behavioral Analytics Summary API Route
 * GET /api/analytics/summary - Get analytics summary for dashboard
 */

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { z } from 'zod'
import { AnalyticsDashboardService } from '@/lib/analytics/dashboard-service'

const summaryQuerySchema = z.object({
  timeRange: z.string().default('30d'),
  companyId: z.string().optional(),
  includeComparisons: z.boolean().default(true),
})

// GET /api/analytics/summary - Get analytics summary
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const queryParams = {
      timeRange: searchParams.get('timeRange') || '30d',
      companyId: searchParams.get('companyId') || undefined,
      includeComparisons: searchParams.get('includeComparisons') === 'true',
    }

    const validatedQuery = summaryQuerySchema.parse(queryParams)

    // Get current period metrics
    const currentMetrics = await AnalyticsDashboardService.getDashboardMetrics({
      timeRange: validatedQuery.timeRange,
      companyId: validatedQuery.companyId,
    })

    // Get comparison metrics if requested
    let comparisonMetrics = null
    if (validatedQuery.includeComparisons) {
      // Calculate previous period timeRange
      const previousTimeRange = getPreviousTimeRange(validatedQuery.timeRange)
      comparisonMetrics = await AnalyticsDashboardService.getDashboardMetrics({
        timeRange: previousTimeRange,
        companyId: validatedQuery.companyId,
      })
    }

    // Calculate growth rates
    const growthRates = comparisonMetrics
      ? {
          totalEvents: calculateGrowthRate(
            currentMetrics.totalEvents,
            comparisonMetrics.totalEvents
          ),
          uniqueUsers: calculateGrowthRate(
            currentMetrics.uniqueUsers,
            comparisonMetrics.uniqueUsers
          ),
          uniqueComponents: calculateGrowthRate(
            currentMetrics.uniqueComponents,
            comparisonMetrics.uniqueComponents
          ),
          engagementRate: calculateGrowthRate(
            currentMetrics.engagementRate,
            comparisonMetrics.engagementRate
          ),
        }
      : null

    // Get additional summary data
    const [usageMetrics, performanceMetrics, engagementMetrics, adoptionMetrics] =
      await Promise.all([
        AnalyticsDashboardService.getUsageMetrics(validatedQuery),
        AnalyticsDashboardService.getPerformanceMetrics(validatedQuery),
        AnalyticsDashboardService.getEngagementMetrics(validatedQuery),
        AnalyticsDashboardService.getAdoptionMetrics(validatedQuery),
      ])

    return NextResponse.json({
      summary: {
        current: currentMetrics,
        previous: comparisonMetrics,
        growthRates,
      },
      metrics: {
        usage: usageMetrics,
        performance: performanceMetrics,
        engagement: engagementMetrics,
        adoption: adoptionMetrics,
      },
      insights: generateInsights(currentMetrics, comparisonMetrics),
      timestamp: new Date().toISOString(),
    })
  } catch {
    // console.error('Analytics summary API error:', error);
    return NextResponse.json({ error: 'Failed to fetch analytics summary' }, { status: 500 })
  }
}

function getPreviousTimeRange(timeRange: string): string {
  // Map current time range to previous period
  switch (timeRange) {
    case '24h':
      return '24h' // Previous 24 hours
    case '7d':
      return '7d' // Previous 7 days
    case '30d':
      return '30d' // Previous 30 days
    case '90d':
      return '90d' // Previous 90 days
    default:
      return '30d'
  }
}

function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}

function generateInsights(current: unknown, previous: unknown) {
  const insights = []

  // Growth insights
  if (previous) {
    const userGrowth = calculateGrowthRate(current.uniqueUsers, previous.uniqueUsers)
    if (userGrowth > 10) {
      insights.push({
        type: 'positive',
        title: 'Strong User Growth',
        description: `User base grew by ${userGrowth.toFixed(1)}% compared to the previous period.`,
      })
    } else if (userGrowth < -5) {
      insights.push({
        type: 'warning',
        title: 'User Decline',
        description: `User base decreased by ${Math.abs(userGrowth).toFixed(1)}% compared to the previous period.`,
      })
    }
  }

  // Engagement insights
  if (current.engagementRate > 5) {
    insights.push({
      type: 'positive',
      title: 'High Engagement',
      description: `Users are highly engaged with an average of ${current.engagementRate.toFixed(1)} events per user.`,
    })
  } else if (current.engagementRate < 2) {
    insights.push({
      type: 'warning',
      title: 'Low Engagement',
      description: `User engagement is low with only ${current.engagementRate.toFixed(1)} events per user on average.`,
    })
  }

  // Component usage insights
  if (current.topComponents.length > 0) {
    const topComponent = current.topComponents[0]
    insights.push({
      type: 'info',
      title: 'Most Popular Component',
      description: `Component "${topComponent.componentId}" is the most used with ${topComponent.usageCount} interactions.`,
    })
  }

  return insights
}
