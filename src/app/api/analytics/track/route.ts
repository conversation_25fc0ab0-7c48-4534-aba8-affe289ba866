/**
 * Behavioral Analytics Tracking API Route
 * POST /api/analytics/track - Track user behavior events
 */

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { z } from 'zod'
import { AnalyticsDashboardService } from '@/lib/analytics/dashboard-service'
import { AnalyticsController } from '@/controllers/analytics-controller'

const trackingEventSchema = z.object({
  eventType: z.string().min(1),
  componentId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  timestamp: z.string().optional(),
})

const batchTrackingSchema = z.object({
  events: z.array(trackingEventSchema),
})

// POST /api/analytics/track - Track single or batch events
export async function POST(request: NextRequest) {
  return AnalyticsController.trackEvent(request)
}

// GET /api/analytics/track - Get tracking status and configuration
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Return tracking configuration and status
    return NextResponse.json({
      trackingEnabled: true,
      supportedEvents: [
        'page_view',
        'component_interaction',
        'user_action',
        'performance_metric',
        'error_event',
        'session_start',
        'session_end',
        'feature_usage',
      ],
      batchingSupported: true,
      maxBatchSize: 100,
      timestamp: new Date().toISOString(),
    })
  } catch {
    // console.error('Analytics tracking config API error:', error);
    return NextResponse.json({ error: 'Failed to get tracking configuration' }, { status: 500 })
  }
}
