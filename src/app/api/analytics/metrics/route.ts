// Phase 4.1: Analytics Metrics API

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { z } from 'zod'
import { AnalyticsDashboardService } from '@/lib/analytics/dashboard-service'

const metricsQuerySchema = z.object({
  metricType: z.enum(['usage', 'performance', 'engagement', 'adoption']),
  timeRange: z.string().default('7d'),
  granularity: z.enum(['hour', 'day', 'week', 'month']).default('day'),
  companyId: z.string().optional(),
  userId: z.string().optional(),
  componentId: z.string().optional(),
})

// GET /api/analytics/metrics - Get specific metrics data
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const queryParams = {
      metricType: searchParams.get('metricType'),
      timeRange: searchParams.get('timeRange') || '7d',
      granularity: searchParams.get('granularity') || 'day',
      companyId: searchParams.get('companyId') || undefined,
      userId: searchParams.get('userId') || undefined,
      componentId: searchParams.get('componentId') || undefined,
    }

    // Validate query parameters
    const validatedQuery = metricsQuerySchema.parse(queryParams)

    // Get metrics data based on type
    let metricsData
    switch (validatedQuery.metricType) {
      case 'usage':
        metricsData = await AnalyticsDashboardService.getUsageMetrics(validatedQuery)
        break
      case 'performance':
        metricsData = await AnalyticsDashboardService.getPerformanceMetrics(validatedQuery)
        break
      case 'engagement':
        metricsData = await AnalyticsDashboardService.getEngagementMetrics(validatedQuery)
        break
      case 'adoption':
        metricsData = await AnalyticsDashboardService.getAdoptionMetrics(validatedQuery)
        break
      default:
        return NextResponse.json({ error: 'Invalid metric type' }, { status: 400 })
    }

    return NextResponse.json({
      metricType: validatedQuery.metricType,
      timeRange: validatedQuery.timeRange,
      granularity: validatedQuery.granularity,
      data: metricsData,
      timestamp: new Date().toISOString(),
    })
  } catch {
    // console.error('Analytics metrics API error:', error);
    return NextResponse.json({ error: 'Failed to fetch metrics data' }, { status: 500 })
  }
}

// POST /api/analytics/metrics - Track custom metrics
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    const trackingSchema = z.object({
      eventType: z.string(),
      componentId: z.string().optional(),
      userId: z.string().optional(),
      metadata: z.record(z.any()).optional(),
      timestamp: z.string().optional(),
    })

    const validatedData = trackingSchema.parse(body)

    // Track the custom metric
    const result = await AnalyticsDashboardService.trackCustomMetric({
      ...validatedData,
      userId: validatedData.userId || session.user.id,
      timestamp: validatedData.timestamp || new Date().toISOString(),
    })

    return NextResponse.json({
      success: true,
      eventId: result.id,
      timestamp: new Date().toISOString(),
    })
  } catch {
    // console.error('Analytics tracking API error:', error);
    return NextResponse.json({ error: 'Failed to track metric' }, { status: 500 })
  }
}
