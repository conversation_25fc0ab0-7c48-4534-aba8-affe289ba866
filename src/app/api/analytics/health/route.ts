import { NextRequest, NextResponse } from 'next/server'

/**
 * Analytics Health Check - GET /api/analytics/health
 * Simple health check that doesn't require database connectivity
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const startTime = Date.now()

    // Basic health check without database dependency
    const responseTime = Date.now() - startTime

    return NextResponse.json({
      status: 'healthy',
      service: 'analytics',
      responseTime,
      target: '25ms',
      withinTarget: responseTime <= 25,
      timestamp: new Date().toISOString(),
      note: 'Basic health check - database connectivity will be added when analytics DB is configured',
    })
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('[ANALYTICS_HEALTH] Health check error:', error)

    return NextResponse.json(
      {
        status: 'unhealthy',
        service: 'analytics',
        error: 'Health check failed',
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    )
  }
}
