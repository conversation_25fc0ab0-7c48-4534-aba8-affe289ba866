/**
 * Enhanced Analytics Session Management API Route
 * POST /api/analytics/enhanced/session - Start or update a user session
 * PUT /api/analytics/enhanced/session - End a user session
 */

import { NextRequest } from 'next/server'
import { EnhancedAnalyticsController } from '@/controllers/enhanced-analytics-controller'

// POST /api/analytics/enhanced/session - Start or update a user session
export async function POST(request: NextRequest) {
  return EnhancedAnalyticsController.updateSession(request)
}

// PUT /api/analytics/enhanced/session - End a user session
export async function PUT(request: NextRequest) {
  return EnhancedAnalyticsController.endSession(request)
}
