/**
 * Enhanced Analytics Tracking API Route
 * POST /api/analytics/enhanced/track - Track user behavior events with high performance
 */

import { NextRequest } from 'next/server'
import { EnhancedAnalyticsController } from '@/controllers/enhanced-analytics-controller'

// POST /api/analytics/enhanced/track - Track single event
export async function POST(request: NextRequest) {
  return EnhancedAnalyticsController.trackEvent(request)
}

// GET /api/analytics/enhanced/track - Get tracking status and configuration
export async function GET(request: NextRequest) {
  return EnhancedAnalyticsController.healthCheck(request)
}
