/**
 * Enhanced Analytics Batch Tracking API Route
 * POST /api/analytics/enhanced/track-batch - Track multiple user behavior events in batch
 */

import { NextRequest } from 'next/server'
import { EnhancedAnalyticsController } from '@/controllers/enhanced-analytics-controller'

// POST /api/analytics/enhanced/track-batch - Track multiple events in batch
export async function POST(request: NextRequest) {
  return EnhancedAnalyticsController.trackEventsBatch(request)
}
