/**
 * Enhanced Analytics Aggregates API Route
 * POST /api/analytics/enhanced/aggregates - Generate daily aggregates for reporting
 */

import { NextRequest } from 'next/server'
import { EnhancedAnalyticsController } from '@/controllers/enhanced-analytics-controller'

// POST /api/analytics/enhanced/aggregates - Generate aggregates
export async function POST(request: NextRequest) {
  return EnhancedAnalyticsController.generateAggregates(request)
}
