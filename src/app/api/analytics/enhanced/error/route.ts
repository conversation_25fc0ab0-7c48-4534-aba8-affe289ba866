/**
 * Enhanced Analytics Error Tracking API Route
 * POST /api/analytics/enhanced/error - Track application errors
 */

import { NextRequest } from 'next/server'
import { EnhancedAnalyticsController } from '@/controllers/enhanced-analytics-controller'

// POST /api/analytics/enhanced/error - Track error events
export async function POST(request: NextRequest) {
  return EnhancedAnalyticsController.trackError(request)
}
