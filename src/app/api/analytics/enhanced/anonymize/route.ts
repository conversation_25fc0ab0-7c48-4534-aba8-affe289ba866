/**
 * Enhanced Analytics User Anonymization API Route
 * POST /api/analytics/enhanced/anonymize - Anonymize user data for GDPR compliance
 */

import { NextRequest } from 'next/server'
import { EnhancedAnalyticsController } from '@/controllers/enhanced-analytics-controller'

// POST /api/analytics/enhanced/anonymize - Anonymize user data
export async function POST(request: NextRequest) {
  return EnhancedAnalyticsController.anonymizeUserData(request)
}
