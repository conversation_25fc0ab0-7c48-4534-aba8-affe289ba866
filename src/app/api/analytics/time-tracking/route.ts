import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { NavigationAnalyticsService } from '@/services/navigation-analytics-service'
import { z } from 'zod'

const timeTrackingSchema = z.object({
  action: z.literal('section_time'),
  section: z.string().min(1),
  duration: z.number().min(0),
  sessionId: z.string(),
  deviceType: z.enum(['desktop', 'mobile', 'tablet']),
  metadata: z.record(z.any()).optional(),
})

const analyticsService = new NavigationAnalyticsService(prisma)

export async function POST(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedEvent = timeTrackingSchema.parse(body)

    // Add userId and timestamp
    const timeTrackingEvent = {
      ...validatedEvent,
      userId: session.user.id,
      timestamp: Date.now(),
    }

    // Track the time event
    await analyticsService.trackNavigationEvent(timeTrackingEvent)

    return NextResponse.json({ success: true })
  } catch {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') console.error('Time tracking error:', error)
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json({ error: 'Failed to track time' }, { status: 500 })
  }
}
