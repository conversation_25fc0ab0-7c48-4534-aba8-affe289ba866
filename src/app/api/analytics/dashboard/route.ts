// Phase 4.1: Advanced Analytics Dashboard API

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { z } from 'zod'
import { AnalyticsDashboardService } from '@/lib/analytics/dashboard-service'
import { DashboardFiltersSchema } from '@/types/analytics'

// GET /api/analytics/dashboard - Get dashboard metrics
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const filters = {
      timeRange: searchParams.get('timeRange') || '7d',
      companyId: searchParams.get('companyId') || undefined,
      userId: searchParams.get('userId') || undefined,
    }

    // Validate filters
    const validatedFilters = DashboardFiltersSchema.parse(filters)

    // Get dashboard metrics
    const metrics = await AnalyticsDashboardService.getDashboardMetrics(validatedFilters)

    return NextResponse.json(metrics)
  } catch {
    // console.error('Analytics dashboard API error:', error);
    return NextResponse.json({ error: 'Failed to fetch dashboard metrics' }, { status: 500 })
  }
}

// POST /api/analytics/dashboard - Update dashboard configuration
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    // Validate request body
    const configSchema = z.object({
      widgets: z.array(
        z.object({
          id: z.string(),
          type: z.string(),
          position: z.object({
            x: z.number(),
            y: z.number(),
            width: z.number(),
            height: z.number(),
          }),
          config: z.record(z.any()),
        })
      ),
      layout: z.string().optional(),
      theme: z.string().optional(),
    })

    const validatedConfig = configSchema.parse(body)

    // Save dashboard configuration
    const result = await AnalyticsDashboardService.saveDashboardConfig(
      session.user.id,
      validatedConfig
    )

    return NextResponse.json(result)
  } catch {
    // console.error('Analytics dashboard config API error:', error);
    return NextResponse.json({ error: 'Failed to save dashboard configuration' }, { status: 500 })
  }
}
