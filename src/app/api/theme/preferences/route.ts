/**
 * 🎨 Theme Preferences API Route
 *
 * Proper client-server implementation using Controller-Service pattern.
 * Replaces Server Actions with RESTful API endpoints.
 */

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { AIThemeController } from '@/controllers/ai-theme-controller'
import { AIThemeService } from '@/services/ai-theme-service'
import { prisma } from '@/lib/prisma'
import { redis } from '@/lib/redis'

// Initialize services and controller
const themeService = new AIThemeService(prisma, redis)
const themeController = new AIThemeController(themeService)

/**
 * GET /api/theme/preferences
 * Retrieve user theme preferences with AI context
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()

    // Convert NextRequest to controller format
    const req = {
      user: session?.user,
      headers: Object.fromEntries(request.headers),
      method: 'GET',
      url: request.url,
    }

    // Mock response object with methods
    let statusCode = 200
    let responseData: any = null

    const res = {
      status: (code: number) => {
        statusCode = code
        return res
      },
      json: (data: any) => {
        responseData = data
        return res
      },
    }

    // Call controller method
    await themeController.getThemePreferences(req as any, res as any)

    // Return NextResponse
    return NextResponse.json(responseData, { status: statusCode })
  } catch {
    if (process.env.NODE_ENV === 'development') console.error('Theme preferences API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve theme preferences',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/theme/preferences
 * Update user theme preferences with validation
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    const body = await request.json()

    // Convert NextRequest to controller format
    const req = {
      user: session?.user,
      headers: Object.fromEntries(request.headers),
      method: 'PUT',
      url: request.url,
      json: async () => body,
      body,
    }

    // Mock response object with methods
    let statusCode = 200
    let responseData: any = null

    const res = {
      status: (code: number) => {
        statusCode = code
        return res
      },
      json: (data: any) => {
        responseData = data
        return res
      },
    }

    // Call controller method
    await themeController.updateThemePreferences(req as any, res as any)

    // Return NextResponse
    return NextResponse.json(responseData, { status: statusCode })
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('Theme preferences update API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update theme preferences',
        },
      },
      { status: 500 }
    )
  }
}
