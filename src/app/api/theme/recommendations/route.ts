/**
 * 🤖 Theme AI Recommendations API Route
 *
 * AI-powered theme recommendations endpoint using Controller-Service pattern.
 */

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { AIThemeController } from '@/controllers/ai-theme-controller'
import { AIThemeService } from '@/services/ai-theme-service'
import { prisma } from '@/lib/prisma'
import { redis } from '@/lib/redis'

// Initialize services and controller
const themeService = new AIThemeService(prisma, redis)
const themeController = new AIThemeController(themeService)

/**
 * GET /api/theme/recommendations
 * Get personalized AI theme recommendations
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()

    // Convert NextRequest to controller format
    const req = {
      user: session?.user,
      headers: Object.fromEntries(request.headers),
      method: 'GET',
      url: request.url,
      query: Object.fromEntries(new URL(request.url).searchParams),
    }

    // Mock response object with methods
    let statusCode = 200
    let responseData: any = null

    const res = {
      status: (code: number) => {
        statusCode = code
        return res
      },
      json: (data: any) => {
        responseData = data
        return res
      },
    }

    // Call controller method
    await themeController.getAIRecommendations(req as any, res as any)

    // Return NextResponse
    return NextResponse.json(responseData, { status: statusCode })
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('Theme recommendations API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve AI recommendations',
        },
      },
      { status: 500 }
    )
  }
}
