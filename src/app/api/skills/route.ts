import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for creating skills
const createSkillSchema = z.object({
  name: z.string().min(1, 'Skill name is required').max(100, 'Skill name too long'),
  description: z.string().optional(),
  category: z.enum([
    'TECHNICAL',
    'SOFT_SKILLS',
    'LEADERSHIP',
    'COMMUNICATION',
    'ANALYTICAL',
    'CREATIVE',
    'DOMAIN_SPECIFIC',
  ]),
  currentLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),
  targetLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  importance: z.number().min(1).max(5).default(3),
})

// GET /api/skills - Get user's skills
export async function GET(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const skills = await prisma.skill.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        assessments: {
          orderBy: {
            assessmentDate: 'desc',
          },
          take: 1, // Get latest assessment only
        },
      },
      orderBy: [{ importance: 'desc' }, { name: 'asc' }],
    })

    // Calculate statistics for the response
    const skillsByCategory = skills.reduce(
      (acc, skill) => {
        acc[skill.category] = (acc[skill.category] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const skillsByLevel = skills.reduce(
      (acc, skill) => {
        acc[skill.currentLevel] = (acc[skill.currentLevel] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    return NextResponse.json({
      skills,
      totalSkills: skills.length,
      skillsByCategory,
      skillsByLevel,
    })
  } catch (error) {
    console.error('Error fetching skills:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/skills - Create a new skill
export async function POST(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id || !session?.user?.companyId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    // Validate request body
    const validationResult = createSkillSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors,
        },
        { status: 400 }
      )
    }

    const skillData = validationResult.data

    // Check if skill with same name already exists for this user
    const existingSkill = await prisma.skill.findFirst({
      where: {
        userId: session.user.id,
        name: {
          equals: skillData.name,
          mode: 'insensitive',
        },
      },
    })

    if (existingSkill) {
      return NextResponse.json({ error: 'A skill with this name already exists' }, { status: 409 })
    }

    // Create the skill
    const skill = await prisma.skill.create({
      data: {
        ...skillData,
        userId: session.user.id,
        companyId: session.user.companyId,
        tags: [], // Initialize empty tags array
      },
      include: {
        assessments: {
          orderBy: {
            assessmentDate: 'desc',
          },
          take: 1,
        },
      },
    })

    return NextResponse.json(skill, { status: 201 })
  } catch (error) {
    console.error('Error creating skill:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
