import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { GoalsController } from '@/controllers/goals-controller'

export async function GET(request: NextRequest) {
  const session = await auth()

  if (!session?.user) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
  }

  return GoalsController.getUserGoals(request, {
    user: session.user,
    company: { id: session.user.companyId },
  })
}

export async function POST(request: NextRequest) {
  const session = await auth()

  if (!session?.user) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
  }

  return GoalsController.createGoal(request, {
    user: session.user,
    company: { id: session.user.companyId },
  })
}
