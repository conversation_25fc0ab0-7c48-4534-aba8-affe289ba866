import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { GoalsController } from '@/controllers/goals-controller'
import { ZodError } from 'zod'

const goalsController = new GoalsController()

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const result = await goalsController.createKeyResult({
      goalId: params.id,
      userId: session.user.id,
      companyId: session.user.companyId,
      keyResultData: body,
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error(`POST /api/goals/${params.id}/key-results error:`, error)
    if (error instanceof ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
