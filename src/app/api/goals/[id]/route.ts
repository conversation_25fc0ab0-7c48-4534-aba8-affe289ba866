import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { GoalsController } from '@/controllers/goals-controller'
import { ZodError } from 'zod'

const goalsController = new GoalsController()

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await goalsController.getGoalById({
      goalId: params.id,
      userId: session.user.id,
      companyId: session.user.companyId,
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error(`GET /api/goals/${params.id} error:`, error)
    if (error instanceof ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const result = await goalsController.updateGoal({
      goalId: params.id,
      userId: session.user.id,
      companyId: session.user.companyId,
      updates: body,
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error(`PATCH /api/goals/${params.id} error:`, error)
    if (error instanceof ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await goalsController.deleteGoal({
      goalId: params.id,
      userId: session.user.id,
      companyId: session.user.companyId,
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error(`DELETE /api/goals/${params.id} error:`, error)
    if (error instanceof ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
