/**
 * API route for insights
 * Provides insights data from FocusAnalyticsService
 * Part of Task 5.5: Develop Insights with AI-powered Suggestions
 */

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { FocusAnalyticsService } from '@/lib/services/focus-analytics-service'

/**
 * GET /api/intelligence/insights
 * Returns insights data for the current user
 */
export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session = await auth()
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Extract user data
    const userId = session.user.id
    const companyId = session.user.companyId

    if (!userId || !companyId) {
      return NextResponse.json({ error: 'User or company ID missing' }, { status: 400 })
    }

    // Get insights data from service
    const analyticsService = new FocusAnalyticsService()
    const insightsData = await analyticsService.getAnalyticsData(userId, companyId)

    return NextResponse.json(insightsData)
  } catch (error: any) {
    console.error('Error fetching insights data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch insights data', message: error.message },
      { status: 500 }
    )
  }
}

/**
 * POST /api/intelligence/insights/feedback
 * Stores user feedback on insights
 */
export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await auth()
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Extract user data
    const userId = session.user.id
    const companyId = session.user.companyId

    if (!userId || !companyId) {
      return NextResponse.json({ error: 'User or company ID missing' }, { status: 400 })
    }

    // Parse request body
    const body = await request.json()
    const { feedback, insightType } = body

    if (!feedback || !['helpful', 'not_helpful'].includes(feedback)) {
      return NextResponse.json({ error: 'Invalid feedback value' }, { status: 400 })
    }

    // Store feedback
    const analyticsService = new FocusAnalyticsService()
    const success = await analyticsService.storeInsightFeedback(
      userId,
      companyId,
      feedback as 'helpful' | 'not_helpful',
      insightType
    )

    if (!success) {
      return NextResponse.json({ error: 'Failed to store feedback' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error('Error storing insight feedback:', error)
    return NextResponse.json(
      { error: 'Failed to store feedback', message: error.message },
      { status: 500 }
    )
  }
}
