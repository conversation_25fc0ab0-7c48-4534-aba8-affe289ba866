import { auth } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { redis } from '@/lib/redis'
import { AIThemeService } from '@/services/ai-theme-service'
import { NextRequest, NextResponse } from 'next/server'

// Initialize AI Theme Service for integration
const aiThemeService = new AIThemeService(prisma, redis)

// Performance optimization constants
const CACHE_TTL = 300 // 5 minutes
const PERFORMANCE_TARGET_MS = 50
const CACHE_TARGET_MS = 25

// Enhanced error handling with retry mechanism
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 10
): Promise<T> {
  let attempts = 0

  while (attempts < maxRetries) {
    try {
      attempts++
      return await operation()
    } catch {
      if (attempts >= maxRetries) throw error

      const delay = baseDelay * Math.pow(2, attempts - 1)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw new Error('Max retries exceeded')
}

/**
 * GET /api/user/preferences
 * Optimized endpoint with 25-50ms response time target
 */
export async function GET(req: NextRequest) {
  const startTime = performance.now()

  try {
    if (process.env.NODE_ENV === 'development')
      console.log('[USER_PREFERENCES_GET] Optimized request started')

    // Enhanced session validation with detailed logging
    const session = await auth()

    if (!session?.user?.email || !session?.user?.companyId) {
      const logData = {
        hasSession: !!session,
        hasUser: !!session?.user,
        hasEmail: !!session?.user?.email,
        hasCompanyId: !!session?.user?.companyId,
        timestamp: new Date().toISOString(),
      }
      if (process.env.NODE_ENV === 'development')
        console.log('[USER_PREFERENCES_GET] Authentication failed:', JSON.stringify(logData))

      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized: Please sign in to view preferences',
          code: 'AUTHENTICATION_REQUIRED',
        },
        { status: 401 }
      )
    }

    // Optimized cache key strategy
    const cacheKey = `user_preferences:${session.user.email}:${session.user.companyId}`

    // Fast cache check with performance tracking
    const cacheStartTime = performance.now()
    const cachedData = await redis.get(cacheKey)
    const cacheTime = performance.now() - cacheStartTime

    if (cachedData) {
      const responseTime = performance.now() - startTime
      if (process.env.NODE_ENV === 'development')
        console.log(
          `[USER_PREFERENCES_GET] Cache hit - Response time: ${responseTime.toFixed(2)}ms`,
          {
            target: `<${CACHE_TARGET_MS}ms`,
            actual: `${responseTime.toFixed(2)}ms`,
            withinTarget: responseTime <= CACHE_TARGET_MS,
            cacheHit: true,
            cacheTime: `${cacheTime.toFixed(2)}ms`,
            email: session.user.email,
            companyId: session.user.companyId,
          }
        )

      const parsedData = JSON.parse(cachedData)

      return NextResponse.json({
        ...parsedData,
        metadata: {
          ...parsedData.metadata,
          responseTime: Math.round(responseTime * 100) / 100,
          cached: true,
          cacheTime: Math.round(cacheTime * 100) / 100,
        },
      })
    }

    // Optimized single database query with retry mechanism
    const dbStartTime = performance.now()

    const user = await withRetry(async () => {
      return await prisma.user.findFirst({
        where: {
          email: session.user.email,
          companyId: session.user.companyId,
        },
        select: {
          id: true,
          email: true,
          themeMode: true,
          colorScheme: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              subscriptionStatus: true,
            },
          },
        },
      })
    })

    const dbTime = performance.now() - dbStartTime

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found. Please contact support if this issue persists.',
          code: 'USER_NOT_FOUND',
        },
        { status: 404 }
      )
    }

    if (user.company.subscriptionStatus === 'EXPIRED') {
      return NextResponse.json(
        {
          success: false,
          error:
            'Company subscription has expired. <NAME_EMAIL> for assistance.',
          code: 'SUBSCRIPTION_EXPIRED',
        },
        { status: 403 }
      )
    }

    // Enhanced response with AI theme integration
    const responseTime = performance.now() - startTime
    const response = {
      success: true,
      user: {
        email: user.email,
        preferences: {
          themeMode: user.themeMode,
          colorScheme: user.colorScheme,
        },
      },
      company: {
        id: user.company.id,
        name: user.company.name,
      },
      metadata: {
        responseTime: Math.round(responseTime * 100) / 100,
        cached: false,
        dbTime: Math.round(dbTime * 100) / 100,
        withinTarget: responseTime <= PERFORMANCE_TARGET_MS,
        version: '1.0.0',
        aiThemeIntegration: true,
      },
    }

    // Optimized cache warming with performance tracking
    const cacheWarmingStartTime = performance.now()
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(response))
    const cacheWarmingTime = performance.now() - cacheWarmingStartTime

    // Performance logging with detailed metrics
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.log(
          `[USER_PREFERENCES_GET] Success - Response time: ${responseTime.toFixed(2)}ms`,
          {
            target: `<${PERFORMANCE_TARGET_MS}ms`,
            actual: `${responseTime.toFixed(2)}ms`,
            withinTarget: responseTime <= PERFORMANCE_TARGET_MS,
            cacheHit: false,
            dbTime: `${dbTime.toFixed(2)}ms`,
            cacheWarmingTime: `${cacheWarmingTime.toFixed(2)}ms`,
            email: user.email,
            companyId: user.companyId,
          }
        )

    return NextResponse.json(response)
  } catch {
    const responseTime = performance.now() - startTime
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('[USER_PREFERENCES_GET] Error occurred:', {
          error: error instanceof Error ? error.message : 'Unknown error',
          responseTime: `${responseTime.toFixed(2)}ms`,
          timestamp: new Date().toISOString(),
        })

    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error: Unable to fetch preferences. Please try again later.',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/user/preferences
 * Optimized endpoint with AI theme integration
 */
export async function PUT(req: NextRequest) {
  const startTime = performance.now()

  try {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.log('[USER_PREFERENCES_PUT] Optimized request started')

    // Enhanced session validation
    const session = await auth()

    if (!session?.user?.email || !session?.user?.companyId) {
      const logData = {
        hasSession: !!session,
        hasUser: !!session?.user,
        hasEmail: !!session?.user?.email,
        hasCompanyId: !!session?.user?.companyId,
        timestamp: new Date().toISOString(),
      }
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log('[USER_PREFERENCES_PUT] Authentication failed:', JSON.stringify(logData))

      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized: Please sign in to update preferences',
          code: 'AUTHENTICATION_REQUIRED',
        },
        { status: 401 }
      )
    }

    // Optimized user lookup with retry mechanism
    const user = await withRetry(async () => {
      return await prisma.user.findFirst({
        where: {
          email: session.user.email,
          companyId: session.user.companyId,
        },
        select: {
          id: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              subscriptionStatus: true,
            },
          },
        },
      })
    })

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found. Please contact support if this issue persists.',
          code: 'USER_NOT_FOUND',
        },
        { status: 404 }
      )
    }

    if (user.company.subscriptionStatus === 'EXPIRED') {
      return NextResponse.json(
        {
          success: false,
          error:
            'Company subscription has expired. <NAME_EMAIL> for assistance.',
          code: 'SUBSCRIPTION_EXPIRED',
        },
        { status: 403 }
      )
    }

    const body = await req.json()
    const { themeMode, colorScheme } = body

    // Enhanced validation with detailed error messages
    if (!themeMode || !['light', 'dark', 'system'].includes(themeMode)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid theme mode. Must be one of: light, dark, system',
          code: 'INVALID_THEME_MODE',
          validOptions: ['light', 'dark', 'system'],
        },
        { status: 400 }
      )
    }

    if (
      !colorScheme ||
      !['emynent-light', 'emynent-dark', 'blue', 'green', 'purple', 'orange'].includes(colorScheme)
    ) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Invalid color scheme. Must be one of: emynent-light, emynent-dark, blue, green, purple, orange',
          code: 'INVALID_COLOR_SCHEME',
          validOptions: ['emynent-light', 'emynent-dark', 'blue', 'green', 'purple', 'orange'],
        },
        { status: 400 }
      )
    }

    // Optimized database update with retry mechanism
    const dbStartTime = performance.now()

    const updatedUser = await withRetry(async () => {
      return await prisma.user.update({
        where: {
          id: user.id,
        },
        data: {
          themeMode,
          colorScheme,
        },
        select: {
          id: true,
          themeMode: true,
          colorScheme: true,
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      })
    })

    const dbTime = performance.now() - dbStartTime

    // Optimized cache invalidation
    const cacheKey = `user_preferences:${session.user.email}:${session.user.companyId}`
    const cacheInvalidationStartTime = performance.now()
    await redis.del(cacheKey)
    const cacheInvalidationTime = performance.now() - cacheInvalidationStartTime

    // AI Theme Service integration for enhanced functionality
    try {
      await aiThemeService.updateThemePreferences(user.id, {
        mode: themeMode,
        colorScheme: colorScheme,
      })
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log('[USER_PREFERENCES_PUT] AI theme service updated successfully')
    } catch (aiError) {
      // AI integration failure shouldn't break the core functionality
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.warn('[USER_PREFERENCES_PUT] AI theme service update failed:', aiError)
    }

    const responseTime = performance.now() - startTime
    const response = {
      success: true,
      message: 'Preferences updated successfully',
      user: {
        preferences: {
          themeMode: updatedUser.themeMode,
          colorScheme: updatedUser.colorScheme,
        },
      },
      company: {
        id: updatedUser.company.id,
        name: updatedUser.company.name,
      },
      metadata: {
        responseTime: Math.round(responseTime * 100) / 100,
        dbTime: Math.round(dbTime * 100) / 100,
        cacheInvalidationTime: Math.round(cacheInvalidationTime * 100) / 100,
        withinTarget: responseTime <= PERFORMANCE_TARGET_MS,
        aiThemeIntegration: true,
      },
    }

    // Performance logging
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.log(
          `[USER_PREFERENCES_PUT] Success - Response time: ${responseTime.toFixed(2)}ms`,
          {
            target: `<${PERFORMANCE_TARGET_MS}ms`,
            actual: `${responseTime.toFixed(2)}ms`,
            withinTarget: responseTime <= PERFORMANCE_TARGET_MS,
            email: session.user.email,
            companyId: session.user.companyId,
            updatedTheme: updatedUser.themeMode,
            updatedColor: updatedUser.colorScheme,
            dbTime: `${dbTime.toFixed(2)}ms`,
            cacheInvalidationTime: `${cacheInvalidationTime.toFixed(2)}ms`,
          }
        )

    return NextResponse.json(response)
  } catch {
    const responseTime = performance.now() - startTime
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('[USER_PREFERENCES_PUT] Error occurred:', {
          error: error instanceof Error ? error.message : 'Unknown error',
          responseTime: `${responseTime.toFixed(2)}ms`,
          timestamp: new Date().toISOString(),
        })

    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error: Unable to update preferences. Please try again later.',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    )
  }
}
