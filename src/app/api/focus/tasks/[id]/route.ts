import { FocusTaskController } from '@/controllers/focus-task-controller'

export async function GET(request: Request, { params }: { params: { id: string } }) {
  return FocusTaskController.getTask(request as any, { params })
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  return FocusTaskController.updateTask(request as any, { params })
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  return FocusTaskController.deleteTask(request as any, { params })
}
