'use client'

import { useSession } from 'next-auth/react'
import { Button } from '@/lib/design-system/components'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/core/card'
import { BarChart, Users, Target, ArrowUpRight } from 'lucide-react'

export default function DashboardPage() {
  const { data: session } = useSession()
  const userTeam = (session?.user as { team?: string })?.team || 'General'

  // Helper function to get team-specific content
  const getTeamSpecificContent = () => {
    switch (userTeam) {
      case 'Engineering':
        return {
          title: 'Engineering Dashboard',
          metrics: {
            primary: { label: 'Code Commits', value: '287', change: '+12.3%' },
            secondary: { label: 'Pull Requests', value: '42', change: '+8.5%' },
            tertiary: { label: 'Build Success Rate', value: '98.2%', change: '+1.2%' },
          },
        }
      case 'Product':
        return {
          title: 'Product Dashboard',
          metrics: {
            primary: { label: 'Active Features', value: '24', change: '+3' },
            secondary: { label: 'User Feedback', value: '156', change: '+27.8%' },
            tertiary: { label: 'Sprint Completion', value: '92.5%', change: '+5.2%' },
          },
        }
      case 'Design':
        return {
          title: 'Design Dashboard',
          metrics: {
            primary: { label: 'Design Systems', value: '3', change: '+1' },
            secondary: { label: 'Prototypes', value: '18', change: '+50%' },
            tertiary: { label: 'User Testing Sessions', value: '12', change: '+20%' },
          },
        }
      case 'Marketing':
        return {
          title: 'Marketing Dashboard',
          metrics: {
            primary: { label: 'Campaign ROI', value: '342%', change: '+15.4%' },
            secondary: { label: 'New Leads', value: '1,284', change: '+32.7%' },
            tertiary: { label: 'Content Views', value: '48.2K', change: '+22.1%' },
          },
        }
      case 'Sales':
        return {
          title: 'Sales Dashboard',
          metrics: {
            primary: { label: 'Revenue', value: '$452K', change: '+20.1%' },
            secondary: { label: 'New Customers', value: '86', change: '+12.4%' },
            tertiary: { label: 'Conversion Rate', value: '12.5%', change: '+1.8%' },
          },
        }
      case 'Customer Success':
        return {
          title: 'Customer Success Dashboard',
          metrics: {
            primary: { label: 'Customer Satisfaction', value: '93%', change: '+2.5%' },
            secondary: { label: 'Support Tickets', value: '128', change: '-18.2%' },
            tertiary: { label: 'Response Time', value: '1.8h', change: '-15.6%' },
          },
        }
      case 'Operations':
        return {
          title: 'Operations Dashboard',
          metrics: {
            primary: { label: 'Process Efficiency', value: '87%', change: '+5.3%' },
            secondary: { label: 'Resource Utilization', value: '92%', change: '+3.7%' },
            tertiary: { label: 'Cost Reduction', value: '12.8%', change: '+4.2%' },
          },
        }
      default:
        return {
          title: 'Dashboard',
          metrics: {
            primary: { label: 'Total Revenue', value: '$45,231.89', change: '+20.1%' },
            secondary: { label: 'Active Users', value: '+2350', change: '+180.1%' },
            tertiary: { label: 'Conversion Rate', value: '+12.5%', change: '+19%' },
          },
        }
    }
  }

  const teamContent = getTeamSpecificContent()

  return (
    <div className='space-y-8'>
      <div>
        <h2 className='text-3xl font-bold tracking-tight'>{teamContent.title}</h2>
        <p className='text-muted-foreground'>
          Welcome back{session?.user?.name ? `, ${session.user.name.split(' ')[0]}` : ''}!
          Here&apos;s an overview of your team&apos;s performance.
        </p>
      </div>

      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {teamContent.metrics.primary.label}
            </CardTitle>
            <BarChart className='h-4 w-4 text-[var(--primary)]' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{teamContent.metrics.primary.value}</div>
            <p className='text-xs text-muted-foreground'>
              {teamContent.metrics.primary.change} from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {teamContent.metrics.secondary.label}
            </CardTitle>
            <Users className='h-4 w-4 text-[var(--primary)]' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{teamContent.metrics.secondary.value}</div>
            <p className='text-xs text-muted-foreground'>
              {teamContent.metrics.secondary.change} from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {teamContent.metrics.tertiary.label}
            </CardTitle>
            <Target className='h-4 w-4 text-[var(--primary)]' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{teamContent.metrics.tertiary.value}</div>
            <p className='text-xs text-muted-foreground'>
              {teamContent.metrics.tertiary.change} from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-7'>
        <Card className='col-span-4'>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
            <CardDescription>Monthly revenue and user growth</CardDescription>
          </CardHeader>
          <CardContent className='pl-2'>
            {/* Chart would go here */}
            <div className='h-[200px] w-full bg-muted/20 rounded-md flex items-center justify-center'>
              <p className='text-muted-foreground'>Chart Placeholder</p>
            </div>
          </CardContent>
        </Card>
        <Card className='col-span-3'>
          <CardHeader>
            <CardTitle>Recent Conversions</CardTitle>
            <CardDescription>Latest user conversions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div className='flex items-center'>
                <div className='mr-2 h-8 w-8 rounded-full bg-[var(--primary)]/10 flex items-center justify-center'>
                  <Target className='h-4 w-4 text-[var(--primary)]' />
                </div>
                <div className='space-y-1'>
                  <p className='text-sm font-medium leading-none'>Premium Plan Conversion</p>
                  <p className='text-sm text-muted-foreground'>User #1234 upgraded</p>
                </div>
                <div className='ml-auto font-medium'>+$199.00</div>
              </div>
              <div className='flex items-center'>
                <div className='mr-2 h-8 w-8 rounded-full bg-[var(--primary)]/10 flex items-center justify-center'>
                  <Users className='h-4 w-4 text-[var(--primary)]' />
                </div>
                <div className='space-y-1'>
                  <p className='text-sm font-medium leading-none'>New Team Member</p>
                  <p className='text-sm text-muted-foreground'>Team Alpha added member</p>
                </div>
                <div className='ml-auto font-medium'>+1</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle>Career Framework</CardTitle>
            <CardDescription>Your current position and growth path</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='text-sm text-muted-foreground'>Current Level</div>
                <div className='text-sm font-medium'>Mid-Level Developer</div>
              </div>
              <div className='flex items-center justify-between'>
                <div className='text-sm text-muted-foreground'>Experience</div>
                <div className='text-sm font-medium'>3 years</div>
              </div>
              <div className='flex items-center justify-between'>
                <div className='text-sm text-muted-foreground'>Next Review</div>
                <div className='text-sm font-medium'>3 months</div>
              </div>
            </div>
            <Button className='w-full'>
              View Full Framework
              <ArrowUpRight className='ml-2 h-4 w-4' />
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Achievements</CardTitle>
            <CardDescription>Latest milestones and badges</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div className='flex items-center space-x-4'>
                <div className='h-8 w-8 rounded-full bg-[var(--primary)]/20 flex items-center justify-center'>
                  <Target className='h-4 w-4 text-[var(--primary)]' />
                </div>
                <div>
                  <div className='text-sm font-medium'>Code Review Master</div>
                  <div className='text-xs text-muted-foreground'>Completed 50 code reviews</div>
                </div>
              </div>
              <div className='flex items-center space-x-4'>
                <div className='h-8 w-8 rounded-full bg-[var(--primary)]/20 flex items-center justify-center'>
                  <Users className='h-4 w-4 text-[var(--primary)]' />
                </div>
                <div>
                  <div className='text-sm font-medium'>Team Player</div>
                  <div className='text-xs text-muted-foreground'>Mentored 3 junior developers</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Frequently used features and tools</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-wrap gap-2'>
            <Button variant='outline'>Settings</Button>
            <Button variant='outline'>Edit Profile</Button>
            <Button variant='outline'>Advanced Settings</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
