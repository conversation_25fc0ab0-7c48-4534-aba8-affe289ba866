'use client'

import { AuthGuard } from '@/components/auth/auth-guard'
import { Button } from '@/components/core/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/core/card'
import { Input } from '@/components/core/input'
import { Label } from '@/components/core/label'
import { Textarea } from '@/components/core/textarea'
import PageHeader from '@/components/settings/PageHeader'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Role } from '@prisma/client'
import { Settings as SettingsIcon, Users } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useState } from 'react'
import { toast } from 'sonner'

export default function TeamSettingsPage() {
  return (
    <AuthGuard
      requiredRole={Role.MANAGER}
      requiredPermission='settings:team'
      fallback={<UnauthorizedMessage />}
    >
      <TeamSettingsContent />
    </AuthGuard>
  )
}

function TeamSettingsContent() {
  const { data: _session } = useSession()
  const [isSaving, setIsSaving] = useState(false)
  const [teamData, setTeamData] = useState({
    name: 'Marketing Team',
    department: 'marketing',
    description: 'Our marketing team focuses on brand awareness and lead generation.',
  })

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Team settings saved successfully')
    } catch (error) {
      toast.error('Failed to save team settings')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setTeamData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div>
      <PageHeader
        title='Team Settings'
        description='Manage your team preferences, information, and member permissions'
      />

      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <div className='flex items-center space-x-2'>
              <Users className='h-5 w-5 text-[var(--primary)]' />
              <CardTitle>Team Information</CardTitle>
            </div>
            <CardDescription>
              Configure your team's basic information and organizational details
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='team-name'>Team Name</Label>
              <Input
                id='team-name'
                value={teamData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                placeholder='Enter team name'
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='department'>Department</Label>
              <Select
                value={teamData.department}
                onValueChange={value => handleInputChange('department', value)}
              >
                <SelectTrigger id='department'>
                  <SelectValue placeholder='Select department' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='marketing'>Marketing</SelectItem>
                  <SelectItem value='engineering'>Engineering</SelectItem>
                  <SelectItem value='sales'>Sales</SelectItem>
                  <SelectItem value='support'>Support</SelectItem>
                  <SelectItem value='design'>Design</SelectItem>
                  <SelectItem value='product'>Product</SelectItem>
                  <SelectItem value='hr'>Human Resources</SelectItem>
                  <SelectItem value='finance'>Finance</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='team-description'>Team Description</Label>
              <Textarea
                id='team-description'
                value={teamData.description}
                onChange={e => handleInputChange('description', e.target.value)}
                placeholder='Describe your team purpose and goals'
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className='flex items-center space-x-2'>
              <SettingsIcon className='h-5 w-5 text-[var(--primary)]' />
              <CardTitle>Team Preferences</CardTitle>
            </div>
            <CardDescription>
              Configure team-specific settings and collaboration preferences
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='rounded-lg border bg-muted/50 p-4'>
              <div className='space-y-0.5'>
                <Label>Team Visibility</Label>
                <p className='text-xs text-muted-foreground'>
                  Control who can see your team information and activities
                </p>
              </div>
              <Select defaultValue='organization'>
                <SelectTrigger className='mt-2'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='public'>Public - Visible to everyone</SelectItem>
                  <SelectItem value='organization'>
                    Organization - Visible to company members
                  </SelectItem>
                  <SelectItem value='team'>Team Only - Visible to team members</SelectItem>
                  <SelectItem value='private'>Private - Visible to managers only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className='rounded-lg border bg-muted/50 p-4'>
              <div className='space-y-0.5'>
                <Label>Collaboration Tools</Label>
                <p className='text-xs text-muted-foreground'>
                  Enable or disable collaboration features for your team
                </p>
              </div>
              <div className='mt-2 space-y-2'>
                <div className='flex items-center space-x-2'>
                  <input type='checkbox' id='enable-chat' defaultChecked className='rounded' />
                  <Label htmlFor='enable-chat' className='text-sm'>
                    Enable team chat
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <input type='checkbox' id='enable-files' defaultChecked className='rounded' />
                  <Label htmlFor='enable-files' className='text-sm'>
                    Enable file sharing
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <input type='checkbox' id='enable-calendar' defaultChecked className='rounded' />
                  <Label htmlFor='enable-calendar' className='text-sm'>
                    Enable shared calendar
                  </Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className='flex justify-end space-x-2'>
          <Button
            variant='outline'
            onClick={() =>
              setTeamData({
                name: 'Marketing Team',
                department: 'marketing',
                description: 'Our marketing team focuses on brand awareness and lead generation.',
              })
            }
          >
            Reset
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  )
}

function UnauthorizedMessage() {
  return (
    <div className='min-h-[50vh] flex flex-col items-center justify-center text-center p-6'>
      <h2 className='text-2xl font-bold text-foreground mb-2'>Access Restricted</h2>
      <p className='text-muted-foreground max-w-md mb-6'>
        You don&apos;t have permission to access team settings. Please contact your administrator if
        you believe you should have access to this page.
      </p>
      <Button asChild>
        <a href='/dashboard'>Return to Dashboard</a>
      </Button>
    </div>
  )
}
