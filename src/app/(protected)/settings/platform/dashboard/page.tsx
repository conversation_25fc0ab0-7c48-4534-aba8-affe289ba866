'use client'

import { <PERSON><PERSON> } from '@/components/core/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/core/card'
import { Input } from '@/components/core/input'
import { Label } from '@/components/core/label'
import { Switch } from '@/components/core/switch'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/core/tabs'
import PageHeader from '@/components/settings/PageHeader'
import SettingsContainer from '@/components/settings/SettingsContainer'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Clock, LayoutDashboard, PieChart, X } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

interface DashboardSettings {
  layout: string
  showQuickActions: boolean
  showRecentActivity: boolean
  showAnalyticsSummary: boolean
  showTaskList: boolean
  chartType: string
  showDataLabels: boolean
  interactiveCharts: boolean
  colorPalette: string
  dateFormat: string
  timeFormat: string
  showRelativeDates: boolean
  customMetrics: string[]
}

const defaultSettings: DashboardSettings = {
  layout: 'grid',
  showQuickActions: true,
  showRecentActivity: true,
  showAnalyticsSummary: true,
  showTaskList: true,
  chartType: 'bar',
  showDataLabels: true,
  interactiveCharts: true,
  colorPalette: 'default',
  dateFormat: 'mdy',
  timeFormat: '12h',
  showRelativeDates: true,
  customMetrics: ['Metric 1', 'Metric 2'],
}

export default function DashboardPreferencesPage() {
  const [mounted, setMounted] = useState(false)
  const [settings, setSettings] = useState<DashboardSettings>(defaultSettings)
  const [newMetric, setNewMetric] = useState('')
  const [_isPreview, _setIsPreview] = useState(false)
  const { _theme } = useTheme()

  useEffect(() => {
    setMounted(true)
    const savedSettings = localStorage.getItem('dashboard-settings')
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings))
    }
  }, [])

  const handleSettingChange = (key: keyof DashboardSettings, value: unknown) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }))
  }

  const handleSave = () => {
    localStorage.setItem('dashboard-settings', JSON.stringify(settings))
    toast.success('Dashboard preferences saved successfully')
  }

  const handleReset = () => {
    setSettings(defaultSettings)
    localStorage.removeItem('dashboard-settings')
    toast.info('Dashboard preferences reset to defaults')
  }

  const handleAddMetric = () => {
    if (newMetric.trim()) {
      setSettings(prev => ({
        ...prev,
        customMetrics: [...prev.customMetrics, newMetric.trim()],
      }))
      setNewMetric('')
    }
  }

  const handleRemoveMetric = (index: number) => {
    setSettings(prev => ({
      ...prev,
      customMetrics: prev.customMetrics.filter((_, i) => i !== index),
    }))
  }

  if (!mounted) {
    return null
  }

  return (
    <SettingsContainer>
      <PageHeader title='Dashboard Preferences' description='Customize your dashboard experience' />

      <Tabs defaultValue='settings' className='w-full'>
        <TabsList className='mb-4'>
          <TabsTrigger value='settings'>Settings</TabsTrigger>
          <TabsTrigger value='preview' data-testid='tab-preview'>
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value='settings'>
          <Card>
            <CardHeader>
              <div className='flex items-center space-x-2'>
                <LayoutDashboard className='h-5 w-5 text-[var(--primary)]' />
                <CardTitle className='text-xl'>Layout & Widgets</CardTitle>
              </div>
              <CardDescription>Configure your dashboard layout and visible widgets</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='layout-type'>Dashboard Layout</Label>
                  <Select
                    value={settings.layout}
                    onValueChange={value => handleSettingChange('layout', value)}
                  >
                    <SelectTrigger id='layout-type' data-testid='select-trigger-layout'>
                      <SelectValue placeholder='Select layout type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='grid'>Grid Layout</SelectItem>
                      <SelectItem value='list'>List Layout</SelectItem>
                      <SelectItem value='compact'>Compact Layout</SelectItem>
                      <SelectItem value='expanded'>Expanded Layout</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='quick-actions'>Show Quick Actions</Label>
                    <p className='text-xs text-muted-foreground'>
                      Display quick action buttons at the top of dashboard
                    </p>
                  </div>
                  <Switch
                    id='quick-actions'
                    checked={settings.showQuickActions}
                    onCheckedChange={checked => handleSettingChange('showQuickActions', checked)}
                  />
                </div>

                <div className='flex items-center justify-between pt-2'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='recent-activity'>Show Recent Activity</Label>
                    <p className='text-xs text-muted-foreground'>Display recent activity feed</p>
                  </div>
                  <Switch
                    id='recent-activity'
                    checked={settings.showRecentActivity}
                    onCheckedChange={checked => handleSettingChange('showRecentActivity', checked)}
                  />
                </div>

                <div className='flex items-center justify-between pt-2'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='analytics-summary'>Show Analytics Summary</Label>
                    <p className='text-xs text-muted-foreground'>
                      Display analytics summary widget
                    </p>
                  </div>
                  <Switch
                    id='analytics-summary'
                    checked={settings.showAnalyticsSummary}
                    onCheckedChange={checked =>
                      handleSettingChange('showAnalyticsSummary', checked)
                    }
                  />
                </div>

                <div className='flex items-center justify-between pt-2'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='task-list'>Show Task List</Label>
                    <p className='text-xs text-muted-foreground'>Display your current tasks</p>
                  </div>
                  <Switch
                    id='task-list'
                    checked={settings.showTaskList}
                    onCheckedChange={checked => handleSettingChange('showTaskList', checked)}
                  />
                </div>

                <div className='space-y-2 pt-4'>
                  <Label>Custom Metrics</Label>
                  <div className='flex gap-2'>
                    <Input
                      data-testid='new-metric-input'
                      placeholder='e.g., Team Velocity'
                      value={newMetric}
                      onChange={e => setNewMetric(e.target.value)}
                    />
                    <Button
                      data-testid='add-metric-button'
                      onClick={handleAddMetric}
                      variant='outline'
                    >
                      Add
                    </Button>
                  </div>
                  <div className='space-y-2'>
                    {settings.customMetrics.map((metric, index) => (
                      <div key={index} className='flex items-center justify-between'>
                        <span data-testid={`metric-name-${index}`}>{metric}</span>
                        <Button
                          data-testid={`remove-metric-${index}`}
                          onClick={() => handleRemoveMetric(index)}
                          variant='ghost'
                          size='sm'
                        >
                          <X className='h-4 w-4' />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className='mt-6'>
            <CardHeader>
              <div className='flex items-center space-x-2'>
                <PieChart className='h-5 w-5 text-[var(--primary)]' />
                <CardTitle className='text-xl'>Data Visualization</CardTitle>
              </div>
              <CardDescription>
                Configure how data is displayed in charts and graphs
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='chart-type'>Default Chart Type</Label>
                  <Select
                    value={settings.chartType}
                    onValueChange={value => handleSettingChange('chartType', value)}
                  >
                    <SelectTrigger id='chart-type'>
                      <SelectValue placeholder='Select chart type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='bar'>Bar Chart</SelectItem>
                      <SelectItem value='line'>Line Chart</SelectItem>
                      <SelectItem value='pie'>Pie Chart</SelectItem>
                      <SelectItem value='area'>Area Chart</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='data-labels'>Show Data Labels</Label>
                    <p className='text-xs text-muted-foreground'>
                      Display values directly on charts
                    </p>
                  </div>
                  <Switch
                    id='data-labels'
                    checked={settings.showDataLabels}
                    onCheckedChange={checked => handleSettingChange('showDataLabels', checked)}
                  />
                </div>

                <div className='flex items-center justify-between pt-2'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='interactive-charts'>Interactive Charts</Label>
                    <p className='text-xs text-muted-foreground'>
                      Enable hover and click interactions on charts
                    </p>
                  </div>
                  <Switch
                    id='interactive-charts'
                    checked={settings.interactiveCharts}
                    onCheckedChange={checked => handleSettingChange('interactiveCharts', checked)}
                  />
                </div>

                <div className='space-y-2 pt-4'>
                  <Label htmlFor='color-palette'>Color Palette</Label>
                  <Select
                    value={settings.colorPalette}
                    onValueChange={value => handleSettingChange('colorPalette', value)}
                  >
                    <SelectTrigger id='color-palette'>
                      <SelectValue placeholder='Select color palette' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='default'>Default</SelectItem>
                      <SelectItem value='monochrome'>Monochrome</SelectItem>
                      <SelectItem value='colorful'>Colorful</SelectItem>
                      <SelectItem value='pastel'>Pastel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className='mt-6'>
            <CardHeader>
              <div className='flex items-center space-x-2'>
                <Clock className='h-5 w-5 text-[var(--primary)]' />
                <CardTitle className='text-xl'>Time & Date Format</CardTitle>
              </div>
              <CardDescription>Configure how time and dates are displayed</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='date-format'>Date Format</Label>
                  <Select
                    value={settings.dateFormat}
                    onValueChange={value => handleSettingChange('dateFormat', value)}
                  >
                    <SelectTrigger id='date-format'>
                      <SelectValue placeholder='Select date format' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='mdy'>MM/DD/YYYY</SelectItem>
                      <SelectItem value='dmy'>DD/MM/YYYY</SelectItem>
                      <SelectItem value='ymd'>YYYY/MM/DD</SelectItem>
                      <SelectItem value='long'>Month DD, YYYY</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='space-y-2 pt-4'>
                  <Label htmlFor='time-format'>Time Format</Label>
                  <Select
                    value={settings.timeFormat}
                    onValueChange={value => handleSettingChange('timeFormat', value)}
                  >
                    <SelectTrigger id='time-format'>
                      <SelectValue placeholder='Select time format' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='12h'>12-hour (AM/PM)</SelectItem>
                      <SelectItem value='24h'>24-hour</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='relative-dates'>Show Relative Dates</Label>
                    <p className='text-xs text-muted-foreground'>
                      Show dates like &quot;2 days ago&quot; instead of exact dates
                    </p>
                  </div>
                  <Switch
                    id='relative-dates'
                    checked={settings.showRelativeDates}
                    onCheckedChange={checked => handleSettingChange('showRelativeDates', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className='flex justify-end space-x-4 mt-6'>
            <Button data-testid='reset-button' onClick={handleReset} variant='outline'>
              Reset to Default
            </Button>
            <Button data-testid='save-button' onClick={handleSave} variant='default'>
              Save Changes
            </Button>
          </div>
        </TabsContent>

        <TabsContent value='preview'>
          <div className='p-4 border rounded-lg'>
            <h3 className='text-lg font-medium mb-4'>Dashboard Preview</h3>
            <div className='grid gap-4'>
              {settings.showQuickActions && (
                <div className='p-4 border rounded bg-muted'>
                  <h4 className='font-medium'>Quick Actions</h4>
                  <div className='flex gap-2 mt-2'>
                    <Button size='sm'>New Task</Button>
                    <Button size='sm'>Add Note</Button>
                    <Button size='sm'>Start Timer</Button>
                  </div>
                </div>
              )}

              {settings.showAnalyticsSummary && (
                <div className='p-4 border rounded bg-muted'>
                  <h4 className='font-medium'>Analytics Summary</h4>
                  <div className='grid grid-cols-3 gap-4 mt-2'>
                    {settings.customMetrics.map((metric, index) => (
                      <div key={index} className='p-2 border rounded bg-background'>
                        <div className='text-sm text-muted-foreground'>{metric}</div>
                        <div className='text-2xl font-medium'>123</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {settings.showRecentActivity && (
                <div className='p-4 border rounded bg-muted'>
                  <h4 className='font-medium'>Recent Activity</h4>
                  <div className='space-y-2 mt-2'>
                    <div className='p-2 border rounded bg-background'>Activity 1</div>
                    <div className='p-2 border rounded bg-background'>Activity 2</div>
                    <div className='p-2 border rounded bg-background'>Activity 3</div>
                  </div>
                </div>
              )}

              {settings.showTaskList && (
                <div className='p-4 border rounded bg-muted'>
                  <h4 className='font-medium'>Task List</h4>
                  <div className='space-y-2 mt-2'>
                    <div className='p-2 border rounded bg-background'>Task 1</div>
                    <div className='p-2 border rounded bg-background'>Task 2</div>
                    <div className='p-2 border rounded bg-background'>Task 3</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </SettingsContainer>
  )
}
