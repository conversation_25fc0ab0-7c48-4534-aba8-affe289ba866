'use client'

import { ColorScheme, Theme, useColorScheme, useCustomTheme } from '@/app/providers/theme-provider'
import { ColorPalette } from '@/components/features/color-palette/ColorPalette'
import PageHeader from '@/components/settings/PageHeader'
import SettingsContainer from '@/components/settings/SettingsContainer'
import { BrandButton } from '@/components/shared/BrandButton'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Slider } from '@/components/ui/slider'
import { useThemeSync } from '@/hooks/useThemeSync'
import { transitions } from '@/lib/design-system'
import {
  CustomColor,
  GradientDirection,
  GradientType,
  ThemeElement,
  useEnhancedTheme,
} from '@/lib/ThemeContext'
import { cn } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import { motion } from 'framer-motion'
import { Check, Copy, Monitor, Moon, Palette, RotateCw, Sun, X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

// Define enhanced color palettes with complete theme variables
const colorPalettes = {
  // Light Themes
  'emynent-light': {
    name: 'Emynent Default (Light)',
    category: 'light',
    mode: 'light',
    primary: '#8957e5',
    secondary: '#6e40c9',
    accent: '#b388ff',
    muted: '#e8e4f3',
    background: '#f4f1fa',
    foreground: '#24292e',
    card: '#ffffff',
    cardForeground: '#24292e',
    popover: '#ffffff',
    popoverForeground: '#24292e',
    border: '#e1e4e8',
    input: '#e1e4e8',
    ring: '#8957e5',
    colors: ['#8957e5', '#6e40c9', '#b388ff', '#ffffff', '#f4f1fa'],
  },
  syntaxlight: {
    name: 'Syntax Light',
    category: 'light',
    mode: 'light',
    primary: '#007acc',
    secondary: '#0098ff',
    accent: '#4cade6',
    muted: '#e6f3fa',
    background: '#f0faff',
    foreground: '#333333',
    card: '#fafafa',
    cardForeground: '#333333',
    popover: '#fafafa',
    popoverForeground: '#333333',
    border: '#e0e0e0',
    input: '#e0e0e0',
    ring: '#007acc',
    colors: ['#007acc', '#0098ff', '#4cade6', '#fafafa', '#f0faff'],
  },
  mintygreen: {
    name: 'Minty Green',
    category: 'light',
    mode: 'light',
    primary: '#2da44e',
    secondary: '#3fb950',
    accent: '#63d188',
    muted: '#e6f7ef',
    background: '#f0fbf5',
    foreground: '#24292f',
    card: '#fbfefc',
    cardForeground: '#24292f',
    popover: '#fbfefc',
    popoverForeground: '#24292f',
    border: '#dafbe1',
    input: '#dafbe1',
    ring: '#2da44e',
    colors: ['#2da44e', '#3fb950', '#63d188', '#fbfefc', '#f0fbf5'],
  },
  sandybeige: {
    name: 'Sandy Beige',
    category: 'light',
    mode: 'light',
    primary: '#db8b2a',
    secondary: '#f0883e',
    accent: '#f2b05e',
    muted: '#f7f0e0',
    background: '#faf5e8',
    foreground: '#3d2919',
    card: '#fffbf2',
    cardForeground: '#3d2919',
    popover: '#fffbf2',
    popoverForeground: '#3d2919',
    border: '#f2e4c7',
    input: '#f2e4c7',
    ring: '#db8b2a',
    colors: ['#db8b2a', '#f0883e', '#f2b05e', '#fffbf2', '#faf5e8'],
  },
  'lavender-light': {
    name: 'Lavender Light',
    category: 'light',
    mode: 'light',
    primary: '#9c6dd9',
    secondary: '#b388ff',
    accent: '#d4a7ff',
    muted: '#eee5fa',
    background: '#f5edff',
    foreground: '#2e1065',
    card: '#fcf9ff',
    cardForeground: '#2e1065',
    popover: '#fcf9ff',
    popoverForeground: '#2e1065',
    border: '#e6d9f5',
    input: '#e6d9f5',
    ring: '#9c6dd9',
    colors: ['#9c6dd9', '#b388ff', '#d4a7ff', '#fcf9ff', '#f5edff'],
  },
  'coral-light': {
    name: 'Coral Light',
    category: 'light',
    mode: 'light',
    primary: '#ff6d6d',
    secondary: '#ff9a9a',
    accent: '#ffbdbd',
    muted: '#fee5e5',
    background: '#fff0f0',
    foreground: '#420000',
    card: '#fff8f8',
    cardForeground: '#420000',
    popover: '#fff8f8',
    popoverForeground: '#420000',
    border: '#ffd6d6',
    input: '#ffd6d6',
    ring: '#ff6d6d',
    colors: ['#ff6d6d', '#ff9a9a', '#ffbdbd', '#fff8f8', '#fff0f0'],
  },
  'aqua-light': {
    name: 'Aqua Light',
    category: 'light',
    mode: 'light',
    primary: '#06b6d4',
    secondary: '#22d3ee',
    accent: '#67e8f9',
    muted: '#e6f7fa',
    background: '#f0feff',
    foreground: '#164e63',
    card: '#f8fdfe',
    cardForeground: '#164e63',
    popover: '#f8fdfe',
    popoverForeground: '#164e63',
    border: '#bae6fd',
    input: '#bae6fd',
    ring: '#06b6d4',
    colors: ['#06b6d4', '#22d3ee', '#67e8f9', '#f8fdfe', '#f0feff'],
  },
  'twilight-light': {
    name: 'Twilight Light',
    category: 'light',
    mode: 'light',
    primary: '#FFB700', // Warm yellow
    secondary: '#FFA000', // Orange
    accent: '#FFD700', // Golden yellow
    muted: '#FFF8E1', // Very light yellow
    background: '#FFFDF7', // Cream white
    foreground: '#2D2D2D', // Dark gray
    card: '#FFFFFF', // White
    cardForeground: '#2D2D2D',
    popover: '#FFFFFF',
    popoverForeground: '#2D2D2D',
    border: '#FFE082', // Light golden
    input: '#FFE082',
    ring: '#FFB700',
    colors: ['#FFB700', '#FFA000', '#FFD700', '#FFFFFF', '#FFFDF7'],
  },

  // Dark Themes
  'emynent-dark': {
    name: 'Emynent Default (Dark)',
    category: 'dark',
    mode: 'dark',
    primary: '#8957e5',
    secondary: '#6e40c9',
    accent: '#b388ff',
    muted: '#2b3036',
    background: '#0d1117',
    foreground: '#c9d1d9',
    card: '#161b22',
    cardForeground: '#c9d1d9',
    popover: '#161b22',
    popoverForeground: '#d6deeb',
    border: '#30363d',
    input: '#30363d',
    ring: '#8957e5',
    colors: ['#8957e5', '#6e40c9', '#b388ff', '#161b22', '#0d1117'],
  },
  nightowl: {
    name: 'Night Owl',
    category: 'dark',
    mode: 'dark',
    primary: '#c792ea',
    secondary: '#7fdbca',
    accent: '#82aaff',
    muted: '#112630',
    background: '#011627',
    foreground: '#d6deeb',
    card: '#011627',
    cardForeground: '#d6deeb',
    popover: '#011627',
    popoverForeground: '#d6deeb',
    border: '#1d3b53',
    input: '#1d3b53',
    ring: '#c792ea',
    colors: ['#c792ea', '#7fdbca', '#82aaff', '#011627', '#011627'],
  },
  draculapro: {
    name: 'Dracula Pro',
    category: 'dark',
    mode: 'dark',
    primary: '#bd93f9',
    secondary: '#ff79c6',
    accent: '#8be9fd',
    muted: '#282a36',
    background: '#282a36',
    foreground: '#f8f8f2',
    card: '#282a36',
    cardForeground: '#f8f8f2',
    popover: '#282a36',
    popoverForeground: '#f8f8f2',
    border: '#44475a',
    input: '#44475a',
    ring: '#bd93f9',
    colors: ['#bd93f9', '#ff79c6', '#8be9fd', '#282a36', '#282a36'],
  },
  'github-dark': {
    name: 'GitHub Dark',
    category: 'dark',
    mode: 'dark',
    primary: '#58a6ff',
    secondary: '#79c0ff',
    accent: '#bd93f9',
    muted: '#30363d',
    background: '#0d1117',
    foreground: '#c9d1d9',
    card: '#161b22',
    cardForeground: '#c9d1d9',
    popover: '#161b22',
    popoverForeground: '#c9d1d9',
    border: '#30363d',
    input: '#30363d',
    ring: '#58a6ff',
    colors: ['#58a6ff', '#79c0ff', '#bd93f9', '#161b22', '#0d1117'],
  },
  'blue-dark': {
    name: 'Blue Dark',
    category: 'dark',
    mode: 'dark',
    primary: '#3b82f6',
    secondary: '#60a5fa',
    accent: '#93c5fd',
    muted: '#1e3a8a',
    background: '#0f172a',
    foreground: '#e2e8f0',
    card: '#1e293b',
    cardForeground: '#e2e8f0',
    popover: '#1e293b',
    popoverForeground: '#e2e8f0',
    border: '#1e40af',
    input: '#1e40af',
    ring: '#3b82f6',
    colors: ['#3b82f6', '#60a5fa', '#93c5fd', '#1e293b', '#0f172a'],
  },
  'green-dark': {
    name: 'Green Dark',
    category: 'dark',
    mode: 'dark',
    primary: '#059669',
    secondary: '#10b981',
    accent: '#34d399',
    muted: '#064e3b',
    background: '#0f1a17',
    foreground: '#e2e8f0',
    card: '#1e3a2b',
    cardForeground: '#e2e8f0',
    popover: '#1e3a2b',
    popoverForeground: '#e2e8f0',
    border: '#065f46',
    input: '#065f46',
    ring: '#059669',
    colors: ['#059669', '#10b981', '#34d399', '#1e3a2b', '#0f1a17'],
  },
  'slate-dark': {
    name: 'Slate Dark',
    category: 'dark',
    mode: 'dark',
    primary: '#6C757D',
    secondary: '#5A6268',
    accent: '#ADB5BD',
    muted: '#343A40',
    background: '#121212',
    foreground: '#F8F9FA',
    card: '#212529',
    cardForeground: '#F8F9FA',
    popover: '#212529',
    popoverForeground: '#F8F9FA',
    border: '#495057',
    input: '#495057',
    ring: '#6C757D',
    colors: ['#6C757D', '#5A6268', '#ADB5BD', '#212529', '#121212'],
  },
  twilight: {
    name: 'Twilight',
    category: 'dark',
    mode: 'dark',
    primary: '#FFD700', // Bright yellow
    secondary: '#FFA500', // Orange
    accent: '#FFE55C', // Light yellow
    muted: '#2D2D2D',
    background: '#1A1A1A',
    foreground: '#E2E2E2',
    card: '#252525',
    cardForeground: '#E2E2E2',
    popover: '#252525',
    popoverForeground: '#E2E2E2',
    border: '#3D3D3D',
    input: '#3D3D3D',
    ring: '#FFD700',
    colors: ['#FFD700', '#FFA500', '#FFE55C', '#252525', '#1A1A1A'],
  },
}

// Define the categories and their labels
const themeCategories = [
  { id: 'light', label: 'Light Themes' },
  { id: 'dark', label: 'Dark Themes' },
]

// Basic color options for the custom color picker
const basicColors = [
  '#000000',
  '#333333',
  '#666666',
  '#999999',
  '#CCCCCC',
  '#FFFFFF',
  '#FF0000',
  '#FF3333',
  '#FF6666',
  '#FF9999',
  '#FFCCCC',
  '#FF7F00',
  '#FFBF00',
  '#FFFF00',
  '#BFFF00',
  '#7FFF00',
  '#00FF00',
  '#00FF7F',
  '#00FFFF',
  '#007FFF',
  '#0000FF',
  '#7F00FF',
  '#FF00FF',
  '#FF007F',
  '#8B4513',
  '#654321',
]

// Gradient types
const gradientTypes = [
  { value: 'linear', label: 'Linear' },
  { value: 'radial', label: 'Radial' },
  { value: 'conic', label: 'Conic' },
]

// Gradient directions
const gradientDirections = [
  { value: 'to-r', label: '→', angle: 90 },
  { value: 'to-br', label: '↘', angle: 135 },
  { value: 'to-b', label: '↓', angle: 180 },
  { value: 'to-bl', label: '↙', angle: 225 },
  { value: 'to-l', label: '←', angle: 270 },
  { value: 'to-tl', label: '↖', angle: 315 },
  { value: 'to-t', label: '↑', angle: 0 },
  { value: 'to-tr', label: '↗', angle: 45 },
]

// Form schema for custom colors
const colorFormSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  hex: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex code'),
})

// Convert RGB to Hex
function rgbToHex(r: number, g: number, b: number): string {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()
}

// Custom color picker component
function CustomColorPicker({
  value,
  onChange,
}: {
  value: string
  onChange: (value: string) => void
}) {
  const [showPicker, setShowPicker] = useState(false)

  // Handle click on a basic color
  const handleColorSelect = (color: string) => {
    onChange(color)
    setShowPicker(false)
  }

  return (
    <div className='relative'>
      <div className='flex items-center space-x-2'>
        <div
          className='h-9 w-9 rounded-md border cursor-pointer'
          style={{ backgroundColor: value }}
          onClick={() => setShowPicker(!showPicker)}
        />
        <Input
          type='text'
          value={value}
          onChange={e => onChange(e.target.value)}
          className='font-mono'
          placeholder='#000000'
        />
      </div>

      {showPicker && (
        <Card className='absolute z-10 mt-2 w-full max-w-[320px] shadow-lg'>
          <CardContent className='p-3'>
            <div className='grid grid-cols-6 gap-2'>
              {basicColors.map(color => (
                <div
                  key={color}
                  className={cn(
                    'h-8 w-8 rounded-md cursor-pointer border hover:scale-110 transition-transform',
                    color === value ? 'ring-2 ring-primary ring-offset-2' : ''
                  )}
                  style={{ backgroundColor: color }}
                  onClick={() => handleColorSelect(color)}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default function AppearancePage() {
  const { theme, setTheme, resolvedTheme } = useCustomTheme()
  const { colorScheme, setColorScheme, gradientEnabled, setGradientEnabled } = useColorScheme()
  const { saveThemePreferences } = useThemeSync()
  const enhancedTheme = useEnhancedTheme()

  // State for custom color picker
  const [color, setColor] = useState('#8b5cf6')
  const [customPalette, setCustomPalette] = useState<CustomColor[]>([])
  const [selectedColors, setSelectedColors] = useState<string[]>([])

  // State for gradient generator
  const [gradientType, setGradientType] = useState<GradientType>('linear')
  const [gradientDirection, setGradientDirection] = useState<GradientDirection>('to-r')
  const [gradientAngle, setGradientAngle] = useState(90)

  // State for color palette modal
  const [showColorPalette, setShowColorPalette] = useState(false)

  // State to track hovered theme element
  const [hoveredElement, setHoveredElement] = useState<string | null>(null)

  // Form for adding custom colors
  const form = useForm<z.infer<typeof colorFormSchema>>({
    resolver: zodResolver(colorFormSchema),
    defaultValues: {
      name: '',
      hex: '#8b5cf6',
    },
  })

  // Add these state variables to track the selected theme's colors for preview
  const [previewTheme, setPreviewTheme] = useState<{
    primary: string
    secondary: string
    accent: string
    card: string
    background: string
    foreground: string
    cardForeground: string
    border: string
    muted: string
    popover: string
    popoverForeground: string
  } | null>(null)

  // Initialize custom palette from theme context
  useEffect(() => {
    setCustomPalette(enhancedTheme.customColors)
    setGradientType(enhancedTheme.gradientSettings.type)
    setGradientDirection(enhancedTheme.gradientSettings.direction)
    setGradientAngle(enhancedTheme.gradientSettings.angle)
  }, [enhancedTheme])

  // Update form hex value when color changes
  useEffect(() => {
    form.setValue('hex', color)
  }, [color, form])

  // Initialize preview theme with current colors
  useEffect(() => {
    if (colorScheme && colorPalettes[colorScheme as keyof typeof colorPalettes]) {
      const palette = colorPalettes[colorScheme as keyof typeof colorPalettes]
      setPreviewTheme({
        primary: palette.primary,
        secondary: palette.secondary,
        accent: palette.accent,
        card: palette.card,
        background: palette.background,
        foreground: palette.foreground,
        cardForeground: palette.cardForeground,
        border: palette.border,
        muted: palette.muted,
        popover: palette.popover,
        popoverForeground: palette.popoverForeground,
      })
    }
  }, [colorScheme])

  // Theme mode handlers - updated to maintain theme-mode consistency and persist to database
  const handleThemeChange = async (value: Theme) => {
    if (value !== theme) {
      setTheme(value)
      enhancedTheme.setSystemTheme(value)

      // Automatically switch to matching theme variant if using Emynent
      let newScheme = colorScheme
      if (colorScheme.startsWith('emynent')) {
        newScheme =
          value === 'dark' || (value === 'system' && resolvedTheme === 'dark')
            ? 'emynent-dark'
            : 'emynent-light'

        if (newScheme !== colorScheme) {
          setColorScheme(newScheme as ColorScheme)
        }
      }

      // Save to database
      try {
        await saveThemePreferences(value as any, newScheme as any)
        toast.success(`Theme mode set to ${value}`)
      } catch (_error) {
        // console.error('Failed to save theme preferences:', error);
        toast.error('Failed to save theme preferences')
      }
    }
  }

  // Enhanced color scheme handler - updated to handle mode-aware theme switching and database persistence
  const handleColorSchemeChange = async (scheme: string) => {
    // Validate if the scheme exists in our palettes
    if (colorPalettes[scheme as keyof typeof colorPalettes]) {
      // Get the full palette data
      const palette = colorPalettes[scheme as keyof typeof colorPalettes]

      // console.log(`Applying color scheme: ${scheme}`, palette);

      // Update preview theme state
      setPreviewTheme({
        primary: palette.primary,
        secondary: palette.secondary,
        accent: palette.accent,
        card: palette.card,
        background: palette.background,
        foreground: palette.foreground,
        cardForeground: palette.cardForeground,
        border: palette.border,
        muted: palette.muted,
        popover: palette.popover,
        popoverForeground: palette.popoverForeground,
      })

      // Set the color scheme in our provider
      setColorScheme(scheme as ColorScheme)

      // Directly apply CSS variables for immediate visual feedback
      if (typeof document !== 'undefined') {
        document.documentElement.style.setProperty('--primary', palette.primary)
        document.documentElement.style.setProperty('--secondary', palette.secondary)
        document.documentElement.style.setProperty('--accent', palette.accent)
        document.documentElement.style.setProperty('--background', palette.background)
        document.documentElement.style.setProperty('--foreground', palette.foreground)
        document.documentElement.style.setProperty('--card', palette.card)
        document.documentElement.style.setProperty('--card-foreground', palette.cardForeground)
        document.documentElement.style.setProperty('--border', palette.border)
        document.documentElement.style.setProperty('--muted', palette.muted)
        document.documentElement.style.setProperty('--popover', palette.popover)
        document.documentElement.style.setProperty(
          '--popover-foreground',
          palette.popoverForeground
        )

        // Add a visual class to trigger repaints
        document.documentElement.classList.add('theme-transition')
        setTimeout(() => {
          document.documentElement.classList.remove('theme-transition')
        }, 50)

        // console.log('CSS variables updated for scheme:', scheme);
      }

      // Don't automatically change theme mode - let user control it
      // The theme mode (light/dark/system) should remain as the user set it
      const newTheme = theme

      // Save to database
      try {
        await saveThemePreferences(newTheme as any, scheme as any)
        // Show success message
        toast.success(`Theme "${palette.name}" applied successfully`)
      } catch (_error) {
        // console.error('Failed to save theme preferences:', error);
        toast.error('Failed to save theme preferences')
      }
    } else {
      // console.error(`Invalid color scheme: ${scheme}`);
      toast.error(`Failed to apply theme: Invalid scheme "${scheme}"`)
    }
  }

  // Initialize the preview theme on component mount
  useEffect(() => {
    // Get the active scheme or default
    const currentScheme =
      colorScheme || (resolvedTheme === 'dark' ? 'emynent-dark' : 'emynent-light')
    const currentPalette = colorPalettes[currentScheme as keyof typeof colorPalettes]

    if (currentPalette) {
      setPreviewTheme({
        primary: currentPalette.primary,
        secondary: currentPalette.secondary,
        accent: currentPalette.accent,
        card: currentPalette.card,
        background: currentPalette.background,
        foreground: currentPalette.foreground,
        cardForeground: currentPalette.cardForeground,
        border: currentPalette.border,
        muted: currentPalette.muted,
        popover: currentPalette.popover,
        popoverForeground: currentPalette.popoverForeground,
      })

      // Set CSS custom properties for ALL theme elements to ensure consistent styling
      if (typeof document !== 'undefined') {
        document.documentElement.style.setProperty('--primary', currentPalette.primary)
        document.documentElement.style.setProperty('--secondary', currentPalette.secondary)
        document.documentElement.style.setProperty('--accent', currentPalette.accent)
        document.documentElement.style.setProperty('--background', currentPalette.background)
        document.documentElement.style.setProperty('--foreground', currentPalette.foreground)
        document.documentElement.style.setProperty('--card', currentPalette.card)
        document.documentElement.style.setProperty(
          '--card-foreground',
          currentPalette.cardForeground
        )
        document.documentElement.style.setProperty('--border', currentPalette.border)
        document.documentElement.style.setProperty('--muted', currentPalette.muted)
        document.documentElement.style.setProperty('--popover', currentPalette.popover)
        document.documentElement.style.setProperty(
          '--popover-foreground',
          currentPalette.popoverForeground
        )

        // Set the button styling variables
        document.documentElement.style.setProperty('--accent-button-bg', currentPalette.accent)
        document.documentElement.style.setProperty(
          '--accent-button-hover',
          `${currentPalette.accent}E6`
        ) // 90% opacity
      }
    }
  }, [colorScheme, resolvedTheme])

  // Add a custom color to the palette
  const handleAddCustomColor = (data: z.infer<typeof colorFormSchema>) => {
    if (customPalette.length >= 10) {
      toast.error('Maximum of 10 colors allowed in the palette')
      return
    }

    const newColor: CustomColor = {
      id: Date.now().toString(),
      name: data.name,
      value: data.hex,
    }

    enhancedTheme.addCustomColor(newColor)
    setCustomPalette([...customPalette, newColor])
    form.reset({ name: '', hex: color })
    toast.success(`Added ${data.name} to your palette`)
  }

  // Remove a color from the palette
  const handleRemoveColor = (id: string) => {
    enhancedTheme.removeCustomColor(id)
    setCustomPalette(customPalette.filter(c => c.id !== id))
    setSelectedColors(selectedColors.filter(c => c !== id))
    toast.success('Removed color from palette')
  }

  // Clear the entire palette
  const handleClearPalette = () => {
    enhancedTheme.clearCustomColors()
    setCustomPalette([])
    setSelectedColors([])
    toast.success('Cleared palette')
  }

  // Toggle color selection for gradient
  const handleToggleColorSelection = (id: string) => {
    if (selectedColors.includes(id)) {
      setSelectedColors(selectedColors.filter(c => c !== id))
    } else {
      if (selectedColors.length < 5) {
        setSelectedColors([...selectedColors, id])
      } else {
        toast.error('Maximum of 5 colors can be selected for a gradient')
      }
    }
  }

  // Update gradient settings
  const handleGradientTypeChange = (type: GradientType) => {
    setGradientType(type)
    enhancedTheme.updateGradientSettings({ type })
  }

  const handleGradientDirectionChange = (direction: GradientDirection) => {
    setGradientDirection(direction)
    const angle = gradientDirections.find(d => d.value === direction)?.angle || 90
    setGradientAngle(angle)
    enhancedTheme.updateGradientSettings({ direction, angle })
  }

  const handleGradientAngleChange = (angle: number[]) => {
    setGradientAngle(angle[0])
    enhancedTheme.updateGradientSettings({ angle: angle[0] })

    // Update direction based on angle
    const closestDirection = gradientDirections.reduce((prev, curr) => {
      return Math.abs(curr.angle - angle[0]) < Math.abs(prev.angle - angle[0]) ? curr : prev
    })
    setGradientDirection(closestDirection.value as GradientDirection)
  }

  // Apply gradient to theme
  const handleApplyGradient = () => {
    if (selectedColors.length < 2) {
      toast.error('Please select at least 2 colors for the gradient')
      return
    }

    const selectedColorValues = selectedColors.map(
      id => customPalette.find(c => c.id === id)?.value || '#000000'
    )

    enhancedTheme.updateGradientSettings({
      type: gradientType,
      direction: gradientDirection,
      angle: gradientAngle,
      colors: selectedColorValues,
    })

    enhancedTheme.setGradientEnabled(true)

    // Generate Tailwind CSS code
    let gradientCSS = ''
    if (gradientType === 'linear') {
      gradientCSS = `bg-gradient-${gradientDirection} ${selectedColorValues
        .map((c, i) => (i === 0 ? `from-[${c}]` : i === 1 ? `to-[${c}]` : `via-[${c}]`))
        .join(' ')}`
    } else if (gradientType === 'radial') {
      gradientCSS = `bg-radial ${selectedColorValues
        .map((c, i) => (i === 0 ? `from-[${c}]` : `to-[${c}]`))
        .join(' ')}`
    } else {
      gradientCSS = `bg-conic ${selectedColorValues
        .map((c, i) => (i === 0 ? `from-[${c}]` : `to-[${c}]`))
        .join(' ')}`
    }

    // Copy to clipboard
    navigator.clipboard.writeText(gradientCSS)
    toast.success('Gradient applied and CSS copied to clipboard')
  }

  // Generate gradient preview CSS
  const getGradientPreviewCSS = () => {
    if (selectedColors.length < 2) return 'background-color: #f3f4f6'

    const selectedColorValues = selectedColors.map(
      id => customPalette.find(c => c.id === id)?.value || '#000000'
    )

    if (gradientType === 'linear') {
      return `background: linear-gradient(${gradientAngle}deg, ${selectedColorValues.join(', ')})`
    } else if (gradientType === 'radial') {
      return `background: radial-gradient(circle, ${selectedColorValues.join(', ')})`
    } else {
      return `background: conic-gradient(from ${gradientAngle}deg, ${selectedColorValues.join(', ')})`
    }
  }

  // Copy the gradient CSS to clipboard
  const handleCopyGradientCSS = () => {
    if (selectedColors.length < 2) {
      toast.error('Please select at least 2 colors for the gradient')
      return
    }

    const selectedColorValues = selectedColors.map(
      id => customPalette.find(c => c.id === id)?.value || '#000000'
    )

    let gradientCSS = ''
    if (gradientType === 'linear') {
      gradientCSS = `bg-gradient-${gradientDirection} ${selectedColorValues
        .map((c, i) => (i === 0 ? `from-[${c}]` : i === 1 ? `to-[${c}]` : `via-[${c}]`))
        .join(' ')}`
    } else if (gradientType === 'radial') {
      gradientCSS = `bg-radial ${selectedColorValues
        .map((c, i) => (i === 0 ? `from-[${c}]` : `to-[${c}]`))
        .join(' ')}`
    } else {
      gradientCSS = `bg-conic ${selectedColorValues
        .map((c, i) => (i === 0 ? `from-[${c}]` : `to-[${c}]`))
        .join(' ')}`
    }

    navigator.clipboard.writeText(gradientCSS)
    toast.success('Gradient CSS copied to clipboard')
  }

  // Apply a color directly to an element
  const handleApplyColorToElement = (element: ThemeElement, colorValue: string) => {
    enhancedTheme.applyColorToElement(element, colorValue)
    toast.success(`Applied color to ${element}`)
  }

  // Reset theme to defaults
  const handleResetTheme = () => {
    enhancedTheme.resetTheme()
    setTheme('system')
    setCustomPalette(enhancedTheme.customColors)
    setSelectedColors([])
    toast.success('Theme reset to defaults')
  }

  // Handle color selection from palette
  const handleColorFromPalette = (selectedColor: string) => {
    setColor(selectedColor)
    setShowColorPalette(false)
  }

  // Within the AppearancePage component, add a useEffect to apply consistent animations/transitions:
  useEffect(() => {
    // Apply consistent landing page styling
    enhancedTheme.applyLandingPageStyling()

    // Add consistent transition classes to particular elements
    const transitionElements = document.querySelectorAll(
      '.theme-btn, .color-swatch, .settings-card'
    )
    transitionElements.forEach(element => {
      element.classList.add('transition-all', 'duration-300')
    })
  }, [])

  // State for active element
  const [activeElement, setActiveElement] = useState<ThemeElement | null>(null)

  return (
    <SettingsContainer>
      <PageHeader
        title='Appearance'
        description='Customize the look and feel of your application'
      />

      {/* Three-column layout */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Column 1: Preset */}
        <Card className='h-full'>
          <CardHeader>
            <CardTitle>Preset</CardTitle>
            <CardDescription>Choose from predefined themes and options</CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* Theme Mode */}
            <div className='space-y-4'>
              <h3 className='text-sm font-medium'>Theme Mode</h3>
              <RadioGroup
                defaultValue={theme}
                value={theme}
                onValueChange={handleThemeChange}
                className='grid grid-cols-3 gap-2'
              >
                <div>
                  <RadioGroupItem value='light' id='light' className='peer sr-only' />
                  <Label
                    htmlFor='light'
                    className='flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary'
                  >
                    <Sun className='mb-2 h-5 w-5' />
                    <span className='text-xs font-medium'>Light</span>
                  </Label>
                </div>

                <div>
                  <RadioGroupItem value='dark' id='dark' className='peer sr-only' />
                  <Label
                    htmlFor='dark'
                    className='flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary'
                  >
                    <Moon className='mb-2 h-5 w-5' />
                    <span className='text-xs font-medium'>Dark</span>
                  </Label>
                </div>

                <div>
                  <RadioGroupItem value='system' id='system' className='peer sr-only' />
                  <Label
                    htmlFor='system'
                    className='flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary'
                  >
                    <Monitor className='mb-2 h-5 w-5' />
                    <span className='text-xs font-medium'>System</span>
                  </Label>
                </div>
              </RadioGroup>
              <div className='text-xs text-muted-foreground flex items-center'>
                <span className='mr-2'>Currently in:</span>
                <Badge variant='outline' className='text-xs'>
                  {resolvedTheme === 'dark' ? 'Dark Mode' : 'Light Mode'}
                </Badge>
                <span className='ml-2'>
                  {theme === 'system' ? '(Based on your system settings)' : ''}
                </span>
              </div>
              <p className='text-xs text-muted-foreground'>
                Changing theme mode will automatically apply the default Emynent theme for that
                mode. You can customize further by selecting specific theme colors below.
              </p>
            </div>

            {/* Theme Colors */}
            <div className='space-y-6'>
              <div className='flex items-center justify-between'>
                <h3 className='text-sm font-medium'>Theme Colors</h3>
                <Badge variant='outline' className='text-xs'>
                  {resolvedTheme === 'dark' ? 'Dark Mode' : 'Light Mode'}
                </Badge>
              </div>

              {themeCategories.map(category => {
                // Filter palettes by category
                const categoryPalettes = Object.entries(colorPalettes).filter(
                  ([_, palette]) => palette.category === category.id
                )

                // Skip if no palettes in this category
                if (categoryPalettes.length === 0) return null

                // Filter all categories by current mode to show only relevant themes
                const filteredPalettes = categoryPalettes.filter(
                  ([_, palette]) => palette.mode === resolvedTheme
                )

                // Skip if no palettes match current mode
                if (filteredPalettes.length === 0) return null

                return (
                  <div key={category.id} className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <h4 className='text-xs font-medium text-muted-foreground'>
                        {category.label}
                      </h4>
                      {category.id === 'emynent' && (
                        <div className='text-xs text-muted-foreground'>Default themes</div>
                      )}
                    </div>
                    <div className='space-y-3'>
                      {filteredPalettes.map(([key, palette]) => (
                        <div key={key} className='space-y-2'>
                          {/* Theme name and selection UI */}
                          <div className='flex items-center justify-between'>
                            <h5 className='text-xs font-medium'>{palette.name}</h5>
                            {colorScheme === key && (
                              <Badge variant='outline' className='text-[10px] px-1 py-0 h-4'>
                                Selected
                              </Badge>
                            )}
                          </div>

                          {/* Color palette container */}
                          <div
                            className={cn(
                              'flex cursor-pointer relative group overflow-hidden rounded-md',
                              'h-6', // Reduced height
                              colorScheme === key ? 'ring-2 ring-primary' : 'ring-1 ring-border'
                            )}
                            onClick={() => handleColorSchemeChange(key)}
                          >
                            {/* Map each color to a specific UI element - with stable hover effects */}
                            <div
                              className='h-full flex-1 relative group-hover:opacity-90 transition-all duration-200'
                              style={{ backgroundColor: palette.primary }}
                              onMouseEnter={() => setHoveredElement('primary')}
                              onMouseLeave={() => setHoveredElement(null)}
                            >
                              <div className='absolute inset-0 opacity-0 hover:opacity-100 flex items-center justify-center text-[9px] font-medium text-white bg-black/50 transition-opacity'>
                                Primary
                              </div>
                            </div>
                            <div
                              className='h-full flex-1 relative group-hover:opacity-90 transition-all duration-200'
                              style={{ backgroundColor: palette.secondary }}
                              onMouseEnter={() => setHoveredElement('secondary')}
                              onMouseLeave={() => setHoveredElement(null)}
                            >
                              <div className='absolute inset-0 opacity-0 hover:opacity-100 flex items-center justify-center text-[9px] font-medium text-white bg-black/50 transition-opacity'>
                                Secondary
                              </div>
                            </div>
                            <div
                              className='h-full flex-1 relative group-hover:opacity-90 transition-all duration-200'
                              style={{ backgroundColor: palette.accent }}
                              onMouseEnter={() => setHoveredElement('accent')}
                              onMouseLeave={() => setHoveredElement(null)}
                            >
                              <div className='absolute inset-0 opacity-0 hover:opacity-100 flex items-center justify-center text-[9px] font-medium text-white bg-black/50 transition-opacity'>
                                Accent
                              </div>
                            </div>
                            <div
                              className='h-full flex-1 relative group-hover:opacity-90 transition-all duration-200'
                              style={{ backgroundColor: palette.card }}
                              onMouseEnter={() => setHoveredElement('card')}
                              onMouseLeave={() => setHoveredElement(null)}
                            >
                              <div className='absolute inset-0 opacity-0 hover:opacity-100 flex items-center justify-center text-[9px] font-medium text-white bg-black/50 transition-opacity'>
                                Card
                              </div>
                            </div>
                            <div
                              className='h-full flex-1 relative group-hover:opacity-90 transition-all duration-200'
                              style={{ backgroundColor: palette.background }}
                              onMouseEnter={() => setHoveredElement('background')}
                              onMouseLeave={() => setHoveredElement(null)}
                            >
                              <div className='absolute inset-0 opacity-0 hover:opacity-100 flex items-center justify-center text-[9px] font-medium text-white bg-black/50 transition-opacity'>
                                Background
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )
              })}

              {/* Color legend to explain what each position in the palette means */}
              <div className='flex items-center justify-between mt-4 bg-muted/30 rounded-md p-2 text-[10px] text-muted-foreground'>
                <div className='flex items-center space-x-1'>
                  <div className='w-2 h-2 rounded-full bg-primary'></div>
                  <span>Primary</span>
                </div>
                <div className='flex items-center space-x-1'>
                  <div className='w-2 h-2 rounded-full bg-secondary'></div>
                  <span>Secondary</span>
                </div>
                <div className='flex items-center space-x-1'>
                  <div className='w-2 h-2 rounded-full bg-accent'></div>
                  <span>Accent</span>
                </div>
                <div className='flex items-center space-x-1'>
                  <div className='w-2 h-2 rounded-full bg-card'></div>
                  <span>Card</span>
                </div>
                <div className='flex items-center space-x-1'>
                  <div className='w-2 h-2 rounded-full bg-background'></div>
                  <span>Background</span>
                </div>
              </div>

              <div className='text-xs text-muted-foreground mt-3'>
                <p>
                  <strong>Note:</strong> Themes are filtered to match your current mode (
                  {resolvedTheme === 'dark' ? 'dark' : 'light'}).
                </p>
                <p>Changing theme mode will reset to the default Emynent theme for that mode.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Column 2: Custom */}
        <Card className='h-full'>
          <CardHeader>
            <CardTitle>Custom</CardTitle>
            <CardDescription>Create your own colors and gradients</CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* UI Preview that matches the Preview section but is interactive */}
            <div className='space-y-6'>
              <div className='rounded-lg border bg-card p-4 shadow-sm'>
                <div className='flex items-center justify-between mb-4'>
                  <div className='flex items-center space-x-2'>
                    <div className='h-8 w-8 rounded-full flex items-center justify-center text-[var(--primary-foreground)]'>
                      <span className='text-xs font-bold text-[var(--primary-foreground)]'>A</span>
                    </div>
                    <div>
                      <div className='font-medium'>Custom UI</div>
                      <div className='text-xs text-muted-foreground'>Click elements to edit</div>
                    </div>
                  </div>
                  <Badge className='border-0' variant='outline'>
                    Custom Theme
                  </Badge>
                </div>

                <div className='space-y-4'>
                  {/* Theme Elements Section - Clickable */}
                  <div className='space-y-2'>
                    <div className='text-sm font-medium'>Theme Elements</div>
                    <div className='grid grid-cols-5 gap-2'>
                      {[
                        {
                          name: 'primary',
                          color: previewTheme?.primary || 'var(--primary)',
                          label: 'Primary',
                        },
                        {
                          name: 'secondary',
                          color: previewTheme?.secondary || 'var(--secondary)',
                          label: 'Secondary',
                        },
                        {
                          name: 'accent',
                          color: previewTheme?.accent || 'var(--accent)',
                          label: 'Accent',
                        },
                        { name: 'card', color: previewTheme?.card || 'var(--card)', label: 'Card' },
                        {
                          name: 'background',
                          color: previewTheme?.background || 'var(--background)',
                          label: 'Background',
                        },
                      ].map(element => (
                        <div key={element.name} className='space-y-1 text-center group'>
                          <div
                            className='h-8 w-full rounded-md relative transition-all duration-200 cursor-pointer hover:ring-2 ring-ring/30'
                            style={{ backgroundColor: element.color }}
                            onClick={() => {
                              setColor(element.color)
                              setActiveElement(element.name as ThemeElement)
                              setShowColorPalette(true)
                            }}
                          >
                            <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-[10px] font-medium text-[var(--background)] bg-black/50 transition-opacity rounded-md'>
                              Edit {element.label}
                            </div>
                          </div>
                          <div className='text-xs'>{element.name}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Components Section - Clickable buttons */}
                  <div className='space-y-2'>
                    <div className='text-sm font-medium'>Components</div>
                    <div className='flex flex-wrap gap-2'>
                      <BrandButton
                        size='sm'
                        className='bg-[var(--primary)] text-[var(--primary-foreground)] relative group'
                        onClick={() => {
                          setColor(previewTheme?.primary || 'var(--primary)')
                          setActiveElement('primary')
                          setShowColorPalette(true)
                        }}
                      >
                        <span>Primary</span>
                        <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-[10px] font-medium bg-black/30 transition-opacity rounded-md'>
                          Edit Color
                        </div>
                      </BrandButton>
                      <BrandButton
                        size='sm'
                        variant='secondary'
                        className='relative group'
                        onClick={() => {
                          setColor(previewTheme?.secondary || 'var(--secondary)')
                          setActiveElement('secondary')
                          setShowColorPalette(true)
                        }}
                      >
                        <span>Secondary</span>
                        <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-[10px] font-medium bg-black/30 transition-opacity rounded-md'>
                          Edit Color
                        </div>
                      </BrandButton>
                      <BrandButton
                        size='sm'
                        variant='outline'
                        className='border-[var(--primary)] text-[var(--primary)] relative group'
                        onClick={() => {
                          setColor(previewTheme?.primary || 'var(--primary)')
                          setActiveElement('primary')
                          setShowColorPalette(true)
                        }}
                      >
                        <span>Outline</span>
                        <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-[10px] font-medium bg-black/30 transition-opacity rounded-md'>
                          Edit Color
                        </div>
                      </BrandButton>
                      <BrandButton
                        size='sm'
                        variant='ghost'
                        className='text-[var(--primary)] relative group'
                        onClick={() => {
                          setColor(previewTheme?.primary || 'var(--primary)')
                          setActiveElement('primary')
                          setShowColorPalette(true)
                        }}
                      >
                        <span>Ghost</span>
                        <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-[10px] font-medium bg-black/30 transition-opacity rounded-md'>
                          Edit Color
                        </div>
                      </BrandButton>
                      <BrandButton
                        size='sm'
                        variant='accent'
                        className='bg-[var(--accent)] text-[var(--accent-foreground)] relative group'
                        onClick={() => {
                          setColor(previewTheme?.accent || 'var(--accent)')
                          setActiveElement('accent')
                          setShowColorPalette(true)
                        }}
                      >
                        <span>Accent</span>
                        <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-[10px] font-medium bg-black/30 transition-opacity rounded-md'>
                          Edit Color
                        </div>
                      </BrandButton>
                    </div>
                  </div>

                  {/* Card Example - Clickable */}
                  <div className='space-y-2'>
                    <div className='text-sm font-medium'>Card Example</div>
                    <div
                      className='rounded-md border p-3 cursor-pointer group relative'
                      style={{
                        backgroundColor: previewTheme?.card || 'var(--card)',
                        borderColor: previewTheme?.border || 'var(--border)',
                        color: previewTheme?.cardForeground || 'var(--card-foreground)',
                      }}
                      onClick={() => {
                        setColor(previewTheme?.card || 'var(--card)')
                        setActiveElement('card')
                        setShowColorPalette(true)
                      }}
                    >
                      <div className='text-sm font-medium'>Card Title</div>
                      <div className='text-xs text-muted-foreground mt-1'>
                        This is how cards will appear with your theme settings.
                      </div>
                      <div className='mt-2 flex justify-end'>
                        <BrandButton
                          size='sm'
                          variant='secondary'
                          style={{
                            backgroundColor: previewTheme?.secondary || 'var(--secondary)',
                            color: 'white',
                          }}
                          onClick={e => {
                            e.stopPropagation()
                            setColor(previewTheme?.secondary || 'var(--secondary)')
                            setActiveElement('secondary')
                            setShowColorPalette(true)
                          }}
                        >
                          Action
                        </BrandButton>
                      </div>
                      <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-sm font-medium text-white bg-black/40 transition-opacity rounded-md'>
                        Edit Card Background
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Color Palette and Tools Section */}
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <h3 className='text-sm font-medium'>Color Palette</h3>
                <div className='flex gap-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleClearPalette}
                    disabled={customPalette.length === 0}
                  >
                    Clear All
                  </Button>
                  <Button variant='outline' size='sm' onClick={() => setShowColorPalette(true)}>
                    <Palette className='h-4 w-4 mr-1' />
                    Add Color
                  </Button>
                </div>
              </div>

              {/* Active Color Indicator */}
              {activeElement && (
                <div className='bg-muted/30 p-2 rounded-md flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <div className='h-6 w-6 rounded-md' style={{ backgroundColor: color }}></div>
                    <span className='text-sm'>
                      Editing: <span className='font-medium capitalize'>{activeElement}</span>
                    </span>
                  </div>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => {
                      setActiveElement(null)
                    }}
                  >
                    <X className='h-4 w-4' />
                  </Button>
                </div>
              )}

              {/* Quick Color Palette */}
              {customPalette.length === 0 ? (
                <div className='text-center py-4 text-sm text-muted-foreground'>
                  No colors in palette. Add some with the color picker.
                </div>
              ) : (
                <motion.div className='grid grid-cols-5 gap-2' layout>
                  {customPalette.map(color => (
                    <motion.div
                      key={color.id}
                      layout
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className={cn(
                        'group relative rounded-md cursor-pointer transition-all',
                        selectedColors.includes(color.id) ? 'ring-2 ring-primary' : ''
                      )}
                      onClick={() => {
                        // If an element is active, apply this color to it
                        if (activeElement) {
                          handleApplyColorToElement(activeElement, color.value)
                        } else {
                          handleToggleColorSelection(color.id)
                        }
                      }}
                    >
                      <div className='absolute top-1 right-1 z-10'>
                        <button
                          type='button'
                          className='rounded-full bg-background/80 p-1 opacity-0 transition-opacity group-hover:opacity-100'
                          onClick={e => {
                            e.stopPropagation()
                            handleRemoveColor(color.id)
                          }}
                        >
                          <X className='h-3 w-3' />
                        </button>
                      </div>
                      <div
                        className='h-12 w-full rounded-sm'
                        style={{ backgroundColor: color.value }}
                      />
                      <div className='flex items-center justify-between p-1 text-xs'>
                        <span className='truncate'>{color.name}</span>
                        {selectedColors.includes(color.id) && (
                          <Check className='h-3 w-3 text-primary' />
                        )}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>

            {/* Gradient Generator - Kept as is but moved below */}
            <div className='space-y-4'>
              <h3 className='text-sm font-medium'>Gradient Generator</h3>

              {/* Gradient Preview */}
              <div
                className='w-full h-20 rounded-md shadow-inner cursor-pointer'
                style={
                  selectedColors.length >= 2
                    ? { [getGradientPreviewCSS()]: '' }
                    : { backgroundColor: 'var(--muted)' }
                }
                onClick={() => {
                  if (selectedColors.length >= 2 && activeElement) {
                    handleApplyGradient()
                  }
                }}
              />

              {/* Gradient Controls */}
              <div className='space-y-4'>
                <div className='grid grid-cols-2 gap-2'>
                  <div className='space-y-1'>
                    <Label className='text-xs'>Type</Label>
                    <select
                      className='w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
                      value={gradientType}
                      onChange={e => handleGradientTypeChange(e.target.value as GradientType)}
                    >
                      {gradientTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className='space-y-1'>
                    <Label className='text-xs'>Direction</Label>
                    <div className='grid grid-cols-4 gap-1'>
                      {gradientDirections.map(dir => (
                        <button
                          key={dir.value}
                          type='button'
                          className={cn(
                            'rounded-md border p-1 text-center text-sm',
                            gradientDirection === dir.value
                              ? 'border-primary bg-primary/10'
                              : 'border-border'
                          )}
                          onClick={() =>
                            handleGradientDirectionChange(dir.value as GradientDirection)
                          }
                        >
                          {dir.label}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                <div className='space-y-1'>
                  <div className='flex items-center justify-between'>
                    <Label className='text-xs'>Angle: {gradientAngle}°</Label>
                  </div>
                  <Slider
                    value={[gradientAngle]}
                    min={0}
                    max={360}
                    step={1}
                    onValueChange={handleGradientAngleChange}
                  />
                </div>

                <div className='flex items-center space-x-2'>
                  <BrandButton
                    onClick={() => {
                      if (activeElement) {
                        handleApplyGradient()
                      } else {
                        toast.info('Select an element first to apply the gradient')
                      }
                    }}
                    disabled={selectedColors.length < 2}
                    variant='primary'
                    fullWidth
                    icon='sparkles'
                    iconPosition='left'
                  >
                    Apply Gradient
                  </BrandButton>
                  <Button
                    variant='outline'
                    size='icon'
                    onClick={handleCopyGradientCSS}
                    disabled={selectedColors.length < 2}
                  >
                    <Copy className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <BrandButton variant='secondary' className='w-full' onClick={handleResetTheme}>
              <RotateCw className='mr-2 h-4 w-4' />
              Reset to Defaults
            </BrandButton>
          </CardFooter>
        </Card>

        {/* Column 3: Preview */}
        <Card className='h-full'>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
            <CardDescription>See how your theme looks in real-time</CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* UI Preview */}
            <div className='space-y-6'>
              <div className='rounded-lg border bg-card p-4 shadow-sm'>
                <div className='flex items-center justify-between mb-4'>
                  <div className='flex items-center space-x-2'>
                    <div className='h-8 w-8 rounded-full flex items-center justify-center text-[var(--primary-foreground)]'>
                      <span className='text-xs font-bold text-[var(--primary-foreground)]'>A</span>
                    </div>
                    <div>
                      <div className='font-medium'>Application UI</div>
                      <div className='text-xs text-muted-foreground'>Theme Preview</div>
                    </div>
                  </div>
                  <Badge
                    style={{
                      backgroundColor: `${previewTheme?.primary}20` || 'var(--primary-20)',
                      color: previewTheme?.primary || 'var(--primary)',
                      borderColor: 'transparent',
                    }}
                    className='border-0'
                  >
                    {colorPalettes[colorScheme as keyof typeof colorPalettes]?.name || 'Custom'}{' '}
                    Theme
                  </Badge>
                </div>

                <div className='space-y-4'>
                  <div className='space-y-2'>
                    <div className='text-sm font-medium'>Theme Elements</div>
                    <div className='grid grid-cols-5 gap-2'>
                      {[
                        {
                          name: 'primary',
                          color: previewTheme?.primary || 'var(--primary)',
                          label: 'Primary',
                        },
                        {
                          name: 'secondary',
                          color: previewTheme?.secondary || 'var(--secondary)',
                          label: 'Secondary',
                        },
                        {
                          name: 'accent',
                          color: previewTheme?.accent || 'var(--accent)',
                          label: 'Accent',
                        },
                        { name: 'card', color: previewTheme?.card || 'var(--card)', label: 'Card' },
                        {
                          name: 'background',
                          color: previewTheme?.background || 'var(--background)',
                          label: 'Background',
                        },
                      ].map(element => (
                        <div key={element.name} className='space-y-1 text-center group'>
                          <div
                            className='h-8 w-full rounded-md relative transition-all duration-200 hover:ring-1 ring-ring/30'
                            style={{ backgroundColor: element.color }}
                            onMouseEnter={() => setHoveredElement(element.name)}
                            onMouseLeave={() => setHoveredElement(null)}
                          >
                            <div className='absolute inset-0 opacity-0 hover:opacity-100 flex items-center justify-center text-[10px] font-medium text-[var(--background)] bg-black/50 transition-opacity rounded-md'>
                              {element.label}
                            </div>
                          </div>
                          <div className='text-xs'>{element.name}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <div className='text-sm font-medium'>Components</div>
                    <div className='flex flex-wrap gap-2'>
                      <BrandButton
                        size='sm'
                        className='bg-[var(--primary)] text-[var(--primary-foreground)]'
                      >
                        Primary
                      </BrandButton>
                      <BrandButton size='sm' variant='secondary'>
                        Secondary
                      </BrandButton>
                      <BrandButton
                        size='sm'
                        variant='outline'
                        className='border-[var(--primary)] text-[var(--primary)]'
                      >
                        Outline
                      </BrandButton>
                      <BrandButton size='sm' variant='ghost' className='text-[var(--primary)]'>
                        Ghost
                      </BrandButton>
                      <BrandButton
                        size='sm'
                        variant='accent'
                        className='bg-[var(--accent)] text-[var(--accent-foreground)]'
                      >
                        Accent
                      </BrandButton>
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <div className='text-sm font-medium'>Card Example</div>
                    <div
                      className='rounded-md border p-3 cursor-pointer group relative'
                      style={{
                        backgroundColor: previewTheme?.card || 'var(--card)',
                        borderColor: previewTheme?.border || 'var(--border)',
                        color: previewTheme?.cardForeground || 'var(--card-foreground)',
                      }}
                      onClick={() => {
                        setColor(previewTheme?.card || 'var(--card)')
                        setActiveElement('card')
                        setShowColorPalette(true)
                      }}
                    >
                      <div className='text-sm font-medium'>Card Title</div>
                      <div className='text-xs text-muted-foreground mt-1'>
                        This is how cards will appear with your theme settings.
                      </div>
                      <div className='mt-2 flex justify-end'>
                        <BrandButton
                          size='sm'
                          variant='secondary'
                          style={{
                            backgroundColor: previewTheme?.secondary || 'var(--secondary)',
                            color: 'white',
                          }}
                          onClick={e => {
                            e.stopPropagation()
                            setColor(previewTheme?.secondary || 'var(--secondary)')
                            setActiveElement('secondary')
                            setShowColorPalette(true)
                          }}
                        >
                          Action
                        </BrandButton>
                      </div>
                      <div className='absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center text-sm font-medium text-white bg-black/40 transition-opacity rounded-md'>
                        Edit Card Background
                      </div>
                    </div>
                  </div>

                  <div className='space-y-2 mt-4'>
                    <div className='text-sm font-medium'>Landing Page Components</div>
                    <div className='flex flex-wrap gap-2'>
                      <BrandButton
                        size='sm'
                        icon='fileText'
                        style={{
                          backgroundColor: previewTheme?.primary || 'var(--primary)',
                        }}
                      >
                        Get Started
                      </BrandButton>
                      <BrandButton
                        size='sm'
                        variant='secondary'
                        icon='sparkles'
                        style={{
                          borderColor: previewTheme?.secondary || 'var(--secondary)',
                          color: previewTheme?.secondary || 'var(--secondary)',
                        }}
                      >
                        Explore Paths
                      </BrandButton>
                    </div>
                  </div>

                  <div className='space-y-2 mt-4'>
                    <div className='text-sm font-medium'>Transition Previews</div>
                    <div className='grid grid-cols-2 gap-4'>
                      <div
                        className='h-16 rounded-md flex items-center justify-center transition-all duration-300 hover:scale-105'
                        style={{
                          background: `linear-gradient(90deg, ${previewTheme?.primary || 'var(--primary)'}, ${previewTheme?.accent || 'var(--accent)'})`,
                          transition: transitions.smooth,
                        }}
                      >
                        <span className='text-white font-medium'>Hover Me</span>
                      </div>
                      <div
                        className='h-16 rounded-md flex items-center justify-center transition-all duration-300 hover:scale-105'
                        style={{
                          background: `linear-gradient(90deg, ${previewTheme?.secondary || 'var(--secondary)'}, ${previewTheme?.primary || 'var(--primary)'})`,
                          transition: transitions.bounce,
                        }}
                      >
                        <span className='text-white font-medium'>Hover Me</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Current Settings */}
            <div className='space-y-4'>
              <h3 className='text-sm font-medium'>Current Settings</h3>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span className='text-muted-foreground'>Theme Mode:</span>
                  <span className='font-medium'>
                    {theme === 'system' ? `System (${resolvedTheme})` : theme}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-muted-foreground'>Color Scheme:</span>
                  <span className='font-medium'>
                    {colorPalettes[colorScheme as keyof typeof colorPalettes]?.name || 'Custom'}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-muted-foreground'>Gradient Effects:</span>
                  <span className='font-medium'>{gradientEnabled ? 'Enabled' : 'Disabled'}</span>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <BrandButton variant='secondary' className='w-full' onClick={handleResetTheme}>
              <RotateCw className='mr-2 h-4 w-4' />
              Reset to Defaults
            </BrandButton>
          </CardFooter>
        </Card>
      </div>

      {/* Color Palette Modal */}
      {showColorPalette && (
        <ColorPalette
          onClose={() => {
            setShowColorPalette(false)
            // Reset active element when closing the palette
            setActiveElement(null)
          }}
          onColorSelect={selectedColor => {
            // Apply color directly to the active element if one is selected
            if (activeElement) {
              handleApplyColorToElement(activeElement, selectedColor)

              // Show confirmation toast
              toast.success(`Applied ${selectedColor} to ${activeElement}`)

              // Update the form color state as well
              form.setValue('hex', selectedColor)

              // Keep the color palette open for continuous editing
            } else {
              // Just update the color if no element is active
              handleColorFromPalette(selectedColor)
            }
          }}
          initialColor={color}
          activeElement={activeElement}
          showElementBadge={true}
        />
      )}
    </SettingsContainer>
  )
}
