'use client'

import { <PERSON><PERSON> } from '@/components/core/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/core/card'
import { Label } from '@/components/core/label'
import { Switch } from '@/components/core/switch'
import PageHeader from '@/components/settings/PageHeader'
import SettingsContainer from '@/components/settings/SettingsContainer'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Bell, Calendar, Clock, Mail, MessageSquare, Shield } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

interface NotificationSettings {
  system: boolean
  messages: boolean
  email: boolean
  events: boolean
  push: boolean
  sound: boolean
  doNotDisturb: boolean
  emailDigest: string
  quietHours: {
    enabled: boolean
    start: string
    end: string
  }
}

const defaultSettings: NotificationSettings = {
  system: true,
  messages: true,
  email: true,
  events: true,
  push: true,
  sound: true,
  doNotDisturb: false,
  emailDigest: 'daily',
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '07:00',
  },
}

const notificationTypes = [
  {
    id: 'system',
    title: 'System Notifications',
    description: 'Important alerts about your account, security, and system updates',
    icon: Shield,
  },
  {
    id: 'messages',
    title: 'Message Notifications',
    description: 'Notifications for direct messages and mentions',
    icon: MessageSquare,
  },
  {
    id: 'email',
    title: 'Email Notifications',
    description: 'Get email notifications for important updates',
    icon: Mail,
  },
  {
    id: 'events',
    title: 'Calendar Events',
    description: 'Reminders for upcoming meetings and events',
    icon: Calendar,
  },
]

export default function PlatformNotificationsPage() {
  const [mounted, setMounted] = useState(false)
  const { _theme } = useTheme()
  const [settings, setSettings] = useState<NotificationSettings>(defaultSettings)

  useEffect(() => {
    setMounted(true)
    const savedSettings = localStorage.getItem('notification-settings')
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings))
    }
  }, [])

  const handleSave = () => {
    localStorage.setItem('notification-settings', JSON.stringify(settings))
    toast.success('Notification preferences saved')
  }

  const handleReset = () => {
    localStorage.removeItem('notification-settings')
    setSettings(defaultSettings)
    toast.info('Notification preferences reset to defaults')
  }

  const handleSettingChange = (key: keyof NotificationSettings, value: boolean | string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }))
  }

  const handleQuietHoursChange = (key: 'enabled' | 'start' | 'end', value: boolean | string) => {
    setSettings(prev => ({
      ...prev,
      quietHours: {
        ...prev.quietHours,
        [key]: value,
      },
    }))
  }

  if (!mounted) {
    return null
  }

  return (
    <SettingsContainer>
      <PageHeader
        title='Notification Preferences'
        description='Manage how and when you receive notifications'
      />

      <Card>
        <CardHeader>
          <div className='flex items-center space-x-2'>
            <Bell className='h-5 w-5 text-[var(--primary)]' />
            <CardTitle className='text-xl'>Notification Settings</CardTitle>
          </div>
          <CardDescription>
            Control which notifications you receive and how they are delivered
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          {notificationTypes.map(type => (
            <div
              key={type.id}
              className='flex items-center justify-between p-4 rounded-md bg-muted border border-border'
            >
              <div className='flex items-start space-x-3'>
                <type.icon className='h-5 w-5 mt-0.5 text-[var(--primary)]' />
                <div className='space-y-0.5'>
                  <Label htmlFor={`switch-${type.id}`}>{type.title}</Label>
                  <p className='text-xs text-muted-foreground'>{type.description}</p>
                </div>
              </div>
              <Switch
                id={`switch-${type.id}`}
                checked={settings[type.id as keyof NotificationSettings] as boolean}
                onCheckedChange={checked =>
                  handleSettingChange(type.id as keyof NotificationSettings, checked)
                }
              />
            </div>
          ))}

          <div className='space-y-4 pt-4'>
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label htmlFor='switch-push'>Push Notifications</Label>
                <p className='text-xs text-muted-foreground'>
                  Receive push notifications on this device
                </p>
              </div>
              <Switch
                id='switch-push'
                checked={settings.push}
                onCheckedChange={checked => handleSettingChange('push', checked)}
              />
            </div>

            <div className='flex items-center justify-between pt-2'>
              <div className='space-y-0.5'>
                <Label htmlFor='switch-sound'>Sound Alerts</Label>
                <p className='text-xs text-muted-foreground'>
                  Play sound when notifications arrive
                </p>
              </div>
              <Switch
                id='switch-sound'
                checked={settings.sound}
                onCheckedChange={checked => handleSettingChange('sound', checked)}
              />
            </div>

            <div className='flex items-center justify-between pt-2'>
              <div className='space-y-0.5'>
                <Label htmlFor='switch-dnd'>Do Not Disturb</Label>
                <p className='text-xs text-muted-foreground'>Temporarily mute all notifications</p>
              </div>
              <Switch
                id='switch-dnd'
                checked={settings.doNotDisturb}
                onCheckedChange={checked => handleSettingChange('doNotDisturb', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className='mt-6'>
        <CardHeader>
          <div className='flex items-center space-x-2'>
            <Clock className='h-5 w-5 text-[var(--primary)]' />
            <CardTitle className='text-xl'>Delivery Schedule</CardTitle>
          </div>
          <CardDescription>Set when you want to receive notifications</CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='delivery-frequency'>Email Digest Frequency</Label>
              <Select
                value={settings.emailDigest}
                onValueChange={value => handleSettingChange('emailDigest', value)}
              >
                <SelectTrigger id='delivery-frequency'>
                  <SelectValue placeholder='Select digest frequency' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='immediate'>Immediately</SelectItem>
                  <SelectItem value='hourly'>Hourly Summary</SelectItem>
                  <SelectItem value='daily'>Daily Digest</SelectItem>
                  <SelectItem value='weekly'>Weekly Digest</SelectItem>
                </SelectContent>
              </Select>
              <p className='text-xs text-muted-foreground mt-1'>
                How often you want to receive email notification digests
              </p>
            </div>

            <div className='flex items-center justify-between pt-4'>
              <div className='space-y-0.5'>
                <Label htmlFor='switch-quiet-hours'>Quiet Hours</Label>
                <p className='text-xs text-muted-foreground'>
                  Mute notifications during specific hours
                </p>
              </div>
              <Switch
                id='switch-quiet-hours'
                checked={settings.quietHours.enabled}
                onCheckedChange={checked => handleQuietHoursChange('enabled', checked)}
              />
            </div>

            <div className='grid grid-cols-2 gap-4 pt-2'>
              <div className='space-y-2'>
                <Label htmlFor='quiet-start'>Start Time</Label>
                <Select
                  value={settings.quietHours.start}
                  onValueChange={value => handleQuietHoursChange('start', value)}
                >
                  <SelectTrigger id='quiet-start'>
                    <SelectValue placeholder='Select start time' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='20:00'>8:00 PM</SelectItem>
                    <SelectItem value='21:00'>9:00 PM</SelectItem>
                    <SelectItem value='22:00'>10:00 PM</SelectItem>
                    <SelectItem value='23:00'>11:00 PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='quiet-end'>End Time</Label>
                <Select
                  value={settings.quietHours.end}
                  onValueChange={value => handleQuietHoursChange('end', value)}
                >
                  <SelectTrigger id='quiet-end'>
                    <SelectValue placeholder='Select end time' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='06:00'>6:00 AM</SelectItem>
                    <SelectItem value='07:00'>7:00 AM</SelectItem>
                    <SelectItem value='08:00'>8:00 AM</SelectItem>
                    <SelectItem value='09:00'>9:00 AM</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className='flex justify-between mt-6'>
        <Button variant='outline' onClick={handleReset} data-testid='reset-button'>
          Reset to Default
        </Button>
        <Button
          className='bg-[var(--primary)] hover:bg-[var(--primary)]/90 text-[var(--primary-foreground)]'
          onClick={handleSave}
          data-testid='save-button'
        >
          Save Notification Settings
        </Button>
      </div>
    </SettingsContainer>
  )
}
