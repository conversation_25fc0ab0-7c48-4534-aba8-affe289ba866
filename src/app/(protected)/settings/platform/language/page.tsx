'use client'

import { <PERSON><PERSON> } from '@/components/core/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/core/card'
import { Label } from '@/components/core/label'
import { Switch } from '@/components/core/switch'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/core/tabs'
import PageHeader from '@/components/settings/PageHeader'
import SettingsContainer from '@/components/settings/SettingsContainer'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Clock, Languages } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

export default function LanguageTimezonePage() {
  const [mounted, setMounted] = useState(false)
  const { _theme } = useTheme()

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  const handleSave = () => {
    // Save settings to localStorage
    const settings = {
      interfaceLanguage: 'en-US',
      contentLanguage: 'en-US',
      automaticTranslation: false,
      regionFormat: 'en-US',
      timezone: 'America/New_York',
      automaticTimezone: true,
      firstDayOfWeek: 'sunday',
      calendarType: 'gregorian',
      use24HourTime: false,
    }
    localStorage.setItem('language-settings', JSON.stringify(settings))
    toast.success('Language & timezone settings saved successfully')
  }

  const handleReset = () => {
    localStorage.removeItem('language-settings')
    toast.info('Language & timezone settings reset to defaults')
  }

  if (!mounted) {
    return null
  }

  return (
    <SettingsContainer>
      <PageHeader
        title='Language & Timezone'
        description='Configure language, timezone, and regional preferences'
      />

      <Tabs defaultValue='language' className='w-full'>
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='language'>Language & Locale</TabsTrigger>
          <TabsTrigger value='timezone'>Timezone & Calendar</TabsTrigger>
        </TabsList>

        <TabsContent value='language' className='mt-4'>
          <Card>
            <CardHeader>
              <div className='flex items-center space-x-2'>
                <Languages className='h-5 w-5 text-[var(--primary)]' data-testid='languages-icon' />
                <CardTitle className='text-xl'>Language Settings</CardTitle>
              </div>
              <CardDescription>Configure your language and locale preferences</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='interface-language'>Interface Language</Label>
                  <Select defaultValue='en-US'>
                    <SelectTrigger id='interface-language' data-testid='language-select'>
                      <SelectValue placeholder='Select interface language' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='en-US' data-testid='language-option-en-US'>
                        English (United States)
                      </SelectItem>
                      <SelectItem value='en-GB' data-testid='language-option-en-GB'>
                        English (United Kingdom)
                      </SelectItem>
                      <SelectItem value='es' data-testid='language-option-es'>
                        Spanish
                      </SelectItem>
                      <SelectItem value='fr' data-testid='language-option-fr'>
                        French
                      </SelectItem>
                      <SelectItem value='de' data-testid='language-option-de'>
                        German
                      </SelectItem>
                      <SelectItem value='ja' data-testid='language-option-ja'>
                        Japanese
                      </SelectItem>
                      <SelectItem value='zh-CN' data-testid='language-option-zh-CN'>
                        Chinese (Simplified)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p className='text-xs text-muted-foreground mt-1'>
                    The language used for menus, buttons, and system messages
                  </p>
                </div>

                <div className='space-y-2 pt-4'>
                  <Label htmlFor='content-language'>Content Language</Label>
                  <Select defaultValue='en-US'>
                    <SelectTrigger id='content-language' data-testid='content-language-select'>
                      <SelectValue placeholder='Select content language' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='default'>Same as interface</SelectItem>
                      <SelectItem value='en-US'>English (United States)</SelectItem>
                      <SelectItem value='en-GB'>English (United Kingdom)</SelectItem>
                      <SelectItem value='es'>Spanish</SelectItem>
                      <SelectItem value='fr'>French</SelectItem>
                      <SelectItem value='de'>German</SelectItem>
                      <SelectItem value='ja'>Japanese</SelectItem>
                      <SelectItem value='zh-CN'>Chinese (Simplified)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Preferred language for content when multiple languages are available
                  </p>
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <div className='space-y-0.5'>
                    <Label>Automatic Translation</Label>
                    <p className='text-xs text-muted-foreground'>
                      Automatically translate content not in your preferred language
                    </p>
                  </div>
                  <Switch data-testid='auto-translate-switch' />
                </div>

                <div className='space-y-2 pt-4'>
                  <Label htmlFor='region-format'>Region Format</Label>
                  <Select defaultValue='en-US'>
                    <SelectTrigger id='region-format' data-testid='region-select'>
                      <SelectValue placeholder='Select region format' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='en-US'>United States</SelectItem>
                      <SelectItem value='en-GB'>United Kingdom</SelectItem>
                      <SelectItem value='en-CA'>Canada</SelectItem>
                      <SelectItem value='en-AU'>Australia</SelectItem>
                      <SelectItem value='de-DE'>Germany</SelectItem>
                      <SelectItem value='fr-FR'>France</SelectItem>
                      <SelectItem value='ja-JP'>Japan</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Determines number, currency, and date formats
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='timezone' className='mt-4'>
          <Card>
            <CardHeader>
              <div className='flex items-center space-x-2'>
                <Clock className='h-5 w-5 text-[var(--primary)]' data-testid='clock-icon' />
                <CardTitle className='text-xl'>Timezone Settings</CardTitle>
              </div>
              <CardDescription>Configure your timezone and calendar preferences</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='timezone'>Timezone</Label>
                  <Select defaultValue='America/New_York'>
                    <SelectTrigger id='timezone' data-testid='timezone-select'>
                      <SelectValue placeholder='Select timezone' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='America/New_York' data-testid='timezone-option-ET'>
                        Eastern Time (ET) - New York
                      </SelectItem>
                      <SelectItem value='America/Chicago' data-testid='timezone-option-CT'>
                        Central Time (CT) - Chicago
                      </SelectItem>
                      <SelectItem value='America/Denver' data-testid='timezone-option-MT'>
                        Mountain Time (MT) - Denver
                      </SelectItem>
                      <SelectItem value='America/Los_Angeles' data-testid='timezone-option-PT'>
                        Pacific Time (PT) - Los Angeles
                      </SelectItem>
                      <SelectItem value='Europe/London' data-testid='timezone-option-GMT'>
                        Greenwich Mean Time (GMT) - London
                      </SelectItem>
                      <SelectItem value='Europe/Paris' data-testid='timezone-option-CET'>
                        Central European Time (CET) - Paris
                      </SelectItem>
                      <SelectItem value='Asia/Tokyo' data-testid='timezone-option-JST'>
                        Japan Standard Time (JST) - Tokyo
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Your current timezone for scheduling and timestamps
                  </p>
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <div className='space-y-0.5'>
                    <Label>Automatic Timezone</Label>
                    <p className='text-xs text-muted-foreground'>
                      Automatically update timezone based on your location
                    </p>
                  </div>
                  <Switch defaultChecked data-testid='auto-timezone-switch' />
                </div>

                <div className='space-y-2 pt-4'>
                  <Label htmlFor='first-day'>First Day of Week</Label>
                  <Select defaultValue='sunday'>
                    <SelectTrigger id='first-day' data-testid='first-day-select'>
                      <SelectValue placeholder='Select first day of week' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='sunday'>Sunday</SelectItem>
                      <SelectItem value='monday'>Monday</SelectItem>
                      <SelectItem value='saturday'>Saturday</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className='text-xs text-muted-foreground mt-1'>
                    First day of the week in calendars and schedules
                  </p>
                </div>

                <div className='space-y-2 pt-4'>
                  <Label htmlFor='calendar-type'>Calendar Type</Label>
                  <Select defaultValue='gregorian'>
                    <SelectTrigger id='calendar-type' data-testid='calendar-type-select'>
                      <SelectValue placeholder='Select calendar type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='gregorian'>Gregorian</SelectItem>
                      <SelectItem value='islamic'>Islamic/Hijri</SelectItem>
                      <SelectItem value='persian'>Persian/Jalali</SelectItem>
                      <SelectItem value='buddhist'>Buddhist</SelectItem>
                      <SelectItem value='hebrew'>Hebrew</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Calendar system to use for dates
                  </p>
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <div className='space-y-0.5'>
                    <Label>24-Hour Time</Label>
                    <p className='text-xs text-muted-foreground'>
                      Use 24-hour clock instead of 12-hour (AM/PM)
                    </p>
                  </div>
                  <Switch data-testid='24-hour-switch' />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className='flex justify-end space-x-4 mt-6'>
        <Button variant='outline' onClick={handleReset} data-testid='reset-button'>
          Reset to Default
        </Button>
        <Button onClick={handleSave} data-testid='save-button'>
          Save Changes
        </Button>
      </div>
    </SettingsContainer>
  )
}
