'use client'

import <PERSON>Header from '@/components/settings/PageHeader'
import SettingsContainer from '@/components/settings/SettingsContainer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Contrast, Keyboard, Type } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'

export default function AccessibilityPage() {
  const [mounted, setMounted] = useState(false)
  const { _theme } = useTheme()

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <SettingsContainer>
      <PageHeader
        title='Accessibility'
        description='Configure accessibility features to improve your experience.'
      />

      <Card>
        <CardHeader>
          <div className='flex items-center space-x-2'>
            <Type className='h-5 w-5 text-[var(--primary)]' />
            <CardTitle className='text-xl'>Text & Display</CardTitle>
          </div>
          <CardDescription>Adjust text size and display settings</CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <Label htmlFor='font-size'>Text Size</Label>
                <span className='text-sm text-muted-foreground'>100%</span>
              </div>
              <Slider
                id='font-size'
                defaultValue={[100]}
                max={200}
                min={50}
                step={10}
                className='w-full'
              />
              <p className='text-xs text-muted-foreground mt-1'>
                Adjust the size of text throughout the application
              </p>
            </div>

            <div className='space-y-2 pt-4'>
              <Label htmlFor='font-weight'>Font Weight</Label>
              <Select defaultValue='regular'>
                <SelectTrigger id='font-weight'>
                  <SelectValue placeholder='Select font weight' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='light'>Light</SelectItem>
                  <SelectItem value='regular'>Regular</SelectItem>
                  <SelectItem value='medium'>Medium</SelectItem>
                  <SelectItem value='bold'>Bold</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className='flex items-center justify-between pt-4'>
              <div className='space-y-0.5'>
                <Label>Reduce Motion</Label>
                <p className='text-xs text-muted-foreground'>
                  Minimize animations throughout the interface
                </p>
              </div>
              <Switch />
            </div>

            <div className='flex items-center justify-between pt-2'>
              <div className='space-y-0.5'>
                <Label>Reduce Transparency</Label>
                <p className='text-xs text-muted-foreground'>
                  Reduce transparency effects in the interface
                </p>
              </div>
              <Switch />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className='mt-6'>
        <CardHeader>
          <div className='flex items-center space-x-2'>
            <Contrast className='h-5 w-5 text-[var(--primary)]' />
            <CardTitle className='text-xl'>Color & Contrast</CardTitle>
          </div>
          <CardDescription>Adjust color and contrast settings</CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <Label htmlFor='contrast'>Contrast Level</Label>
                <span className='text-sm text-muted-foreground'>Normal</span>
              </div>
              <Slider
                id='contrast'
                defaultValue={[50]}
                max={100}
                min={0}
                step={10}
                className='w-full'
              />
              <p className='text-xs text-muted-foreground mt-1'>
                Adjust the contrast between text and background
              </p>
            </div>

            <div className='flex items-center justify-between pt-4'>
              <div className='space-y-0.5'>
                <Label>High Contrast Mode</Label>
                <p className='text-xs text-muted-foreground'>
                  Use high contrast colors for better visibility
                </p>
              </div>
              <Switch />
            </div>

            <div className='flex items-center justify-between pt-2'>
              <div className='space-y-0.5'>
                <Label>Color Blind Mode</Label>
                <p className='text-xs text-muted-foreground'>
                  Optimize colors for different types of color blindness
                </p>
              </div>
              <Switch />
            </div>

            <div className='space-y-2 pt-4'>
              <Label htmlFor='color-blind-type'>Color Blind Type</Label>
              <Select defaultValue='none'>
                <SelectTrigger id='color-blind-type'>
                  <SelectValue placeholder='Select color blind type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='none'>None</SelectItem>
                  <SelectItem value='protanopia'>Protanopia (Red-Blind)</SelectItem>
                  <SelectItem value='deuteranopia'>Deuteranopia (Green-Blind)</SelectItem>
                  <SelectItem value='tritanopia'>Tritanopia (Blue-Blind)</SelectItem>
                  <SelectItem value='achromatopsia'>Achromatopsia (Complete)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className='mt-6'>
        <CardHeader>
          <div className='flex items-center space-x-2'>
            <Keyboard className='h-5 w-5 text-[var(--primary)]' />
            <CardTitle className='text-xl'>Input & Navigation</CardTitle>
          </div>
          <CardDescription>Configure keyboard and mouse settings</CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div className='space-y-0.5'>
                <Label>Keyboard Navigation</Label>
                <p className='text-xs text-muted-foreground'>Enable enhanced keyboard navigation</p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className='flex items-center justify-between pt-2'>
              <div className='space-y-0.5'>
                <Label>Screen Reader Support</Label>
                <p className='text-xs text-muted-foreground'>
                  Optimize interface for screen readers
                </p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className='flex items-center justify-between pt-2'>
              <div className='space-y-0.5'>
                <Label>Focus Indicators</Label>
                <p className='text-xs text-muted-foreground'>
                  Show enhanced focus indicators when navigating
                </p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className='space-y-2 pt-4'>
              <div className='flex items-center justify-between'>
                <Label htmlFor='cursor-size'>Cursor Size</Label>
                <span className='text-sm text-muted-foreground'>Normal</span>
              </div>
              <Slider
                id='cursor-size'
                defaultValue={[50]}
                max={100}
                min={0}
                step={10}
                className='w-full'
              />
              <p className='text-xs text-muted-foreground mt-1'>Adjust the size of the cursor</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className='flex justify-end mt-6'>
        <Button className='bg-[var(--primary)] hover:bg-[var(--primary)]/90 text-[var(--primary-foreground)]'>
          Save Accessibility Settings
        </Button>
      </div>
    </SettingsContainer>
  )
}
