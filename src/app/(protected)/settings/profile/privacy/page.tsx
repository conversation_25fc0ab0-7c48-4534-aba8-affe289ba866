import { Metadata } from 'next'
import PageHeader from '@/components/settings/PageHeader'
import SettingsContainer from '@/components/settings/SettingsContainer'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/core/card'
import { <PERSON><PERSON> } from '@/components/core/button'
import { RadioGroup, RadioGroupItem } from '@/components/core/radio-group'
import { Label } from '@/components/core/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core/tabs'
import { Switch } from '@/components/core/switch'
import { Download, Lock, UserCog, Database, EyeOff, Globe, Shield } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Data & Privacy | Settings | Emynent',
  description: 'Manage your data and privacy settings',
}

export default function PrivacyPage() {
  return (
    <SettingsContainer>
      <PageHeader
        title='Data & Privacy'
        description='Control how your data is used and manage your privacy settings.'
      />

      <Tabs defaultValue='privacy' className='w-full'>
        <TabsList className='grid grid-cols-2 mb-6'>
          <TabsTrigger value='privacy'>Privacy Settings</TabsTrigger>
          <TabsTrigger value='data'>Data Management</TabsTrigger>
        </TabsList>

        <TabsContent value='privacy'>
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <div className='flex items-center space-x-2'>
                  <UserCog className='h-5 w-5 text-[var(--primary)]' />
                  <CardTitle className='text-xl'>Profile Visibility</CardTitle>
                </div>
                <CardDescription>Control who can view your profile information</CardDescription>
              </CardHeader>
              <CardContent className='space-y-6'>
                <div className='space-y-2'>
                  <h3 className='font-medium'>Who can see your profile</h3>
                  <RadioGroup defaultValue='team' className='space-y-3'>
                    <div className='flex items-center space-x-3 p-3 rounded-md bg-muted/50 border'>
                      <RadioGroupItem id='public' value='public' />
                      <div className='flex items-center space-x-2'>
                        <Globe className='h-4 w-4 text-blue-400' />
                        <Label htmlFor='public' className='font-medium'>
                          Public
                        </Label>
                      </div>
                      <p className='text-sm text-muted-foreground ml-2'>
                        Anyone on the internet can see your profile
                      </p>
                    </div>

                    <div className='flex items-center space-x-3 p-3 rounded-md bg-muted/50 border'>
                      <RadioGroupItem id='team' value='team' />
                      <div className='flex items-center space-x-2'>
                        <Shield className='h-4 w-4 text-green-400' />
                        <Label htmlFor='team' className='font-medium'>
                          Team members only
                        </Label>
                      </div>
                      <p className='text-sm text-muted-foreground ml-2'>
                        Only people in your organization can see your profile
                      </p>
                    </div>

                    <div className='flex items-center space-x-3 p-3 rounded-md bg-muted/50 border'>
                      <RadioGroupItem id='private' value='private' />
                      <div className='flex items-center space-x-2'>
                        <EyeOff className='h-4 w-4 text-red-400' />
                        <Label htmlFor='private' className='font-medium'>
                          Private
                        </Label>
                      </div>
                      <p className='text-sm text-muted-foreground ml-2'>
                        Only you and admins can see your profile
                      </p>
                    </div>
                  </RadioGroup>
                </div>

                <div className='space-y-4 pt-4 border-t'>
                  <h3 className='font-medium'>Profile information visibility</h3>
                  <div className='space-y-3'>
                    <div className='flex justify-between items-center p-3 rounded-md bg-muted/50 border'>
                      <div>
                        <h4>Email Address</h4>
                        <p className='text-sm text-muted-foreground'>
                          Show your email address on your profile
                        </p>
                      </div>
                      <Switch id='email-visible' />
                    </div>

                    <div className='flex justify-between items-center p-3 rounded-md bg-muted/50 border'>
                      <div>
                        <h4>Job Title</h4>
                        <p className='text-sm text-muted-foreground'>
                          Show your job title on your profile
                        </p>
                      </div>
                      <Switch id='job-visible' defaultChecked />
                    </div>

                    <div className='flex justify-between items-center p-3 rounded-md bg-muted/50 border'>
                      <div>
                        <h4>Activity Status</h4>
                        <p className='text-sm text-muted-foreground'>
                          Show when you&apos;re active on the platform
                        </p>
                      </div>
                      <Switch id='activity-visible' defaultChecked />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className='flex items-center space-x-2'>
                  <Lock className='h-5 w-5 text-[var(--primary)]' />
                  <CardTitle className='text-xl'>Privacy Controls</CardTitle>
                </div>
                <CardDescription>
                  Manage how your information is used within the platform
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex justify-between items-center p-3 rounded-md bg-muted/50 border'>
                  <div>
                    <h4>Search Visibility</h4>
                    <p className='text-sm text-muted-foreground'>
                      Allow others to find you in search results
                    </p>
                  </div>
                  <Switch id='search-visible' defaultChecked />
                </div>

                <div className='flex justify-between items-center p-3 rounded-md bg-muted/50 border'>
                  <div>
                    <h4>Mention Notifications</h4>
                    <p className='text-sm text-muted-foreground'>
                      Allow others to mention you in comments and posts
                    </p>
                  </div>
                  <Switch id='mentions' defaultChecked />
                </div>

                <div className='flex justify-between items-center p-3 rounded-md bg-muted/50 border'>
                  <div>
                    <h4>Analytics Tracking</h4>
                    <p className='text-sm text-muted-foreground'>
                      Allow us to collect usage data to improve your experience
                    </p>
                  </div>
                  <Switch id='analytics' defaultChecked />
                </div>

                <Button variant='outline' className='mt-2'>
                  Advanced Privacy Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='data'>
          <Card>
            <CardHeader>
              <div className='flex items-center space-x-2'>
                <Database className='h-5 w-5 text-[var(--primary)]' />
                <CardTitle className='text-xl'>Data Management</CardTitle>
              </div>
              <CardDescription>Control and manage your personal data</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='flex justify-between items-center p-4 rounded-md bg-muted/50 border'>
                  <div>
                    <h3 className='font-medium'>Export Your Data</h3>
                    <p className='text-sm text-muted-foreground'>
                      Download a copy of your personal data in a machine-readable format
                    </p>
                  </div>
                  <Button variant='outline' size='sm'>
                    <Download className='mr-2 h-4 w-4' />
                    Export
                  </Button>
                </div>

                <div className='flex justify-between items-center p-4 rounded-md bg-muted/50 border'>
                  <div>
                    <h3 className='font-medium'>Data Retention</h3>
                    <p className='text-sm text-muted-foreground'>
                      Control how long we keep your data
                    </p>
                  </div>
                  <Button variant='outline' size='sm'>
                    Configure
                  </Button>
                </div>

                <div className='flex justify-between items-center p-4 rounded-md bg-muted/50 border'>
                  <div>
                    <h3 className='font-medium text-red-500'>Delete Account</h3>
                    <p className='text-sm text-muted-foreground'>
                      Permanently delete your account and all associated data
                    </p>
                  </div>
                  <Button variant='destructive' size='sm'>
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </SettingsContainer>
  )
}
