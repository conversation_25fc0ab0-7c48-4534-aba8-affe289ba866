'use client'

import { <PERSON><PERSON> } from '@/components/core/button'
import { Card, CardContent } from '@/components/core/card'
import StatusCard from '@/components/settings/StatusCard'
import { Github, Mail, ShieldCheck, Slack, X, GitBranch, MessageSquare } from 'lucide-react'
import { useTheme } from 'next-themes'

const connectedAccounts = [
  {
    id: 'github',
    name: 'GitH<PERSON>',
    icon: Github,
    iconBg: 'bg-[var(--accent)]/20',
    isConnected: true,
    lastSynced: '3 hours ago',
    username: 'johndo<PERSON>',
  },
  {
    id: 'slack',
    name: 'Slack',
    icon: Slack,
    iconBg: 'bg-[var(--accent)]/20',
    isConnected: true,
    lastSynced: '1 day ago',
    username: 'john.doe',
  },
  {
    id: 'discord',
    name: 'Discord',
    icon: MessageSquare,
    iconBg: 'bg-[var(--accent)]/20',
    isConnected: false,
    lastSynced: null,
    username: null,
  },
  {
    id: 'gitlab',
    name: 'Git<PERSON><PERSON>',
    icon: GitBranch,
    iconBg: 'bg-[var(--accent)]/20',
    isConnected: false,
    lastSynced: null,
    username: null,
  },
]

// Profile information
const profileDetails = {
  email: '<EMAIL>',
  emailVerified: true,
  twoFactorEnabled: true,
  lastUpdated: '2 weeks ago',
}

export default function ConnectedAccountsPage() {
  const { resolvedTheme: _resolvedTheme } = useTheme()

  return (
    <div className='space-y-6'>
      <div>
        <h1 className='text-2xl font-bold tracking-tight'>Connected Accounts</h1>
        <p className='text-muted-foreground'>Manage your connected accounts and services</p>
      </div>

      <StatusCard
        title='Account Security'
        description='Your account security status and verification'
        icon={<ShieldCheck className='h-5 w-5 text-[var(--accent)]' />}
        items={[
          { label: 'Email', value: profileDetails.email },
          { label: 'Email Verified', value: profileDetails.emailVerified ? 'Yes' : 'No' },
          {
            label: 'Two-Factor Authentication',
            value: profileDetails.twoFactorEnabled ? 'Enabled' : 'Disabled',
          },
          { label: 'Last Updated', value: profileDetails.lastUpdated },
        ]}
        actionButton={
          <Button size='sm' variant='outline'>
            <Mail className='mr-2 h-4 w-4' />
            Change Email
          </Button>
        }
      />

      <div className='grid gap-4'>
        {connectedAccounts.map(account => (
          <Card key={account.id} className='overflow-hidden'>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-4'>
                  <div className={`p-2 rounded-full ${account.iconBg}`}>
                    <account.icon className='h-5 w-5 text-[var(--accent)]' />
                  </div>
                  <div>
                    <h3 className='font-medium'>{account.name}</h3>
                    {account.isConnected ? (
                      <p className='text-sm text-muted-foreground'>
                        Connected as <span className='font-medium'>{account.username}</span>
                      </p>
                    ) : (
                      <p className='text-sm text-muted-foreground'>Not connected</p>
                    )}
                  </div>
                </div>

                <div>
                  {account.isConnected ? (
                    <div className='flex items-center gap-4'>
                      <p className='text-sm text-muted-foreground hidden sm:inline-block'>
                        Last synced {account.lastSynced}
                      </p>
                      <Button variant='outline' size='sm'>
                        <X className='mr-2 h-4 w-4' />
                        Disconnect
                      </Button>
                    </div>
                  ) : (
                    <Button size='sm'>Connect {account.name}</Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className='border-t pt-6'>
        <h2 className='text-xl font-semibold mb-4'>Authorization Details</h2>
        <p className='text-muted-foreground mb-6'>
          Connecting these services allows Emynent to access limited information from your accounts
          to provide integrated experiences. We never post on your behalf without your explicit
          permission. You can revoke access at any time.
        </p>

        <div className='grid gap-6 md:grid-cols-2'>
          <div className='space-y-2'>
            <h3 className='font-medium'>GitHub Integration</h3>
            <ul className='text-sm text-muted-foreground list-disc pl-6 space-y-1'>
              <li>Read your public profile information</li>
              <li>Access repositories and commit history</li>
              <li>Show commit activity on your career timeline</li>
            </ul>
          </div>

          <div className='space-y-2'>
            <h3 className='font-medium'>Slack Integration</h3>
            <ul className='text-sm text-muted-foreground list-disc pl-6 space-y-1'>
              <li>Access your workspace name and profile</li>
              <li>Send notification updates to channels</li>
              <li>Receive notifications about mentions</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
