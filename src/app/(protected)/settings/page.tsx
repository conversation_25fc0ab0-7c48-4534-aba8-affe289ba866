import { Metadata } from 'next'
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON> as _<PERSON><PERSON>ooter,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/core/card'
import RecentlyChangedSettings from '@/components/settings/RecentlyChangedSettings'

export const metadata: Metadata = {
  title: 'Settings | Emynent',
  description: 'Configure your Emynent platform settings',
}

export default function SettingsPage() {
  return (
    <div className='space-y-8'>
      <div>
        <h1 className='text-3xl font-bold tracking-tight'>Settings</h1>
        <p className='text-muted-foreground mt-2'>Manage your account settings and preferences.</p>
      </div>

      <div className='grid gap-6'>
        <Card>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>
              Explore the various sections to customize your experience.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className='text-sm'>
              Use the sidebar to navigate between different settings categories. The most commonly
              accessed sections include:
            </p>
            <ul className='list-disc ml-6 mt-2 text-sm'>
              <li>Personal Info - Update your name, email, and profile picture</li>
              <li>Security & Login - Manage your password and login methods</li>
              <li>Appearance - Customize the visual theme and layout</li>
              <li>Notifications - Configure how you receive notifications</li>
            </ul>
          </CardContent>
        </Card>

        <RecentlyChangedSettings />
      </div>
    </div>
  )
}
