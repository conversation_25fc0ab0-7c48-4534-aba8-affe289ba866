'use client'

import { useState, useEffect } from 'react'
import { useTheme } from 'next-themes'
import PageHeader from '@/components/settings/PageHeader'
import SettingsContainer from '@/components/settings/SettingsContainer'
import { Card, CardContent } from '@/components/core/card'
import { But<PERSON> } from '@/components/core/button'
import { Input } from '@/components/core/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/core/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/core/dropdown-menu'
import { Badge } from '@/components/core/badge'
import { Building2, MoreHorizontal, Search, Users } from 'lucide-react'

// Mock department data
const departments = [
  {
    id: '1',
    name: 'Engineering',
    description: 'Software development and technical operations',
    members: 24,
    subDepartments: 3,
    status: 'Active',
  },
  {
    id: '2',
    name: 'Marketing',
    description: 'Brand management and marketing campaigns',
    members: 12,
    subDepartments: 2,
    status: 'Active',
  },
  {
    id: '3',
    name: 'Human Resources',
    description: 'Employee management and recruitment',
    members: 8,
    subDepartments: 1,
    status: 'Active',
  },
  {
    id: '4',
    name: 'Finance',
    description: 'Budget management and financial operations',
    members: 10,
    subDepartments: 2,
    status: 'Active',
  },
  {
    id: '5',
    name: 'Research & Development',
    description: 'Innovation and product research',
    members: 15,
    subDepartments: 0,
    status: 'Inactive',
  },
]

// Function to get status color
const getStatusColor = (status: string) => {
  return status === 'Active' ? 'bg-green-500 text-white' : 'bg-muted text-muted-foreground'
}

export default function DepartmentsPage() {
  const { _theme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Handle hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Determine theme-based classes
  const _isDark = mounted && resolvedTheme === 'dark'
  const cardClass = 'bg-card border-border'
  const tableHeaderClass = 'bg-muted/50'
  const tableRowHoverClass = 'hover:bg-muted/50 border-border/50'
  const tableContainerClass = 'border-border'

  if (!mounted) return null

  return (
    <SettingsContainer>
      <PageHeader
        title='Department Management'
        description='Create, edit, and organize departments to reflect your organizational structure'
      />

      <Card className={cardClass}>
        <CardContent className='p-6'>
          <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6'>
            <div className='relative flex-1 max-w-md'>
              <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
              <Input placeholder='Search departments...' className='pl-8 w-full' />
            </div>

            <Button className='bg-[var(--primary)] hover:bg-[var(--primary)]/90 text-[var(--primary-foreground)]'>
              <Building2 className='mr-2 h-4 w-4' />
              Add Department
            </Button>
          </div>

          <div className={`rounded-md border overflow-hidden ${tableContainerClass}`}>
            <Table>
              <TableHeader className={tableHeaderClass}>
                <TableRow>
                  <TableHead className='w-[250px]'>Department</TableHead>
                  <TableHead className='hidden md:table-cell'>Description</TableHead>
                  <TableHead>Members</TableHead>
                  <TableHead className='hidden md:table-cell'>Sub-departments</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='w-[50px]'></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {departments.map(dept => (
                  <TableRow key={dept.id} className={tableRowHoverClass}>
                    <TableCell className='font-medium'>
                      <div className='flex items-center gap-3'>
                        <div className='h-8 w-8 rounded-md flex items-center justify-center bg-[var(--primary)]/20'>
                          <Building2 className='h-4 w-4 text-[var(--primary)]' />
                        </div>
                        <div className='text-sm font-medium'>{dept.name}</div>
                      </div>
                    </TableCell>
                    <TableCell className='hidden md:table-cell text-sm text-muted-foreground'>
                      {dept.description}
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center gap-1'>
                        <Users className='h-3.5 w-3.5 text-muted-foreground' />
                        <span>{dept.members}</span>
                      </div>
                    </TableCell>
                    <TableCell className='hidden md:table-cell'>{dept.subDepartments}</TableCell>
                    <TableCell>
                      <Badge variant='outline' className={getStatusColor(dept.status)}>
                        {dept.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <span className='sr-only'>Open menu</span>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem>Edit Department</DropdownMenuItem>
                          <DropdownMenuItem>View Members</DropdownMenuItem>
                          <DropdownMenuItem>Add Sub-department</DropdownMenuItem>
                          <DropdownMenuItem className='text-destructive'>
                            Deactivate Department
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className='flex items-center justify-between mt-4'>
            <div className='text-sm text-muted-foreground'>
              Showing <span className='font-medium'>5</span> of{' '}
              <span className='font-medium'>5</span> departments
            </div>
            <div className='flex items-center space-x-2'>
              <Button variant='outline' size='sm' className='border-border'>
                Previous
              </Button>
              <Button variant='outline' size='sm' className='border-border'>
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </SettingsContainer>
  )
}
