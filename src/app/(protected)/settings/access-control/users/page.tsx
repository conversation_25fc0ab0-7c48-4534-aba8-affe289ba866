'use client'

import { Ava<PERSON>, AvatarFallback, AvatarImage } from '@/components/core/avatar'
import { Badge } from '@/components/core/badge'
import { Button } from '@/components/core/button'
import { Card, CardContent } from '@/components/core/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/core/dialog'
import { Input } from '@/components/core/input'
import { Label } from '@/components/core/label'
import { Switch } from '@/components/core/switch'
import { Tabs, TabsList, TabsTrigger } from '@/components/core/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { PlusCircle, Search, Settings, Shield, Trash2, User } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState } from 'react'

// Mock data (would normally come from your database)
const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'ADMIN',
    status: 'ACTIVE',
    departments: ['Engineering', 'Product'],
    lastActive: '2023-12-15T10:30:00Z',
    imageUrl: '/avatars/user-01.jpg',
  },
  {
    id: '2',
    name: 'Sarah Miller',
    email: '<EMAIL>',
    role: 'SUPERADMIN',
    status: 'ACTIVE',
    departments: ['Leadership', 'Finance'],
    lastActive: '2023-12-16T14:22:00Z',
    imageUrl: null,
  },
  {
    id: '3',
    name: 'Robert Chen',
    email: '<EMAIL>',
    role: 'USER',
    status: 'ACTIVE',
    departments: ['Marketing'],
    lastActive: '2023-12-14T09:15:00Z',
    imageUrl: '/avatars/user-03.jpg',
  },
  {
    id: '4',
    name: 'Maria Garcia',
    email: '<EMAIL>',
    role: 'USER',
    status: 'INACTIVE',
    departments: ['HR', 'Operations'],
    lastActive: '2023-11-30T16:45:00Z',
    imageUrl: null,
  },
  {
    id: '5',
    name: 'James Wilson',
    email: '<EMAIL>',
    role: 'MANAGER',
    status: 'ACTIVE',
    departments: ['Sales'],
    lastActive: '2023-12-16T11:10:00Z',
    imageUrl: '/avatars/user-05.jpg',
  },
]

export default function UserManagementPage() {
  const { resolvedTheme } = useTheme()
  const [users, _setUsers] = useState(mockUsers)
  const [filter, setFilter] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')

  // Filter users based on tab and search query
  const filteredUsers = users.filter(user => {
    // Apply tab filter
    if (filter === 'active' && user.status !== 'ACTIVE') return false
    if (filter === 'inactive' && user.status !== 'INACTIVE') return false
    if (filter === 'admin' && user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') return false

    // Apply search
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        user.role.toLowerCase().includes(query) ||
        user.departments.some(dept => dept.toLowerCase().includes(query))
      )
    }

    return true
  })

  // Get role badge
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'SUPERADMIN':
        return (
          <Badge className='bg-[var(--accent)] text-[var(--accent-foreground)]'>Super Admin</Badge>
        )
      case 'ADMIN':
        return <Badge variant='secondary'>Admin</Badge>
      case 'MANAGER':
        return <Badge variant='outline'>Manager</Badge>
      default:
        return (
          <Badge variant='outline' className='bg-muted/50'>
            User
          </Badge>
        )
    }
  }

  // Card styles for consistent theming
  const getCardStyle = () => {
    return resolvedTheme === 'dark'
      ? 'hover:bg-muted/50 border-muted'
      : 'hover:bg-[var(--accent)]/5 border-[var(--accent)]/10'
  }

  return (
    <div className='space-y-6'>
      <div className='flex justify-between items-center'>
        <div>
          <h1 className='text-2xl font-bold'>User Management</h1>
          <p className='text-muted-foreground'>Manage user accounts, roles and permissions</p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className='mr-2 h-4 w-4' />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
              <DialogDescription>
                Create a new user account and assign permissions.
              </DialogDescription>
            </DialogHeader>

            <div className='grid gap-4 py-4'>
              <div className='grid grid-cols-4 items-center gap-4'>
                <Label htmlFor='name' className='text-right'>
                  Name
                </Label>
                <Input id='name' className='col-span-3' />
              </div>
              <div className='grid grid-cols-4 items-center gap-4'>
                <Label htmlFor='email' className='text-right'>
                  Email
                </Label>
                <Input id='email' type='email' className='col-span-3' />
              </div>
              <div className='grid grid-cols-4 items-center gap-4'>
                <Label htmlFor='role' className='text-right'>
                  Role
                </Label>
                <Select>
                  <SelectTrigger className='col-span-3'>
                    <SelectValue placeholder='Select role' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='USER'>User</SelectItem>
                    <SelectItem value='MANAGER'>Manager</SelectItem>
                    <SelectItem value='ADMIN'>Admin</SelectItem>
                    <SelectItem value='SUPERADMIN'>Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='grid grid-cols-4 items-center gap-4'>
                <Label htmlFor='department' className='text-right'>
                  Department
                </Label>
                <Select>
                  <SelectTrigger className='col-span-3'>
                    <SelectValue placeholder='Select department' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='engineering'>Engineering</SelectItem>
                    <SelectItem value='product'>Product</SelectItem>
                    <SelectItem value='marketing'>Marketing</SelectItem>
                    <SelectItem value='sales'>Sales</SelectItem>
                    <SelectItem value='hr'>Human Resources</SelectItem>
                    <SelectItem value='finance'>Finance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='grid grid-cols-4 items-center gap-4'>
                <Label htmlFor='status' className='text-right'>
                  Active
                </Label>
                <div className='col-span-3'>
                  <Switch id='status' defaultChecked />
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant='outline'>Cancel</Button>
              <Button>Create User</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className='flex flex-col md:flex-row gap-4 justify-between'>
        <div className='relative w-full md:w-96'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            type='search'
            placeholder='Search users...'
            className='pl-9'
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />
        </div>

        <Tabs defaultValue='all' className='w-full md:w-auto' onValueChange={setFilter}>
          <TabsList>
            <TabsTrigger value='all'>All Users</TabsTrigger>
            <TabsTrigger value='active'>Active</TabsTrigger>
            <TabsTrigger value='inactive'>Inactive</TabsTrigger>
            <TabsTrigger value='admin'>Admins</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className='grid gap-4'>
        {filteredUsers.map(user => (
          <Card key={user.id} className={cn('overflow-hidden transition-colors', getCardStyle())}>
            <CardContent className='p-0'>
              <div className='flex flex-col md:flex-row items-start md:items-center gap-4 p-6'>
                <Avatar className='h-12 w-12 border'>
                  <AvatarImage src={user.imageUrl || ''} alt={user.name} />
                  <AvatarFallback className='bg-muted'>
                    {user.name
                      .split(' ')
                      .map(n => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>

                <div className='flex-1 space-y-1'>
                  <div className='flex items-center gap-2'>
                    <h3 className='font-medium'>{user.name}</h3>
                    {getRoleBadge(user.role)}
                    {user.status === 'INACTIVE' && (
                      <Badge variant='outline' className='bg-muted text-muted-foreground'>
                        Inactive
                      </Badge>
                    )}
                  </div>
                  <p className='text-sm text-muted-foreground'>{user.email}</p>
                  <div className='pt-1 flex flex-wrap gap-1'>
                    {user.departments.map(dept => (
                      <Badge variant='outline' className='text-xs bg-muted/50' key={dept}>
                        {dept}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className='flex gap-2 md:ml-auto'>
                  <Button size='sm' variant='outline'>
                    <Shield className='h-4 w-4 mr-2' />
                    Permissions
                  </Button>
                  <Button size='sm' variant='outline'>
                    <Settings className='h-4 w-4 mr-2' />
                    Edit
                  </Button>
                  <Button size='sm' variant='outline' className='text-destructive'>
                    <Trash2 className='h-4 w-4' />
                    <span className='sr-only'>Delete</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredUsers.length === 0 && (
          <Card className='py-12'>
            <CardContent className='flex flex-col items-center justify-center text-center'>
              <User className='h-12 w-12 text-muted-foreground mb-4' />
              <h3 className='text-lg font-medium'>No users found</h3>
              <p className='text-muted-foreground text-sm mt-1'>
                {searchQuery
                  ? 'No users match your search criteria. Try a different search.'
                  : 'No users available in this category.'}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
