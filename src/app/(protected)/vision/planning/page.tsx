import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Planning | Emynent',
  description: 'Strategic planning and roadmap management for organizational growth',
}

export default function VisionPlanningPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Planning</h1>
          <p className='text-muted-foreground'>
            Strategic planning and roadmap management for organizational growth
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Planning Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Active Plans</h3>
                <span className='text-2xl'>📋</span>
              </div>
              <div className='text-2xl font-bold text-foreground'>8</div>
              <p className='text-xs text-muted-foreground'>Strategic plans</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Completion Rate</h3>
                <span className='text-2xl'>✅</span>
              </div>
              <div className='text-2xl font-bold text-green-600'>89%</div>
              <p className='text-xs text-muted-foreground'>On schedule</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Milestones</h3>
                <span className='text-2xl'>🎯</span>
              </div>
              <div className='text-2xl font-bold text-blue-600'>24</div>
              <p className='text-xs text-muted-foreground'>This quarter</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Resource Utilization</h3>
                <span className='text-2xl'>⚡</span>
              </div>
              <div className='text-2xl font-bold text-purple-600'>92%</div>
              <p className='text-xs text-muted-foreground'>Efficiency</p>
            </div>
          </div>

          {/* Strategic Roadmap */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>Strategic Roadmap</h2>
            <div className='space-y-6'>
              {/* Q1 2024 */}
              <div className='relative'>
                <div className='flex items-center space-x-4 mb-3'>
                  <div className='w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center'>
                    <span className='text-green-600 font-semibold'>Q1</span>
                  </div>
                  <div>
                    <h3 className='font-semibold text-foreground'>Foundation & Infrastructure</h3>
                    <p className='text-sm text-muted-foreground'>January - March 2024</p>
                  </div>
                  <div className='ml-auto'>
                    <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                      Completed
                    </span>
                  </div>
                </div>
                <div className='ml-16 grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>System Modernization</h4>
                    <p className='text-xs text-muted-foreground mt-1'>
                      Core infrastructure upgrade
                    </p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-green-500 h-1.5 rounded-full'
                        style={{ width: '100%' }}
                      ></div>
                    </div>
                  </div>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>Team Expansion</h4>
                    <p className='text-xs text-muted-foreground mt-1'>Key role recruitment</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-green-500 h-1.5 rounded-full'
                        style={{ width: '100%' }}
                      ></div>
                    </div>
                  </div>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>Process Optimization</h4>
                    <p className='text-xs text-muted-foreground mt-1'>Workflow improvements</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-green-500 h-1.5 rounded-full'
                        style={{ width: '100%' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Q2 2024 */}
              <div className='relative'>
                <div className='flex items-center space-x-4 mb-3'>
                  <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center'>
                    <span className='text-blue-600 font-semibold'>Q2</span>
                  </div>
                  <div>
                    <h3 className='font-semibold text-foreground'>Growth & Expansion</h3>
                    <p className='text-sm text-muted-foreground'>April - June 2024</p>
                  </div>
                  <div className='ml-auto'>
                    <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                      In Progress
                    </span>
                  </div>
                </div>
                <div className='ml-16 grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>Market Entry</h4>
                    <p className='text-xs text-muted-foreground mt-1'>New geographic markets</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-blue-500 h-1.5 rounded-full'
                        style={{ width: '75%' }}
                      ></div>
                    </div>
                  </div>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>Product Launch</h4>
                    <p className='text-xs text-muted-foreground mt-1'>Next-gen platform</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-blue-500 h-1.5 rounded-full'
                        style={{ width: '60%' }}
                      ></div>
                    </div>
                  </div>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>Partnership Development</h4>
                    <p className='text-xs text-muted-foreground mt-1'>Strategic alliances</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-blue-500 h-1.5 rounded-full'
                        style={{ width: '45%' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Q3 2024 */}
              <div className='relative'>
                <div className='flex items-center space-x-4 mb-3'>
                  <div className='w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center'>
                    <span className='text-orange-600 font-semibold'>Q3</span>
                  </div>
                  <div>
                    <h3 className='font-semibold text-foreground'>Innovation & Scale</h3>
                    <p className='text-sm text-muted-foreground'>July - September 2024</p>
                  </div>
                  <div className='ml-auto'>
                    <span className='bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full text-xs font-medium'>
                      Planned
                    </span>
                  </div>
                </div>
                <div className='ml-16 grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>AI Integration</h4>
                    <p className='text-xs text-muted-foreground mt-1'>Advanced automation</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-orange-500 h-1.5 rounded-full'
                        style={{ width: '25%' }}
                      ></div>
                    </div>
                  </div>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>Global Scaling</h4>
                    <p className='text-xs text-muted-foreground mt-1'>International expansion</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-orange-500 h-1.5 rounded-full'
                        style={{ width: '15%' }}
                      ></div>
                    </div>
                  </div>
                  <div className='bg-muted/50 rounded-lg p-3'>
                    <h4 className='font-medium text-foreground text-sm'>Innovation Lab</h4>
                    <p className='text-xs text-muted-foreground mt-1'>R&D initiatives</p>
                    <div className='w-full bg-muted rounded-full h-1.5 mt-2'>
                      <div
                        className='bg-orange-500 h-1.5 rounded-full'
                        style={{ width: '10%' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Resource Allocation */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Resource Allocation</h2>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Engineering</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div className='bg-blue-500 h-2 rounded-full' style={{ width: '45%' }}></div>
                    </div>
                    <span className='text-sm font-medium text-blue-600'>45%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Marketing</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div className='bg-green-500 h-2 rounded-full' style={{ width: '25%' }}></div>
                    </div>
                    <span className='text-sm font-medium text-green-600'>25%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Sales</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div
                        className='bg-purple-500 h-2 rounded-full'
                        style={{ width: '20%' }}
                      ></div>
                    </div>
                    <span className='text-sm font-medium text-purple-600'>20%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Operations</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div
                        className='bg-orange-500 h-2 rounded-full'
                        style={{ width: '10%' }}
                      ></div>
                    </div>
                    <span className='text-sm font-medium text-orange-600'>10%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Planning Metrics */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Planning Metrics</h2>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Plan Accuracy</div>
                    <div className='text-sm text-muted-foreground'>Forecast vs actual</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-green-600'>94%</div>
                    <div className='text-xs text-muted-foreground'>Last quarter</div>
                  </div>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Timeline Adherence</div>
                    <div className='text-sm text-muted-foreground'>On-time delivery</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-blue-600'>89%</div>
                    <div className='text-xs text-muted-foreground'>Current quarter</div>
                  </div>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Budget Efficiency</div>
                    <div className='text-sm text-muted-foreground'>Cost optimization</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-purple-600'>96%</div>
                    <div className='text-xs text-muted-foreground'>Within budget</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI Planning Assistant */}
          <div className='bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 rounded-lg p-6 border border-green-200 dark:border-green-800'>
            <div className='flex items-center space-x-3 mb-4'>
              <div className='w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center'>
                <span className='text-white text-sm'>🤖</span>
              </div>
              <h2 className='text-xl font-semibold text-foreground'>AI Planning Assistant</h2>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Schedule Optimization</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  AI suggests moving Product Launch milestone 2 weeks earlier to optimize resource
                  utilization.
                </p>
                <button className='text-xs bg-green-600 text-white px-3 py-1 rounded-full hover:bg-green-700 transition-colors'>
                  Apply Suggestion
                </button>
              </div>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Risk Mitigation</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  Potential bottleneck identified in Q3. Recommend adding 2 additional engineers to
                  critical path.
                </p>
                <button className='text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 transition-colors'>
                  View Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
