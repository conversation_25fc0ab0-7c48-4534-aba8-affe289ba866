import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Future Roadmap | Emynent',
  description: 'Visualize and plan the future direction of the organization',
}

export default function FutureRoadmapPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Future Roadmap</h1>
          <p className='text-muted-foreground'>
            Chart the course for organizational growth and transformation with strategic roadmaps
            and future planning.
          </p>
        </div>

        <div className='grid gap-6'>
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Strategic Roadmap 2024-2027</h2>
            <div className='space-y-6'>
              <div className='relative'>
                <div className='absolute left-4 top-8 bottom-0 w-0.5 bg-border'></div>

                <div className='relative flex items-start gap-4 pb-6'>
                  <div className='w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    ✓
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-green-700'>2024 Q1-Q2: Foundation</h3>
                    <p className='text-sm text-muted-foreground mb-2'>
                      Establish core infrastructure and team capabilities
                    </p>
                    <div className='space-y-1'>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-green-500 rounded-full'></div>
                        <span>Digital transformation initiative launch</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-green-500 rounded-full'></div>
                        <span>Team restructuring and skill development</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-green-500 rounded-full'></div>
                        <span>Technology stack modernization</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='relative flex items-start gap-4 pb-6'>
                  <div className='w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    2
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-blue-700'>2024 Q3-Q4: Expansion</h3>
                    <p className='text-sm text-muted-foreground mb-2'>
                      Scale operations and enter new markets
                    </p>
                    <div className='space-y-1'>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-blue-500 rounded-full'></div>
                        <span>Market expansion into APAC region</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-blue-500 rounded-full'></div>
                        <span>Product portfolio diversification</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-blue-500 rounded-full'></div>
                        <span>Strategic partnerships development</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='relative flex items-start gap-4 pb-6'>
                  <div className='w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    3
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-purple-700'>2025: Innovation</h3>
                    <p className='text-sm text-muted-foreground mb-2'>
                      Lead industry innovation and technological advancement
                    </p>
                    <div className='space-y-1'>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-purple-500 rounded-full'></div>
                        <span>AI and machine learning integration</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-purple-500 rounded-full'></div>
                        <span>Sustainable technology initiatives</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-purple-500 rounded-full'></div>
                        <span>Industry thought leadership</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='relative flex items-start gap-4'>
                  <div className='w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    4
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-orange-700'>2026-2027: Transformation</h3>
                    <p className='text-sm text-muted-foreground mb-2'>
                      Complete organizational transformation and market leadership
                    </p>
                    <div className='space-y-1'>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-orange-500 rounded-full'></div>
                        <span>Global market leadership position</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-orange-500 rounded-full'></div>
                        <span>Next-generation product launch</span>
                      </div>
                      <div className='flex items-center gap-2 text-xs'>
                        <div className='w-1.5 h-1.5 bg-orange-500 rounded-full'></div>
                        <span>Ecosystem platform development</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Key Performance Indicators</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Revenue Growth</h3>
                <p className='text-2xl font-bold text-primary'>35%</p>
                <p className='text-sm text-muted-foreground'>Target by 2027</p>
              </div>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Market Share</h3>
                <p className='text-2xl font-bold text-primary'>25%</p>
                <p className='text-sm text-muted-foreground'>Industry leadership</p>
              </div>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Innovation Index</h3>
                <p className='text-2xl font-bold text-primary'>8.5</p>
                <p className='text-sm text-muted-foreground'>Out of 10</p>
              </div>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Sustainability Score</h3>
                <p className='text-2xl font-bold text-primary'>90%</p>
                <p className='text-sm text-muted-foreground'>ESG compliance</p>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Technology Roadmap</h2>
            <div className='space-y-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Cloud Infrastructure Migration</h3>
                  <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                    2024 Q3
                  </span>
                </div>
                <p className='text-sm text-muted-foreground'>
                  Complete migration to cloud-native architecture for improved scalability and
                  performance
                </p>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>AI/ML Platform Development</h3>
                  <span className='text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded'>
                    2025 Q1
                  </span>
                </div>
                <p className='text-sm text-muted-foreground'>
                  Build comprehensive AI/ML platform for data-driven decision making and automation
                </p>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>IoT Integration Framework</h3>
                  <span className='text-sm bg-green-100 text-green-800 px-2 py-1 rounded'>
                    2025 Q4
                  </span>
                </div>
                <p className='text-sm text-muted-foreground'>
                  Develop IoT ecosystem for enhanced product connectivity and smart features
                </p>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Quantum Computing Research</h3>
                  <span className='text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded'>
                    2026 Q2
                  </span>
                </div>
                <p className='text-sm text-muted-foreground'>
                  Explore quantum computing applications for complex optimization and security
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
