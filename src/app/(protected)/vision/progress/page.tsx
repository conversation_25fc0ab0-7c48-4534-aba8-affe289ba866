import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Progress | Emynent',
  description: 'Track progress toward vision achievement and strategic milestones',
}

export default function VisionProgressPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Progress</h1>
          <p className='text-muted-foreground'>
            Track progress toward vision achievement and strategic milestones
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Progress Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Vision Progress</h3>
                <span className='text-2xl'>🎯</span>
              </div>
              <div className='text-2xl font-bold text-green-600'>73%</div>
              <p className='text-xs text-muted-foreground'>Overall completion</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Milestones Hit</h3>
                <span className='text-2xl'>✅</span>
              </div>
              <div className='text-2xl font-bold text-blue-600'>18/24</div>
              <p className='text-xs text-muted-foreground'>This year</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Velocity</h3>
                <span className='text-2xl'>⚡</span>
              </div>
              <div className='text-2xl font-bold text-purple-600'>+12%</div>
              <p className='text-xs text-muted-foreground'>vs last quarter</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Risk Level</h3>
                <span className='text-2xl'>⚠️</span>
              </div>
              <div className='text-2xl font-bold text-orange-600'>Low</div>
              <p className='text-xs text-muted-foreground'>Current assessment</p>
            </div>
          </div>

          {/* Strategic Progress Timeline */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>
              Strategic Progress Timeline
            </h2>
            <div className='relative'>
              {/* Progress Bar */}
              <div className='w-full bg-muted rounded-full h-3 mb-6'>
                <div
                  className='bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full'
                  style={{ width: '73%' }}
                ></div>
              </div>

              {/* Timeline Markers */}
              <div className='flex justify-between items-center mb-8'>
                <div className='text-center'>
                  <div className='w-4 h-4 bg-green-500 rounded-full mx-auto mb-2'></div>
                  <div className='text-xs font-medium text-foreground'>Q1 2024</div>
                  <div className='text-xs text-muted-foreground'>Foundation</div>
                </div>
                <div className='text-center'>
                  <div className='w-4 h-4 bg-green-500 rounded-full mx-auto mb-2'></div>
                  <div className='text-xs font-medium text-foreground'>Q2 2024</div>
                  <div className='text-xs text-muted-foreground'>Growth</div>
                </div>
                <div className='text-center'>
                  <div className='w-4 h-4 bg-blue-500 rounded-full mx-auto mb-2'></div>
                  <div className='text-xs font-medium text-foreground'>Q3 2024</div>
                  <div className='text-xs text-muted-foreground'>Scale</div>
                </div>
                <div className='text-center'>
                  <div className='w-4 h-4 bg-muted border-2 border-blue-500 rounded-full mx-auto mb-2'></div>
                  <div className='text-xs font-medium text-foreground'>Q4 2024</div>
                  <div className='text-xs text-muted-foreground'>Optimize</div>
                </div>
              </div>

              {/* Detailed Progress Items */}
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800'>
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center'>
                      <span className='text-white text-sm'>✓</span>
                    </div>
                    <div>
                      <h3 className='font-medium text-foreground'>Platform Foundation</h3>
                      <p className='text-sm text-muted-foreground'>
                        Core infrastructure and team setup
                      </p>
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='text-sm font-medium text-green-600'>Completed</div>
                    <div className='text-xs text-muted-foreground'>March 2024</div>
                  </div>
                </div>

                <div className='flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800'>
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center'>
                      <span className='text-white text-sm'>⚡</span>
                    </div>
                    <div>
                      <h3 className='font-medium text-foreground'>Market Expansion</h3>
                      <p className='text-sm text-muted-foreground'>
                        Geographic growth and customer acquisition
                      </p>
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='text-sm font-medium text-blue-600'>In Progress</div>
                    <div className='text-xs text-muted-foreground'>75% complete</div>
                  </div>
                </div>

                <div className='flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-950/20 rounded-lg border border-orange-200 dark:border-orange-800'>
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center'>
                      <span className='text-white text-sm'>⏳</span>
                    </div>
                    <div>
                      <h3 className='font-medium text-foreground'>AI Integration</h3>
                      <p className='text-sm text-muted-foreground'>
                        Advanced intelligence and automation
                      </p>
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='text-sm font-medium text-orange-600'>Planned</div>
                    <div className='text-xs text-muted-foreground'>Q3 2024</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Key Performance Indicators */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>
                Key Performance Indicators
              </h2>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Revenue Growth</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div className='bg-green-500 h-2 rounded-full' style={{ width: '85%' }}></div>
                    </div>
                    <span className='text-sm font-medium text-green-600'>85%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Customer Satisfaction</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div className='bg-blue-500 h-2 rounded-full' style={{ width: '92%' }}></div>
                    </div>
                    <span className='text-sm font-medium text-blue-600'>92%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Market Share</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div
                        className='bg-purple-500 h-2 rounded-full'
                        style={{ width: '68%' }}
                      ></div>
                    </div>
                    <span className='text-sm font-medium text-purple-600'>68%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Team Productivity</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-24 bg-muted rounded-full h-2'>
                      <div
                        className='bg-orange-500 h-2 rounded-full'
                        style={{ width: '78%' }}
                      ></div>
                    </div>
                    <span className='text-sm font-medium text-orange-600'>78%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Blockers */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Progress Blockers</h2>
              <div className='space-y-3'>
                <div className='flex items-center space-x-3 p-3 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800'>
                  <div className='w-2 h-2 bg-red-500 rounded-full'></div>
                  <div className='flex-1'>
                    <div className='font-medium text-foreground text-sm'>Resource Constraints</div>
                    <div className='text-xs text-muted-foreground'>
                      Engineering team capacity at 95%
                    </div>
                  </div>
                  <span className='text-xs bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300 px-2 py-1 rounded-full'>
                    Critical
                  </span>
                </div>
                <div className='flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800'>
                  <div className='w-2 h-2 bg-yellow-500 rounded-full'></div>
                  <div className='flex-1'>
                    <div className='font-medium text-foreground text-sm'>Market Dependencies</div>
                    <div className='text-xs text-muted-foreground'>
                      Waiting for regulatory approval
                    </div>
                  </div>
                  <span className='text-xs bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full'>
                    Medium
                  </span>
                </div>
                <div className='flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                  <div className='flex-1'>
                    <div className='font-medium text-foreground text-sm'>
                      Technology Integration
                    </div>
                    <div className='text-xs text-muted-foreground'>Third-party API limitations</div>
                  </div>
                  <span className='text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full'>
                    Low
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Progress Analytics */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>Progress Analytics</h2>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='text-center'>
                <div className='w-20 h-20 mx-auto mb-3 relative'>
                  <svg className='w-20 h-20 transform -rotate-90' viewBox='0 0 36 36'>
                    <path
                      d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                      fill='none'
                      stroke='currentColor'
                      strokeWidth='2'
                      className='text-muted'
                    />
                    <path
                      d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                      fill='none'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeDasharray='73, 100'
                      className='text-green-500'
                    />
                  </svg>
                  <div className='absolute inset-0 flex items-center justify-center'>
                    <span className='text-lg font-bold text-foreground'>73%</span>
                  </div>
                </div>
                <h3 className='font-medium text-foreground'>Overall Progress</h3>
                <p className='text-sm text-muted-foreground'>Vision completion</p>
              </div>

              <div className='text-center'>
                <div className='w-20 h-20 mx-auto mb-3 relative'>
                  <svg className='w-20 h-20 transform -rotate-90' viewBox='0 0 36 36'>
                    <path
                      d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                      fill='none'
                      stroke='currentColor'
                      strokeWidth='2'
                      className='text-muted'
                    />
                    <path
                      d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                      fill='none'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeDasharray='89, 100'
                      className='text-blue-500'
                    />
                  </svg>
                  <div className='absolute inset-0 flex items-center justify-center'>
                    <span className='text-lg font-bold text-foreground'>89%</span>
                  </div>
                </div>
                <h3 className='font-medium text-foreground'>Timeline Adherence</h3>
                <p className='text-sm text-muted-foreground'>Schedule compliance</p>
              </div>

              <div className='text-center'>
                <div className='w-20 h-20 mx-auto mb-3 relative'>
                  <svg className='w-20 h-20 transform -rotate-90' viewBox='0 0 36 36'>
                    <path
                      d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                      fill='none'
                      stroke='currentColor'
                      strokeWidth='2'
                      className='text-muted'
                    />
                    <path
                      d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                      fill='none'
                      stroke='currentColor'
                      strokeWidth='2'
                      strokeDasharray='94, 100'
                      className='text-purple-500'
                    />
                  </svg>
                  <div className='absolute inset-0 flex items-center justify-center'>
                    <span className='text-lg font-bold text-foreground'>94%</span>
                  </div>
                </div>
                <h3 className='font-medium text-foreground'>Quality Score</h3>
                <p className='text-sm text-muted-foreground'>Deliverable quality</p>
              </div>
            </div>
          </div>

          {/* AI Progress Insights */}
          <div className='bg-gradient-to-r from-indigo-50 to-cyan-50 dark:from-indigo-950/20 dark:to-cyan-950/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-800'>
            <div className='flex items-center space-x-3 mb-4'>
              <div className='w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center'>
                <span className='text-white text-sm'>🤖</span>
              </div>
              <h2 className='text-xl font-semibold text-foreground'>AI Progress Insights</h2>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Acceleration Opportunities</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  AI predicts 15% faster completion if Engineering team is expanded by 2 senior
                  developers in Q3.
                </p>
                <button className='text-xs bg-indigo-600 text-white px-3 py-1 rounded-full hover:bg-indigo-700 transition-colors'>
                  View Analysis
                </button>
              </div>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Risk Mitigation</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  Current trajectory suggests 92% probability of meeting Q4 targets. Monitor
                  resource allocation closely.
                </p>
                <button className='text-xs bg-cyan-600 text-white px-3 py-1 rounded-full hover:bg-cyan-700 transition-colors'>
                  Set Alerts
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
