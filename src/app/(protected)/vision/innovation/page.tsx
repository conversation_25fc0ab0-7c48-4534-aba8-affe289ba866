import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Innovation | Emynent',
  description: 'Drive innovation and foster creative thinking across the organization',
}

export default function InnovationPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Innovation</h1>
          <p className='text-muted-foreground'>
            Foster a culture of innovation, creativity, and continuous improvement across all levels
            of the organization.
          </p>
        </div>

        <div className='grid gap-6'>
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Innovation Pipeline</h2>
            <div className='space-y-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>AI-Powered Customer Support</h3>
                  <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                    Development
                  </span>
                </div>
                <p className='text-sm text-muted-foreground mb-3'>
                  Implement machine learning algorithms to enhance customer support efficiency and
                  satisfaction
                </p>
                <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                  <span>Submitted by: Sarah Chen</span>
                  <span>•</span>
                  <span>Impact Score: 8.5/10</span>
                  <span>•</span>
                  <span>Est. ROI: 250%</span>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Blockchain Supply Chain Tracking</h3>
                  <span className='text-sm bg-green-100 text-green-800 px-2 py-1 rounded'>
                    Evaluation
                  </span>
                </div>
                <p className='text-sm text-muted-foreground mb-3'>
                  Use blockchain technology to create transparent and secure supply chain tracking
                </p>
                <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                  <span>Submitted by: Mike Rodriguez</span>
                  <span>•</span>
                  <span>Impact Score: 7.2/10</span>
                  <span>•</span>
                  <span>Est. ROI: 180%</span>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Sustainable Packaging Initiative</h3>
                  <span className='text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded'>
                    Ideation
                  </span>
                </div>
                <p className='text-sm text-muted-foreground mb-3'>
                  Develop eco-friendly packaging solutions to reduce environmental impact
                </p>
                <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                  <span>Submitted by: Alex Morgan</span>
                  <span>•</span>
                  <span>Impact Score: 6.8/10</span>
                  <span>•</span>
                  <span>Est. ROI: 120%</span>
                </div>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Innovation Metrics</h2>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='text-center p-4 bg-muted rounded-lg'>
                <p className='text-2xl font-bold text-primary'>24</p>
                <p className='text-sm text-muted-foreground'>Ideas Submitted</p>
                <p className='text-xs text-green-600'>+15% this quarter</p>
              </div>
              <div className='text-center p-4 bg-muted rounded-lg'>
                <p className='text-2xl font-bold text-primary'>8</p>
                <p className='text-sm text-muted-foreground'>Projects in Development</p>
                <p className='text-xs text-blue-600'>3 launching soon</p>
              </div>
              <div className='text-center p-4 bg-muted rounded-lg'>
                <p className='text-2xl font-bold text-primary'>$2.4M</p>
                <p className='text-sm text-muted-foreground'>Innovation Budget</p>
                <p className='text-xs text-purple-600'>68% allocated</p>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Innovation Framework</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Idea Generation</h3>
                <p className='text-sm text-muted-foreground mb-2'>
                  Encourage creative thinking and idea submission from all employees
                </p>
                <div className='space-y-1'>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Innovation challenges</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Brainstorming sessions</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Cross-functional workshops</span>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Evaluation Process</h3>
                <p className='text-sm text-muted-foreground mb-2'>
                  Systematic assessment of ideas based on impact and feasibility
                </p>
                <div className='space-y-1'>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Impact assessment</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Technical feasibility</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>ROI analysis</span>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Rapid Prototyping</h3>
                <p className='text-sm text-muted-foreground mb-2'>
                  Quick development and testing of promising concepts
                </p>
                <div className='space-y-1'>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>MVP development</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>User testing</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Iterative improvement</span>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Implementation</h3>
                <p className='text-sm text-muted-foreground mb-2'>
                  Scale successful innovations across the organization
                </p>
                <div className='space-y-1'>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Resource allocation</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Change management</span>
                  </div>
                  <div className='flex items-center gap-2 text-xs'>
                    <div className='w-1.5 h-1.5 bg-primary rounded-full'></div>
                    <span>Success measurement</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
