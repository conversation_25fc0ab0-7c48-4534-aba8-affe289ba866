import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Alignment | Emynent',
  description: 'Organizational alignment and vision coherence across all levels',
}

export default function VisionAlignmentPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Alignment</h1>
          <p className='text-muted-foreground'>
            Organizational alignment and vision coherence across all levels
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Alignment Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Overall Alignment</h3>
                <span className='text-2xl'>🎯</span>
              </div>
              <div className='text-2xl font-bold text-green-600'>87%</div>
              <p className='text-xs text-muted-foreground'>Organization-wide</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Goal Coherence</h3>
                <span className='text-2xl'>🔗</span>
              </div>
              <div className='text-2xl font-bold text-blue-600'>92%</div>
              <p className='text-xs text-muted-foreground'>Cross-functional</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Communication Score</h3>
                <span className='text-2xl'>💬</span>
              </div>
              <div className='text-2xl font-bold text-purple-600'>89%</div>
              <p className='text-xs text-muted-foreground'>Clarity index</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Action Alignment</h3>
                <span className='text-2xl'>⚡</span>
              </div>
              <div className='text-2xl font-bold text-orange-600'>84%</div>
              <p className='text-xs text-muted-foreground'>Execution sync</p>
            </div>
          </div>

          {/* Alignment Matrix */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>Alignment Matrix</h2>
            <div className='overflow-x-auto'>
              <table className='w-full'>
                <thead>
                  <tr className='border-b'>
                    <th className='text-left py-3 font-medium text-muted-foreground'>Department</th>
                    <th className='text-center py-3 font-medium text-muted-foreground'>
                      Vision Understanding
                    </th>
                    <th className='text-center py-3 font-medium text-muted-foreground'>
                      Goal Alignment
                    </th>
                    <th className='text-center py-3 font-medium text-muted-foreground'>
                      Action Coherence
                    </th>
                    <th className='text-center py-3 font-medium text-muted-foreground'>
                      Overall Score
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className='border-b'>
                    <td className='py-3 font-medium text-foreground'>Engineering</td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        95%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        92%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        88%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        92%
                      </span>
                    </td>
                  </tr>
                  <tr className='border-b'>
                    <td className='py-3 font-medium text-foreground'>Product</td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        94%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        96%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        91%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        94%
                      </span>
                    </td>
                  </tr>
                  <tr className='border-b'>
                    <td className='py-3 font-medium text-foreground'>Marketing</td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        89%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        87%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        85%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        87%
                      </span>
                    </td>
                  </tr>
                  <tr className='border-b'>
                    <td className='py-3 font-medium text-foreground'>Sales</td>
                    <td className='text-center py-3'>
                      <span className='bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full text-xs font-medium'>
                        82%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        86%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full text-xs font-medium'>
                        79%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full text-xs font-medium'>
                        82%
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td className='py-3 font-medium text-foreground'>Operations</td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        88%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium'>
                        90%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        87%
                      </span>
                    </td>
                    <td className='text-center py-3'>
                      <span className='bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium'>
                        88%
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Alignment Trends */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Alignment Trends</h2>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>This Quarter</div>
                    <div className='text-sm text-muted-foreground'>Q2 2024</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-green-600'>87%</div>
                    <div className='text-xs text-green-600'>+3% from Q1</div>
                  </div>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Last Quarter</div>
                    <div className='text-sm text-muted-foreground'>Q1 2024</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-blue-600'>84%</div>
                    <div className='text-xs text-blue-600'>+2% from Q4</div>
                  </div>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Year Average</div>
                    <div className='text-sm text-muted-foreground'>2024 YTD</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-purple-600'>85%</div>
                    <div className='text-xs text-purple-600'>+5% from 2023</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Alignment Actions */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Alignment Actions</h2>
              <div className='space-y-3'>
                <div className='flex items-center space-x-3 p-3 bg-muted/50 rounded-lg'>
                  <div className='w-2 h-2 bg-red-500 rounded-full'></div>
                  <div className='flex-1'>
                    <div className='font-medium text-foreground text-sm'>Sales Team Alignment</div>
                    <div className='text-xs text-muted-foreground'>
                      Schedule vision alignment session
                    </div>
                  </div>
                  <button className='text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700 transition-colors'>
                    High Priority
                  </button>
                </div>
                <div className='flex items-center space-x-3 p-3 bg-muted/50 rounded-lg'>
                  <div className='w-2 h-2 bg-yellow-500 rounded-full'></div>
                  <div className='flex-1'>
                    <div className='font-medium text-foreground text-sm'>
                      Cross-Department Goals
                    </div>
                    <div className='text-xs text-muted-foreground'>
                      Improve goal coherence between teams
                    </div>
                  </div>
                  <button className='text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700 transition-colors'>
                    Medium
                  </button>
                </div>
                <div className='flex items-center space-x-3 p-3 bg-muted/50 rounded-lg'>
                  <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                  <div className='flex-1'>
                    <div className='font-medium text-foreground text-sm'>Communication Clarity</div>
                    <div className='text-xs text-muted-foreground'>Enhance message consistency</div>
                  </div>
                  <button className='text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700 transition-colors'>
                    Low Priority
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Vision Cascade */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>Vision Cascade</h2>
            <div className='space-y-4'>
              <div className='flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg border border-blue-200 dark:border-blue-800'>
                <div className='w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center'>
                  <span className='text-white font-semibold'>C</span>
                </div>
                <div className='flex-1'>
                  <h3 className='font-semibold text-foreground'>Company Vision</h3>
                  <p className='text-sm text-muted-foreground'>
                    Transform how organizations understand and empower their people
                  </p>
                </div>
                <div className='text-right'>
                  <div className='text-sm font-medium text-green-600'>100% Clarity</div>
                  <div className='text-xs text-muted-foreground'>Leadership level</div>
                </div>
              </div>

              <div className='ml-8 space-y-3'>
                <div className='flex items-center space-x-4 p-3 bg-muted/50 rounded-lg'>
                  <div className='w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center'>
                    <span className='text-white text-sm font-semibold'>D</span>
                  </div>
                  <div className='flex-1'>
                    <h4 className='font-medium text-foreground'>Department Goals</h4>
                    <p className='text-xs text-muted-foreground'>
                      Aligned strategic objectives per department
                    </p>
                  </div>
                  <div className='text-right'>
                    <div className='text-sm font-medium text-green-600'>92% Alignment</div>
                    <div className='text-xs text-muted-foreground'>Department level</div>
                  </div>
                </div>

                <div className='ml-8 space-y-2'>
                  <div className='flex items-center space-x-4 p-3 bg-muted/30 rounded-lg'>
                    <div className='w-6 h-6 bg-blue-400 rounded flex items-center justify-center'>
                      <span className='text-white text-xs font-semibold'>T</span>
                    </div>
                    <div className='flex-1'>
                      <h5 className='font-medium text-foreground text-sm'>Team Objectives</h5>
                      <p className='text-xs text-muted-foreground'>
                        Specific team-level targets and KPIs
                      </p>
                    </div>
                    <div className='text-right'>
                      <div className='text-sm font-medium text-blue-600'>87% Alignment</div>
                      <div className='text-xs text-muted-foreground'>Team level</div>
                    </div>
                  </div>

                  <div className='ml-6 flex items-center space-x-4 p-3 bg-muted/20 rounded-lg'>
                    <div className='w-4 h-4 bg-purple-400 rounded flex items-center justify-center'>
                      <span className='text-white text-xs font-semibold'>I</span>
                    </div>
                    <div className='flex-1'>
                      <h6 className='font-medium text-foreground text-sm'>Individual Goals</h6>
                      <p className='text-xs text-muted-foreground'>
                        Personal objectives and development plans
                      </p>
                    </div>
                    <div className='text-right'>
                      <div className='text-sm font-medium text-purple-600'>84% Alignment</div>
                      <div className='text-xs text-muted-foreground'>Individual level</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI Alignment Insights */}
          <div className='bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800'>
            <div className='flex items-center space-x-3 mb-4'>
              <div className='w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center'>
                <span className='text-white text-sm'>🤖</span>
              </div>
              <h2 className='text-xl font-semibold text-foreground'>AI Alignment Insights</h2>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Misalignment Detection</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  AI identified potential misalignment in Sales team&apos;s Q3 targets. Recommend
                  realignment session.
                </p>
                <button className='text-xs bg-purple-600 text-white px-3 py-1 rounded-full hover:bg-purple-700 transition-colors'>
                  Schedule Session
                </button>
              </div>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Optimization Opportunities</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  Cross-functional collaboration could improve by 15% with better goal alignment
                  between Product and Engineering.
                </p>
                <button className='text-xs bg-pink-600 text-white px-3 py-1 rounded-full hover:bg-pink-700 transition-colors'>
                  View Recommendations
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
