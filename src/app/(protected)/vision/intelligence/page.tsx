import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Intelligence | Emynent',
  description: 'AI-powered strategic intelligence and insights for informed decision making',
}

export default function VisionIntelligencePage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Intelligence</h1>
          <p className='text-muted-foreground'>
            AI-powered strategic intelligence and insights for informed decision making
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Intelligence Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>AI Insights</h3>
                <span className='text-2xl'>🧠</span>
              </div>
              <div className='text-2xl font-bold text-blue-600'>47</div>
              <p className='text-xs text-muted-foreground'>Generated today</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Accuracy Score</h3>
                <span className='text-2xl'>🎯</span>
              </div>
              <div className='text-2xl font-bold text-green-600'>94%</div>
              <p className='text-xs text-muted-foreground'>Prediction accuracy</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Actions Taken</h3>
                <span className='text-2xl'>⚡</span>
              </div>
              <div className='text-2xl font-bold text-purple-600'>23</div>
              <p className='text-xs text-muted-foreground'>This week</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Impact Score</h3>
                <span className='text-2xl'>📈</span>
              </div>
              <div className='text-2xl font-bold text-orange-600'>8.7</div>
              <p className='text-xs text-muted-foreground'>Business impact</p>
            </div>
          </div>

          {/* Strategic Intelligence Dashboard */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>
              Strategic Intelligence Dashboard
            </h2>
            <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
              {/* Market Intelligence */}
              <div className='bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800'>
                <div className='flex items-center space-x-3 mb-3'>
                  <div className='w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center'>
                    <span className='text-white text-sm'>📊</span>
                  </div>
                  <h3 className='font-semibold text-foreground'>Market Intelligence</h3>
                </div>
                <div className='space-y-3'>
                  <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-3'>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-foreground'>Market Growth</span>
                      <span className='text-sm text-green-600'>+12.5%</span>
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      HR tech sector expanding rapidly
                    </p>
                  </div>
                  <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-3'>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-foreground'>
                        Competitor Activity
                      </span>
                      <span className='text-sm text-orange-600'>Medium</span>
                    </div>
                    <p className='text-xs text-muted-foreground'>3 new entrants this quarter</p>
                  </div>
                  <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-3'>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-foreground'>
                        Customer Sentiment
                      </span>
                      <span className='text-sm text-green-600'>Positive</span>
                    </div>
                    <p className='text-xs text-muted-foreground'>
                      87% satisfaction across segments
                    </p>
                  </div>
                </div>
              </div>

              {/* Operational Intelligence */}
              <div className='bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-lg p-4 border border-green-200 dark:border-green-800'>
                <div className='flex items-center space-x-3 mb-3'>
                  <div className='w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center'>
                    <span className='text-white text-sm'>⚙️</span>
                  </div>
                  <h3 className='font-semibold text-foreground'>Operational Intelligence</h3>
                </div>
                <div className='space-y-3'>
                  <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-3'>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-foreground'>Team Efficiency</span>
                      <span className='text-sm text-green-600'>92%</span>
                    </div>
                    <p className='text-xs text-muted-foreground'>Above industry average</p>
                  </div>
                  <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-3'>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-foreground'>
                        Resource Utilization
                      </span>
                      <span className='text-sm text-blue-600'>89%</span>
                    </div>
                    <p className='text-xs text-muted-foreground'>Optimal allocation detected</p>
                  </div>
                  <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-3'>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-foreground'>
                        Process Optimization
                      </span>
                      <span className='text-sm text-purple-600'>15%</span>
                    </div>
                    <p className='text-xs text-muted-foreground'>Improvement opportunities</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI Predictions & Recommendations */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>
              AI Predictions & Recommendations
            </h2>
            <div className='space-y-4'>
              <div className='flex items-start space-x-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-lg border border-purple-200 dark:border-purple-800'>
                <div className='w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0'>
                  <span className='text-white text-sm'>🔮</span>
                </div>
                <div className='flex-1'>
                  <h3 className='font-semibold text-foreground mb-2'>Revenue Forecast</h3>
                  <p className='text-sm text-muted-foreground mb-3'>
                    AI predicts 23% revenue growth in Q3 based on current market trends and pipeline
                    analysis. Confidence level: 89%
                  </p>
                  <div className='flex items-center space-x-2'>
                    <span className='text-xs bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full'>
                      High Confidence
                    </span>
                    <button className='text-xs bg-purple-600 text-white px-3 py-1 rounded-full hover:bg-purple-700 transition-colors'>
                      View Details
                    </button>
                  </div>
                </div>
              </div>

              <div className='flex items-start space-x-4 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20 rounded-lg border border-blue-200 dark:border-blue-800'>
                <div className='w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0'>
                  <span className='text-white text-sm'>🎯</span>
                </div>
                <div className='flex-1'>
                  <h3 className='font-semibold text-foreground mb-2'>Strategic Opportunity</h3>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Market analysis suggests entering the mid-market segment could increase TAM by
                    40%. Recommended timeline: Q4 2024
                  </p>
                  <div className='flex items-center space-x-2'>
                    <span className='text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full'>
                      Strategic Priority
                    </span>
                    <button className='text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 transition-colors'>
                      Create Plan
                    </button>
                  </div>
                </div>
              </div>

              <div className='flex items-start space-x-4 p-4 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 rounded-lg border border-orange-200 dark:border-orange-800'>
                <div className='w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center flex-shrink-0'>
                  <span className='text-white text-sm'>⚠️</span>
                </div>
                <div className='flex-1'>
                  <h3 className='font-semibold text-foreground mb-2'>Risk Alert</h3>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Potential talent retention risk detected in Engineering team. Recommend
                    immediate intervention and retention strategy.
                  </p>
                  <div className='flex items-center space-x-2'>
                    <span className='text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full'>
                      Immediate Action
                    </span>
                    <button className='text-xs bg-orange-600 text-white px-3 py-1 rounded-full hover:bg-orange-700 transition-colors'>
                      Address Risk
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Intelligence Sources */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Intelligence Sources</h2>
              <div className='space-y-3'>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center'>
                      <span className='text-blue-600 text-sm'>📊</span>
                    </div>
                    <div>
                      <div className='font-medium text-foreground text-sm'>Internal Analytics</div>
                      <div className='text-xs text-muted-foreground'>
                        User behavior, performance metrics
                      </div>
                    </div>
                  </div>
                  <span className='text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full'>
                    Active
                  </span>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center'>
                      <span className='text-purple-600 text-sm'>🌐</span>
                    </div>
                    <div>
                      <div className='font-medium text-foreground text-sm'>Market Data</div>
                      <div className='text-xs text-muted-foreground'>
                        Industry trends, competitor analysis
                      </div>
                    </div>
                  </div>
                  <span className='text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full'>
                    Active
                  </span>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center'>
                      <span className='text-green-600 text-sm'>👥</span>
                    </div>
                    <div>
                      <div className='font-medium text-foreground text-sm'>Customer Feedback</div>
                      <div className='text-xs text-muted-foreground'>
                        Surveys, support tickets, reviews
                      </div>
                    </div>
                  </div>
                  <span className='text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full'>
                    Active
                  </span>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div className='flex items-center space-x-3'>
                    <div className='w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center'>
                      <span className='text-orange-600 text-sm'>🔗</span>
                    </div>
                    <div>
                      <div className='font-medium text-foreground text-sm'>External APIs</div>
                      <div className='text-xs text-muted-foreground'>Third-party data sources</div>
                    </div>
                  </div>
                  <span className='text-xs bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full'>
                    Pending
                  </span>
                </div>
              </div>
            </div>

            {/* Intelligence Metrics */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Intelligence Metrics</h2>
              <div className='space-y-4'>
                <div className='text-center'>
                  <div className='w-16 h-16 mx-auto mb-2 relative'>
                    <svg className='w-16 h-16 transform -rotate-90' viewBox='0 0 36 36'>
                      <path
                        d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                        fill='none'
                        stroke='currentColor'
                        strokeWidth='2'
                        className='text-muted'
                      />
                      <path
                        d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
                        fill='none'
                        stroke='currentColor'
                        strokeWidth='2'
                        strokeDasharray='94, 100'
                        className='text-blue-500'
                      />
                    </svg>
                    <div className='absolute inset-0 flex items-center justify-center'>
                      <span className='text-sm font-bold text-foreground'>94%</span>
                    </div>
                  </div>
                  <h3 className='font-medium text-foreground text-sm'>Prediction Accuracy</h3>
                  <p className='text-xs text-muted-foreground'>Last 30 days</p>
                </div>

                <div className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <span className='text-xs text-muted-foreground'>Data Quality</span>
                    <span className='text-xs font-medium text-green-600'>98%</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-xs text-muted-foreground'>Processing Speed</span>
                    <span className='text-xs font-medium text-blue-600'>1.2s avg</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-xs text-muted-foreground'>Model Confidence</span>
                    <span className='text-xs font-medium text-purple-600'>91%</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-xs text-muted-foreground'>Action Success Rate</span>
                    <span className='text-xs font-medium text-orange-600'>87%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Advanced AI Capabilities */}
          <div className='bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/20 dark:to-purple-950/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-800'>
            <div className='flex items-center space-x-3 mb-4'>
              <div className='w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center'>
                <span className='text-white text-sm'>🤖</span>
              </div>
              <h2 className='text-xl font-semibold text-foreground'>Advanced AI Capabilities</h2>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Predictive Analytics</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  Advanced machine learning models predict business outcomes with 94% accuracy.
                </p>
                <div className='flex items-center space-x-2'>
                  <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                  <span className='text-xs text-muted-foreground'>Operational</span>
                </div>
              </div>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Natural Language Processing</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  Analyze unstructured data from feedback, reviews, and communications.
                </p>
                <div className='flex items-center space-x-2'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                  <span className='text-xs text-muted-foreground'>Active</span>
                </div>
              </div>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Anomaly Detection</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  Automatically identify unusual patterns and potential risks in real-time.
                </p>
                <div className='flex items-center space-x-2'>
                  <div className='w-2 h-2 bg-purple-500 rounded-full'></div>
                  <span className='text-xs text-muted-foreground'>Beta</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
