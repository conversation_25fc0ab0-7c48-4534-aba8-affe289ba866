import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Strategic Planning | Emynent',
  description: 'Develop and execute strategic plans for long-term success',
}

export default function StrategicPlanningPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Strategic Planning</h1>
          <p className='text-muted-foreground'>
            Develop comprehensive strategic plans and align organizational goals with long-term
            vision.
          </p>
        </div>

        <div className='grid gap-6'>
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Current Strategic Initiatives</h2>
            <div className='space-y-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Digital Transformation 2025</h3>
                  <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                    Active
                  </span>
                </div>
                <p className='text-sm text-muted-foreground mb-3'>
                  Modernize technology infrastructure and processes to improve efficiency and
                  customer experience
                </p>
                <div className='flex items-center gap-2 mb-2'>
                  <div className='flex-1 bg-background rounded-full h-2'>
                    <div className='w-2/3 h-full bg-primary rounded-full'></div>
                  </div>
                  <span className='text-sm text-muted-foreground'>67%</span>
                </div>
                <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                  <span>Q1 2024 - Q4 2025</span>
                  <span>•</span>
                  <span>12 key milestones</span>
                  <span>•</span>
                  <span>8 completed</span>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Market Expansion Strategy</h3>
                  <span className='text-sm bg-green-100 text-green-800 px-2 py-1 rounded'>
                    Planning
                  </span>
                </div>
                <p className='text-sm text-muted-foreground mb-3'>
                  Expand into new geographic markets and customer segments
                </p>
                <div className='flex items-center gap-2 mb-2'>
                  <div className='flex-1 bg-background rounded-full h-2'>
                    <div className='w-1/4 h-full bg-primary rounded-full'></div>
                  </div>
                  <span className='text-sm text-muted-foreground'>25%</span>
                </div>
                <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                  <span>Q2 2024 - Q2 2026</span>
                  <span>•</span>
                  <span>15 key milestones</span>
                  <span>•</span>
                  <span>4 completed</span>
                </div>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Strategic Framework</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Vision & Mission</h3>
                <p className='text-sm text-muted-foreground'>
                  Define organizational purpose and long-term aspirations
                </p>
              </div>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>SWOT Analysis</h3>
                <p className='text-sm text-muted-foreground'>
                  Assess strengths, weaknesses, opportunities, and threats
                </p>
              </div>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>OKRs & KPIs</h3>
                <p className='text-sm text-muted-foreground'>
                  Set measurable objectives and key performance indicators
                </p>
              </div>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-medium mb-2'>Resource Allocation</h3>
                <p className='text-sm text-muted-foreground'>
                  Optimize budget and resource distribution across initiatives
                </p>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Planning Timeline</h2>
            <div className='space-y-3'>
              <div className='flex items-center gap-3 p-3 bg-muted rounded-lg'>
                <div className='w-3 h-3 bg-green-500 rounded-full'></div>
                <div className='flex-1'>
                  <span className='font-medium'>Q1 2024: Foundation Phase</span>
                  <p className='text-sm text-muted-foreground'>
                    Infrastructure setup and team alignment
                  </p>
                </div>
                <span className='text-sm bg-green-100 text-green-800 px-2 py-1 rounded'>
                  Completed
                </span>
              </div>

              <div className='flex items-center gap-3 p-3 bg-muted rounded-lg'>
                <div className='w-3 h-3 bg-blue-500 rounded-full'></div>
                <div className='flex-1'>
                  <span className='font-medium'>Q2 2024: Implementation Phase</span>
                  <p className='text-sm text-muted-foreground'>
                    Execute key initiatives and monitor progress
                  </p>
                </div>
                <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                  In Progress
                </span>
              </div>

              <div className='flex items-center gap-3 p-3 bg-muted rounded-lg'>
                <div className='w-3 h-3 bg-gray-400 rounded-full'></div>
                <div className='flex-1'>
                  <span className='font-medium'>Q3-Q4 2024: Optimization Phase</span>
                  <p className='text-sm text-muted-foreground'>
                    Refine processes and scale successful initiatives
                  </p>
                </div>
                <span className='text-sm bg-gray-100 text-gray-800 px-2 py-1 rounded'>Planned</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
