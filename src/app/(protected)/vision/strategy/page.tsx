import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Strategy | Emynent',
  description: 'Strategic planning and vision alignment for organizational success',
}

export default function VisionStrategyPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Strategy</h1>
          <p className='text-muted-foreground'>
            Strategic planning and vision alignment for organizational success
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Strategic Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Strategic Goals</h3>
                <span className='text-2xl'>🎯</span>
              </div>
              <div className='text-2xl font-bold text-foreground'>12</div>
              <p className='text-xs text-muted-foreground'>Active initiatives</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Alignment Score</h3>
                <span className='text-2xl'>📊</span>
              </div>
              <div className='text-2xl font-bold text-green-600'>87%</div>
              <p className='text-xs text-muted-foreground'>Team alignment</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Progress Rate</h3>
                <span className='text-2xl'>⚡</span>
              </div>
              <div className='text-2xl font-bold text-blue-600'>94%</div>
              <p className='text-xs text-muted-foreground'>On track</p>
            </div>
            <div className='bg-card rounded-lg p-6 border'>
              <div className='flex items-center justify-between mb-2'>
                <h3 className='text-sm font-medium text-muted-foreground'>Impact Score</h3>
                <span className='text-2xl'>🚀</span>
              </div>
              <div className='text-2xl font-bold text-purple-600'>8.9</div>
              <p className='text-xs text-muted-foreground'>Business impact</p>
            </div>
          </div>

          {/* Strategic Initiatives */}
          <div className='bg-card rounded-lg p-6 border'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>Strategic Initiatives</h2>
            <div className='space-y-4'>
              <div className='flex items-center justify-between p-4 bg-muted/50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-green-500 rounded-full'></div>
                  <div>
                    <h3 className='font-medium text-foreground'>Digital Transformation</h3>
                    <p className='text-sm text-muted-foreground'>
                      Modernize core systems and processes
                    </p>
                  </div>
                </div>
                <div className='flex items-center space-x-4'>
                  <div className='text-right'>
                    <div className='text-sm font-medium text-foreground'>Q2 2024</div>
                    <div className='text-xs text-muted-foreground'>Target completion</div>
                  </div>
                  <div className='w-24 bg-muted rounded-full h-2'>
                    <div className='bg-green-500 h-2 rounded-full' style={{ width: '75%' }}></div>
                  </div>
                  <span className='text-sm font-medium text-green-600'>75%</span>
                </div>
              </div>
              <div className='flex items-center justify-between p-4 bg-muted/50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-blue-500 rounded-full'></div>
                  <div>
                    <h3 className='font-medium text-foreground'>Market Expansion</h3>
                    <p className='text-sm text-muted-foreground'>Enter new geographic markets</p>
                  </div>
                </div>
                <div className='flex items-center space-x-4'>
                  <div className='text-right'>
                    <div className='text-sm font-medium text-foreground'>Q3 2024</div>
                    <div className='text-xs text-muted-foreground'>Target completion</div>
                  </div>
                  <div className='w-24 bg-muted rounded-full h-2'>
                    <div className='bg-blue-500 h-2 rounded-full' style={{ width: '60%' }}></div>
                  </div>
                  <span className='text-sm font-medium text-blue-600'>60%</span>
                </div>
              </div>
              <div className='flex items-center justify-between p-4 bg-muted/50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-orange-500 rounded-full'></div>
                  <div>
                    <h3 className='font-medium text-foreground'>Product Innovation</h3>
                    <p className='text-sm text-muted-foreground'>
                      Develop next-generation solutions
                    </p>
                  </div>
                </div>
                <div className='flex items-center space-x-4'>
                  <div className='text-right'>
                    <div className='text-sm font-medium text-foreground'>Q4 2024</div>
                    <div className='text-xs text-muted-foreground'>Target completion</div>
                  </div>
                  <div className='w-24 bg-muted rounded-full h-2'>
                    <div className='bg-orange-500 h-2 rounded-full' style={{ width: '45%' }}></div>
                  </div>
                  <span className='text-sm font-medium text-orange-600'>45%</span>
                </div>
              </div>
            </div>
          </div>

          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Vision Alignment */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Vision Alignment</h2>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Leadership Team</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-20 bg-muted rounded-full h-2'>
                      <div className='bg-green-500 h-2 rounded-full' style={{ width: '95%' }}></div>
                    </div>
                    <span className='text-sm font-medium text-green-600'>95%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Department Heads</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-20 bg-muted rounded-full h-2'>
                      <div className='bg-blue-500 h-2 rounded-full' style={{ width: '87%' }}></div>
                    </div>
                    <span className='text-sm font-medium text-blue-600'>87%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Team Leads</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-20 bg-muted rounded-full h-2'>
                      <div
                        className='bg-yellow-500 h-2 rounded-full'
                        style={{ width: '82%' }}
                      ></div>
                    </div>
                    <span className='text-sm font-medium text-yellow-600'>82%</span>
                  </div>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-muted-foreground'>Individual Contributors</span>
                  <div className='flex items-center space-x-2'>
                    <div className='w-20 bg-muted rounded-full h-2'>
                      <div
                        className='bg-orange-500 h-2 rounded-full'
                        style={{ width: '78%' }}
                      ></div>
                    </div>
                    <span className='text-sm font-medium text-orange-600'>78%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Strategic Metrics */}
            <div className='bg-card rounded-lg p-6 border'>
              <h2 className='text-xl font-semibold text-foreground mb-4'>Strategic Metrics</h2>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Revenue Growth</div>
                    <div className='text-sm text-muted-foreground'>Year over year</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-green-600'>+24%</div>
                    <div className='text-xs text-muted-foreground'>Target: +20%</div>
                  </div>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Market Share</div>
                    <div className='text-sm text-muted-foreground'>Industry position</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-blue-600'>18.5%</div>
                    <div className='text-xs text-muted-foreground'>Target: 20%</div>
                  </div>
                </div>
                <div className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'>
                  <div>
                    <div className='font-medium text-foreground'>Customer Satisfaction</div>
                    <div className='text-sm text-muted-foreground'>NPS Score</div>
                  </div>
                  <div className='text-right'>
                    <div className='text-lg font-bold text-purple-600'>72</div>
                    <div className='text-xs text-muted-foreground'>Target: 70</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI Strategic Insights */}
          <div className='bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800'>
            <div className='flex items-center space-x-3 mb-4'>
              <div className='w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center'>
                <span className='text-white text-sm'>🤖</span>
              </div>
              <h2 className='text-xl font-semibold text-foreground'>AI Strategic Insights</h2>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Strategic Opportunities</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  AI analysis suggests focusing on emerging markets in Southeast Asia for Q3
                  expansion.
                </p>
                <button className='text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 transition-colors'>
                  View Analysis
                </button>
              </div>
              <div className='bg-white/50 dark:bg-gray-900/50 rounded-lg p-4'>
                <h3 className='font-medium text-foreground mb-2'>Risk Assessment</h3>
                <p className='text-sm text-muted-foreground mb-3'>
                  Potential supply chain disruptions identified. Recommend diversification strategy.
                </p>
                <button className='text-xs bg-purple-600 text-white px-3 py-1 rounded-full hover:bg-purple-700 transition-colors'>
                  View Recommendations
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
