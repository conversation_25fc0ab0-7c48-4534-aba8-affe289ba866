'use client'

import { CreateCompanyForm } from '@/components/superadmin/CreateCompanyForm'
import { UpdateSubscriptionForm } from '@/components/superadmin/UpdateSubscriptionForm'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Role, SubscriptionStatus } from '@prisma/client'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

interface Company {
  id: string
  name: string
  subscriptionStatus: SubscriptionStatus
  subscriptionEndDate: string
  maxUsers: number
  currentPlan: string
  allowedEmailDomains: string[]
  updatedAt: string
}

export default function SuperadminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [showCreateForm, setShowCreateForm] = useState(false)

  useEffect(() => {
    if (status === 'loading') return

    if (!session || session.user.role !== Role.SUPERADMIN) {
      router.push('/error?message=Unauthorized%20access')
      return
    }

    fetchCompanies()
  }, [session, status, page, search, statusFilter])

  const fetchCompanies = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(search && { search }),
        ...(statusFilter && { status: statusFilter }),
      })

      const response = await fetch(`/api/companies?${params}`, {
        headers: {
          'X-API-Key': process.env.NEXT_PUBLIC_SUPERADMIN_API_KEY!,
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch companies')
      }

      const data = await response.json()
      setCompanies(data.companies)
      setTotalPages(Math.ceil(data.total / 10))
      setError(null)
    } catch {
      setError('Error loading companies. Please try again.')
      toast.error('Failed to load companies')
    } finally {
      setLoading(false)
    }
  }

  const handleViewAuditLogs = (companyId: string) => {
    router.push(`/superadmin/audit-logs/${companyId}`)
  }

  return (
    <div className='space-y-8'>
      {/* Header section - consistent with dashboard layout */}
      <div>
        <h2 className='text-3xl font-bold tracking-tight'>Superadmin Dashboard</h2>
        <p className='text-muted-foreground'>
          Manage companies, design system, and platform settings
        </p>
      </div>

      {/* Quick Actions Grid - consistent with dashboard card layout */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        <div className='rounded-lg border bg-card text-card-foreground shadow-sm p-6'>
          <h3 className='text-lg font-semibold mb-2'>Design System Manager</h3>
          <p className='text-muted-foreground text-sm mb-4'>
            Live-edit component library, colors, typography, and spacing
          </p>
          <Button
            variant='outline'
            size='sm'
            onClick={() => router.push('/superadmin/design-system')}
          >
            Manage Design System
          </Button>
        </div>

        <div className='rounded-lg border bg-card text-card-foreground shadow-sm p-6'>
          <h3 className='text-lg font-semibold mb-2'>Company Management</h3>
          <p className='text-muted-foreground text-sm mb-4'>
            View and manage all companies, subscriptions, and settings
          </p>
          <Button variant='outline' size='sm' onClick={() => setShowCreateForm(true)}>
            Add New Company
          </Button>
        </div>

        <div className='rounded-lg border bg-card text-card-foreground shadow-sm p-6'>
          <h3 className='text-lg font-semibold mb-2'>System Health</h3>
          <p className='text-muted-foreground text-sm mb-4'>
            Monitor application performance and system status
          </p>
          <Button variant='outline' size='sm' disabled>
            View Metrics
          </Button>
        </div>
      </div>

      {/* Company Management Section */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div>
            <h3 className='text-xl font-semibold'>Company Management</h3>
            <p className='text-muted-foreground text-sm'>
              Manage all companies, subscriptions, and access controls
            </p>
          </div>
          <Button onClick={() => setShowCreateForm(true)}>Add Company</Button>
        </div>

        {/* Search and Filters */}
        <div className='flex gap-4'>
          <Input
            placeholder='Search companies...'
            value={search}
            onChange={e => setSearch(e.target.value)}
            className='max-w-xs'
          />
          <Select value={statusFilter} onValueChange={setStatusFilter} className='max-w-xs'>
            <option value=''>All Statuses</option>
            <option value='TRIAL'>Trial</option>
            <option value='ACTIVE'>Active</option>
            <option value='EXPIRED'>Expired</option>
          </Select>
        </div>

        {error && (
          <div className='rounded-lg border border-destructive/50 bg-destructive/10 px-4 py-3'>
            <p className='text-sm text-destructive'>{error}</p>
          </div>
        )}

        {/* Company Table */}
        <div className='rounded-lg border bg-card text-card-foreground shadow-sm'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company Name</TableHead>
                <TableHead>Email Domains</TableHead>
                <TableHead>Subscription Status</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Max Users</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className='text-center py-8'>
                    Loading...
                  </TableCell>
                </TableRow>
              ) : companies.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className='text-center py-8'>
                    No companies found
                  </TableCell>
                </TableRow>
              ) : (
                companies.map(company => (
                  <TableRow key={company.id}>
                    <TableCell>{company.name}</TableCell>
                    <TableCell>{company.allowedEmailDomains.join(', ')}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded text-sm ${
                          company.subscriptionStatus === 'ACTIVE'
                            ? 'bg-green-100 text-green-800'
                            : company.subscriptionStatus === 'TRIAL'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {company.subscriptionStatus}
                      </span>
                    </TableCell>
                    <TableCell>
                      {new Date(company.subscriptionEndDate).toLocaleDateString()}
                    </TableCell>
                    <TableCell>{company.currentPlan}</TableCell>
                    <TableCell>{company.maxUsers}</TableCell>
                    <TableCell>
                      <div className='flex gap-2'>
                        <UpdateSubscriptionForm company={company} onUpdate={fetchCompanies} />
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleViewAuditLogs(company.id)}
                        >
                          View Logs
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {totalPages > 1 && (
          <div className='flex justify-center gap-2 mt-4'>
            <Button
              variant='outline'
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className='py-2'>
              Page {page} of {totalPages}
            </span>
            <Button
              variant='outline'
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>

      {showCreateForm && (
        <CreateCompanyForm
          onClose={() => setShowCreateForm(false)}
          onSuccess={() => {
            setShowCreateForm(false)
            fetchCompanies()
          }}
        />
      )}
    </div>
  )
}
