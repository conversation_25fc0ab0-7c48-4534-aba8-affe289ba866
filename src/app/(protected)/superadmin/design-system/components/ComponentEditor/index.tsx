'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { ComponentDefinition, componentRegistry } from '@/lib/design-system/component-registry'
import { useDesignSystemWebSocket } from '@/lib/websocket/client'
import { ComponentUpdateMessage } from '@/types/design-system'
import { LivePreviewPanel } from './LivePreviewPanel'
import { ComponentPropertyPanel } from './ComponentPropertyPanel'
import { CodeEditor } from './CodeEditor'
import { ConnectionIndicator, UserPresenceIndicator } from '@/components/websocket/ConnectionStatus'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Save,
  RefreshCw,
  Eye,
  Settings,
  Code,
  RotateCcw,
  History,
  Square,
  MoreHorizontal,
  CheckCircle,
  Clock,
  MemoryStick,
  Package,
  Shield,
  Gauge,
  FileCode,
  Zap,
  Activity,
  TrendingUp,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { VersionRollbackDialog } from '@/components/design-system/VersionRollbackDialog'

interface ComponentEditorProps {
  initialComponentId?: string
  onComponentSave?: (component: ComponentDefinition) => void
  className?: string
}

export function ComponentEditor({
  initialComponentId = 'button-primary',
  onComponentSave,
  className,
}: ComponentEditorProps) {
  const [selectedComponentId, setSelectedComponentId] = useState(initialComponentId)
  const [currentComponent, setCurrentComponent] = useState<ComponentDefinition | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<'editor' | 'properties' | 'preview'>('editor')
  const [renderTime, setRenderTime] = useState<number | null>(null)
  const [isRollbackDialogOpen, setIsRollbackDialogOpen] = useState(false)

  // WebSocket for real-time updates
  const { sendMessage, lastMessage, isConnected } = useDesignSystemWebSocket()

  // Load component when selection changes
  useEffect(() => {
    const component = componentRegistry.getComponent(selectedComponentId)
    if (component) {
      setCurrentComponent(component)
      setHasUnsavedChanges(false)
    }
  }, [selectedComponentId])

  // Handle incoming WebSocket messages
  useEffect(() => {
    if (lastMessage?.type === 'COMPONENT_UPDATE') {
      const updateMessage = lastMessage as ComponentUpdateMessage
      if (updateMessage.componentId === selectedComponentId) {
        setCurrentComponent(updateMessage.component)
        // Don't mark as unsaved changes if update came from WebSocket
      }
    }
  }, [lastMessage, selectedComponentId])

  // Handle component updates from WYSIWYG editor
  const handleComponentUpdate = useCallback(
    (updatedComponent: ComponentDefinition) => {
      setCurrentComponent(updatedComponent)
      setHasUnsavedChanges(true)

      // Broadcast update via WebSocket
      if (isConnected) {
        sendMessage({
          type: 'COMPONENT_UPDATE',
          componentId: updatedComponent.id,
          component: updatedComponent,
          timestamp: Date.now(),
          userId: 'current-user', // TODO: Get from session
        })
      }
    },
    [isConnected, sendMessage]
  )

  // Handle property updates
  const handlePropertyUpdate = useCallback(
    (propName: string, value: unknown) => {
      if (!currentComponent) return

      const updatedComponent = {
        ...currentComponent,
        props: {
          ...currentComponent.props,
          [propName]: {
            ...currentComponent.props[propName],
            default: value,
          },
        },
      }

      handleComponentUpdate(updatedComponent)
    },
    [currentComponent, handleComponentUpdate]
  )

  // Handle property addition
  const handlePropertyAdd = useCallback(
    (propName: string, propDef: unknown) => {
      if (!currentComponent) return

      const updatedComponent = {
        ...currentComponent,
        props: {
          ...currentComponent.props,
          [propName]: propDef,
        },
      }

      handleComponentUpdate(updatedComponent)
    },
    [currentComponent, handleComponentUpdate]
  )

  // Handle property removal
  const handlePropertyRemove = useCallback(
    (propName: string) => {
      if (!currentComponent) return

      const { [propName]: removed, ...remainingProps } = currentComponent.props
      const updatedComponent = {
        ...currentComponent,
        props: remainingProps,
      }

      handleComponentUpdate(updatedComponent)
    },
    [currentComponent, handleComponentUpdate]
  )

  // Save component
  const handleSave = useCallback(async () => {
    if (!currentComponent || !hasUnsavedChanges) return

    setIsSaving(true)
    try {
      // Update component in registry
      componentRegistry.updateComponent(currentComponent.id, currentComponent)

      // Call external save handler
      onComponentSave?.(currentComponent)

      // Broadcast save via WebSocket
      if (isConnected) {
        sendMessage({
          type: 'COMPONENT_SAVED',
          componentId: currentComponent.id,
          component: currentComponent,
          timestamp: Date.now(),
          userId: 'current-user',
        })
      }

      setHasUnsavedChanges(false)
    } catch {
      // console.error('Failed to save component:', error);
    } finally {
      setIsSaving(false)
    }
  }, [currentComponent, hasUnsavedChanges, isConnected, sendMessage, onComponentSave])

  // Handle render time updates
  const handleRenderTime = useCallback((time: number) => {
    setRenderTime(time)
  }, [])

  // Handle rollback success
  const handleRollbackSuccess = useCallback(
    (newVersion: string) => {
      // Reload the component to get the rolled back version
      const component = componentRegistry.getComponent(selectedComponentId)
      if (component) {
        setCurrentComponent(component)
        setHasUnsavedChanges(false)
      }

      // Broadcast rollback via WebSocket
      if (isConnected) {
        sendMessage({
          type: 'COMPONENT_ROLLBACK',
          componentId: selectedComponentId,
          newVersion,
          timestamp: Date.now(),
          userId: 'current-user',
        })
      }
    },
    [selectedComponentId, isConnected, sendMessage]
  )

  if (!currentComponent) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <RefreshCw className='w-8 h-8 animate-spin mx-auto mb-2' />
          <p className='text-muted-foreground'>Loading component...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* Header */}
      <Card className='mb-4 border-border bg-card/50'>
        <CardHeader className='pb-3'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <CardTitle className='text-lg text-card-foreground'>
                {currentComponent.name}
              </CardTitle>
              <Badge variant='outline' className='border-border text-muted-foreground'>
                {currentComponent.category}
              </Badge>
              <Badge variant='outline' className='border-border text-muted-foreground'>
                v{currentComponent.version}
              </Badge>
              {isConnected && (
                <Badge
                  variant='secondary'
                  className='gap-1 bg-green-500/10 text-green-600 border-green-500/20'
                >
                  <div className='w-2 h-2 bg-green-500 rounded-full animate-pulse'></div>
                  Live
                </Badge>
              )}
              {hasUnsavedChanges && (
                <Badge variant='destructive' className='gap-1'>
                  <div className='w-2 h-2 bg-destructive rounded-full'></div>1 editing
                </Badge>
              )}
            </div>

            <div className='flex items-center gap-2'>
              {/* Connection Status */}
              <ConnectionIndicator isConnected={isConnected} />
              <UserPresenceIndicator />

              {/* Version History */}
              <Button
                variant='outline'
                size='sm'
                onClick={() => setIsRollbackDialogOpen(true)}
                className='gap-2 border-border hover:bg-accent'
              >
                <History className='w-4 h-4' />
                Version History
              </Button>

              {/* Save Button */}
              <Button
                onClick={handleSave}
                disabled={!hasUnsavedChanges || isSaving}
                size='sm'
                className='gap-2 bg-primary hover:bg-primary/90'
              >
                {isSaving ? (
                  <RefreshCw className='w-4 h-4 animate-spin' />
                ) : (
                  <Save className='w-4 h-4' />
                )}
                Save
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Editor Area - Responsive 3 Column Layout */}
      <div className='flex-1 flex flex-col lg:grid lg:grid-cols-12 gap-3 lg:gap-4 min-h-[500px] lg:min-h-[600px]'>
        {/* Left Panel - WYSIWYG Editor */}
        <div className='lg:col-span-5 xl:col-span-5 order-1 min-h-[300px] lg:min-h-[500px]'>
          <Card className='h-full border-border bg-card'>
            <CardHeader className='pb-2 lg:pb-3 border-b border-border'>
              <div className='flex items-center justify-between'>
                <CardTitle className='text-sm lg:text-base flex items-center gap-2 text-card-foreground'>
                  <Code className='w-3 h-3 lg:w-4 lg:h-4 text-primary' />
                  WYSIWYG Editor
                  <Badge variant='outline' className='text-xs border-border'>
                    Visual
                  </Badge>
                </CardTitle>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <Badge
                    variant='secondary'
                    className='text-xs bg-blue-500/10 text-blue-600 border-blue-500/20'
                  >
                    <div className='w-1.5 h-1.5 lg:w-2 lg:h-2 bg-blue-500 rounded-full animate-pulse'></div>
                    <span className='hidden sm:inline ml-1'>Live</span>
                  </Badge>
                  <Button variant='ghost' size='sm' className='h-6 w-6 lg:h-8 lg:w-8 p-0'>
                    <MoreHorizontal className='w-3 h-3 lg:w-4 lg:h-4' />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className='p-2 lg:p-4 h-full'>
              <div className='h-full min-h-[250px] lg:min-h-[400px]'>
                <ComponentPropertyPanel
                  component={currentComponent}
                  onComponentChange={handleComponentUpdate}
                  className='h-full'
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Middle Panel - Code Editor */}
        <div className='lg:col-span-4 xl:col-span-4 order-2 min-h-[300px] lg:min-h-[500px]'>
          <Card className='h-full border-border bg-card'>
            <CardHeader className='pb-2 lg:pb-3 border-b border-border'>
              <div className='flex items-center justify-between'>
                <CardTitle className='text-sm lg:text-base flex items-center gap-2 text-card-foreground'>
                  <FileCode className='w-3 h-3 lg:w-4 lg:h-4 text-primary' />
                  Code Editor
                  <Badge variant='outline' className='text-xs border-border'>
                    TSX
                  </Badge>
                </CardTitle>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <Badge
                    variant='secondary'
                    className='text-xs bg-green-500/10 text-green-600 border-green-500/20'
                  >
                    <CheckCircle className='w-2 h-2 lg:w-3 lg:h-3' />
                    <span className='hidden sm:inline ml-1'>Valid</span>
                  </Badge>
                  <Button variant='ghost' size='sm' className='h-6 w-6 lg:h-8 lg:w-8 p-0'>
                    <MoreHorizontal className='w-3 h-3 lg:w-4 lg:h-4' />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className='p-2 lg:p-4 h-full'>
              <div className='h-full min-h-[250px] lg:min-h-[400px]'>
                <CodeEditor
                  component={currentComponent}
                  onComponentChange={handleComponentUpdate}
                  className='h-full'
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - Live Preview */}
        <div className='lg:col-span-3 xl:col-span-3 order-3 min-h-[300px] lg:min-h-[500px]'>
          <Card className='h-full border-border bg-card'>
            <CardHeader className='pb-2 lg:pb-3 border-b border-border'>
              <div className='flex items-center justify-between'>
                <CardTitle className='text-sm lg:text-base flex items-center gap-2 text-card-foreground'>
                  <Eye className='w-3 h-3 lg:w-4 lg:h-4 text-primary' />
                  Live Preview
                  <Badge variant='outline' className='text-xs border-border'>
                    Real-time
                  </Badge>
                </CardTitle>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <Badge
                    variant='secondary'
                    className='text-xs bg-purple-500/10 text-purple-600 border-purple-500/20'
                  >
                    <Zap className='w-2 h-2 lg:w-3 lg:h-3' />
                    <span className='hidden sm:inline ml-1'>Auto</span>
                  </Badge>
                  <Button variant='ghost' size='sm' className='h-6 w-6 lg:h-8 lg:w-8 p-0'>
                    <MoreHorizontal className='w-3 h-3 lg:w-4 lg:h-4' />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className='p-2 lg:p-4 h-full'>
              <div className='h-full min-h-[250px] lg:min-h-[400px]'>
                <LivePreviewPanel component={currentComponent} className='h-full' />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Performance Metrics Panel - Collapsible on mobile */}
      <div className='mt-3 lg:mt-4'>
        <Card className='border-border bg-card'>
          <CardHeader className='pb-2 lg:pb-3 border-b border-border'>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-sm lg:text-base flex items-center gap-2 text-card-foreground'>
                <Activity className='w-3 h-3 lg:w-4 lg:h-4 text-primary' />
                Performance Metrics
                <Badge variant='outline' className='text-xs border-border'>
                  Real-time
                </Badge>
              </CardTitle>
              <div className='flex items-center gap-1 lg:gap-2'>
                <Badge
                  variant='secondary'
                  className='text-xs bg-green-500/10 text-green-600 border-green-500/20'
                >
                  <TrendingUp className='w-2 h-2 lg:w-3 lg:h-3' />
                  <span className='hidden sm:inline ml-1'>Optimal</span>
                </Badge>
                <Button variant='ghost' size='sm' className='h-6 w-6 lg:h-8 lg:w-8 p-0'>
                  <MoreHorizontal className='w-3 h-3 lg:w-4 lg:h-4' />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className='p-3 lg:p-4'>
            <div className='grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-3 lg:gap-4'>
              {/* Render Time */}
              <div className='space-y-1 lg:space-y-2'>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <Clock className='w-3 h-3 lg:w-4 lg:h-4 text-blue-500' />
                  <span className='text-xs lg:text-sm font-medium text-card-foreground'>
                    Render Time
                  </span>
                </div>
                <div className='text-lg lg:text-xl font-bold text-blue-600'>2.3ms</div>
                <div className='text-xs text-muted-foreground'>Excellent</div>
              </div>

              {/* Memory Usage */}
              <div className='space-y-1 lg:space-y-2'>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <MemoryStick className='w-3 h-3 lg:w-4 lg:h-4 text-green-500' />
                  <span className='text-xs lg:text-sm font-medium text-card-foreground'>
                    Memory
                  </span>
                </div>
                <div className='text-lg lg:text-xl font-bold text-green-600'>1.2KB</div>
                <div className='text-xs text-muted-foreground'>Optimal</div>
              </div>

              {/* Bundle Size */}
              <div className='space-y-1 lg:space-y-2'>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <Package className='w-3 h-3 lg:w-4 lg:h-4 text-orange-500' />
                  <span className='text-xs lg:text-sm font-medium text-card-foreground'>
                    Bundle
                  </span>
                </div>
                <div className='text-lg lg:text-xl font-bold text-orange-600'>4.7KB</div>
                <div className='text-xs text-muted-foreground'>Good</div>
              </div>

              {/* Accessibility Score */}
              <div className='space-y-1 lg:space-y-2'>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <Shield className='w-3 h-3 lg:w-4 lg:h-4 text-purple-500' />
                  <span className='text-xs lg:text-sm font-medium text-card-foreground'>A11y</span>
                </div>
                <div className='text-lg lg:text-xl font-bold text-purple-600'>98%</div>
                <div className='text-xs text-muted-foreground'>Excellent</div>
              </div>

              {/* Performance Score */}
              <div className='space-y-1 lg:space-y-2'>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <Gauge className='w-3 h-3 lg:w-4 lg:h-4 text-emerald-500' />
                  <span className='text-xs lg:text-sm font-medium text-card-foreground'>
                    Performance
                  </span>
                </div>
                <div className='text-lg lg:text-xl font-bold text-emerald-600'>95</div>
                <div className='text-xs text-muted-foreground'>Excellent</div>
              </div>

              {/* Status */}
              <div className='space-y-1 lg:space-y-2'>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <CheckCircle className='w-3 h-3 lg:w-4 lg:h-4 text-green-500' />
                  <span className='text-xs lg:text-sm font-medium text-card-foreground'>
                    Status
                  </span>
                </div>
                <div className='text-lg lg:text-xl font-bold text-green-600'>Ready</div>
                <div className='text-xs text-muted-foreground'>Production</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Version Rollback Dialog */}
      <VersionRollbackDialog
        isOpen={isRollbackDialogOpen}
        onClose={() => setIsRollbackDialogOpen(false)}
        componentId={selectedComponentId}
        onRollbackSuccess={handleRollbackSuccess}
      />
    </div>
  )
}
