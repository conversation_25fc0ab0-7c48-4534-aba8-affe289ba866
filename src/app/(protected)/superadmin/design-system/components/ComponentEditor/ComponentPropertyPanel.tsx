'use client'

import React, { useState, useEffect } from 'react'
import { ComponentDefinition } from '@/types/design-system'
import { componentRegistry } from '@/lib/design-system/component-registry'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface ComponentPropertyPanelProps {
  componentId: string
  onPropertyChange?: (propName: string, value: unknown) => void
}

export const ComponentPropertyPanel = ({
  componentId,
  onPropertyChange,
}: ComponentPropertyPanelProps) => {
  const [component, setComponent] = useState<ComponentDefinition | null>(null)
  const [editingProperty, setEditingProperty] = useState<string | null>(null)
  const [localValues, setLocalValues] = useState<Record<string, unknown>>({})

  // Load component data
  useEffect(() => {
    const loadedComponent = componentRegistry.getComponent(componentId)
    if (loadedComponent) {
      setComponent(loadedComponent)
      // Initialize local values with current defaults
      const defaultValues: Record<string, unknown> = {}
      Object.entries(loadedComponent.props).forEach(([propName, propDef]) => {
        defaultValues[propName] = propDef.default
      })
      setLocalValues(defaultValues)
    }
  }, [componentId])

  // Subscribe to component changes
  useEffect(() => {
    const unsubscribe = componentRegistry.subscribe(componentId, updatedComponent => {
      setComponent(updatedComponent)
    })

    return unsubscribe
  }, [componentId])

  const handlePropertyUpdate = (propName: string, value: unknown) => {
    setLocalValues(prev => ({ ...prev, [propName]: value }))
    onPropertyChange?.(propName, value)

    // Update component in registry
    if (component) {
      const updatedComponent: ComponentDefinition = {
        ...component,
        props: {
          ...component.props,
          [propName]: {
            ...component.props[propName],
            default: value,
          },
        },
        metadata: {
          ...component.metadata,
          updatedAt: Date.now(),
        },
      }

      componentRegistry.updateComponent(componentId, updatedComponent)
    }
  }

  const handleStartEditing = (propName: string) => {
    setEditingProperty(propName)
  }

  const handleStopEditing = () => {
    setEditingProperty(null)
  }

  const resetPropertyToDefault = (propName: string) => {
    if (component?.props[propName]) {
      const originalDefault = component.props[propName].default
      handlePropertyUpdate(propName, originalDefault)
    }
  }

  if (!component) {
    return (
      <Card>
        <CardContent className='p-6'>
          <div className='text-center text-muted-foreground'>
            Component not found: {componentId}
          </div>
        </CardContent>
      </Card>
    )
  }

  const requiredProps = Object.entries(component.props).filter(([, propDef]) => propDef.required)
  const optionalProps = Object.entries(component.props).filter(([, propDef]) => !propDef.required)

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h3 className='text-lg font-semibold'>Properties</h3>
          <p className='text-sm text-muted-foreground'>
            Configure component properties and defaults
          </p>
        </div>
        <Badge variant='outline'>{Object.keys(component.props).length} properties</Badge>
      </div>

      <Tabs defaultValue='required' className='w-full'>
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='required'>Required ({requiredProps.length})</TabsTrigger>
          <TabsTrigger value='optional'>Optional ({optionalProps.length})</TabsTrigger>
          <TabsTrigger value='all'>All Properties</TabsTrigger>
        </TabsList>

        <TabsContent value='required' className='space-y-4'>
          {requiredProps.length > 0 ? (
            <div className='space-y-4'>
              {requiredProps.map(([propName, propDef]) => (
                <PropertyCard
                  key={propName}
                  propName={propName}
                  propDef={propDef}
                  value={localValues[propName] ?? propDef.default}
                  isEditing={editingProperty === propName}
                  onStartEditing={() => handleStartEditing(propName)}
                  onStopEditing={handleStopEditing}
                  onChange={value => handlePropertyUpdate(propName, value)}
                  onReset={() => resetPropertyToDefault(propName)}
                />
              ))}
            </div>
          ) : (
            <div className='text-center text-muted-foreground py-8'>No required properties</div>
          )}
        </TabsContent>

        <TabsContent value='optional' className='space-y-4'>
          {optionalProps.length > 0 ? (
            <div className='space-y-4'>
              {optionalProps.map(([propName, propDef]) => (
                <PropertyCard
                  key={propName}
                  propName={propName}
                  propDef={propDef}
                  value={localValues[propName] ?? propDef.default}
                  isEditing={editingProperty === propName}
                  onStartEditing={() => handleStartEditing(propName)}
                  onStopEditing={handleStopEditing}
                  onChange={value => handlePropertyUpdate(propName, value)}
                  onReset={() => resetPropertyToDefault(propName)}
                />
              ))}
            </div>
          ) : (
            <div className='text-center text-muted-foreground py-8'>No optional properties</div>
          )}
        </TabsContent>

        <TabsContent value='all' className='space-y-4'>
          <div className='space-y-4'>
            {Object.entries(component.props).map(([propName, propDef]) => (
              <PropertyCard
                key={propName}
                propName={propName}
                propDef={propDef}
                value={localValues[propName] ?? propDef.default}
                isEditing={editingProperty === propName}
                onStartEditing={() => handleStartEditing(propName)}
                onStopEditing={handleStopEditing}
                onChange={value => handlePropertyUpdate(propName, value)}
                onReset={() => resetPropertyToDefault(propName)}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface PropertyCardProps {
  propName: string
  propDef: unknown
  value: unknown
  isEditing: boolean
  onStartEditing: () => void
  onStopEditing: () => void
  onChange: (value: unknown) => void
  onReset: () => void
}

function PropertyCard({
  propName,
  propDef,
  value,
  isEditing,
  onStartEditing,
  onStopEditing,
  onChange,
  onReset,
}: PropertyCardProps) {
  return (
    <Card>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <div>
            <CardTitle className='text-base flex items-center gap-2'>
              {propName}
              {propDef.required && (
                <Badge variant='destructive' className='text-xs'>
                  Required
                </Badge>
              )}
            </CardTitle>
            <p className='text-sm text-muted-foreground'>
              {propDef.description || 'No description available'}
            </p>
          </div>
          <div className='flex items-center gap-2'>
            <Badge variant='outline' className='text-xs'>
              {propDef.type}
            </Badge>
            <Button
              size='sm'
              variant='outline'
              onClick={isEditing ? onStopEditing : onStartEditing}
            >
              {isEditing ? 'Done' : 'Edit'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className='space-y-3'>
        <div className='space-y-2'>
          <label className='text-sm font-medium'>Current Value</label>
          {isEditing ? (
            <PropertyEditor propDef={propDef} value={value} onChange={onChange} />
          ) : (
            <div className='p-2 bg-muted rounded-md text-sm font-mono'>
              {value !== undefined ? JSON.stringify(value) : 'undefined'}
            </div>
          )}
        </div>

        {propDef.default !== undefined && (
          <div className='space-y-2'>
            <label className='text-sm font-medium text-muted-foreground'>Default Value</label>
            <div className='p-2 bg-muted/50 rounded-md text-sm font-mono text-muted-foreground'>
              {JSON.stringify(propDef.default)}
            </div>
          </div>
        )}

        {isEditing && (
          <div className='flex justify-end'>
            <Button size='sm' variant='ghost' onClick={onReset}>
              Reset to Default
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function PropertyEditor({
  propDef,
  value,
  onChange,
}: {
  propDef: unknown
  value: unknown
  onChange: (value: unknown) => void
}) {
  switch (propDef.type) {
    case 'string':
      return (
        <input
          type='text'
          className='w-full p-2 border rounded-md'
          value={value || ''}
          onChange={e => onChange(e.target.value)}
          placeholder={propDef.description}
        />
      )

    case 'number':
      return (
        <input
          type='number'
          className='w-full p-2 border rounded-md'
          value={value || 0}
          onChange={e => onChange(Number(e.target.value))}
          placeholder={propDef.description}
        />
      )

    case 'boolean':
      return (
        <label className='flex items-center space-x-2'>
          <input
            type='checkbox'
            checked={value || false}
            onChange={e => onChange(e.target.checked)}
          />
          <span>Enabled</span>
        </label>
      )

    case 'select':
    case 'enum':
      return (
        <select
          className='w-full p-2 border rounded-md'
          value={value || ''}
          onChange={e => onChange(e.target.value)}
        >
          <option value=''>Choose...</option>
          {propDef.options?.map((option: string) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      )

    default:
      return (
        <textarea
          className='w-full p-2 border rounded-md text-sm font-mono'
          rows={3}
          value={value ? JSON.stringify(value, null, 2) : ''}
          onChange={e => {
            try {
              const parsed = JSON.parse(e.target.value)
              onChange(parsed)
            } catch {
              // Invalid JSON, keep as string
              onChange(e.target.value)
            }
          }}
          placeholder={propDef.description || 'Enter value...'}
        />
      )
  }
}

export default ComponentPropertyPanel
