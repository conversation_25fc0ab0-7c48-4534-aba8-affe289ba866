'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { ComponentDefinition } from '@/lib/design-system/component-registry'

interface LivePreviewProps {
  component: ComponentDefinition
  previewProps?: Record<string, unknown>
  className?: string
}

/**
 * LivePreview Component
 * Renders a live preview of the component being edited
 * Updates in real-time as the component definition changes
 */
export function LivePreview({ component, previewProps = {}, className }: LivePreviewProps) {
  const [error, setError] = useState<string | null>(null)
  const [renderKey, setRenderKey] = useState(0)

  // Force re-render when component definition changes
  useEffect(() => {
    setRenderKey(prev => prev + 1)
    setError(null)
  }, [component])

  // Merge default props with preview props
  const mergedProps = useMemo(() => {
    const defaultProps: Record<string, unknown> = {}

    // Extract default values from component props definition
    Object.entries(component.props || {}).forEach(([key, propDef]) => {
      if (typeof propDef === 'object' && propDef.default !== undefined) {
        defaultProps[key] = propDef.default
      }
    })

    return { ...defaultProps, ...previewProps }
  }, [component.props, previewProps])

  // Generate CSS classes from component styling
  const generateClasses = useMemo(() => {
    const { baseClasses, variants } = component.styling || {}
    let classes = baseClasses || ''

    // Apply variant classes based on props
    if (variants) {
      Object.entries(variants).forEach(([variantName, variantOptions]) => {
        const propValue = mergedProps[variantName]
        if (propValue && variantOptions && typeof variantOptions === 'object') {
          const variantClass = variantOptions[propValue]
          if (variantClass) {
            classes += ` ${variantClass}`
          }
        }
      })
    }

    return classes
  }, [component.styling, mergedProps])

  // Render the actual component based on its type
  const renderComponent = () => {
    try {
      const componentProps = {
        ...mergedProps,
        className: cn(generateClasses, mergedProps.className),
        key: renderKey,
      }

      // Handle different component types
      switch (component.id) {
        case 'button-primary':
          return <Button {...componentProps}>{componentProps.children || 'Button'}</Button>

        case 'card-basic':
          return (
            <Card {...componentProps}>
              <div className='p-6'>
                {componentProps.children || (
                  <>
                    <h3 className='text-lg font-semibold mb-2'>Card Title</h3>
                    <p className='text-muted-foreground'>
                      This is a sample card with some content to demonstrate the layout.
                    </p>
                  </>
                )}
              </div>
            </Card>
          )

        default:
          // Generic component renderer
          return (
            <div {...componentProps}>
              <Badge variant='outline' className='mb-2'>
                {component.name}
              </Badge>
              <div className='text-sm text-muted-foreground'>
                {componentProps.children || `Preview of ${component.name}`}
              </div>
            </div>
          )
      }
    } catch {
      setError(err instanceof Error ? err.message : 'Unknown error')
      return null
    }
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Preview Header */}
      <div className='flex items-center justify-between'>
        <div className='space-y-1'>
          <h3 className='text-sm font-medium'>Live Preview</h3>
          <p className='text-xs text-muted-foreground'>
            Real-time preview of {component.name} v{component.version}
          </p>
        </div>
        <Badge variant='secondary' className='text-xs'>
          {component.category}
        </Badge>
      </div>

      <Separator />

      {/* Preview Content */}
      <div className='min-h-[200px] p-6 border rounded-lg bg-background'>
        {error ? (
          <div className='flex items-center justify-center h-full'>
            <div className='text-center space-y-2'>
              <Badge variant='destructive'>Error</Badge>
              <p className='text-sm text-muted-foreground'>{error}</p>
            </div>
          </div>
        ) : (
          <div className='flex items-center justify-center h-full'>{renderComponent()}</div>
        )}
      </div>

      {/* Props Display */}
      {Object.keys(mergedProps).length > 0 && (
        <div className='space-y-2'>
          <h4 className='text-xs font-medium text-muted-foreground uppercase'>Current Props</h4>
          <div className='text-xs bg-muted p-2 rounded border'>
            <pre className='font-mono'>{JSON.stringify(mergedProps, null, 2)}</pre>
          </div>
        </div>
      )}

      {/* Component Metadata */}
      <div className='space-y-2'>
        <h4 className='text-xs font-medium text-muted-foreground uppercase'>Component Info</h4>
        <div className='grid grid-cols-2 gap-2 text-xs'>
          <div>
            <span className='text-muted-foreground'>ID:</span> {component.id}
          </div>
          <div>
            <span className='text-muted-foreground'>Version:</span> {component.version}
          </div>
          <div>
            <span className='text-muted-foreground'>Category:</span> {component.category}
          </div>
          <div>
            <span className='text-muted-foreground'>Updated:</span>{' '}
            {new Date(component.metadata?.updatedAt || Date.now()).toLocaleDateString()}
          </div>
        </div>
      </div>
    </div>
  )
}
