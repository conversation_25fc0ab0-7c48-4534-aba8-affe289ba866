'use client'

import { cn } from '@/lib/utils'
import { Laptop, Monitor, RotateCcw, Smartphone, Tablet, ZoomIn, ZoomOut } from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

interface LivePreviewPanelProps {
  componentCode?: string
  componentProps?: Record<string, unknown>
  componentId?: string
  component?: unknown // Legacy support for component object
  onError?: (_error: string) => void
  onRenderComplete?: (renderTime: number) => void
  onRenderTime?: (renderTime: number) => void // Legacy support
  showMetrics?: boolean
  enableInteractions?: boolean
  enableDevTools?: boolean
  className?: string
}

interface RenderMetrics {
  renderTime: number
  componentCount: number
  memoryUsage: number
  lastRender: Date
  errors: string[]
}

type ViewportSize = {
  name: string
  width: number
  height: number
  icon: React.ComponentType<unknown>
}

const VIEWPORT_SIZES: ViewportSize[] = [
  { name: 'Mobile', width: 375, height: 667, icon: Smartphone },
  { name: 'Tablet', width: 768, height: 1024, icon: Tablet },
  { name: 'Laptop', width: 1024, height: 768, icon: Laptop },
  { name: 'Desktop', width: 1440, height: 900, icon: Monitor },
]

export function LivePreviewPanel({
  componentCode = '',
  componentProps = {},
  componentId = 'preview-component',
  component,
  onError,
  onRenderComplete,
  onRenderTime,
  _showMetrics = true,
  enableInteractions = true,
  _enableDevTools = false,
  className,
}: LivePreviewPanelProps) {
  const [_isFullscreen, _setIsFullscreen] = useState(false)
  const [currentViewport, setCurrentViewport] = useState<ViewportSize>(VIEWPORT_SIZES[3]) // Desktop
  const [_isLoading, setIsLoading] = useState(false)
  const [_renderMetrics, setRenderMetrics] = useState<RenderMetrics>({
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0,
    lastRender: new Date(),
    errors: [],
  })
  const [isAutoRefresh, _setIsAutoRefresh] = useState(true)
  const [_previewKey, setPreviewKey] = useState(0) // Force re-render
  const [_devMode, _setDevMode] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(100)
  const [selectedVariant, setSelectedVariant] = useState('default')
  const [selectedSize, setSelectedSize] = useState('md')
  const [isDarkMode, setIsDarkMode] = useState(false)

  const previewRef = useRef<HTMLDivElement>(null)
  const _iframeRef = useRef<HTMLIFrameElement>(null)
  const renderStartTime = useRef<number>(0)

  // Handle legacy component prop
  const actualComponentCode = componentCode || component?.code || ''
  const actualComponentProps = useMemo(
    () => componentProps || component?.props || {},
    [componentProps, component]
  )
  const actualComponentId = componentId || component?.id || 'preview-component'

  // Force refresh preview
  const _forceRefresh = useCallback(() => {
    setPreviewKey(prev => prev + 1)
    renderComponent()
  }, [])

  // Render component with error handling and metrics
  const renderComponent = useCallback(async () => {
    if (!actualComponentCode && !actualComponentId) return

    setIsLoading(true)
    renderStartTime.current = performance.now()

    try {
      const startTime = performance.now()

      // In a real implementation, this would compile and render the actual component
      // For now, we'll simulate the rendering process
      await new Promise(resolve => setTimeout(resolve, 100)) // Simulate render time

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Create a more realistic component preview based on the component type
      const createComponentPreview = (componentId: string) => {
        switch (componentId) {
          case 'button-primary':
            return `
              <div style="display: flex; flex-direction: column; gap: 16px;">
                <div style="
                  padding: 12px 24px;
                  background: hsl(var(--primary));
                  color: hsl(var(--primary-foreground));
                  border: none;
                  border-radius: 6px;
                  font-size: 14px;
                  font-weight: 500;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                  width: fit-content;
                " onmouseover="this.style.background='hsl(var(--primary) / 0.9)'" onmouseout="this.style.background='hsl(var(--primary))'">
                  Primary Button
                </div>
                <div style="
                  padding: 12px 24px;
                  background: transparent;
                  color: hsl(var(--primary));
                  border: 1px solid hsl(var(--primary));
                  border-radius: 6px;
                  font-size: 14px;
                  font-weight: 500;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                  width: fit-content;
                " onmouseover="this.style.background='hsl(var(--primary) / 0.1)'" onmouseout="this.style.background='transparent'">
                  Secondary Button
                </div>
                <div style="
                  padding: 12px 24px;
                  background: hsl(var(--destructive));
                  color: hsl(var(--destructive-foreground));
                  border: none;
                  border-radius: 6px;
                  font-size: 14px;
                  font-weight: 500;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                  width: fit-content;
                " onmouseover="this.style.background='hsl(var(--destructive) / 0.9)'" onmouseout="this.style.background='hsl(var(--destructive))'">
                  Destructive Button
                </div>
              </div>
            `
          case 'card-basic':
            return `
              <div style="
                border: 1px solid hsl(var(--border));
                border-radius: 8px;
                background: hsl(var(--card));
                color: hsl(var(--card-foreground));
                padding: 24px;
                box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
              ">
                <div style="
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 8px;
                  color: hsl(var(--foreground));
                ">
                  Card Title
                </div>
                <div style="
                  font-size: 14px;
                  color: hsl(var(--muted-foreground));
                  margin-bottom: 16px;
                ">
                  This is a card description that explains what this card component does.
                </div>
                <div style="display: flex; gap: 8px;">
                  <div style="
                    padding: 8px 16px;
                    background: hsl(var(--primary));
                    color: hsl(var(--primary-foreground));
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                    cursor: pointer;
                  ">
                    Action
                  </div>
                  <div style="
                    padding: 8px 16px;
                    background: transparent;
                    color: hsl(var(--muted-foreground));
                    border: 1px solid hsl(var(--border));
                    border-radius: 4px;
                    font-size: 12px;
                    cursor: pointer;
                  ">
                    Cancel
                  </div>
                </div>
              </div>
            `
          default:
            return `
              <div style="
                padding: 20px;
                border: 1px dashed hsl(var(--border));
                border-radius: 8px;
                background: hsl(var(--muted) / 0.3);
                color: hsl(var(--muted-foreground));
                text-align: center;
              ">
                <div style="
                  font-size: 16px;
                  font-weight: 500;
                  margin-bottom: 8px;
                  color: hsl(var(--foreground));
                ">
                  ${actualComponentId}
                </div>
                <div style="font-size: 14px; margin-bottom: 12px;">
                  Component preview would render here
                </div>
                <div style="
                  padding: 8px 16px;
                  background: hsl(var(--primary));
                  color: hsl(var(--primary-foreground));
                  border: none;
                  border-radius: 4px;
                  font-size: 12px;
                  cursor: pointer;
                  display: inline-block;
                ">
                  Interactive Element
                </div>
              </div>
            `
        }
      }

      // Update preview content with theme-aware styling
      if (previewRef.current) {
        previewRef.current.innerHTML = `
          <div style="
            padding: 20px;
            background: hsl(var(--background));
            color: hsl(var(--foreground));
            border-radius: 8px;
            min-height: 200px;
            transform: scale(${zoomLevel / 100});
            transform-origin: top left;
            transition: all 0.2s ease;
          ">
            <div style="
              margin-bottom: 16px;
              padding-bottom: 12px;
              border-bottom: 1px solid hsl(var(--border));
            ">
              <div style="
                font-weight: 600;
                color: hsl(var(--foreground));
                font-size: 16px;
                margin-bottom: 4px;
              ">
                ${actualComponentId}
              </div>
              <div style="
                font-size: 12px;
                color: hsl(var(--muted-foreground));
              ">
                Live component preview • ${Object.keys(actualComponentProps).length} props
              </div>
            </div>

            <div style="margin-bottom: 20px;">
              ${createComponentPreview(actualComponentId)}
            </div>

            ${
              enableInteractions
                ? `
              <div style="
                padding: 16px;
                background: hsl(var(--muted) / 0.5);
                border-radius: 6px;
                border: 1px solid hsl(var(--border));
                margin-top: 16px;
              ">
                <div style="
                  font-size: 12px;
                  font-weight: 500;
                  color: hsl(var(--foreground));
                  margin-bottom: 8px;
                ">
                  Interactive Demo
                </div>
                <div style="display: flex; gap: 8px; align-items: center; flex-wrap: wrap;">
                  <input style="
                    padding: 8px 12px;
                    border: 1px solid hsl(var(--border));
                    border-radius: 6px;
                    font-size: 12px;
                    background: hsl(var(--background));
                    color: hsl(var(--foreground));
                    min-width: 120px;
                  " placeholder="Test input" />
                  <select style="
                    padding: 8px 12px;
                    border: 1px solid hsl(var(--border));
                    border-radius: 6px;
                    font-size: 12px;
                    background: hsl(var(--background));
                    color: hsl(var(--foreground));
                  ">
                    <option>Option 1</option>
                    <option>Option 2</option>
                  </select>
                  <label style="display: flex; align-items: center; gap: 4px; font-size: 12px; color: hsl(var(--foreground));">
                    <input type="checkbox" style="margin: 0;">
                    Checkbox
                  </label>
                </div>
              </div>
            `
                : ''
            }
          </div>
        `
      }

      // Update metrics
      const newMetrics: RenderMetrics = {
        renderTime,
        componentCount: 1, // Would be calculated from actual component tree
        memoryUsage: Math.random() * 10, // Would get from performance.memory
        lastRender: new Date(),
        errors: [],
      }

      setRenderMetrics(newMetrics)

      // Support both callback patterns
      onRenderComplete?.(renderTime)
      onRenderTime?.(renderTime)
    } catch {
      const errorMessage = error instanceof Error ? error.message : 'Render failed'

      if (previewRef.current) {
        previewRef.current.innerHTML = `
          <div style="
            padding: 16px;
            border: 1px solid hsl(var(--destructive));
            border-radius: 8px;
            background: hsl(var(--destructive) / 0.1);
            color: hsl(var(--destructive));
          ">
            <div style="
              font-weight: 600;
              margin-bottom: 8px;
              display: flex;
              align-items: center;
              gap: 8px;
            ">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L1 21h22L12 2zm0 3.5L19.5 19h-15L12 5.5zm-1 8.5h2v2h-2v-2zm0-6h2v4h-2V8z"/>
              </svg>
              Render Error
            </div>
            <div style="font-size: 14px;">${errorMessage}</div>
          </div>
        `
      }

      setRenderMetrics(prev => ({
        ...prev,
        errors: [...prev.errors, errorMessage],
        lastRender: new Date(),
      }))

      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [
    actualComponentCode,
    actualComponentProps,
    actualComponentId,
    onError,
    onRenderComplete,
    onRenderTime,
    enableInteractions,
    zoomLevel,
  ])

  // Auto-refresh when code/props change
  useEffect(() => {
    if (isAutoRefresh) {
      const debounceTimer = setTimeout(() => {
        renderComponent()
      }, 300)
      return () => clearTimeout(debounceTimer)
    }
  }, [actualComponentCode, actualComponentProps, isAutoRefresh, renderComponent])

  // Initial render
  useEffect(() => {
    renderComponent()
  }, [renderComponent])

  // Export preview as image (simulated)
  const _exportPreview = useCallback(async () => {
    if (!previewRef.current) return

    try {
      if (process.env.NODE_ENV === 'development')
        console.log('Exporting preview...', {
          componentId: actualComponentId,
          viewport: currentViewport,
          zoom: zoomLevel,
        })

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch {
      // Error handling
    }
  }, [actualComponentId, currentViewport, zoomLevel])

  // Share preview (simulated)
  const _sharePreview = useCallback(async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Component Preview: ${actualComponentId}`,
          text: 'Check out this component preview',
          url: window.location.href,
        })
      } catch {
        // console.log('Share failed:', _err);
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href)
        // console.log('URL copied to clipboard');
      } catch {
        // console.log('Copy failed:', _err);
      }
    }
  }, [actualComponentId])

  const _ViewportIcon = currentViewport.icon

  // Generate realistic preview based on component type
  const generatePreview = () => {
    if (!component) {
      return (
        <div className='flex items-center justify-center h-32 lg:h-48 text-muted-foreground text-sm'>
          No component selected
        </div>
      )
    }

    const componentType = component.category || 'button'

    switch (componentType.toLowerCase()) {
      case 'button':
        return (
          <div className='space-y-3 lg:space-y-4'>
            <div className='space-y-2'>
              <h4 className='text-xs lg:text-sm font-medium text-card-foreground'>
                Button Variants
              </h4>
              <div className='flex flex-wrap gap-2'>
                <button className='px-3 py-1.5 lg:px-4 lg:py-2 bg-primary text-primary-foreground rounded-md text-xs lg:text-sm font-medium hover:bg-primary/90 transition-colors'>
                  Primary
                </button>
                <button className='px-3 py-1.5 lg:px-4 lg:py-2 bg-secondary text-secondary-foreground rounded-md text-xs lg:text-sm font-medium hover:bg-secondary/80 transition-colors'>
                  Secondary
                </button>
                <button className='px-3 py-1.5 lg:px-4 lg:py-2 border border-border bg-background text-foreground rounded-md text-xs lg:text-sm font-medium hover:bg-accent transition-colors'>
                  Outline
                </button>
              </div>
            </div>

            <div className='space-y-2'>
              <h4 className='text-xs lg:text-sm font-medium text-card-foreground'>Sizes</h4>
              <div className='flex flex-wrap items-center gap-2'>
                <button className='px-2 py-1 bg-primary text-primary-foreground rounded text-xs font-medium'>
                  Small
                </button>
                <button className='px-3 py-1.5 lg:px-4 lg:py-2 bg-primary text-primary-foreground rounded-md text-xs lg:text-sm font-medium'>
                  Medium
                </button>
                <button className='px-4 py-2 lg:px-6 lg:py-3 bg-primary text-primary-foreground rounded-lg text-sm lg:text-base font-medium'>
                  Large
                </button>
              </div>
            </div>
          </div>
        )

      case 'card':
        return (
          <div className='space-y-3 lg:space-y-4'>
            <div className='border border-border rounded-lg p-3 lg:p-4 bg-card'>
              <div className='space-y-2 lg:space-y-3'>
                <div className='flex items-center gap-2'>
                  <div className='w-6 h-6 lg:w-8 lg:h-8 bg-primary rounded-full flex items-center justify-center'>
                    <span className='text-xs lg:text-sm text-primary-foreground font-medium'>
                      A
                    </span>
                  </div>
                  <div>
                    <h3 className='text-sm lg:text-base font-medium text-card-foreground'>
                      Card Title
                    </h3>
                    <p className='text-xs lg:text-sm text-muted-foreground'>Card description</p>
                  </div>
                </div>
                <p className='text-xs lg:text-sm text-card-foreground'>
                  This is a sample card component with responsive design.
                </p>
                <div className='flex gap-2'>
                  <button className='px-2 py-1 lg:px-3 lg:py-1.5 bg-primary text-primary-foreground rounded text-xs lg:text-sm'>
                    Action
                  </button>
                  <button className='px-2 py-1 lg:px-3 lg:py-1.5 border border-border rounded text-xs lg:text-sm'>
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )

      case 'input':
        return (
          <div className='space-y-3 lg:space-y-4'>
            <div className='space-y-2'>
              <label className='text-xs lg:text-sm font-medium text-card-foreground'>
                _Input Variants
              </label>
              <div className='space-y-2 lg:space-y-3'>
                <input
                  type='text'
                  placeholder='Default input'
                  className='w-full px-3 py-1.5 lg:py-2 border border-border rounded-md bg-background text-foreground text-xs lg:text-sm focus:outline-none focus:ring-2 focus:ring-primary/20'
                />
                <input
                  type='email'
                  placeholder='Email input'
                  className='w-full px-3 py-1.5 lg:py-2 border border-border rounded-md bg-background text-foreground text-xs lg:text-sm focus:outline-none focus:ring-2 focus:ring-primary/20'
                />
                <textarea
                  placeholder='Textarea input'
                  rows={2}
                  className='w-full px-3 py-1.5 lg:py-2 border border-border rounded-md bg-background text-foreground text-xs lg:text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none'
                />
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div className='space-y-3 lg:space-y-4'>
            <div className='border border-border rounded-lg p-3 lg:p-4 bg-card'>
              <div className='space-y-2 lg:space-y-3'>
                <h3 className='text-sm lg:text-base font-medium text-card-foreground'>
                  {component.name}
                </h3>
                <p className='text-xs lg:text-sm text-muted-foreground'>
                  Component preview for {componentType}
                </p>
                <div className='flex items-center gap-2'>
                  <div className='w-3 h-3 lg:w-4 lg:h-4 bg-primary rounded-full'></div>
                  <span className='text-xs lg:text-sm text-card-foreground'>
                    Interactive element
                  </span>
                </div>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* Preview Controls */}
      <div className='flex flex-col gap-2 mb-3 lg:mb-4 p-2 lg:p-3 bg-muted/30 rounded-lg border border-border'>
        {/* Top Row: Viewport Controls */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-1 lg:gap-2'>
            <span className='text-xs lg:text-sm font-medium text-card-foreground'>Viewport:</span>
            <div className='flex items-center gap-1'>
              {VIEWPORT_SIZES.map(viewport => {
                const ViewportIcon = viewport.icon
                return (
                  <button
                    key={viewport.name}
                    onClick={() => setCurrentViewport(viewport)}
                    className={cn(
                      'p-1.5 lg:p-2 rounded border transition-colors',
                      currentViewport.name === viewport.name
                        ? 'bg-primary text-primary-foreground border-primary'
                        : 'bg-background border-border hover:bg-accent'
                    )}
                    title={`${viewport.name} (${viewport.width}x${viewport.height})`}
                    aria-label={`Switch to ${viewport.name} viewport`}
                  >
                    <ViewportIcon
                      className='w-3 h-3 lg:w-4 lg:h-4'
                      data-testid={viewport.name.toLowerCase()}
                    />
                  </button>
                )
              })}
            </div>
            <span className='text-xs text-muted-foreground ml-2'>
              {currentViewport.width}×{currentViewport.height}
            </span>
          </div>

          {/* Zoom Controls */}
          <div className='flex items-center gap-1 lg:gap-2'>
            <span className='text-xs lg:text-sm font-medium text-card-foreground'>Zoom:</span>
            <div className='flex items-center gap-1'>
              <button
                onClick={() => setZoomLevel(Math.max(25, zoomLevel - 25))}
                className='p-1.5 lg:p-2 rounded border bg-background border-border hover:bg-accent transition-colors'
                title='Zoom out'
                aria-label='Zoom out'
                disabled={zoomLevel <= 25}
              >
                <ZoomOut className='w-3 h-3 lg:w-4 lg:h-4' />
              </button>

              <span className='text-xs lg:text-sm font-mono min-w-[3rem] text-center px-2 py-1 bg-background border border-border rounded'>
                {zoomLevel}%
              </span>

              <button
                onClick={() => setZoomLevel(Math.min(200, zoomLevel + 25))}
                className='p-1.5 lg:p-2 rounded border bg-background border-border hover:bg-accent transition-colors'
                title='Zoom in'
                aria-label='Zoom in'
                disabled={zoomLevel >= 200}
              >
                <ZoomIn className='w-3 h-3 lg:w-4 lg:h-4' />
              </button>

              <button
                onClick={() => setZoomLevel(100)}
                className='p-1.5 lg:p-2 rounded border bg-background border-border hover:bg-accent transition-colors'
                title='Reset zoom'
                aria-label='Reset zoom to 100%'
              >
                <RotateCcw className='w-3 h-3 lg:w-4 lg:h-4' />
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Row: Preview and Size Controls */}
        <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-2'>
          <div className='flex items-center gap-1 lg:gap-2'>
            <span className='text-xs lg:text-sm font-medium text-card-foreground'>Preview:</span>
            <select
              value={selectedVariant}
              onChange={e => setSelectedVariant(e.target.value)}
              className='text-xs lg:text-sm bg-background border border-border rounded px-2 py-1'
              aria-label='Select component variant'
            >
              <option value='default'>Default</option>
              <option value='hover'>Hover</option>
              <option value='active'>Active</option>
              <option value='disabled'>Disabled</option>
            </select>
          </div>

          <div className='flex items-center gap-1 lg:gap-2'>
            <button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className='text-xs lg:text-sm px-2 py-1 bg-background border border-border rounded hover:bg-accent transition-colors'
              aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}
            >
              {isDarkMode ? '🌙' : '☀️'}
            </button>
            <select
              value={selectedSize}
              onChange={e => setSelectedSize(e.target.value)}
              className='text-xs lg:text-sm bg-background border border-border rounded px-2 py-1'
              aria-label='Select component size'
            >
              <option value='sm'>Small</option>
              <option value='md'>Medium</option>
              <option value='lg'>Large</option>
            </select>
          </div>
        </div>
      </div>

      {/* Preview Area */}
      <div className='flex-1 min-h-[200px] lg:min-h-[300px]'>
        <div
          className={cn(
            'h-full border border-border rounded-lg p-3 lg:p-4 overflow-auto',
            isDarkMode ? 'bg-slate-900 text-white' : 'bg-background'
          )}
          style={{
            maxWidth: currentViewport.width > 0 ? `${currentViewport.width}px` : '100%',
            transition: 'max-width 0.3s ease',
          }}
        >
          <div
            className='h-full flex flex-col'
            style={{
              transform: `scale(${zoomLevel / 100})`,
              transformOrigin: 'top left',
              transition: 'transform 0.2s ease',
            }}
          >
            {/* Component Info */}
            <div className='mb-3 lg:mb-4 pb-2 lg:pb-3 border-b border-border'>
              <div className='flex items-center justify-between'>
                <div>
                  <h3 className='text-sm lg:text-base font-medium text-card-foreground'>
                    Live component preview
                  </h3>
                  <p className='text-xs lg:text-sm text-muted-foreground'>
                    {component?.name || 'Component'} - {component?.category || 'Unknown'} •{' '}
                    {selectedVariant} variant • {Object.keys(actualComponentProps).length} props
                  </p>
                </div>
                <div className='flex items-center gap-1 lg:gap-2'>
                  <span className='text-xs px-2 py-1 bg-green-500/10 text-green-600 rounded border border-green-500/20'>
                    Live
                  </span>
                  <span className='text-xs px-2 py-1 bg-blue-500/10 text-blue-600 rounded border border-blue-500/20'>
                    {selectedSize.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            {/* Generated Preview */}
            <div className='flex-1'>{generatePreview()}</div>

            {/* Component Stats */}
            <div className='mt-3 lg:mt-4 pt-2 lg:pt-3 border-t border-border'>
              <div className='grid grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3 text-xs lg:text-sm'>
                <div className='text-center'>
                  <div className='font-medium text-card-foreground'>Props</div>
                  <div className='text-muted-foreground'>
                    {Object.keys(actualComponentProps).length}
                  </div>
                </div>
                <div className='text-center'>
                  <div className='font-medium text-card-foreground'>Size</div>
                  <div className='text-muted-foreground'>2.1KB</div>
                </div>
                <div className='text-center col-span-2 lg:col-span-1'>
                  <div className='font-medium text-card-foreground'>Status</div>
                  <div className='text-green-600'>Ready</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
