'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Eye,
  Code,
  Save,
  Undo,
  Redo,
  Copy,
  Download,
  Upload,
  RefreshCw,
  Play,
  Square,
  AlertTriangle,
} from 'lucide-react'

interface WYSIWYGEditorProps {
  component: unknown
  onComponentChange: (component: unknown) => void
  className?: string
}

export function WYSIWYGEditor({ component, onComponentChange, className }: WYSIWYGEditorProps) {
  const [code, setCode] = useState(component.code)
  const [props, setProps] = useState(component.props)
  const [activeTab, setActiveTab] = useState<'visual' | 'code' | 'props'>('visual')
  const [previewMode, setPreviewMode] = useState<'preview' | 'edit'>('edit')
  const [isLivePreview, setIsLivePreview] = useState(true)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // History for undo/redo
  const [history, setHistory] = useState<Array<{ code: string; props: Record<string, any> }>>([
    { code: component.code, props: component.props },
  ])
  const [historyIndex, setHistoryIndex] = useState(0)

  const codeEditorRef = useRef<HTMLTextAreaElement>(null)
  const previewRef = useRef<HTMLDivElement>(null)

  // Handle code changes
  const handleCodeChange = useCallback(
    (newCode: string) => {
      setCode(newCode)
      setHasUnsavedChanges(true)
      onComponentChange({ ...component, code: newCode })

      // Add to history
      const newHistoryEntry = { code: newCode, props }
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(newHistoryEntry)
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)
    },
    [component, history, historyIndex, onComponentChange]
  )

  // Handle props changes
  const handlePropsChange = useCallback(
    (newProps: Record<string, any>) => {
      setProps(newProps)
      setHasUnsavedChanges(true)
      onComponentChange({ ...component, props: newProps })

      // Add to history
      const newHistoryEntry = { code, props: newProps }
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(newHistoryEntry)
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)
    },
    [component, history, historyIndex, onComponentChange]
  )

  // Undo/Redo functions
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      const { code: prevCode, props: prevProps } = history[newIndex]
      setCode(prevCode)
      setProps(prevProps)
      setHistoryIndex(newIndex)
      setHasUnsavedChanges(true)
    }
  }, [history, historyIndex])

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      const { code: nextCode, props: nextProps } = history[newIndex]
      setCode(nextCode)
      setProps(nextProps)
      setHistoryIndex(newIndex)
      setHasUnsavedChanges(true)
    }
  }, [history, historyIndex])

  // Save function
  const handleSave = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      onComponentChange({ ...component, code, props })
      setHasUnsavedChanges(false)
    } catch {
      setError(err instanceof Error ? err.message : 'Save failed')
    } finally {
      setIsLoading(false)
    }
  }, [component, onComponentChange])

  // Copy code to clipboard
  const copyToClipboard = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code)
    } catch {
      // console.error('Failed to copy code:', err);
    }
  }, [code])

  // Live preview update
  useEffect(() => {
    if (isLivePreview && previewRef.current) {
      // In a real implementation, this would compile and render the component
      // For now, we'll just show the code
      try {
        previewRef.current.innerHTML = `
          <div class="p-4 border rounded-lg bg-background">
            <h4 class="text-sm font-medium mb-2">Live Preview</h4>
            <div class="text-xs text-muted-foreground mb-2">Component: ${component.id}</div>
            <pre class="text-xs bg-muted p-2 rounded overflow-auto max-h-32">${code}</pre>
            <div class="mt-2 text-xs text-muted-foreground">
              Props: ${JSON.stringify(props, null, 2)}
            </div>
          </div>
        `
      } catch {
        previewRef.current.innerHTML = `
          <div class="p-4 border rounded-lg bg-destructive/10 border-destructive">
            <div class="text-destructive text-sm">Preview Error: ${err}</div>
          </div>
        `
      }
    }
  }, [code, props, isLivePreview, component.id])

  return (
    <div className='flex flex-col h-full bg-background'>
      {/* Toolbar */}
      <div className='flex items-center justify-between p-4 border-b border-border bg-card/30'>
        <div className='flex items-center gap-2'>
          <h3 className='text-sm font-medium text-card-foreground'>WYSIWYG Editor</h3>
          <Badge variant='outline' className='text-xs border-border text-muted-foreground'>
            {component.id}
          </Badge>
          {hasUnsavedChanges && (
            <Badge variant='destructive' className='text-xs'>
              Unsaved
            </Badge>
          )}
          {error && (
            <Badge variant='destructive' className='text-xs gap-1'>
              <AlertTriangle className='w-3 h-3' />
              Error
            </Badge>
          )}
        </div>

        <div className='flex items-center gap-2'>
          {/* Undo/Redo */}
          <Button
            variant='ghost'
            size='sm'
            onClick={undo}
            disabled={historyIndex <= 0}
            title='Undo (Ctrl+Z)'
            className='h-8 w-8 p-0 hover:bg-accent'
          >
            <Undo className='h-4 w-4' />
          </Button>
          <Button
            variant='ghost'
            size='sm'
            onClick={redo}
            disabled={historyIndex >= history.length - 1}
            title='Redo (Ctrl+Y)'
            className='h-8 w-8 p-0 hover:bg-accent'
          >
            <Redo className='h-4 w-4' />
          </Button>

          <div className='w-px h-6 bg-border mx-1' />

          {/* Copy/Export */}
          <Button
            variant='ghost'
            size='sm'
            onClick={copyToClipboard}
            title='Copy to clipboard'
            className='h-8 w-8 p-0 hover:bg-accent'
          >
            <Copy className='h-4 w-4' />
          </Button>

          <div className='w-px h-6 bg-border mx-1' />

          {/* Live Preview Toggle */}
          <Button
            variant={isLivePreview ? 'default' : 'ghost'}
            size='sm'
            onClick={() => setIsLivePreview(!isLivePreview)}
            title='Toggle live preview'
            className='h-8 px-3 text-xs'
          >
            {isLivePreview ? (
              <Square className='h-3 w-3 mr-1' />
            ) : (
              <Play className='h-3 w-3 mr-1' />
            )}
            {isLivePreview ? 'Stop' : 'Live'}
          </Button>

          {/* Save */}
          <Button
            onClick={handleSave}
            disabled={!hasUnsavedChanges || isLoading}
            size='sm'
            className='h-8 px-3 text-xs bg-primary hover:bg-primary/90'
          >
            {isLoading ? (
              <RefreshCw className='h-3 w-3 mr-1 animate-spin' />
            ) : (
              <Save className='h-3 w-3 mr-1' />
            )}
            Save
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className='flex-1 flex flex-col'>
        <Tabs
          value={activeTab}
          onValueChange={value => setActiveTab(value as any)}
          className='flex-1 flex flex-col'
        >
          {/* Tab List */}
          <div className='border-b border-border bg-muted/30'>
            <TabsList className='h-10 bg-transparent border-0 rounded-none w-full justify-start'>
              <TabsTrigger
                value='visual'
                className='data-[state=active]:bg-background data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none border-b-2 border-transparent'
              >
                Visual Editor
              </TabsTrigger>
              <TabsTrigger
                value='code'
                className='data-[state=active]:bg-background data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none border-b-2 border-transparent'
              >
                Code Editor
              </TabsTrigger>
              <TabsTrigger
                value='props'
                className='data-[state=active]:bg-background data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none border-b-2 border-transparent'
              >
                Properties
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab Content */}
          <div className='flex-1 flex'>
            <TabsContent value='visual' className='flex-1 m-0 p-0'>
              <div className='h-full flex'>
                {/* Visual Editor Area */}
                <div className='flex-1 p-6 bg-background/50'>
                  <div className='h-full border-2 border-dashed border-border rounded-lg flex items-center justify-center bg-card/30'>
                    <div className='text-center'>
                      <div className='text-sm font-medium text-card-foreground mb-2'>
                        Visual Editor Area
                      </div>
                      <div className='text-xs text-muted-foreground mb-4'>
                        Component: {component.id}
                      </div>
                      <div className='text-xs text-muted-foreground'>
                        Drag and drop components, click to edit properties.
                      </div>
                    </div>
                  </div>
                </div>

                {/* Live Preview Sidebar */}
                {isLivePreview && (
                  <div className='w-80 border-l border-border bg-card/50'>
                    <div className='p-4 border-b border-border'>
                      <h4 className='text-sm font-medium text-card-foreground'>Live Preview</h4>
                    </div>
                    <div className='p-4'>
                      <div ref={previewRef} className='min-h-32' />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value='code' className='flex-1 m-0 p-0'>
              <div className='h-full flex'>
                {/* Code Editor */}
                <div className='flex-1 flex flex-col'>
                  <div className='flex-1 p-4 bg-background/50'>
                    <Textarea
                      ref={codeEditorRef}
                      value={code}
                      onChange={e => handleCodeChange(e.target.value)}
                      placeholder='Enter your component code here...'
                      className='h-full resize-none font-mono text-sm bg-background border-border text-foreground'
                    />
                  </div>

                  {error && (
                    <div className='p-3 bg-destructive/10 border-t border-destructive/20'>
                      <div className='text-sm text-destructive flex items-center gap-2'>
                        <AlertTriangle className='w-4 h-4' />
                        {error}
                      </div>
                    </div>
                  )}
                </div>

                {/* Live Preview Sidebar */}
                {isLivePreview && (
                  <div className='w-80 border-l border-border bg-card/50'>
                    <div className='p-4 border-b border-border'>
                      <h4 className='text-sm font-medium text-card-foreground'>Live Preview</h4>
                    </div>
                    <div className='p-4'>
                      <div ref={previewRef} className='min-h-32' />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value='props' className='flex-1 m-0 p-0'>
              <div className='h-full flex'>
                {/* Properties Panel */}
                <div className='flex-1 p-6 bg-background/50 overflow-auto'>
                  <div className='space-y-4'>
                    <div className='text-sm font-medium text-card-foreground mb-4'>
                      Component Properties
                    </div>

                    {Object.entries(props).map(([key, value]) => (
                      <div key={key} className='space-y-2'>
                        <Label htmlFor={key} className='text-sm text-card-foreground'>
                          {key}
                        </Label>
                        <Input
                          id={key}
                          value={typeof value === 'string' ? value : JSON.stringify(value)}
                          onChange={e => {
                            const newProps = { ...props }
                            try {
                              newProps[key] = JSON.parse(e.target.value)
                            } catch {
                              newProps[key] = e.target.value
                            }
                            handlePropsChange(newProps)
                          }}
                          className='bg-background border-border text-foreground'
                        />
                      </div>
                    ))}

                    {Object.keys(props).length === 0 && (
                      <div className='text-center py-8'>
                        <div className='text-sm text-muted-foreground'>No properties defined</div>
                        <div className='text-xs text-muted-foreground mt-1'>
                          Add properties in the code editor
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Live Preview Sidebar */}
                {isLivePreview && (
                  <div className='w-80 border-l border-border bg-card/50'>
                    <div className='p-4 border-b border-border'>
                      <h4 className='text-sm font-medium text-card-foreground'>Live Preview</h4>
                    </div>
                    <div className='p-4'>
                      <div ref={previewRef} className='min-h-32' />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  )
}
