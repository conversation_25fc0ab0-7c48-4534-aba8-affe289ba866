'use client'

import React, { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Copy, RefreshCw, Save, AlertTriangle, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CodeEditorProps {
  component: unknown
  onComponentChange: (component: unknown) => void
  className?: string
}

export function CodeEditor({ component, onComponentChange, className }: CodeEditorProps) {
  const [code, setCode] = useState(component?.code || '')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isValidCode, setIsValidCode] = useState(true)

  // Handle code changes
  const handleCodeChange = useCallback(
    (newCode: string) => {
      setCode(newCode)
      setHasUnsavedChanges(true)

      // Basic validation
      try {
        // Simple validation - check for basic JSX structure
        const isValid = newCode.includes('<') && newCode.includes('>')
        setIsValidCode(isValid)
      } catch {
        setIsValidCode(false)
      }

      // Update component
      if (component) {
        onComponentChange({
          ...component,
          code: newCode,
        })
      }
    },
    [component, onComponentChange]
  )

  // Copy code to clipboard
  const copyToClipboard = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code)
    } catch {
      // console.error('Failed to copy code:', err);
    }
  }, [code])

  // Save changes
  const handleSave = useCallback(() => {
    setHasUnsavedChanges(false)
    // In a real implementation, this would save to the backend
  }, [])

  // Format code
  const formatCode = useCallback(() => {
    try {
      // Simple formatting - add basic indentation
      const formatted = code
        .split('\n')
        .map(line => line.trim())
        .join('\n')
      setCode(formatted)
      handleCodeChange(formatted)
    } catch {
      // console.error('Failed to format code:', err);
    }
  }, [code, handleCodeChange])

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Code Editor Toolbar */}
      <div className='flex items-center justify-between p-2 lg:p-3 border-b border-border bg-muted/30'>
        <div className='flex items-center gap-1 lg:gap-2'>
          <span className='text-xs lg:text-sm font-medium text-card-foreground'>Code:</span>
          <Badge variant={isValidCode ? 'secondary' : 'destructive'} className='text-xs'>
            {isValidCode ? (
              <>
                <CheckCircle className='w-2 h-2 lg:w-3 lg:h-3 mr-1' />
                Valid
              </>
            ) : (
              <>
                <AlertTriangle className='w-2 h-2 lg:w-3 lg:h-3 mr-1' />
                Invalid
              </>
            )}
          </Badge>
          {hasUnsavedChanges && (
            <Badge variant='outline' className='text-xs border-orange-500/20 text-orange-600'>
              Unsaved
            </Badge>
          )}
        </div>

        <div className='flex items-center gap-1'>
          <Button
            variant='ghost'
            size='sm'
            onClick={copyToClipboard}
            className='h-6 w-6 lg:h-8 lg:w-8 p-0'
            title='Copy code'
          >
            <Copy className='w-3 h-3 lg:w-4 lg:h-4' />
          </Button>
          <Button
            variant='ghost'
            size='sm'
            onClick={formatCode}
            className='h-6 w-6 lg:h-8 lg:w-8 p-0'
            title='Format code'
          >
            <RefreshCw className='w-3 h-3 lg:w-4 lg:h-4' />
          </Button>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleSave}
            disabled={!hasUnsavedChanges}
            className='h-6 w-6 lg:h-8 lg:w-8 p-0'
            title='Save changes'
          >
            <Save className='w-3 h-3 lg:w-4 lg:h-4' />
          </Button>
        </div>
      </div>

      {/* Code Editor */}
      <div className='flex-1 p-2 lg:p-3'>
        <Textarea
          value={code}
          onChange={e => handleCodeChange(e.target.value)}
          placeholder='Enter your component code here...'
          className='h-full min-h-[200px] lg:min-h-[300px] font-mono text-xs lg:text-sm bg-background border-border resize-none'
          style={{ fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace' }}
        />
      </div>

      {/* Code Info */}
      <div className='p-2 lg:p-3 border-t border-border bg-muted/30'>
        <div className='grid grid-cols-3 gap-2 lg:gap-3 text-xs lg:text-sm'>
          <div className='text-center'>
            <div className='font-medium text-card-foreground'>Lines</div>
            <div className='text-muted-foreground'>{code.split('\n').length}</div>
          </div>
          <div className='text-center'>
            <div className='font-medium text-card-foreground'>Characters</div>
            <div className='text-muted-foreground'>{code.length}</div>
          </div>
          <div className='text-center'>
            <div className='font-medium text-card-foreground'>Size</div>
            <div className='text-muted-foreground'>{(code.length / 1024).toFixed(1)}KB</div>
          </div>
        </div>
      </div>
    </div>
  )
}
