'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import { Code, Component, Eye, Palette, Plus, RefreshCw, Search } from 'lucide-react'
import { useState } from 'react'

// Functional Design System Manager
export default function SuperAdminDesignSystemPage() {
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Mock component data - replace with actual component registry
  const components = [
    {
      id: 'button',
      name: 'But<PERSON>',
      category: 'Forms',
      description: 'Interactive button component',
    },
    { id: 'card', name: 'Card', category: 'Layout', description: 'Container for content' },
    { id: 'input', name: 'Input', category: 'Forms', description: 'Text input field' },
    { id: 'modal', name: 'Modal', category: 'Overlay', description: 'Dialog overlay component' },
  ]

  const filteredComponents = components.filter(component =>
    component.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className='flex h-screen bg-background'>
      {/* Sidebar - Component List */}
      <div className='w-80 border-r border-border bg-card/50'>
        <div className='p-6 border-b border-border'>
          <div className='flex items-center justify-between mb-4'>
            <h1 className='text-2xl font-bold'>Design System</h1>
            <Button size='sm' className='h-8 w-8 p-0'>
              <Plus className='h-4 w-4' />
            </Button>
          </div>

          {/* Search */}
          <div className='relative'>
            <Search className='absolute left-3 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search components...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-9'
            />
          </div>
        </div>

        {/* Component List */}
        <div className='p-4 space-y-2 overflow-y-auto'>
          {isLoading ? (
            <div className='text-center py-8 text-muted-foreground'>
              <RefreshCw className='h-6 w-6 animate-spin mx-auto mb-2' />
              Loading components...
            </div>
          ) : (
            filteredComponents.map(component => (
              <div
                key={component.id}
                className={cn(
                  'p-3 rounded-lg border cursor-pointer transition-all',
                  selectedComponent === component.id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                )}
                onClick={() => setSelectedComponent(component.id)}
              >
                <div className='flex items-center justify-between mb-1'>
                  <h3 className='font-medium text-sm'>{component.name}</h3>
                  <Badge variant='outline' className='text-xs'>
                    {component.category}
                  </Badge>
                </div>
                <p className='text-xs text-muted-foreground'>{component.description}</p>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className='flex-1 flex flex-col'>
        {selectedComponent ? (
          <div className='flex-1'>
            <Tabs defaultValue='editor' className='h-full flex flex-col'>
              <div className='border-b border-border px-6 py-4'>
                <div className='flex items-center justify-between'>
                  <div>
                    <h2 className='text-xl font-semibold'>
                      {components.find(c => c.id === selectedComponent)?.name}
                    </h2>
                    <p className='text-sm text-muted-foreground'>
                      Edit component properties and styling
                    </p>
                  </div>
                  <TabsList>
                    <TabsTrigger value='editor'>
                      <Code className='h-4 w-4 mr-2' />
                      Editor
                    </TabsTrigger>
                    <TabsTrigger value='preview'>
                      <Eye className='h-4 w-4 mr-2' />
                      Preview
                    </TabsTrigger>
                    <TabsTrigger value='theme'>
                      <Palette className='h-4 w-4 mr-2' />
                      Theme
                    </TabsTrigger>
                  </TabsList>
                </div>
              </div>

              <div className='flex-1 p-6'>
                <TabsContent value='editor' className='h-full'>
                  <Card className='h-full'>
                    <CardHeader>
                      <CardTitle>Component Properties</CardTitle>
                    </CardHeader>
                    <CardContent className='space-y-4'>
                      <div>
                        <Label>Component Name</Label>
                        <Input
                          value={components.find(c => c.id === selectedComponent)?.name || ''}
                        />
                      </div>
                      <div>
                        <Label>Description</Label>
                        <Input
                          value={
                            components.find(c => c.id === selectedComponent)?.description || ''
                          }
                        />
                      </div>
                      <div>
                        <Label>Category</Label>
                        <Input
                          value={components.find(c => c.id === selectedComponent)?.category || ''}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value='preview' className='h-full'>
                  <Card className='h-full'>
                    <CardHeader>
                      <CardTitle>Live Preview</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className='border border-dashed border-border rounded-lg p-8 text-center'>
                        <Component className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
                        <p className='text-muted-foreground'>Component preview will appear here</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value='theme' className='h-full'>
                  <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
                    <Card>
                      <CardHeader>
                        <CardTitle>Color Palette</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className='grid grid-cols-4 gap-2'>
                          {['primary', 'secondary', 'accent', 'muted'].map(color => (
                            <div key={color} className='space-y-1'>
                              <div className={`h-12 rounded-md bg-${color}`} />
                              <p className='text-xs text-center'>{color}</p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader>
                        <CardTitle>Typography</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className='space-y-3'>
                          <div>
                            <p className='text-xs text-muted-foreground'>Heading 1</p>
                            <h1 className='text-2xl font-bold'>Sample heading</h1>
                          </div>
                          <div>
                            <p className='text-xs text-muted-foreground'>Body</p>
                            <p>Sample body text content</p>
                          </div>
                          <div>
                            <p className='text-xs text-muted-foreground'>Small</p>
                            <p className='text-sm'>Sample small text</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        ) : (
          <div className='flex-1 flex items-center justify-center'>
            <div className='text-center'>
              <Component className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
              <h3 className='text-lg font-medium mb-2'>No Component Selected</h3>
              <p className='text-muted-foreground'>
                Select a component from the sidebar to start editing
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Status Panel */}
      <div className='w-64 border-l border-border bg-card/30 p-4 space-y-4'>
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm'>Quick Stats</CardTitle>
          </CardHeader>
          <CardContent className='space-y-2 text-sm'>
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>Components:</span>
              <span className='font-medium'>{components.length}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>Categories:</span>
              <span className='font-medium'>3</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-muted-foreground'>Custom:</span>
              <span className='font-medium'>0</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-sm'>Recent Changes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2 text-xs'>
              <div className='flex items-center gap-2'>
                <div className='w-2 h-2 rounded-full bg-green-500' />
                <span>Button updated</span>
              </div>
              <div className='flex items-center gap-2'>
                <div className='w-2 h-2 rounded-full bg-blue-500' />
                <span>Card created</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
