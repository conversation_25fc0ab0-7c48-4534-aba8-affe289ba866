'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Role } from '@prisma/client'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { use, useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

interface AuditLog {
  id: string
  companyId: string
  oldStatus: string | null
  newStatus: string
  changedBy: string
  changedAt: string
  details: string
}

interface Company {
  id: string
  name: string
}

export default function AuditLogs({ params }: { params: Promise<{ companyId: string }> }) {
  // Unwrap params using React.use() for Next.js 15 compatibility
  const { companyId } = use(params)

  const { data: session, status } = useSession()
  const router = useRouter()
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [company, setCompany] = useState<Company | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Define fetchAuditLogs before useEffect to avoid initialization order issues
  const fetchAuditLogs = useCallback(async () => {
    try {
      setLoading(true)

      // Fetch company details
      const companyResponse = await fetch(`/api/companies/${companyId}`, {
        headers: {
          'X-API-Key': process.env.NEXT_PUBLIC_SUPERADMIN_API_KEY!,
        },
      })

      if (!companyResponse.ok) {
        throw new Error('Failed to fetch company details')
      }

      const companyData = await companyResponse.json()
      setCompany(companyData)

      // Fetch audit logs
      const logsResponse = await fetch(`/api/companies/${companyId}/audit-logs`, {
        headers: {
          'X-API-Key': process.env.NEXT_PUBLIC_SUPERADMIN_API_KEY!,
        },
      })

      if (!logsResponse.ok) {
        throw new Error('Failed to fetch audit logs')
      }

      const logsData = await logsResponse.json()
      setAuditLogs(logsData.logs)
      setError(null)
    } catch {
      setError('Error loading audit logs. Please try again.')
      toast.error('Failed to load audit logs')
    } finally {
      setLoading(false)
    }
  }, [companyId])

  useEffect(() => {
    if (status === 'loading') return

    if (!session || session.user.role !== Role.SUPERADMIN) {
      router.push('/error?message=Unauthorized%20access')
      return
    }

    fetchAuditLogs()
  }, [session, status, companyId, router, fetchAuditLogs])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const formatStatus = (status: string | null) => {
    return status || 'N/A'
  }

  return (
    <div className='w-full max-w-none py-10'>
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-3xl font-bold'>Subscription Audit Logs</h1>
          {company && <p className='text-gray-600 mt-2'>Company: {company.name}</p>}
        </div>
        <Button variant='outline' onClick={() => router.push('/superadmin')}>
          Back to Companies
        </Button>
      </div>

      {error && (
        <div className='bg-red-100 border border-red-400 text-red-700 py-3 rounded mb-4'>
          {error}
        </div>
      )}

      <div className='bg-white rounded-lg shadow overflow-hidden'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Changed By</TableHead>
              <TableHead>Old Status</TableHead>
              <TableHead>New Status</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className='text-center py-8'>
                  Loading...
                </TableCell>
              </TableRow>
            ) : auditLogs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className='text-center py-8'>
                  No audit logs found
                </TableCell>
              </TableRow>
            ) : (
              auditLogs.map(log => (
                <TableRow key={log.id}>
                  <TableCell>{formatDate(log.changedAt)}</TableCell>
                  <TableCell>{log.changedBy}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded text-sm ${
                        log.oldStatus === 'ACTIVE'
                          ? 'bg-green-100 text-green-800'
                          : log.oldStatus === 'TRIAL'
                            ? 'bg-blue-100 text-blue-800'
                            : log.oldStatus === 'EXPIRED'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {formatStatus(log.oldStatus)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded text-sm ${
                        log.newStatus === 'ACTIVE'
                          ? 'bg-green-100 text-green-800'
                          : log.newStatus === 'TRIAL'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {log.newStatus}
                    </span>
                  </TableCell>
                  <TableCell>
                    {log.details ? (
                      <pre className='text-sm whitespace-pre-wrap'>
                        {JSON.stringify(JSON.parse(log.details), null, 2)}
                      </pre>
                    ) : (
                      'No additional details'
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
