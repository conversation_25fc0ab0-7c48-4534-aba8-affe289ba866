import { Metadata } from 'next'
import { Card } from '@/components/ui/card'

export const metadata: Metadata = {
  title: 'Pulse - Organizational Insights | Emynent',
  description: 'Organizational insights and analytics dashboard',
}

export default function PulsePage() {
  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Pulse</h1>
          <p className='text-muted-foreground'>Organizational insights and analytics dashboard</p>
        </div>
      </div>

      <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
        <Card className='rounded-lg border bg-card p-6'>
          <h3 className='text-lg font-semibold mb-2'>Team Engagement</h3>
          <p className='text-sm text-muted-foreground'>Monitor team engagement and satisfaction</p>
        </Card>

        <Card className='rounded-lg border bg-card p-6'>
          <h3 className='text-lg font-semibold mb-2'>Performance Trends</h3>
          <p className='text-sm text-muted-foreground'>Track performance trends and insights</p>
        </Card>

        <Card className='rounded-lg border bg-card p-6'>
          <h3 className='text-lg font-semibold mb-2'>AI Forecasting</h3>
          <p className='text-sm text-muted-foreground'>AI-powered forecasting and trend analysis</p>
        </Card>
      </div>
    </div>
  )
}
