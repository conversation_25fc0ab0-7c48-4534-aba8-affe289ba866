import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { auth } from '@/lib/auth/config'
import { redirect } from 'next/navigation'
// import { Heading } from "@/components/ui/heading"

export default async function AdminDashboard() {
  const session = await auth()

  if (!session?.user || (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN')) {
    redirect('/dashboard')
  }

  return (
    <div className='w-full max-w-none py-10'>
      {/* <Heading title="Admin Dashboard" description="Overview of system and users" /> */}
      <h1 className='text-3xl font-bold mb-2'>Admin Dashboard</h1>
      <p className='text-muted-foreground mb-6'>Overview of system and users</p>

      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3 mt-6'>
        <Card>
          <CardHeader>
            <CardTitle>Users</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Manage user roles and permissions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Company Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Configure company-wide settings</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <p>View company analytics and insights</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
