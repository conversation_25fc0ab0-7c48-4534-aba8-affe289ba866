import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Team Dashboard | Emynent',
  description: 'Overview of your team performance and development',
}

export default function TeamDashboardPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Dashboard</h1>
          <p className='text-muted-foreground'>
            Monitor your team&apos;s performance, development progress, and key metrics.
          </p>
        </div>

        <div className='grid gap-6'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Team Size</h3>
              <p className='text-3xl font-bold text-primary'>8</p>
              <p className='text-sm text-muted-foreground'>Active members</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Avg Performance</h3>
              <p className='text-3xl font-bold text-green-600'>4.2</p>
              <p className='text-sm text-muted-foreground'>Out of 5.0</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Goals Progress</h3>
              <p className='text-3xl font-bold text-blue-600'>78%</p>
              <p className='text-sm text-muted-foreground'>On track</p>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Team Members</h2>
            <div className='space-y-3'>
              <div className='flex items-center justify-between p-3 bg-muted rounded-lg'>
                <div className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    JD
                  </div>
                  <div>
                    <span className='font-medium'>John Doe</span>
                    <p className='text-sm text-muted-foreground'>Senior Developer</p>
                  </div>
                </div>
                <span className='text-sm bg-green-100 text-green-800 px-2 py-1 rounded'>
                  Exceeding
                </span>
              </div>
              <div className='flex items-center justify-between p-3 bg-muted rounded-lg'>
                <div className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    JS
                  </div>
                  <div>
                    <span className='font-medium'>Jane Smith</span>
                    <p className='text-sm text-muted-foreground'>Product Designer</p>
                  </div>
                </div>
                <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>Meeting</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
