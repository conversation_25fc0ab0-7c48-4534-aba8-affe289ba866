import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Team Feedback | Emynent',
  description: 'Manage team feedback, reviews, and continuous improvement initiatives',
}

export default function TeamFeedbackPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Feedback</h1>
          <p className='text-muted-foreground'>
            Manage team feedback, reviews, and continuous improvement initiatives
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Feedback Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Pending Reviews</h3>
              <p className='text-3xl font-bold text-primary mb-1'>5</p>
              <p className='text-sm text-muted-foreground'>Due this week</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Avg Rating</h3>
              <p className='text-3xl font-bold text-green-600'>4.3</p>
              <p className='text-sm text-muted-foreground'>Out of 5.0</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Response Rate</h3>
              <p className='text-3xl font-bold text-blue-600'>92%</p>
              <p className='text-sm text-muted-foreground'>Team participation</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Action Items</h3>
              <p className='text-3xl font-bold text-purple-600'>8</p>
              <p className='text-sm text-muted-foreground'>In progress</p>
            </div>
          </div>

          {/* Recent Feedback */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <div className='flex items-center justify-between mb-6'>
              <h2 className='text-xl font-semibold'>Recent Feedback</h2>
              <button className='py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors'>
                Give Feedback
              </button>
            </div>

            <div className='space-y-4'>
              <div className='p-4 bg-green-50 border border-green-200 rounded-lg'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold'>
                      JD
                    </div>
                    <div>
                      <h3 className='font-semibold'>John Doe</h3>
                      <p className='text-sm text-muted-foreground'>Frontend Lead</p>
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <div className='flex'>
                      {[1, 2, 3, 4, 5].map(star => (
                        <span key={star} className='text-yellow-400'>
                          ★
                        </span>
                      ))}
                    </div>
                    <span className='text-sm text-muted-foreground'>2 days ago</span>
                  </div>
                </div>

                <p className='text-sm text-green-800 mb-3'>
                  &quot;Excellent leadership during the authentication module implementation. John
                  provided clear guidance and helped the team overcome technical challenges
                  efficiently.&quot;
                </p>

                <div className='flex items-center justify-between'>
                  <span className='text-xs text-green-600'>From: Alex Lee</span>
                  <div className='flex gap-2'>
                    <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                      Acknowledge
                    </button>
                    <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                      Respond
                    </button>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold'>
                      SM
                    </div>
                    <div>
                      <h3 className='font-semibold'>Sarah Miller</h3>
                      <p className='text-sm text-muted-foreground'>Frontend Developer</p>
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <div className='flex'>
                      {[1, 2, 3, 4].map(star => (
                        <span key={star} className='text-yellow-400'>
                          ★
                        </span>
                      ))}
                      <span className='text-gray-300'>★</span>
                    </div>
                    <span className='text-sm text-muted-foreground'>1 week ago</span>
                  </div>
                </div>

                <p className='text-sm text-yellow-800 mb-3'>
                  &quot;Good progress on React components, but would benefit from more focus on
                  TypeScript best practices. Consider pairing with senior developers for complex
                  features.&quot;
                </p>

                <div className='flex items-center justify-between'>
                  <span className='text-xs text-yellow-600'>From: John Doe</span>
                  <div className='flex gap-2'>
                    <button className='px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600 transition-colors'>
                      Create Action Plan
                    </button>
                    <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                      Schedule 1:1
                    </button>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold'>
                      AL
                    </div>
                    <div>
                      <h3 className='font-semibold'>Alex Lee</h3>
                      <p className='text-sm text-muted-foreground'>Backend Lead</p>
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <div className='flex'>
                      {[1, 2, 3, 4, 5].map(star => (
                        <span key={star} className='text-yellow-400'>
                          ★
                        </span>
                      ))}
                    </div>
                    <span className='text-sm text-muted-foreground'>3 days ago</span>
                  </div>
                </div>

                <p className='text-sm text-blue-800 mb-3'>
                  &quot;Outstanding system architecture design and mentoring of junior developers.
                  Alex consistently delivers high-quality solutions and shares knowledge
                  effectively.&quot;
                </p>

                <div className='flex items-center justify-between'>
                  <span className='text-xs text-blue-600'>From: Lisa Taylor</span>
                  <div className='flex gap-2'>
                    <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                      Acknowledge
                    </button>
                    <button className='px-3 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600 transition-colors'>
                      Nominate for Recognition
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Feedback Analytics */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Feedback Analytics</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Feedback Trends</h3>
                  <div className='h-32 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Feedback Trend Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    20% increase in positive feedback over last quarter
                  </p>
                </div>

                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Feedback Categories</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Technical Skills</span>
                      <span className='text-sm font-medium'>35%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Communication</span>
                      <span className='text-sm font-medium'>25%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Leadership</span>
                      <span className='text-sm font-medium'>20%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Collaboration</span>
                      <span className='text-sm font-medium'>20%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Team Sentiment</h3>
                  <div className='h-32 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Sentiment Analysis Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Overall positive sentiment with 88% satisfaction
                  </p>
                </div>

                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Action Item Status</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Completed</span>
                      <span className='text-sm font-medium text-green-600'>12</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>In Progress</span>
                      <span className='text-sm font-medium text-blue-600'>8</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Pending</span>
                      <span className='text-sm font-medium text-yellow-600'>3</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Overdue</span>
                      <span className='text-sm font-medium text-red-600'>1</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 360 Feedback Cycles */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>360° Feedback Cycles</h2>

            <div className='space-y-4'>
              <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
                <div className='flex items-center justify-between mb-3'>
                  <h3 className='font-semibold text-blue-900'>Q1 2025 Performance Review</h3>
                  <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                    Active
                  </span>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Participants</h4>
                    <p className='text-sm text-blue-700'>12/12 team members</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Completion Rate</h4>
                    <p className='text-sm text-blue-700'>75% (9/12)</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Due Date</h4>
                    <p className='text-sm text-blue-700'>February 15, 2025</p>
                  </div>
                </div>

                <div className='flex gap-2'>
                  <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                    View Progress
                  </button>
                  <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                    Send Reminders
                  </button>
                </div>
              </div>

              <div className='p-4 bg-green-50 border border-green-200 rounded-lg'>
                <div className='flex items-center justify-between mb-3'>
                  <h3 className='font-semibold text-green-900'>Q4 2024 Performance Review</h3>
                  <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                    Completed
                  </span>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Participants</h4>
                    <p className='text-sm text-green-700'>12/12 team members</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Completion Rate</h4>
                    <p className='text-sm text-green-700'>100% (12/12)</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Avg Rating</h4>
                    <p className='text-sm text-green-700'>4.2/5.0</p>
                  </div>
                </div>

                <div className='flex gap-2'>
                  <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                    View Results
                  </button>
                  <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                    Export Report
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Peer Recognition */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Peer Recognition</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='p-4 bg-purple-50 border border-purple-200 rounded-lg'>
                  <h3 className='font-semibold text-purple-900 mb-2'>Recent Recognition</h3>
                  <div className='space-y-3'>
                    <div className='flex items-center gap-3'>
                      <div className='w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs'>
                        JD
                      </div>
                      <div className='flex-1'>
                        <p className='text-sm font-medium'>John Doe</p>
                        <p className='text-xs text-purple-700'>
                          Recognized for &quot;Outstanding Leadership&quot;
                        </p>
                      </div>
                      <span className='text-xs text-purple-600'>🏆</span>
                    </div>

                    <div className='flex items-center gap-3'>
                      <div className='w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs'>
                        AL
                      </div>
                      <div className='flex-1'>
                        <p className='text-sm font-medium'>Alex Lee</p>
                        <p className='text-xs text-purple-700'>
                          Recognized for &quot;Technical Excellence&quot;
                        </p>
                      </div>
                      <span className='text-xs text-purple-600'>⭐</span>
                    </div>

                    <div className='flex items-center gap-3'>
                      <div className='w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center text-white text-xs'>
                        LT
                      </div>
                      <div className='flex-1'>
                        <p className='text-sm font-medium'>Lisa Taylor</p>
                        <p className='text-xs text-purple-700'>
                          Recognized for &quot;Quality Champion&quot;
                        </p>
                      </div>
                      <span className='text-xs text-purple-600'>🎯</span>
                    </div>
                  </div>
                </div>

                <div className='p-4 bg-orange-50 border border-orange-200 rounded-lg'>
                  <h3 className='font-semibold text-orange-900 mb-2'>Recognition Categories</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Team Player</span>
                      <span className='text-sm font-medium text-orange-800'>8 awards</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Innovation</span>
                      <span className='text-sm font-medium text-orange-800'>5 awards</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Leadership</span>
                      <span className='text-sm font-medium text-orange-800'>4 awards</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Quality</span>
                      <span className='text-sm font-medium text-orange-800'>6 awards</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='p-4 bg-green-50 border border-green-200 rounded-lg'>
                  <h3 className='font-semibold text-green-900 mb-2'>Top Performers</h3>
                  <div className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs'>
                          JD
                        </div>
                        <span className='text-sm font-medium'>John Doe</span>
                      </div>
                      <span className='text-sm font-medium text-green-800'>4.8/5.0</span>
                    </div>

                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs'>
                          AL
                        </div>
                        <span className='text-sm font-medium'>Alex Lee</span>
                      </div>
                      <span className='text-sm font-medium text-green-800'>4.7/5.0</span>
                    </div>

                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center text-white text-xs'>
                          LT
                        </div>
                        <span className='text-sm font-medium'>Lisa Taylor</span>
                      </div>
                      <span className='text-sm font-medium text-green-800'>4.6/5.0</span>
                    </div>
                  </div>
                </div>

                <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
                  <h3 className='font-semibold text-blue-900 mb-2'>Improvement Areas</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Communication</span>
                      <span className='text-sm font-medium text-blue-800'>3 mentions</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Time Management</span>
                      <span className='text-sm font-medium text-blue-800'>2 mentions</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Documentation</span>
                      <span className='text-sm font-medium text-blue-800'>4 mentions</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Quick Actions</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3'>
              <button className='p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
                Give Feedback
              </button>
              <button className='p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors'>
                Start 360 Review
              </button>
              <button className='p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors'>
                Recognize Peer
              </button>
              <button className='p-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors'>
                View Reports
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
