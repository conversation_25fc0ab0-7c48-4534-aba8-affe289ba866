import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'One-on-Ones | Emynent',
  description: 'Schedule and manage one-on-one meetings with your team',
}

export default function OneOnOnesPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>One-on-Ones</h1>
          <p className='text-muted-foreground'>
            Schedule, track, and manage one-on-one meetings with your team members.
          </p>
        </div>

        <div className='grid gap-6'>
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Upcoming Meetings</h2>
            <div className='space-y-3'>
              <div className='flex items-center justify-between p-3 bg-muted rounded-lg'>
                <div className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    JD
                  </div>
                  <div>
                    <span className='font-medium'>John Doe</span>
                    <p className='text-sm text-muted-foreground'>Tomorrow, 2:00 PM</p>
                  </div>
                </div>
                <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                  Scheduled
                </span>
              </div>
              <div className='flex items-center justify-between p-3 bg-muted rounded-lg'>
                <div className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium'>
                    JS
                  </div>
                  <div>
                    <span className='font-medium'>Jane Smith</span>
                    <p className='text-sm text-muted-foreground'>Friday, 10:00 AM</p>
                  </div>
                </div>
                <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                  Scheduled
                </span>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Meeting Templates</h2>
            <p className='text-muted-foreground mb-4'>
              Use structured templates to make your one-on-ones more effective.
            </p>
            <div className='space-y-2'>
              <div className='flex items-center gap-3 p-3 bg-muted rounded-lg'>
                <div className='w-2 h-2 bg-primary rounded-full'></div>
                <span>Weekly Check-in Template</span>
              </div>
              <div className='flex items-center gap-3 p-3 bg-muted rounded-lg'>
                <div className='w-2 h-2 bg-primary rounded-full'></div>
                <span>Career Development Discussion</span>
              </div>
              <div className='flex items-center gap-3 p-3 bg-muted rounded-lg'>
                <div className='w-2 h-2 bg-primary rounded-full'></div>
                <span>Performance Review Prep</span>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Recent Notes</h2>
            <p className='text-muted-foreground'>
              Access notes and action items from your recent one-on-one meetings.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
