import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Team Analytics | Emynent',
  description: 'Advanced analytics and insights for team performance and productivity',
}

export default function TeamAnalyticsPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Analytics</h1>
          <p className='text-muted-foreground'>
            Advanced analytics and insights for team performance and productivity
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Key Metrics Dashboard */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Team Velocity</h3>
              <p className='text-3xl font-bold text-primary mb-1'>42</p>
              <p className='text-sm text-muted-foreground'>Story points/sprint</p>
              <div className='flex items-center gap-1 mt-2'>
                <span className='text-xs text-green-600'>↗ +15%</span>
                <span className='text-xs text-muted-foreground'>vs last sprint</span>
              </div>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Productivity Score</h3>
              <p className='text-3xl font-bold text-green-600'>87</p>
              <p className='text-sm text-muted-foreground'>Out of 100</p>
              <div className='flex items-center gap-1 mt-2'>
                <span className='text-xs text-green-600'>↗ +8%</span>
                <span className='text-xs text-muted-foreground'>vs last month</span>
              </div>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Code Quality</h3>
              <p className='text-3xl font-bold text-blue-600'>94%</p>
              <p className='text-sm text-muted-foreground'>Test coverage</p>
              <div className='flex items-center gap-1 mt-2'>
                <span className='text-xs text-green-600'>↗ +3%</span>
                <span className='text-xs text-muted-foreground'>vs last month</span>
              </div>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Team Health</h3>
              <p className='text-3xl font-bold text-purple-600'>4.3</p>
              <p className='text-sm text-muted-foreground'>Satisfaction score</p>
              <div className='flex items-center gap-1 mt-2'>
                <span className='text-xs text-green-600'>↗ +0.2</span>
                <span className='text-xs text-muted-foreground'>vs last quarter</span>
              </div>
            </div>
          </div>

          {/* Performance Trends */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Performance Trends</h2>

            <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Sprint Velocity Trend</h3>
                  <div className='h-40 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Velocity Trend Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Consistent upward trend with 15% improvement over 6 sprints
                  </p>
                </div>

                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Bug Rate Analysis</h3>
                  <div className='h-40 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Bug Rate Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Bug rate decreased by 40% with improved testing practices
                  </p>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Team Productivity</h3>
                  <div className='h-40 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Productivity Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Steady productivity gains with peak performance in Q1
                  </p>
                </div>

                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Code Review Efficiency</h3>
                  <div className='h-40 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Review Time Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Average review time reduced from 2.5 days to 1.2 days
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Individual Performance Analytics */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Individual Performance Analytics</h2>

            <div className='overflow-x-auto'>
              <table className='w-full'>
                <thead>
                  <tr className='border-b border-border'>
                    <th className='text-left py-3'>Team Member</th>
                    <th className='text-left py-3'>Velocity</th>
                    <th className='text-left py-3'>Code Quality</th>
                    <th className='text-left py-3'>Review Score</th>
                    <th className='text-left py-3'>Collaboration</th>
                    <th className='text-left py-3'>Growth Trend</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className='border-b border-border'>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs'>
                          JD
                        </div>
                        <span className='font-medium'>John Doe</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-16 bg-background rounded-full h-2'>
                          <div className='w-5/6 h-full bg-green-500 rounded-full'></div>
                        </div>
                        <span className='text-sm font-medium'>85%</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Excellent
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-sm font-medium'>4.8/5.0</span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                        High
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-green-600 text-sm'>↗ +12%</span>
                    </td>
                  </tr>
                  <tr className='border-b border-border'>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs'>
                          SM
                        </div>
                        <span className='font-medium'>Sarah Miller</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-16 bg-background rounded-full h-2'>
                          <div className='w-3/5 h-full bg-yellow-500 rounded-full'></div>
                        </div>
                        <span className='text-sm font-medium'>68%</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Good
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-sm font-medium'>4.2/5.0</span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        High
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-green-600 text-sm'>↗ +18%</span>
                    </td>
                  </tr>
                  <tr className='border-b border-border'>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs'>
                          AL
                        </div>
                        <span className='font-medium'>Alex Lee</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-16 bg-background rounded-full h-2'>
                          <div className='w-full h-full bg-green-500 rounded-full'></div>
                        </div>
                        <span className='text-sm font-medium'>92%</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Excellent
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-sm font-medium'>4.9/5.0</span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                        High
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-green-600 text-sm'>↗ +8%</span>
                    </td>
                  </tr>
                  <tr className='border-b border-border'>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center text-white text-xs'>
                          LT
                        </div>
                        <span className='font-medium'>Lisa Taylor</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-16 bg-background rounded-full h-2'>
                          <div className='w-4/5 h-full bg-green-500 rounded-full'></div>
                        </div>
                        <span className='text-sm font-medium'>78%</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Excellent
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-sm font-medium'>4.6/5.0</span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        High
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='text-green-600 text-sm'>↗ +10%</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Predictive Analytics */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Predictive Analytics & AI Insights</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
                  <h3 className='font-semibold text-blue-900 mb-2'>Sprint Forecast</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Next Sprint Velocity</span>
                      <span className='text-sm font-medium text-blue-800'>45-48 points</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Completion Probability</span>
                      <span className='text-sm font-medium text-blue-800'>87%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Risk Factors</span>
                      <span className='text-sm font-medium text-blue-800'>Low</span>
                    </div>
                  </div>
                </div>

                <div className='p-4 bg-green-50 border border-green-200 rounded-lg'>
                  <h3 className='font-semibold text-green-900 mb-2'>Team Health Prediction</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Burnout Risk</span>
                      <span className='text-sm font-medium text-green-800'>Low (15%)</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Satisfaction Trend</span>
                      <span className='text-sm font-medium text-green-800'>Improving</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Retention Score</span>
                      <span className='text-sm font-medium text-green-800'>High (92%)</span>
                    </div>
                  </div>
                </div>

                <div className='p-4 bg-purple-50 border border-purple-200 rounded-lg'>
                  <h3 className='font-semibold text-purple-900 mb-2'>Skill Gap Analysis</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Critical Gaps</span>
                      <span className='text-sm font-medium text-purple-800'>2 identified</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Training Needed</span>
                      <span className='text-sm font-medium text-purple-800'>
                        TypeScript, DevOps
                      </span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Timeline to Close</span>
                      <span className='text-sm font-medium text-purple-800'>3-4 months</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='p-4 bg-orange-50 border border-orange-200 rounded-lg'>
                  <h3 className='font-semibold text-orange-900 mb-2'>AI Recommendations</h3>
                  <div className='space-y-3'>
                    <div className='p-3 bg-white rounded border border-orange-200'>
                      <h4 className='font-medium text-sm text-orange-900'>Optimize Code Reviews</h4>
                      <p className='text-xs text-orange-700 mt-1'>
                        Implement automated checks to reduce review time by 25%
                      </p>
                    </div>
                    <div className='p-3 bg-white rounded border border-orange-200'>
                      <h4 className='font-medium text-sm text-orange-900'>Pair Programming</h4>
                      <p className='text-xs text-orange-700 mt-1'>
                        Pair Sarah with John for TypeScript knowledge transfer
                      </p>
                    </div>
                    <div className='p-3 bg-white rounded border border-orange-200'>
                      <h4 className='font-medium text-sm text-orange-900'>Sprint Planning</h4>
                      <p className='text-xs text-orange-700 mt-1'>
                        Adjust story point allocation based on team capacity
                      </p>
                    </div>
                  </div>
                </div>

                <div className='p-4 bg-red-50 border border-red-200 rounded-lg'>
                  <h3 className='font-semibold text-red-900 mb-2'>Risk Alerts</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center gap-2'>
                      <span className='w-2 h-2 bg-yellow-500 rounded-full'></span>
                      <span className='text-sm text-red-800'>
                        Mike Johnson showing signs of decreased productivity
                      </span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                      <span className='text-sm text-red-800'>
                        Test coverage below target in authentication module
                      </span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <span className='w-2 h-2 bg-green-500 rounded-full'></span>
                      <span className='text-sm text-red-800'>
                        Sprint velocity trending above sustainable pace
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Collaboration Analytics */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Collaboration Analytics</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-semibold mb-2'>Communication Patterns</h3>
                <div className='h-32 bg-background rounded flex items-center justify-center'>
                  <p className='text-muted-foreground text-sm'>Communication Network Chart</p>
                </div>
                <p className='text-sm text-muted-foreground mt-2'>
                  Strong cross-team communication with balanced interaction patterns
                </p>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-semibold mb-2'>Knowledge Sharing</h3>
                <div className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>Code Reviews Given</span>
                    <span className='text-sm font-medium'>156 this month</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>Documentation Updates</span>
                    <span className='text-sm font-medium'>23 this month</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>Knowledge Sessions</span>
                    <span className='text-sm font-medium'>8 this month</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>Mentoring Hours</span>
                    <span className='text-sm font-medium'>45 this month</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Export and Actions */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Analytics Actions</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3'>
              <button className='p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
                Export Report
              </button>
              <button className='p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors'>
                Schedule Review
              </button>
              <button className='p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors'>
                Set Alerts
              </button>
              <button className='p-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors'>
                Custom Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
