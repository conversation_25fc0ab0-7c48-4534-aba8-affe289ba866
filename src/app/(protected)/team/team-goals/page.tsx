import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Team Goals | Emynent',
  description: 'Set and track team goals and objectives',
}

export default function TeamGoalsPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Goals</h1>
          <p className='text-muted-foreground'>
            Set, track, and manage team goals and objectives to drive collective success.
          </p>
        </div>

        <div className='grid gap-6'>
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Active Goals</h2>
            <div className='space-y-4'>
              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Improve Code Quality</h3>
                  <span className='text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                    In Progress
                  </span>
                </div>
                <p className='text-sm text-muted-foreground mb-3'>
                  Reduce technical debt and improve code review processes
                </p>
                <div className='flex items-center gap-2'>
                  <div className='flex-1 bg-background rounded-full h-2'>
                    <div className='w-3/4 h-full bg-primary rounded-full'></div>
                  </div>
                  <span className='text-sm text-muted-foreground'>75%</span>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-semibold'>Launch New Feature</h3>
                  <span className='text-sm bg-green-100 text-green-800 px-2 py-1 rounded'>
                    On Track
                  </span>
                </div>
                <p className='text-sm text-muted-foreground mb-3'>
                  Complete development and testing of the new user dashboard
                </p>
                <div className='flex items-center gap-2'>
                  <div className='flex-1 bg-background rounded-full h-2'>
                    <div className='w-1/2 h-full bg-primary rounded-full'></div>
                  </div>
                  <span className='text-sm text-muted-foreground'>50%</span>
                </div>
              </div>
            </div>
          </div>

          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Goal Categories</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='p-3 bg-muted rounded-lg'>
                <h3 className='font-medium mb-1'>Performance Goals</h3>
                <p className='text-sm text-muted-foreground'>Metrics and KPI targets</p>
              </div>
              <div className='p-3 bg-muted rounded-lg'>
                <h3 className='font-medium mb-1'>Development Goals</h3>
                <p className='text-sm text-muted-foreground'>Skill building and learning</p>
              </div>
              <div className='p-3 bg-muted rounded-lg'>
                <h3 className='font-medium mb-1'>Project Goals</h3>
                <p className='text-sm text-muted-foreground'>Deliverables and milestones</p>
              </div>
              <div className='p-3 bg-muted rounded-lg'>
                <h3 className='font-medium mb-1'>Culture Goals</h3>
                <p className='text-sm text-muted-foreground'>Team collaboration and values</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
