'use client'

import React from 'react'
import { useSession } from 'next-auth/react'

/**
 * Team Page Component
 * One of the 9 main navigation areas: Team (squad management)
 * Provides role-based team management and communication features
 */
export default function TeamPage() {
  const { data: session } = useSession()
  const userRole = session?.user?.role

  // Check if user is a manager or higher (can delegate tasks)
  const canDelegate = ['MANAGER', 'DIRECTOR', 'ADMIN', 'SUPERADMIN'].includes(userRole || '')

  return (
    <main className='w-full max-w-none p-6'>
      <div className='mb-6'>
        <h1 className='text-3xl font-bold text-gray-900 dark:text-gray-100'>Team</h1>
        <p className='text-gray-600 dark:text-gray-400 mt-2'>Your squad management</p>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {/* Team Overview Section */}
        <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700'>
          <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2'>
            Team Overview
          </h3>
          <p className='text-gray-600 dark:text-gray-400 text-sm'>
            View your team structure and members
          </p>
        </div>

        {/* Team Communication Section */}
        <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700'>
          <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2'>
            Team Communication
          </h3>
          <p className='text-gray-600 dark:text-gray-400 text-sm'>
            Direct communication channels with team members
          </p>
        </div>

        {/* Task Delegation Section - Only for Managers and above */}
        {canDelegate && (
          <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700'>
            <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2'>
              Task Delegation
            </h3>
            <p className='text-gray-600 dark:text-gray-400 text-sm'>
              Delegate tasks and track team progress
            </p>
          </div>
        )}
      </div>
    </main>
  )
}
