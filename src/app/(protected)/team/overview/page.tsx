import { Metadata } from 'next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Users,
  TrendingUp,
  Heart,
  Target,
  Brain,
  Clock,
  Star,
  Award,
  Calendar,
  MessageSquare,
  BarChart3,
  Zap,
  CheckCircle,
  AlertTriangle,
  ArrowRight,
  Filter,
  MoreHorizontal,
} from 'lucide-react'
import { TeamMembersSection } from '@/components/team/TeamMembersSection'

export const metadata: Metadata = {
  title: 'Team Overview | Emynent',
  description: 'Monitor team health, engagement, and performance metrics with AI-powered insights',
}

export default function TeamOverviewPage() {
  return (
    <main role='main' data-testid='team-overview-container' className='w-full max-w-none py-10'>
      <div className='w-full'>
        {/* Page Header */}
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Overview</h1>
          <p className='text-muted-foreground'>
            Monitor team health, engagement, and performance metrics
          </p>
        </div>

        {/* Team Health Metrics */}
        <div className='grid gap-6 mb-8'>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4' aria-label='Team health metrics'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Team Health Score</CardTitle>
                <Heart className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-green-600'>8.4</div>
                <p className='text-xs text-muted-foreground'>Out of 10</p>
                <Badge variant='secondary' className='mt-2 bg-green-100 text-green-800'>
                  Excellent
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Engagement Level</CardTitle>
                <TrendingUp className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-blue-600'>92%</div>
                <p className='text-xs text-muted-foreground'>High engagement</p>
                <p className='text-xs text-green-600 mt-1'>+5% vs last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Performance Rating</CardTitle>
                <Star className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-purple-600'>4.6</div>
                <p className='text-xs text-muted-foreground'>Above average</p>
                <p className='text-xs text-purple-600 mt-1'>Top 15% in org</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Collaboration Index</CardTitle>
                <Users className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-orange-600'>87%</div>
                <p className='text-xs text-muted-foreground'>Strong collaboration</p>
                <p className='text-xs text-orange-600 mt-1'>Cross-team projects</p>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className='grid gap-6 lg:grid-cols-3'>
          {/* Team Members Section */}
          <TeamMembersSection />

          {/* Right Sidebar */}
          <div className='space-y-6'>
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest team updates and achievements</CardDescription>
              </CardHeader>
              <CardContent className='space-y-3'>
                <div className='flex items-start space-x-3'>
                  <CheckCircle className='h-4 w-4 text-green-500 mt-0.5' />
                  <div className='text-sm'>
                    <p>Sarah completed React Advanced certification</p>
                    <p className='text-muted-foreground text-xs'>2 hours ago</p>
                  </div>
                </div>
                <div className='flex items-start space-x-3'>
                  <TrendingUp className='h-4 w-4 text-blue-500 mt-0.5' />
                  <div className='text-sm'>
                    <p>Team achieved 95% sprint completion rate</p>
                    <p className='text-muted-foreground text-xs'>1 day ago</p>
                  </div>
                </div>
                <div className='flex items-start space-x-3'>
                  <Users className='h-4 w-4 text-purple-500 mt-0.5' />
                  <div className='text-sm'>
                    <p>Mike started mentoring junior developers</p>
                    <p className='text-muted-foreground text-xs'>3 days ago</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Milestones */}
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Milestones</CardTitle>
                <CardDescription>Important deadlines and events</CardDescription>
              </CardHeader>
              <CardContent className='space-y-3'>
                <div className='flex items-start space-x-3'>
                  <Calendar className='h-4 w-4 text-orange-500 mt-0.5' />
                  <div className='text-sm'>
                    <p className='font-medium'>Q1 Performance Reviews</p>
                    <p className='text-muted-foreground text-xs'>Due in 2 weeks</p>
                  </div>
                </div>
                <div className='flex items-start space-x-3'>
                  <Users className='h-4 w-4 text-blue-500 mt-0.5' />
                  <div className='text-sm'>
                    <p className='font-medium'>Team Building Workshop</p>
                    <p className='text-muted-foreground text-xs'>Next Friday</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* AI Insights */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Brain className='h-4 w-4' />
                  AI Insights
                </CardTitle>
                <CardDescription>Intelligent recommendations for your team</CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg'>
                  <h4 className='font-medium text-sm mb-1'>Team Productivity Trend</h4>
                  <p className='text-xs text-muted-foreground'>
                    Your team shows 15% higher productivity during morning hours
                  </p>
                </div>
                <div className='p-3 bg-green-50 dark:bg-green-950/20 rounded-lg'>
                  <h4 className='font-medium text-sm mb-1'>Skill Development Opportunity</h4>
                  <p className='text-xs text-muted-foreground'>
                    Consider cross-training in cloud technologies
                  </p>
                </div>
                <div className='p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg'>
                  <h4 className='font-medium text-sm mb-1'>Performance Forecast</h4>
                  <p className='text-xs text-muted-foreground'>
                    Projected to exceed Q1 goals by 12%
                  </p>
                </div>
                <div className='p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg'>
                  <h4 className='font-medium text-sm mb-1'>Risk Assessment</h4>
                  <p className='text-xs text-muted-foreground'>Low risk of burnout detected</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Actions */}
        <div className='mt-8'>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common team management tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                <Button variant='outline' className='h-auto p-4 flex flex-col items-center gap-2'>
                  <Calendar className='h-5 w-5' />
                  <span className='text-sm'>Schedule 1:1s</span>
                </Button>
                <Button variant='outline' className='h-auto p-4 flex flex-col items-center gap-2'>
                  <MessageSquare className='h-5 w-5' />
                  <span className='text-sm'>Team Check-in</span>
                </Button>
                <Button variant='outline' className='h-auto p-4 flex flex-col items-center gap-2'>
                  <Star className='h-5 w-5' />
                  <span className='text-sm'>Performance Review</span>
                </Button>
                <Button variant='outline' className='h-auto p-4 flex flex-col items-center gap-2'>
                  <Target className='h-5 w-5' />
                  <span className='text-sm'>Goal Setting</span>
                </Button>
              </div>

              <div className='flex gap-4 mt-6'>
                <Button variant='outline'>
                  <BarChart3 className='h-4 w-4 mr-2' />
                  Team Analytics
                </Button>
                <Button variant='outline'>
                  <TrendingUp className='h-4 w-4 mr-2' />
                  Development Plans
                </Button>
                <Button variant='outline'>
                  <MessageSquare className='h-4 w-4 mr-2' />
                  Feedback History
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}
