import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Team Goals | Emynent',
  description: 'Manage and track team goals, objectives, and key results',
}

export default function TeamGoalsPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Goals</h1>
          <p className='text-muted-foreground'>
            Manage and track team goals, objectives, and key results
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Goals Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Active Goals</h3>
              <p className='text-3xl font-bold text-primary mb-1'>8</p>
              <p className='text-sm text-muted-foreground'>Currently in progress</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Completed</h3>
              <p className='text-3xl font-bold text-green-600'>12</p>
              <p className='text-sm text-muted-foreground'>This quarter</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>On Track</h3>
              <p className='text-3xl font-bold text-blue-600'>75%</p>
              <p className='text-sm text-muted-foreground'>Meeting targets</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Team Score</h3>
              <p className='text-3xl font-bold text-purple-600'>4.2</p>
              <p className='text-sm text-muted-foreground'>Out of 5.0</p>
            </div>
          </div>

          {/* Current Sprint Goals */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <div className='flex items-center justify-between mb-6'>
              <h2 className='text-xl font-semibold'>Current Sprint Goals</h2>
              <button className='py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors'>
                Add Goal
              </button>
            </div>

            <div className='space-y-4'>
              <div className='p-4 bg-muted rounded-lg border border-border'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-3 h-3 bg-green-500 rounded-full'></div>
                    <h3 className='font-semibold'>Complete User Authentication Module</h3>
                    <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                      High Priority
                    </span>
                  </div>
                  <span className='text-sm text-muted-foreground'>Due: Jan 25</span>
                </div>

                <p className='text-sm text-muted-foreground mb-3'>
                  Implement OAuth integration, JWT tokens, and user session management
                </p>

                <div className='flex items-center justify-between mb-3'>
                  <span className='text-sm font-medium'>Progress: 85%</span>
                  <div className='flex items-center gap-2'>
                    <div className='w-32 bg-background rounded-full h-2'>
                      <div className='w-5/6 h-full bg-green-500 rounded-full'></div>
                    </div>
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <span className='text-sm text-muted-foreground'>Assigned to:</span>
                    <div className='flex -space-x-2'>
                      <div className='w-6 h-6 bg-blue-500 rounded-full border-2 border-background flex items-center justify-center text-white text-xs'>
                        JD
                      </div>
                      <div className='w-6 h-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center text-white text-xs'>
                        SM
                      </div>
                    </div>
                  </div>
                  <div className='flex gap-2'>
                    <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                      View Details
                    </button>
                    <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                      Update Progress
                    </button>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg border border-border'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-3 h-3 bg-yellow-500 rounded-full'></div>
                    <h3 className='font-semibold'>API Performance Optimization</h3>
                    <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                      Medium Priority
                    </span>
                  </div>
                  <span className='text-sm text-muted-foreground'>Due: Jan 30</span>
                </div>

                <p className='text-sm text-muted-foreground mb-3'>
                  Reduce API response times by 40% and implement caching strategies
                </p>

                <div className='flex items-center justify-between mb-3'>
                  <span className='text-sm font-medium'>Progress: 60%</span>
                  <div className='flex items-center gap-2'>
                    <div className='w-32 bg-background rounded-full h-2'>
                      <div className='w-3/5 h-full bg-yellow-500 rounded-full'></div>
                    </div>
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <span className='text-sm text-muted-foreground'>Assigned to:</span>
                    <div className='flex -space-x-2'>
                      <div className='w-6 h-6 bg-orange-500 rounded-full border-2 border-background flex items-center justify-center text-white text-xs'>
                        AL
                      </div>
                      <div className='w-6 h-6 bg-red-500 rounded-full border-2 border-background flex items-center justify-center text-white text-xs'>
                        EB
                      </div>
                    </div>
                  </div>
                  <div className='flex gap-2'>
                    <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                      View Details
                    </button>
                    <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                      Update Progress
                    </button>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg border border-border'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-3 h-3 bg-red-500 rounded-full'></div>
                    <h3 className='font-semibold'>Test Coverage Improvement</h3>
                    <span className='px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full'>
                      At Risk
                    </span>
                  </div>
                  <span className='text-sm text-muted-foreground'>Due: Feb 5</span>
                </div>

                <p className='text-sm text-muted-foreground mb-3'>
                  Increase test coverage from 75% to 90% across all modules
                </p>

                <div className='flex items-center justify-between mb-3'>
                  <span className='text-sm font-medium'>Progress: 35%</span>
                  <div className='flex items-center gap-2'>
                    <div className='w-32 bg-background rounded-full h-2'>
                      <div className='w-1/3 h-full bg-red-500 rounded-full'></div>
                    </div>
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <span className='text-sm text-muted-foreground'>Assigned to:</span>
                    <div className='flex -space-x-2'>
                      <div className='w-6 h-6 bg-teal-500 rounded-full border-2 border-background flex items-center justify-center text-white text-xs'>
                        LT
                      </div>
                      <div className='w-6 h-6 bg-pink-500 rounded-full border-2 border-background flex items-center justify-center text-white text-xs'>
                        RG
                      </div>
                    </div>
                  </div>
                  <div className='flex gap-2'>
                    <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                      View Details
                    </button>
                    <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                      Update Progress
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quarterly OKRs */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Q1 2025 OKRs</h2>

            <div className='space-y-6'>
              <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
                <h3 className='font-semibold text-blue-900 mb-2'>
                  Objective: Improve Product Quality & User Experience
                </h3>
                <p className='text-sm text-blue-700 mb-4'>
                  Deliver a more stable, performant, and user-friendly product
                </p>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>KR1: Reduce bug reports by 50%</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-24 bg-blue-200 rounded-full h-2'>
                        <div className='w-3/4 h-full bg-blue-600 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium text-blue-800'>75%</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>KR2: Achieve 95% uptime</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-24 bg-blue-200 rounded-full h-2'>
                        <div className='w-5/6 h-full bg-blue-600 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium text-blue-800'>88%</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>KR3: Improve page load times by 30%</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-24 bg-blue-200 rounded-full h-2'>
                        <div className='w-1/2 h-full bg-blue-600 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium text-blue-800'>45%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className='p-4 bg-green-50 border border-green-200 rounded-lg'>
                <h3 className='font-semibold text-green-900 mb-2'>
                  Objective: Enhance Team Productivity & Collaboration
                </h3>
                <p className='text-sm text-green-700 mb-4'>
                  Streamline development processes and improve team efficiency
                </p>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>KR1: Increase sprint velocity by 25%</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-24 bg-green-200 rounded-full h-2'>
                        <div className='w-4/5 h-full bg-green-600 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium text-green-800'>82%</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>KR2: Reduce code review time by 40%</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-24 bg-green-200 rounded-full h-2'>
                        <div className='w-3/5 h-full bg-green-600 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium text-green-800'>65%</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm'>KR3: Achieve 90% team satisfaction score</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-24 bg-green-200 rounded-full h-2'>
                        <div className='w-full h-full bg-green-600 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium text-green-800'>95%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Goal Analytics */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Goal Analytics</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Goal Completion Trend</h3>
                  <div className='h-32 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Completion Rate Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    15% improvement in completion rate over last quarter
                  </p>
                </div>

                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Team Performance by Goal Type</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Feature Development</span>
                      <span className='text-sm font-medium'>92%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Bug Fixes</span>
                      <span className='text-sm font-medium'>88%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Technical Debt</span>
                      <span className='text-sm font-medium'>76%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Documentation</span>
                      <span className='text-sm font-medium'>65%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Goal Distribution</h3>
                  <div className='h-32 bg-background rounded flex items-center justify-center'>
                    <p className='text-muted-foreground text-sm'>Goal Type Distribution Chart</p>
                  </div>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Balanced distribution across development areas
                  </p>
                </div>

                <div className='p-4 bg-muted rounded-lg'>
                  <h3 className='font-semibold mb-2'>Risk Assessment</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Low Risk</span>
                      <span className='text-sm font-medium text-green-600'>5 goals</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Medium Risk</span>
                      <span className='text-sm font-medium text-yellow-600'>2 goals</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>High Risk</span>
                      <span className='text-sm font-medium text-red-600'>1 goal</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Quick Actions</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3'>
              <button className='p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
                Create New Goal
              </button>
              <button className='p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors'>
                Review Progress
              </button>
              <button className='p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors'>
                Set OKRs
              </button>
              <button className='p-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors'>
                Export Report
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
