import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Team Development | Emynent',
  description: 'Track team skill development, learning progress, and growth initiatives',
}

export default function TeamDevelopmentPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Development</h1>
          <p className='text-muted-foreground'>
            Track team skill development, learning progress, and growth initiatives
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Development Overview */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Active Learners</h3>
              <p className='text-3xl font-bold text-primary mb-1'>11</p>
              <p className='text-sm text-muted-foreground'>Out of 12 members</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Skills Developed</h3>
              <p className='text-3xl font-bold text-green-600'>28</p>
              <p className='text-sm text-muted-foreground'>This quarter</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Avg Growth Rate</h3>
              <p className='text-3xl font-bold text-blue-600'>15%</p>
              <p className='text-sm text-muted-foreground'>Skill improvement</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Learning Hours</h3>
              <p className='text-3xl font-bold text-purple-600'>240</p>
              <p className='text-sm text-muted-foreground'>This month</p>
            </div>
          </div>

          {/* Individual Development Plans */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <div className='flex items-center justify-between mb-6'>
              <h2 className='text-xl font-semibold'>Individual Development Plans</h2>
              <button className='py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors'>
                Create IDP
              </button>
            </div>

            <div className='space-y-4'>
              <div className='p-4 bg-muted rounded-lg border border-border'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold'>
                      JD
                    </div>
                    <div>
                      <h3 className='font-semibold'>John Doe</h3>
                      <p className='text-sm text-muted-foreground'>Frontend Lead</p>
                    </div>
                  </div>
                  <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                    On Track
                  </span>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Current Focus</h4>
                    <p className='text-sm text-muted-foreground'>Advanced React Patterns</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Target Completion</h4>
                    <p className='text-sm text-muted-foreground'>March 2025</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Progress</h4>
                    <div className='flex items-center gap-2'>
                      <div className='flex-1 bg-background rounded-full h-2'>
                        <div className='w-3/4 h-full bg-green-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>75%</span>
                    </div>
                  </div>
                </div>

                <div className='flex gap-2'>
                  <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                    View Full Plan
                  </button>
                  <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                    Update Progress
                  </button>
                  <button className='px-3 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600 transition-colors'>
                    Schedule Review
                  </button>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg border border-border'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold'>
                      SM
                    </div>
                    <div>
                      <h3 className='font-semibold'>Sarah Miller</h3>
                      <p className='text-sm text-muted-foreground'>Frontend Developer</p>
                    </div>
                  </div>
                  <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                    Needs Support
                  </span>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Current Focus</h4>
                    <p className='text-sm text-muted-foreground'>TypeScript Mastery</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Target Completion</h4>
                    <p className='text-sm text-muted-foreground'>February 2025</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Progress</h4>
                    <div className='flex items-center gap-2'>
                      <div className='flex-1 bg-background rounded-full h-2'>
                        <div className='w-2/5 h-full bg-yellow-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>40%</span>
                    </div>
                  </div>
                </div>

                <div className='flex gap-2'>
                  <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                    View Full Plan
                  </button>
                  <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                    Update Progress
                  </button>
                  <button className='px-3 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600 transition-colors'>
                    Schedule Review
                  </button>
                </div>
              </div>

              <div className='p-4 bg-muted rounded-lg border border-border'>
                <div className='flex items-center justify-between mb-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold'>
                      AL
                    </div>
                    <div>
                      <h3 className='font-semibold'>Alex Lee</h3>
                      <p className='text-sm text-muted-foreground'>Backend Lead</p>
                    </div>
                  </div>
                  <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                    Exceeding
                  </span>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Current Focus</h4>
                    <p className='text-sm text-muted-foreground'>System Architecture</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Target Completion</h4>
                    <p className='text-sm text-muted-foreground'>April 2025</p>
                  </div>
                  <div>
                    <h4 className='font-medium text-sm mb-2'>Progress</h4>
                    <div className='flex items-center gap-2'>
                      <div className='flex-1 bg-background rounded-full h-2'>
                        <div className='w-5/6 h-full bg-blue-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>85%</span>
                    </div>
                  </div>
                </div>

                <div className='flex gap-2'>
                  <button className='px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors'>
                    View Full Plan
                  </button>
                  <button className='px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors'>
                    Update Progress
                  </button>
                  <button className='px-3 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600 transition-colors'>
                    Schedule Review
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Skill Development Matrix */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Team Skill Development Matrix</h2>

            <div className='overflow-x-auto'>
              <table className='w-full'>
                <thead>
                  <tr className='border-b border-border'>
                    <th className='text-left py-3'>Team Member</th>
                    <th className='text-left py-3'>React/Frontend</th>
                    <th className='text-left py-3'>Node.js/Backend</th>
                    <th className='text-left py-3'>TypeScript</th>
                    <th className='text-left py-3'>Testing</th>
                    <th className='text-left py-3'>DevOps</th>
                    <th className='text-left py-3'>Leadership</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className='border-b border-border'>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs'>
                          JD
                        </div>
                        <span className='font-medium'>John Doe</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Expert
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Intermediate
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Advanced
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Advanced
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Intermediate
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Advanced
                      </span>
                    </td>
                  </tr>
                  <tr className='border-b border-border'>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs'>
                          SM
                        </div>
                        <span className='font-medium'>Sarah Miller</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Intermediate
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full'>
                        Beginner
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full'>
                        Beginner
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Intermediate
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full'>
                        Beginner
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full'>
                        Beginner
                      </span>
                    </td>
                  </tr>
                  <tr className='border-b border-border'>
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <div className='w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs'>
                          AL
                        </div>
                        <span className='font-medium'>Alex Lee</span>
                      </div>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Intermediate
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Expert
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Expert
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Advanced
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Advanced
                      </span>
                    </td>
                    <td className='py-3'>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Advanced
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Learning Resources & Programs */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Learning Resources & Programs</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
                  <h3 className='font-semibold text-blue-900 mb-2'>Active Learning Programs</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>React Advanced Patterns</span>
                      <span className='text-sm font-medium text-blue-800'>5 enrolled</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>TypeScript Mastery</span>
                      <span className='text-sm font-medium text-blue-800'>3 enrolled</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>System Design Fundamentals</span>
                      <span className='text-sm font-medium text-blue-800'>4 enrolled</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Leadership Development</span>
                      <span className='text-sm font-medium text-blue-800'>2 enrolled</span>
                    </div>
                  </div>
                </div>

                <div className='p-4 bg-green-50 border border-green-200 rounded-lg'>
                  <h3 className='font-semibold text-green-900 mb-2'>Mentorship Pairs</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>John Doe → Sarah Miller</span>
                      <span className='text-sm font-medium text-green-800'>React/Frontend</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Alex Lee → Mike Johnson</span>
                      <span className='text-sm font-medium text-green-800'>
                        Backend/Architecture
                      </span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Lisa Taylor → Emma Brown</span>
                      <span className='text-sm font-medium text-green-800'>Testing/QA</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='p-4 bg-purple-50 border border-purple-200 rounded-lg'>
                  <h3 className='font-semibold text-purple-900 mb-2'>Upcoming Training</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Advanced Testing Strategies</span>
                      <span className='text-sm font-medium text-purple-800'>Feb 15</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Microservices Architecture</span>
                      <span className='text-sm font-medium text-purple-800'>Feb 22</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Performance Optimization</span>
                      <span className='text-sm font-medium text-purple-800'>Mar 1</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Team Leadership Workshop</span>
                      <span className='text-sm font-medium text-purple-800'>Mar 8</span>
                    </div>
                  </div>
                </div>

                <div className='p-4 bg-orange-50 border border-orange-200 rounded-lg'>
                  <h3 className='font-semibold text-orange-900 mb-2'>Certification Progress</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>AWS Solutions Architect</span>
                      <span className='text-sm font-medium text-orange-800'>2 pursuing</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>React Professional</span>
                      <span className='text-sm font-medium text-orange-800'>3 pursuing</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Scrum Master</span>
                      <span className='text-sm font-medium text-orange-800'>1 pursuing</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Development Analytics */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-6'>Development Analytics</h2>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-semibold mb-2'>Skill Growth Trends</h3>
                <div className='h-32 bg-background rounded flex items-center justify-center'>
                  <p className='text-muted-foreground text-sm'>Skill Growth Chart</p>
                </div>
                <p className='text-sm text-muted-foreground mt-2'>
                  Average 15% skill improvement across all team members
                </p>
              </div>

              <div className='p-4 bg-muted rounded-lg'>
                <h3 className='font-semibold mb-2'>Learning Engagement</h3>
                <div className='h-32 bg-background rounded flex items-center justify-center'>
                  <p className='text-muted-foreground text-sm'>Engagement Metrics Chart</p>
                </div>
                <p className='text-sm text-muted-foreground mt-2'>
                  92% team participation in development programs
                </p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Quick Actions</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3'>
              <button className='p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
                Create IDP
              </button>
              <button className='p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors'>
                Schedule Training
              </button>
              <button className='p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors'>
                Assign Mentor
              </button>
              <button className='p-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors'>
                View Reports
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
