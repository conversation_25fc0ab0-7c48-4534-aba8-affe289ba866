import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Team Members | Emynent',
  description: 'Manage and view detailed information about your team members',
}

export default function TeamMembersPage() {
  return (
    <div className='w-full max-w-none py-10'>
      <div className='w-full'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>Team Members</h1>
          <p className='text-muted-foreground'>
            Manage and view detailed information about your team members
          </p>
        </div>

        <div className='grid gap-6'>
          {/* Team Stats */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Total Members</h3>
              <p className='text-3xl font-bold text-primary mb-1'>12</p>
              <p className='text-sm text-muted-foreground'>Active team members</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>New This Month</h3>
              <p className='text-3xl font-bold text-green-600'>2</p>
              <p className='text-sm text-muted-foreground'>Recent hires</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Avg Tenure</h3>
              <p className='text-3xl font-bold text-blue-600'>2.3</p>
              <p className='text-sm text-muted-foreground'>Years</p>
            </div>
            <div className='bg-card border border-border rounded-lg p-6'>
              <h3 className='text-lg font-semibold mb-2'>Remote</h3>
              <p className='text-3xl font-bold text-purple-600'>8</p>
              <p className='text-sm text-muted-foreground'>Working remotely</p>
            </div>
          </div>

          {/* Search and Filters */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <div className='flex flex-col md:flex-row gap-4 mb-6'>
              <div className='flex-1'>
                <input
                  type='text'
                  placeholder='Search team members...'
                  className='w-full py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary'
                />
              </div>
              <div className='flex gap-2'>
                <select className='py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary'>
                  <option>All Departments</option>
                  <option>Frontend</option>
                  <option>Backend</option>
                  <option>QA</option>
                  <option>Design</option>
                </select>
                <select className='py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary'>
                  <option>All Levels</option>
                  <option>Junior</option>
                  <option>Mid</option>
                  <option>Senior</option>
                  <option>Lead</option>
                </select>
                <button className='py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors'>
                  Filter
                </button>
              </div>
            </div>

            {/* Team Members Grid */}
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {/* Member Card 1 */}
              <div className='p-6 bg-muted rounded-lg border border-border'>
                <div className='flex items-center gap-4 mb-4'>
                  <div className='w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold'>
                    JD
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-lg'>John Doe</h3>
                    <p className='text-sm text-muted-foreground'>Frontend Lead</p>
                    <div className='flex items-center gap-2 mt-1'>
                      <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                        Senior
                      </span>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Remote
                      </span>
                    </div>
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Performance</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-16 bg-background rounded-full h-2'>
                        <div className='w-5/6 h-full bg-green-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>4.8</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Goals Progress</span>
                    <span className='text-sm font-medium'>85%</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Tenure</span>
                    <span className='text-sm font-medium'>3.2 years</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Last 1:1</span>
                    <span className='text-sm font-medium'>2 days ago</span>
                  </div>
                </div>

                <div className='flex gap-2 mt-4'>
                  <button className='flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm'>
                    View Profile
                  </button>
                  <button className='px-3 py-2 bg-background border border-border rounded-lg hover:bg-muted transition-colors text-sm'>
                    Schedule 1:1
                  </button>
                </div>
              </div>

              {/* Member Card 2 */}
              <div className='p-6 bg-muted rounded-lg border border-border'>
                <div className='flex items-center gap-4 mb-4'>
                  <div className='w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-xl font-bold'>
                    SM
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-lg'>Sarah Miller</h3>
                    <p className='text-sm text-muted-foreground'>Frontend Developer</p>
                    <div className='flex items-center gap-2 mt-1'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Mid
                      </span>
                      <span className='px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full'>
                        Hybrid
                      </span>
                    </div>
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Performance</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-16 bg-background rounded-full h-2'>
                        <div className='w-4/5 h-full bg-green-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>4.2</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Goals Progress</span>
                    <span className='text-sm font-medium'>78%</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Tenure</span>
                    <span className='text-sm font-medium'>1.8 years</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Last 1:1</span>
                    <span className='text-sm font-medium'>1 week ago</span>
                  </div>
                </div>

                <div className='flex gap-2 mt-4'>
                  <button className='flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm'>
                    View Profile
                  </button>
                  <button className='px-3 py-2 bg-background border border-border rounded-lg hover:bg-muted transition-colors text-sm'>
                    Schedule 1:1
                  </button>
                </div>
              </div>

              {/* Member Card 3 */}
              <div className='p-6 bg-muted rounded-lg border border-border'>
                <div className='flex items-center gap-4 mb-4'>
                  <div className='w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center text-white text-xl font-bold'>
                    AL
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-lg'>Alex Lee</h3>
                    <p className='text-sm text-muted-foreground'>Backend Lead</p>
                    <div className='flex items-center gap-2 mt-1'>
                      <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                        Senior
                      </span>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Remote
                      </span>
                    </div>
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Performance</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-16 bg-background rounded-full h-2'>
                        <div className='w-full h-full bg-green-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>4.9</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Goals Progress</span>
                    <span className='text-sm font-medium'>92%</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Tenure</span>
                    <span className='text-sm font-medium'>4.1 years</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Last 1:1</span>
                    <span className='text-sm font-medium'>3 days ago</span>
                  </div>
                </div>

                <div className='flex gap-2 mt-4'>
                  <button className='flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm'>
                    View Profile
                  </button>
                  <button className='px-3 py-2 bg-background border border-border rounded-lg hover:bg-muted transition-colors text-sm'>
                    Schedule 1:1
                  </button>
                </div>
              </div>

              {/* Member Card 4 */}
              <div className='p-6 bg-muted rounded-lg border border-border'>
                <div className='flex items-center gap-4 mb-4'>
                  <div className='w-16 h-16 bg-teal-500 rounded-full flex items-center justify-center text-white text-xl font-bold'>
                    LT
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-lg'>Lisa Taylor</h3>
                    <p className='text-sm text-muted-foreground'>QA Lead</p>
                    <div className='flex items-center gap-2 mt-1'>
                      <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full'>
                        Senior
                      </span>
                      <span className='px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full'>
                        Office
                      </span>
                    </div>
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Performance</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-16 bg-background rounded-full h-2'>
                        <div className='w-5/6 h-full bg-green-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>4.6</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Goals Progress</span>
                    <span className='text-sm font-medium'>88%</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Tenure</span>
                    <span className='text-sm font-medium'>2.7 years</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Last 1:1</span>
                    <span className='text-sm font-medium'>5 days ago</span>
                  </div>
                </div>

                <div className='flex gap-2 mt-4'>
                  <button className='flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm'>
                    View Profile
                  </button>
                  <button className='px-3 py-2 bg-background border border-border rounded-lg hover:bg-muted transition-colors text-sm'>
                    Schedule 1:1
                  </button>
                </div>
              </div>

              {/* Member Card 5 */}
              <div className='p-6 bg-muted rounded-lg border border-border'>
                <div className='flex items-center gap-4 mb-4'>
                  <div className='w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl font-bold'>
                    MJ
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-lg'>Mike Johnson</h3>
                    <p className='text-sm text-muted-foreground'>Frontend Developer</p>
                    <div className='flex items-center gap-2 mt-1'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Mid
                      </span>
                      <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full'>
                        Remote
                      </span>
                    </div>
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Performance</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-16 bg-background rounded-full h-2'>
                        <div className='w-3/4 h-full bg-yellow-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>3.9</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Goals Progress</span>
                    <span className='text-sm font-medium'>72%</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Tenure</span>
                    <span className='text-sm font-medium'>1.2 years</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Last 1:1</span>
                    <span className='text-sm font-medium'>1 week ago</span>
                  </div>
                </div>

                <div className='flex gap-2 mt-4'>
                  <button className='flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm'>
                    View Profile
                  </button>
                  <button className='px-3 py-2 bg-background border border-border rounded-lg hover:bg-muted transition-colors text-sm'>
                    Schedule 1:1
                  </button>
                </div>
              </div>

              {/* Member Card 6 */}
              <div className='p-6 bg-muted rounded-lg border border-border'>
                <div className='flex items-center gap-4 mb-4'>
                  <div className='w-16 h-16 bg-red-500 rounded-full flex items-center justify-center text-white text-xl font-bold'>
                    EB
                  </div>
                  <div className='flex-1'>
                    <h3 className='font-semibold text-lg'>Emma Brown</h3>
                    <p className='text-sm text-muted-foreground'>Backend Developer</p>
                    <div className='flex items-center gap-2 mt-1'>
                      <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                        Mid
                      </span>
                      <span className='px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full'>
                        Hybrid
                      </span>
                    </div>
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Performance</span>
                    <div className='flex items-center gap-2'>
                      <div className='w-16 bg-background rounded-full h-2'>
                        <div className='w-4/5 h-full bg-green-500 rounded-full'></div>
                      </div>
                      <span className='text-sm font-medium'>4.4</span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Goals Progress</span>
                    <span className='text-sm font-medium'>81%</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Tenure</span>
                    <span className='text-sm font-medium'>2.1 years</span>
                  </div>

                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-muted-foreground'>Last 1:1</span>
                    <span className='text-sm font-medium'>4 days ago</span>
                  </div>
                </div>

                <div className='flex gap-2 mt-4'>
                  <button className='flex-1 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm'>
                    View Profile
                  </button>
                  <button className='px-3 py-2 bg-background border border-border rounded-lg hover:bg-muted transition-colors text-sm'>
                    Schedule 1:1
                  </button>
                </div>
              </div>
            </div>

            {/* Load More */}
            <div className='flex justify-center mt-8'>
              <button className='py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors'>
                Load More Members
              </button>
            </div>
          </div>

          {/* Team Actions */}
          <div className='bg-card border border-border rounded-lg p-6'>
            <h2 className='text-xl font-semibold mb-4'>Team Actions</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3'>
              <button className='p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
                Add New Member
              </button>
              <button className='p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors'>
                Bulk Schedule 1:1s
              </button>
              <button className='p-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors'>
                Export Team Data
              </button>
              <button className='p-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors'>
                Team Analytics
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
