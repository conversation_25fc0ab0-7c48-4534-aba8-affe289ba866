'use client'

import { SmartSidebar } from '@/components/navigation/SmartSidebar'
import { EnhancedMainNavbar } from '@/components/shared/EnhancedMainNavbar'
import {
  NavigationContextProvider,
  useNavigationContext,
} from '@/lib/context/NavigationContextProvider'
import { useSession } from 'next-auth/react'
import React from 'react'

interface LayoutClientProps {
  children: React.ReactNode
}

// Inner component that uses the navigation context
function LayoutClientInner({ children }: LayoutClientProps) {
  const { data: session } = useSession()
  const { getSidebarWidth, getSidebarState } = useNavigationContext()

  if (!session?.user) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <h2 className='text-lg font-semibold'>Loading...</h2>
          <p className='text-muted-foreground'>Please wait while we load your session</p>
        </div>
      </div>
    )
  }

  const sidebarState = getSidebarState()
  const isHidden = sidebarState === 'hidden'
  const isMinimized = sidebarState === 'minimized'
  const isExpanded = sidebarState === 'expanded'

  return (
    <div className='h-screen bg-background'>
      {/* Smart Navigation Sidebar - Context-aware sidebar routing */}
      <SmartSidebar />

      {/* Main Content Area - Fixed positioning to match navbar */}
      <div
        className={`fixed top-0 right-0 h-full transition-all duration-300 ease-in-out flex flex-col ${
          isHidden
            ? 'left-0 w-full'
            : isMinimized
              ? 'left-16 w-[calc(100%-4rem)]'
              : 'left-64 w-[calc(100%-16rem)]'
        }`}
      >
        {/* Top Navigation */}
        <EnhancedMainNavbar />

        {/* Page Content */}
        <main
          className='flex-1 pt-14 px-6 overflow-y-auto transition-all duration-300 ease-in-out'
          role='main'
          aria-label='Main content'
        >
          {children}
        </main>
      </div>
    </div>
  )
}

// Main layout component with context provider
export default function LayoutClient({ children }: LayoutClientProps) {
  return (
    <NavigationContextProvider>
      <LayoutClientInner>{children}</LayoutClientInner>
    </NavigationContextProvider>
  )
}
