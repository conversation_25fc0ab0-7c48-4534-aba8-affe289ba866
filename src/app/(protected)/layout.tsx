import { auth } from '@/lib/auth/config'
import { redirect } from 'next/navigation'
import { Toaster } from 'sonner'
import LayoutClient from './layout-client'

export default async function ProtectedLayout({ children }: { children: React.ReactNode }) {
  const session = await auth()

  if (!session) {
    redirect('/signin')
  }

  return (
    <>
      <LayoutClient>{children}</LayoutClient>
      <Toaster />
    </>
  )
}
