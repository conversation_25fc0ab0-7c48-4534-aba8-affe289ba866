import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { ComponentDiscoveryService } from '@/lib/design-system/utils/component-discovery'
import { componentRegistry } from '@/lib/design-system/component-registry'
import { ComponentCategory } from '@/types/design-system'
import * as fs from 'fs'
import * as path from 'path'

/**
 * Integration Tests for Design System Manager + Component Discovery Service
 *
 * These tests verify that the Design System Manager correctly integrates with
 * the ComponentDiscoveryService to display dynamically discovered components.
 *
 * TDD Approach: Tests are written first, then implementation follows.
 * No Mocks: Tests use real file system and actual component discovery.
 */

describe('Design System Manager Integration with Component Discovery', () => {
  let discoveryService: ComponentDiscoveryService
  let testComponentsDir: string

  beforeEach(() => {
    // Create a temporary test directory for components
    testComponentsDir = path.join(process.cwd(), 'test-components-temp')

    // Ensure clean state with more robust cleanup
    if (fs.existsSync(testComponentsDir)) {
      try {
        // First try to remove all files
        const files = fs.readdirSync(testComponentsDir)
        for (const file of files) {
          const filePath = path.join(testComponentsDir, file)
          try {
            if (fs.statSync(filePath).isDirectory()) {
              fs.rmSync(filePath, { recursive: true, force: true })
            } else {
              fs.unlinkSync(filePath)
            }
          } catch (_fileError) {
            // Ignore individual file errors
          }
        }
        // Then remove the directory
        fs.rmSync(testComponentsDir, { recursive: true, force: true })
      } catch {
        // If all else fails, try alternative cleanup
        try {
          const files = fs.readdirSync(testComponentsDir)
          for (const file of files) {
            const filePath = path.join(testComponentsDir, file)
            try {
              fs.unlinkSync(filePath)
            } catch (_fileError) {
              // Ignore individual file errors
            }
          }
          fs.rmdirSync(testComponentsDir)
        } catch (_cleanupError) {
          // console.warn('Failed to clean up test directory:', cleanupError);
        }
      }
    }

    // Create fresh directory
    fs.mkdirSync(testComponentsDir, { recursive: true })

    // Initialize discovery service with test directory
    discoveryService = new ComponentDiscoveryService([testComponentsDir])

    // Clear the component registry
    componentRegistry.replaceWithDiscoveredComponents([])
  })

  afterEach(() => {
    // Clean up test directory with improved error handling
    if (fs.existsSync(testComponentsDir)) {
      try {
        // First try to remove all files
        const files = fs.readdirSync(testComponentsDir)
        for (const file of files) {
          const filePath = path.join(testComponentsDir, file)
          try {
            if (fs.statSync(filePath).isDirectory()) {
              fs.rmSync(filePath, { recursive: true, force: true })
            } else {
              fs.unlinkSync(filePath)
            }
          } catch (_fileError) {
            // Ignore individual file errors
          }
        }
        // Then remove the directory
        fs.rmSync(testComponentsDir, { recursive: true, force: true })
      } catch {
        // If rmSync fails, try alternative cleanup
        try {
          const files = fs.readdirSync(testComponentsDir)
          for (const file of files) {
            const filePath = path.join(testComponentsDir, file)
            try {
              fs.unlinkSync(filePath)
            } catch (_fileError) {
              // Ignore individual file errors
            }
          }
          fs.rmdirSync(testComponentsDir)
        } catch (_cleanupError) {
          // Final fallback - just log the error
          // console.warn('Failed to clean up test directory in afterEach:', cleanupError);
        }
      }
    }
  })

  describe('Component Registry Integration', () => {
    it('should populate component registry with discovered components', async () => {
      // Create a test component file
      const testComponentContent = `
        /**
         * A test button component
         * @displayName Test Button
         * @category forms
         */
        interface TestButtonProps {
          label: string;
          variant?: 'primary' | 'secondary';
          onClick?: () => void;
        }

        export function TestButton({ label, variant = 'primary', onClick }: TestButtonProps) {
          return (
            <button 
              className={\`btn btn-\${variant}\`}
              onClick={onClick}
            >
              {label}
            </button>
          );
        }
      `

      fs.writeFileSync(path.join(testComponentsDir, 'TestButton.tsx'), testComponentContent)

      // Discover components
      const discoveredComponents = await discoveryService.scanAndRegister()

      // Verify component was discovered
      expect(discoveredComponents).toHaveLength(1)
      expect(discoveredComponents[0]).toMatchObject({
        name: 'Test Button',
        category: ComponentCategory.FORMS,
        props: expect.objectContaining({
          label: expect.objectContaining({ type: 'string', required: true }),
          variant: expect.objectContaining({ type: 'enum', required: false }),
          onClick: expect.objectContaining({ type: 'function', required: false }),
        }),
      })

      // Verify component registry integration
      // This test will initially fail until we implement the integration
      const registryComponents = componentRegistry.getAllComponents()
      const discoveredComponent = registryComponents.find(c => c.name === 'Test Button')
      expect(discoveredComponent).toBeDefined()
      expect(discoveredComponent?.id).toBe(discoveredComponents[0].id)
    })

    it('should handle multiple components from different categories', async () => {
      // Ensure test directory exists and is clean
      if (fs.existsSync(testComponentsDir)) {
        try {
          fs.rmSync(testComponentsDir, { recursive: true, force: true })
        } catch {
          // Ignore cleanup errors
        }
      }
      fs.mkdirSync(testComponentsDir, { recursive: true })

      // Clear registry first
      componentRegistry.replaceWithDiscoveredComponents([])

      // Create components from different categories
      const buttonComponent = `
        interface ButtonProps {
          label: string;
          variant: 'primary' | 'secondary';
        }
        export const Button = ({ label, variant }: ButtonProps) => {
          return <button className={\`btn btn-\${variant}\`}>{label}</button>;
        };
      `

      const cardComponent = `
        interface CardProps {
          title: string;
          children: React.ReactNode;
        }
        export const Card = ({ title, children }: CardProps) => {
          return <div className="card"><h3>{title}</h3>{children}</div>;
        };
      `

      const alertComponent = `
        interface AlertProps {
          message: string;
          type: 'success' | 'error' | 'warning';
        }
        export const Alert = ({ message, type }: AlertProps) => {
          return <div className={\`alert alert-\${type}\`}>{message}</div>;
        };
      `

      fs.writeFileSync(path.join(testComponentsDir, 'Button.tsx'), buttonComponent)
      fs.writeFileSync(path.join(testComponentsDir, 'Card.tsx'), cardComponent)
      fs.writeFileSync(path.join(testComponentsDir, 'Alert.tsx'), alertComponent)

      // Discover and register components
      const discoveredComponents = await discoveryService.scanAndRegister()

      // Verify all components were discovered
      expect(discoveredComponents).toHaveLength(3)

      // Verify component names
      const componentNames = discoveredComponents.map(c => c.name).sort()
      expect(componentNames).toEqual(['Alert', 'Button', 'Card'])

      // Verify categories are assigned correctly
      const button = discoveredComponents.find(c => c.name === 'Button')
      const card = discoveredComponents.find(c => c.name === 'Card')
      const alert = discoveredComponents.find(c => c.name === 'Alert')

      expect(button?.category).toBe('forms') // Button is categorized as forms
      expect(card?.category).toBe('layout') // Card is categorized as layout
      expect(alert?.category).toBe('feedback') // Alert is categorized as feedback

      // Verify registry was updated
      const registryComponents = componentRegistry.getAllComponents()
      expect(registryComponents).toHaveLength(3)
    })

    it('should replace default components with discovered components', async () => {
      // Ensure test directory exists
      if (!fs.existsSync(testComponentsDir)) {
        fs.mkdirSync(testComponentsDir, { recursive: true })
      }

      // Clear registry first
      componentRegistry.replaceWithDiscoveredComponents([])

      // Create a custom component
      const customComponent = `
        interface CustomButtonProps {
          text: string;
          variant?: 'primary' | 'secondary';
        }
        export const CustomButton = ({ text, variant = 'primary' }: CustomButtonProps) => {
          return <button className={\`btn btn-\${variant}\`}>{text}</button>;
        };
      `

      fs.writeFileSync(path.join(testComponentsDir, 'CustomButton.tsx'), customComponent)

      // Discover and register components
      const discoveredComponents = await discoveryService.scanAndRegister()

      // Verify discovered component exists
      expect(discoveredComponents).toHaveLength(1)
      expect(discoveredComponents[0].name).toBe('Custom Button')

      // Verify registry was updated
      const registryComponents = componentRegistry.getAllComponents()
      expect(registryComponents).toHaveLength(1)
      expect(registryComponents[0].name).toBe('Custom Button')
    })
  })

  describe('Error Handling', () => {
    it('should handle components with syntax errors gracefully', async () => {
      // Ensure test directory exists
      if (!fs.existsSync(testComponentsDir)) {
        fs.mkdirSync(testComponentsDir, { recursive: true })
      }

      // Create a component with syntax errors
      const brokenComponent = `
        interface BrokenProps {
          prop1: string
          // Missing semicolon and unmatched braces
        export const BrokenComponent = ({ prop1 }: BrokenProps) => {
          return <div>{prop1</div>; // Missing closing bracket and unmatched braces
        `

      // Create a valid component
      const validComponent = `
        interface ValidProps {
          prop1: string;
        }
        export const ValidComponent = ({ prop1 }: ValidProps) => {
          return <div>{prop1}</div>;
        };
      `

      fs.writeFileSync(path.join(testComponentsDir, 'BrokenComponent.tsx'), brokenComponent)
      fs.writeFileSync(path.join(testComponentsDir, 'ValidComponent.tsx'), validComponent)

      // Discover components - should not throw error
      const components = await discoveryService.scanAndRegister()

      // Should only include the valid component
      expect(components.length).toBe(1)
      expect(components[0].name).toBe('Valid Component')
    })

    it('should handle empty component directories', async () => {
      // Test with empty directory
      const discoveredComponents = await discoveryService.scanAndRegister()

      // Should return empty array, not throw error
      expect(discoveredComponents).toHaveLength(0)

      // Registry should be empty since we cleared it in beforeEach and no components were discovered
      const registryComponents = componentRegistry.getAllComponents()
      expect(registryComponents.length).toBe(0) // No components should exist since directory is empty
    })

    it('should handle non-existent component directories', async () => {
      // Create service with non-existent paths
      const invalidService = new ComponentDiscoveryService(['non/existent/path'])

      // Should not throw error
      const discoveredComponents = await invalidService.scanAndRegister()
      expect(discoveredComponents).toHaveLength(0)
    })
  })

  describe('Component Metadata Accuracy', () => {
    it('should preserve all component metadata during integration', async () => {
      const componentWithMetadata = `
        /**
         * A comprehensive test component with full metadata
         * @displayName Comprehensive Component
         * @category navigation
         * @description A component that demonstrates all metadata features
         */
        interface ComprehensiveProps {
          /** The main title text */
          title: string;
          /** Optional subtitle */
          subtitle?: string;
          /** Navigation items */
          items: Array<{ label: string; href: string }>;
          /** Click handler */
          onItemClick?: (href: string) => void;
          /** Visual variant */
          variant: 'horizontal' | 'vertical' | 'dropdown';
          /** Whether the component is disabled */
          disabled?: boolean;
        }

        export function ComprehensiveComponent({
          title,
          subtitle,
          items,
          onItemClick,
          variant = 'horizontal',
          disabled = false
        }: ComprehensiveProps) {
          return (
            <nav className={\`nav nav-\${variant} \${disabled ? 'nav-disabled' : ''}\`}>
              <h2>{title}</h2>
              {subtitle && <p>{subtitle}</p>}
              <ul>
                {items.map((item, index) => (
                  <li key={index}>
                    <a href={item.href} onClick={() => onItemClick?.(item.href)}>
                      {item.label}
                    </a>
                  </li>
                ))}
              </ul>
            </nav>
          );
        }
      `

      fs.writeFileSync(
        path.join(testComponentsDir, 'ComprehensiveComponent.tsx'),
        componentWithMetadata
      )

      const discoveredComponents = await discoveryService.scanAndRegister()

      expect(discoveredComponents).toHaveLength(1)
      const component = discoveredComponents[0]

      // Verify all metadata is preserved
      expect(component.name).toBe('Comprehensive Component')
      expect(component.category).toBe(ComponentCategory.NAVIGATION)
      expect(component.description).toBe('A comprehensive test component with full metadata')

      // Verify props are correctly extracted
      expect(component.props).toMatchObject({
        title: { type: 'string', required: true },
        subtitle: { type: 'string', required: false },
        items: { type: 'array', required: true },
        onItemClick: { type: 'function', required: false },
        variant: { type: 'enum', required: true },
        disabled: { type: 'boolean', required: false },
      })

      // Verify registry integration preserves metadata
      const registryComponents = componentRegistry.getAllComponents()
      const registryComponent = registryComponents.find(c => c.name === 'Comprehensive Component')

      expect(registryComponent).toBeDefined()
      expect(registryComponent?.description).toBe(component.description)
      expect(registryComponent?.category).toBe(component.category)
      expect(registryComponent?.props).toEqual(component.props)
    })
  })
})
