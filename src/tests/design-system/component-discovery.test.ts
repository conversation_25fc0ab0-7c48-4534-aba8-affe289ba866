import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { ComponentDiscoveryService } from '@/lib/design-system/utils/component-discovery'
import { componentRegistry } from '@/lib/design-system/component-registry'
import { ComponentCategory } from '@/types/design-system'
import * as fs from 'fs'
import * as path from 'path'

/**
 * ComponentDiscoveryService Tests
 *
 * Tests the component discovery functionality using real file operations.
 * No mocks - tests create actual files and directories.
 *
 * TDD Approach: Tests written first, implementation follows.
 */

describe('ComponentDiscoveryService', () => {
  let discoveryService: ComponentDiscoveryService
  let testComponentsDir: string

  beforeEach(() => {
    // Create temporary test directory with unique name to avoid conflicts
    testComponentsDir = path.join(
      process.cwd(),
      `test-components-temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    )

    // Ensure clean state
    if (fs.existsSync(testComponentsDir)) {
      try {
        fs.rmSync(testComponentsDir, { recursive: true, force: true })
      } catch {
        // If rmSync fails, try alternative cleanup
        try {
          const files = fs.readdirSync(testComponentsDir)
          for (const file of files) {
            const filePath = path.join(testComponentsDir, file)
            try {
              if (fs.statSync(filePath).isDirectory()) {
                fs.rmSync(filePath, { recursive: true, force: true })
              } else {
                fs.unlinkSync(filePath)
              }
            } catch (_fileError) {
              // Ignore individual file errors
            }
          }
          fs.rmdirSync(testComponentsDir)
        } catch (_cleanupError) {
          // console.warn('Failed to clean up test directory:', cleanupError);
        }
      }
    }

    // Create fresh directory
    fs.mkdirSync(testComponentsDir, { recursive: true })

    // Initialize discovery service with test directory
    discoveryService = new ComponentDiscoveryService([testComponentsDir])

    // Clear component registry to start fresh
    componentRegistry.replaceWithDiscoveredComponents([])
  })

  afterEach(() => {
    // Clean up test directory
    if (fs.existsSync(testComponentsDir)) {
      try {
        fs.rmSync(testComponentsDir, { recursive: true, force: true })
      } catch {
        // If rmSync fails, try to clean up files individually
        try {
          const files = fs.readdirSync(testComponentsDir)
          for (const file of files) {
            const filePath = path.join(testComponentsDir, file)
            if (fs.statSync(filePath).isDirectory()) {
              fs.rmSync(filePath, { recursive: true, force: true })
            } else {
              fs.unlinkSync(filePath)
            }
          }
          fs.rmdirSync(testComponentsDir)
        } catch (_cleanupError) {
          // console.warn('Failed to clean up test directory:', cleanupError);
        }
      }
    }
  })

  describe('scanAndRegister', () => {
    it('should discover components from configured paths', async () => {
      // Create test components
      const testComponents = [
        {
          name: 'Button.tsx',
          content: `
interface ButtonProps {
  label: string;
  onClick: () => void;
}
export const Button = ({ label, onClick }: ButtonProps) => <button onClick={onClick}>{label}</button>;
          `,
        },
        {
          name: 'Card.tsx',
          content: `
interface CardProps {
  title: string;
  children: React.ReactNode;
}
export const Card = ({ title, children }: CardProps) => <div><h2>{title}</h2>{children}</div>;
          `,
        },
        {
          name: 'Input.tsx',
          content: `
interface InputProps {
  value: string;
  onChange: (value: string) => void;
}
export const Input = ({ value, onChange }: InputProps) => <input value={value} onChange={(e) => onChange(e.target.value)} />;
          `,
        },
        {
          name: 'Modal.tsx',
          content: `
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}
export const Modal = ({ isOpen, onClose, children }: ModalProps) => {
  if (!isOpen) return null;
  return <div className="modal">{children}<button onClick={onClose}>Close</button></div>;
};
          `,
        },
      ]

      // Write test components to disk
      for (const component of testComponents) {
        fs.writeFileSync(path.join(testComponentsDir, component.name), component.content)
      }

      // Verify files were created
      const createdFiles = fs.readdirSync(testComponentsDir)
      expect(createdFiles).toHaveLength(4)

      const components = await discoveryService.scanAndRegister()

      // Should find all 4 components we created
      expect(components.length).toBe(4)

      // Verify the first component has the expected structure
      expect(components[0]).toMatchObject({
        id: expect.any(String),
        name: expect.any(String),
        category: expect.any(String),
        version: '1.0.0',
        metadata: expect.any(Object),
      })
    })

    it('should handle missing directories gracefully', async () => {
      // Use non-existent directory
      discoveryService = new ComponentDiscoveryService(['non-existent-directory'])

      const components = await discoveryService.scanAndRegister()

      expect(components).toHaveLength(0)
    })

    it('should skip non-component files', async () => {
      // Create a mix of component and non-component files
      const files = [
        {
          name: 'Button.tsx',
          content: `
interface ButtonProps {
  text: string;
}
export const Button = ({ text }: ButtonProps) => <button>{text}</button>;
          `,
        },
        {
          name: 'utils.ts',
          content: 'export const helper = () => "helper";',
        },
        {
          name: 'styles.css',
          content: '.button { color: blue; }',
        },
        {
          name: 'README.md',
          content: '# Components',
        },
      ]

      // Write files to disk
      for (const file of files) {
        fs.writeFileSync(path.join(testComponentsDir, file.name), file.content)
      }

      // Verify all files were created
      const createdFiles = fs.readdirSync(testComponentsDir)
      expect(createdFiles).toHaveLength(4)

      const components = await discoveryService.scanAndRegister()

      expect(components).toHaveLength(1) // Only Button.tsx should be processed
      expect(components[0].name).toBe('Button')
    })
  })

  describe('component metadata extraction', () => {
    it('should extract props interface correctly', async () => {
      const componentContent = `
        interface TestComponentProps {
          title: string;
          count?: number;
          isVisible: boolean;
          variant: 'primary' | 'secondary';
          children: React.ReactNode;
        }

        export function TestComponent(props: TestComponentProps) {
          return <div>{props.title}</div>;
        }
      `

      fs.writeFileSync(path.join(testComponentsDir, 'TestComponent.tsx'), componentContent)

      const components = await discoveryService.scanAndRegister()

      expect(components[0].props).toEqual({
        title: { type: 'string', required: true, default: undefined },
        count: { type: 'number', required: false, default: undefined },
        isVisible: { type: 'boolean', required: true, default: undefined },
        variant: { type: 'enum', required: true, default: undefined },
        children: { type: 'node', required: true, default: undefined },
      })
    })

    it('should extract className patterns for styling', async () => {
      const componentContent = `
        export function StyledComponent() {
          return (
            <div className="flex items-center justify-center p-4 bg-white rounded-lg shadow-md">
              <span className="text-lg font-semibold text-gray-800">Content</span>
            </div>
          );
        }
      `

      fs.writeFileSync(path.join(testComponentsDir, 'StyledComponent.tsx'), componentContent)

      const components = await discoveryService.scanAndRegister()

      expect(components[0].styling.baseClasses).toContain('flex items-center')
      expect(components[0].styling.baseClasses).toContain('p-4 bg-white')
    })

    it('should determine correct component category', async () => {
      const testCases = [
        {
          name: 'Header.tsx',
          content: 'export const Header = () => <header>Header</header>',
          expected: 'navigation',
        },
        {
          name: 'Input.tsx',
          content: 'export const Input = () => <input />',
          expected: 'forms',
        },
        {
          name: 'Button.tsx',
          content: 'export const Button = () => <button>Click</button>',
          expected: 'forms',
        },
      ]

      for (const testCase of testCases) {
        // Clean directory for each test case
        if (fs.existsSync(testComponentsDir)) {
          fs.rmSync(testComponentsDir, { recursive: true, force: true })
        }
        fs.mkdirSync(testComponentsDir, { recursive: true })

        fs.writeFileSync(path.join(testComponentsDir, testCase.name), testCase.content)

        const components = await discoveryService.scanAndRegister()

        expect(components[0].category).toBe(testCase.expected)
      }
    })
  })

  describe('getComponentStats', () => {
    it('should return component count by category', async () => {
      // Create components in different categories
      const buttonComponent = `
        export function Button() {
          return <button>Test</button>;
        }
      `

      const cardComponent = `
        export function Card() {
          return <div>Test</div>;
        }
      `

      const inputComponent = `
        export function Input() {
          return <input />;
        }
      `

      fs.writeFileSync(path.join(testComponentsDir, 'Button.tsx'), buttonComponent)
      fs.writeFileSync(path.join(testComponentsDir, 'Card.tsx'), cardComponent)
      fs.writeFileSync(path.join(testComponentsDir, 'Input.tsx'), inputComponent)

      const stats = await discoveryService.getComponentStats()

      expect(stats).toHaveProperty(ComponentCategory.UI_COMPONENTS)
      expect(stats).toHaveProperty(ComponentCategory.LAYOUT)
      expect(stats).toHaveProperty(ComponentCategory.FORMS)
      expect(stats).toHaveProperty(ComponentCategory.NAVIGATION)
      expect(stats).toHaveProperty(ComponentCategory.FEEDBACK)
    })
  })

  describe('error handling', () => {
    it('should handle file read errors gracefully', async () => {
      // Create a file with invalid syntax
      fs.writeFileSync(
        path.join(testComponentsDir, 'ErrorComponent.tsx'),
        `
        // This is invalid TypeScript syntax
        export function ErrorComponent({
          title: string // Missing closing brace and other issues
        }: {
          return <div>{title</div>; // Missing closing brace
        }
      `
      )

      const components = await discoveryService.scanAndRegister()

      // Should handle the error gracefully and return empty array or skip the broken component
      expect(components).toEqual([])
    })

    it('should handle components with syntax errors', async () => {
      // Ensure clean directory for this test
      if (fs.existsSync(testComponentsDir)) {
        fs.rmSync(testComponentsDir, { recursive: true, force: true })
      }
      fs.mkdirSync(testComponentsDir, { recursive: true })

      // Create one valid and one invalid component
      const validComponent = `
        export function ValidComponent() {
          return <div>Valid</div>;
        }
      `

      const invalidComponent = `
        export function InvalidComponent({
          // Missing closing brace and other syntax errors
          title: string
        }: {
          return <div>{title</div>; // Missing closing brace
        }
      `

      fs.writeFileSync(path.join(testComponentsDir, 'ValidComponent.tsx'), validComponent)
      fs.writeFileSync(path.join(testComponentsDir, 'InvalidComponent.tsx'), invalidComponent)

      // Verify only 2 files exist
      const files = fs.readdirSync(testComponentsDir)
      expect(files).toHaveLength(2)

      const components = await discoveryService.scanAndRegister()

      // Should only return the valid component
      expect(components).toHaveLength(1)
      expect(components[0].name).toBe('Valid Component')
    })
  })

  describe('integration with component registry', () => {
    it('should generate valid ComponentDefinition objects', async () => {
      const componentContent = `
        /**
         * Test component for registry integration
         * @displayName Test Component
         * @category ui-components
         */
        interface TestComponentProps {
          title: string;
          variant?: 'primary' | 'secondary';
        }

        export function TestComponent({ title, variant = 'primary' }: TestComponentProps) {
          return (
            <div className="test-component">
              <h1>{title}</h1>
            </div>
          );
        }
      `

      fs.writeFileSync(path.join(testComponentsDir, 'TestComponent.tsx'), componentContent)

      const components = await discoveryService.scanAndRegister()

      expect(components[0]).toMatchObject({
        id: expect.any(String),
        name: expect.any(String),
        description: expect.any(String),
        category: expect.any(String),
        version: expect.any(String),
        props: expect.any(Object),
        styling: expect.objectContaining({
          baseClasses: expect.any(String),
          variants: expect.any(Object),
        }),
        interactions: expect.objectContaining({
          events: expect.any(Array),
          animations: expect.any(Object),
        }),
        accessibility: expect.objectContaining({
          ariaLabel: expect.any(String),
          keyboardNavigation: expect.any(Boolean),
          screenReaderSupport: expect.any(Boolean),
          focusIndicator: expect.any(Boolean),
        }),
        metadata: expect.objectContaining({
          author: expect.any(String),
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
          tags: expect.any(Array),
        }),
      })
    })

    it('should integrate discovered components with registry', async () => {
      const componentContent = `
        export function RegistryTestComponent() {
          return <div>Registry Test</div>;
        }
      `

      fs.writeFileSync(path.join(testComponentsDir, 'RegistryTestComponent.tsx'), componentContent)

      // Scan and register components
      await discoveryService.scanAndRegister()

      // Verify components are in the registry
      const registryComponents = componentRegistry.getAllComponents()
      expect(registryComponents.length).toBeGreaterThan(0)

      const discoveredComponent = registryComponents.find(c => c.name === 'Registry Test Component')
      expect(discoveredComponent).toBeDefined()
    })
  })
})
