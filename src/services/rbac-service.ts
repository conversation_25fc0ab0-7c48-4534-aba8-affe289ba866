/**
 * RBAC Service
 *
 * Provides business logic for role and permission management across the application.
 */

import { Role, User } from '@prisma/client'
import { prisma } from '@/lib/prisma'
import { redis } from '@/lib/redis'
import {
  Permission,
  hasPermission,
  hasRole,
  ROLE_HIERARCHY,
  PERMISSIONS,
} from '@/lib/auth/permissions'
import {
  PermissionContext,
  EnhancedPermissionContext,
  canUserAccess,
  invalidateUserPermissionsCache,
  invalidateRolePermissionsCache,
} from '@/lib/auth/rbac'

// Cache constants
const COMPANY_ROLES_CACHE_KEY = 'company_roles:'
const COMPANY_ROLES_CACHE_TTL = 60 * 60 // 1 hour in seconds

/**
 * Result interfaces for operation outcomes
 */
interface RoleAssignmentResult {
  success: boolean
  message?: string
  userId?: string
  role?: Role
}

interface PermissionCheckResult {
  allowed: boolean
  message?: string
  resource?: string
  action?: string
}

interface RoleValidationResult {
  valid: boolean
  message?: string
  role?: Role
}

/**
 * Context data interface for permission checks
 */
export interface PermissionContextData {
  entityId?: string
  entityType?: string
  sensitivityLevel?: number
  isBulkOperation?: boolean
  userRole?: string
  customContext?: Record<string, any>
}

/**
 * Permission check result interface
 */
export interface PermissionCheckResult {
  allowed: boolean
  message: string
}

/**
 * RBAC service singleton
 */
class RBACService {
  /**
   * Checks if a user can perform an action on a resource
   */
  async checkPermission(
    userId: string,
    resource: string,
    action: string
  ): Promise<PermissionCheckResult> {
    try {
      // Get user with role
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true, companyId: true },
      })

      if (!user) {
        return {
          allowed: false,
          message: 'User not found',
          resource,
          action,
        }
      }

      // Create permission context
      const context: PermissionContext = {
        userId: user.id,
        role: user.role,
        companyId: user.companyId || undefined,
      }

      // Check permission using core RBAC module
      const allowed = await canUserAccess(context, resource, action)

      return {
        allowed,
        message: allowed ? 'Access allowed' : 'Access denied',
        resource,
        action,
      }
    } catch {
      // console.error('Permission check error:', error)
      return {
        allowed: false,
        message: 'Error checking permissions',
        resource,
        action,
      }
    }
  }

  /**
   * Evaluates context-enhanced permission checks
   * Placeholder for future Gen AI-driven authorization
   */
  async checkContextPermission(
    userId: string,
    resource: string,
    action: string,
    contextData?: PermissionContextData
  ): Promise<PermissionCheckResult> {
    try {
      // First, check if the user has the basic permission
      const basicPermission = await this.checkPermission(userId, resource, action)

      // If basic permission is denied, no need for contextual check
      if (!basicPermission.allowed) {
        return basicPermission
      }

      // Get user details
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          role: true,
          companyId: true,
        },
      })

      if (!user) {
        return {
          allowed: false,
          message: 'User not found',
        }
      }

      // Check for contextual rules
      // This is simplified logic that will be replaced with Gen AI in the future

      // Rule: High sensitivity data requires ADMIN or higher
      if (contextData?.sensitivityLevel && contextData.sensitivityLevel > 7) {
        if (!['ADMIN', 'SUPERADMIN'].includes(user.role)) {
          return {
            allowed: false,
            message: 'High sensitivity data requires admin privileges',
          }
        }
      }

      // Rule: Bulk operations require MANAGER or higher
      if (
        contextData?.isBulkOperation &&
        !['MANAGER', 'DIRECTOR', 'ADMIN', 'SUPERADMIN'].includes(user.role)
      ) {
        return {
          allowed: false,
          message: 'Bulk operations require manager privileges or higher',
        }
      }

      // Rule: Users can only operate on entities within their scope
      // For example, managers can only access their team members' data
      if (contextData?.entityType === 'user' && contextData?.entityId) {
        if (user.role === 'MANAGER') {
          // In the future, this would check if the target user is in this manager's team
          // For now, we're using a placeholder implementation
          const isTeamMember = await this.isUserInManagersTeam(contextData.entityId, userId)

          if (!isTeamMember) {
            return {
              allowed: false,
              message: 'You can only access users in your team',
            }
          }
        }
      }

      // In the future, this would use Gen AI to analyze the full context
      // including user history, preferences, recent actions, etc.

      // If passed all contextual checks
      return {
        allowed: true,
        message: 'Permission granted based on context',
      }
    } catch {
      // console.error('Error checking context permission:', error)
      return {
        allowed: false,
        message: 'Error checking permissions',
      }
    }
  }

  /**
   * Placeholder: Checks if a user is in a manager's team
   * In a real implementation, this would access the team relationship data
   *
   * @param targetUserId The ID of the user being accessed
   * @param managerId The ID of the manager
   * @returns True if the user is in the manager's team
   */
  private async isUserInManagersTeam(targetUserId: string, managerId: string): Promise<boolean> {
    // This is a placeholder implementation
    // In a real system, you would query team/department relationships

    try {
      const targetUser = await prisma.user.findUnique({
        where: { id: targetUserId },
        select: {
          companyId: true,
          // In the future: departmentId, managerId, etc.
        },
      })

      const manager = await prisma.user.findUnique({
        where: { id: managerId },
        select: {
          companyId: true,
          role: true,
          // In the future: departments they manage, etc.
        },
      })

      if (!targetUser || !manager) {
        return false
      }

      // Ensure they're in the same company at minimum
      if (targetUser.companyId !== manager.companyId) {
        return false
      }

      // In a real implementation, you would check if:
      // 1. The manager is assigned as the user's direct manager
      // 2. The user is in a department managed by the manager
      // 3. The user is in a subdepartment under the manager's supervision

      // For now, we'll return true for same company as a placeholder
      // TODO: Replace with actual team relationship logic
      return true
    } catch {
      // console.error('Error checking team relationship:', error)
      return false
    }
  }

  /**
   * Gets all users with a specific role in a company
   */
  async getUsersByRole(companyId: string, role: Role): Promise<User[]> {
    return prisma.user.findMany({
      where: {
        companyId,
        role,
      },
    })
  }

  /**
   * Assigns a role to a user
   */
  async assignRoleToUser(
    targetUserId: string,
    role: Role,
    assignedByUserId: string
  ): Promise<RoleAssignmentResult> {
    try {
      // Verify the role is valid
      const roleCheck = this.validateRole(role)
      if (!roleCheck.valid) {
        return {
          success: false,
          message: roleCheck.message,
        }
      }

      // Get the user assigning the role
      const assigningUser = await prisma.user.findUnique({
        where: { id: assignedByUserId },
        select: { id: true, role: true, companyId: true },
      })

      if (!assigningUser) {
        return {
          success: false,
          message: 'Assigning user not found',
        }
      }

      // Get the target user
      const targetUser = await prisma.user.findUnique({
        where: { id: targetUserId },
        select: { id: true, role: true, companyId: true },
      })

      if (!targetUser) {
        return {
          success: false,
          message: 'Target user not found',
        }
      }

      // Check company match for multi-tenant security
      if (assigningUser.companyId !== targetUser.companyId) {
        return {
          success: false,
          message: 'Cannot assign roles to users in different companies',
        }
      }

      // Check if assigning user has permission to manage roles
      const canManageRoles = hasPermission(assigningUser.role, PERMISSIONS.ROLES.ASSIGN)
      if (!canManageRoles) {
        return {
          success: false,
          message: 'You do not have permission to assign roles',
        }
      }

      // Check if assigning user has sufficient role hierarchy
      // Users can only assign roles lower than their own
      if (!hasRole(assigningUser.role, role)) {
        return {
          success: false,
          message: 'You cannot assign a role higher than or equal to your own',
        }
      }

      // Update the user's role
      await prisma.user.update({
        where: { id: targetUserId },
        data: { role },
      })

      // Invalidate caches
      await invalidateUserPermissionsCache(targetUserId)

      // Log the role change for audit purposes
      await prisma.auditLog.create({
        data: {
          userId: assignedByUserId,
          action: 'ROLE_ASSIGNED',
          details: JSON.stringify({
            targetUserId,
            previousRole: targetUser.role,
            newRole: role,
          }),
          resourceType: 'USER',
          resourceId: targetUserId,
        },
      })

      return {
        success: true,
        message: `Role ${role} assigned successfully`,
        userId: targetUserId,
        role,
      }
    } catch {
      // console.error('Role assignment error:', error)
      return {
        success: false,
        message: 'Error assigning role',
      }
    }
  }

  /**
   * Validates that a role exists
   */
  validateRole(role: string): RoleValidationResult {
    // Check if role is a valid enum value
    if (!Object.keys(ROLE_HIERARCHY).includes(role)) {
      return {
        valid: false,
        message: `Invalid role: ${role}`,
      }
    }

    return {
      valid: true,
      role: role as Role,
    }
  }

  /**
   * Gets all roles available in the system
   */
  getAllRoles(): Role[] {
    return Object.keys(ROLE_HIERARCHY) as Role[]
  }

  /**
   * Gets roles that can be assigned by a specific role
   */
  getAssignableRoles(userRole: Role): Role[] {
    const userRoleLevel = ROLE_HIERARCHY[userRole]

    // Users can assign roles of lower hierarchy
    return Object.entries(ROLE_HIERARCHY)
      .filter(([_, level]) => level < userRoleLevel)
      .map(([role]) => role as Role)
  }

  /**
   * Gets company role statistics cached for performance
   */
  async getCompanyRoleStats(companyId: string): Promise<Record<Role, number>> {
    const cacheKey = `${COMPANY_ROLES_CACHE_KEY}${companyId}`

    // Try to get from cache first
    const cachedStats = await redis.get(cacheKey)

    if (cachedStats) {
      return JSON.parse(cachedStats as string) as Record<Role, number>
    }

    // Not in cache, calculate stats
    const stats = await prisma.user.groupBy({
      by: ['role'],
      where: { companyId },
      _count: true,
    })

    // Format into a record
    const roleStats = Object.keys(ROLE_HIERARCHY).reduce(
      (acc, role) => {
        acc[role as Role] = 0
        return acc
      },
      {} as Record<Role, number>
    )

    // Fill with actual counts
    stats.forEach(stat => {
      roleStats[stat.role] = stat._count
    })

    // Cache the result
    await redis.set(cacheKey, JSON.stringify(roleStats), { ex: COMPANY_ROLES_CACHE_TTL })

    return roleStats
  }

  /**
   * Invalidates company role stats cache
   */
  async invalidateCompanyRoleStats(companyId: string): Promise<void> {
    const cacheKey = `${COMPANY_ROLES_CACHE_KEY}${companyId}`
    await redis.del(cacheKey)
  }

  /**
   * Gets permissions for a specific user
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, role: true, companyId: true },
    })

    if (!user) {
      return []
    }

    // For now, just return role-based permissions
    // In a production implementation, this would merge with custom user permissions
    return await this.getRolePermissions(user.role)
  }

  /**
   * Gets all permissions for a role
   */
  async getRolePermissions(role: Role): Promise<Permission[]> {
    const cacheKey = `role_permissions:${role}`

    // Try to get from cache first
    const cachedPermissions = await redis.get(cacheKey)

    if (cachedPermissions) {
      return JSON.parse(cachedPermissions as string) as Permission[]
    }

    // Not in cache, get the permissions
    // In a more complex system, this might come from a database
    // For now, we use the static definitions
    const permissions = hasPermission(role, '*:*')
      ? this.getAllPermissions()
      : this.getPermissionsForRole(role)

    // Cache the result
    await redis.set(cacheKey, JSON.stringify(permissions), { ex: PERMISSIONS_CACHE_TTL })

    return permissions
  }

  /**
   * Gets all permissions defined in the system
   */
  getAllPermissions(): Permission[] {
    const permissions: Permission[] = []

    // Flatten all permission definitions
    Object.values(PERMISSIONS).forEach(resource => {
      Object.values(resource).forEach(permission => {
        if (typeof permission === 'string' && !permission.includes('*')) {
          permissions.push(permission)
        }
      })
    })

    return permissions
  }

  /**
   * Gets permissions for a specific role using static definitions
   */
  getPermissionsForRole(role: Role): Permission[] {
    // This would typically involve:
    // 1. Getting base role permissions from static definitions
    // 2. Adding role-specific custom permissions from database

    // For now, we just use the static definitions
    if (role === 'SUPERADMIN') {
      return ['*:*']
    }

    // Use the permissions module's function
    return hasPermission(role, '*:*')
      ? this.getAllPermissions()
      : ([...Object.values(PERMISSIONS)].flatMap(resourceObj =>
          Object.values(resourceObj).filter(permission => hasPermission(role, permission as string))
        ) as Permission[])
  }
}

// Export a singleton instance of the service
export const rbacService = new RBACService()
