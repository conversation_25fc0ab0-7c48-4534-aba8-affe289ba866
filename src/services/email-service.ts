/**
 * Email Service using SendGrid for transactional emails
 */

import sgMail from '@sendgrid/mail'

interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
  from?: string
}

export interface PasswordResetEmailData {
  email: string
  resetToken: string
  userName?: string
}

// Initialize SendGrid
const initializeSendGrid = () => {
  const apiKey = process.env.SENDGRID_API_KEY
  if (!apiKey) {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.warn('⚠️  SENDGRID_API_KEY not found. Email sending will fail.')
    return false
  }

  sgMail.setApiKey(apiKey)
  if (process.env.NODE_ENV === 'development') console.log('✅ SendGrid initialized successfully')
  return true
}

/**
 * Simple email validation
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Send email using SendGrid
 */
async function sendEmail(emailData: EmailData): Promise<{ success: boolean; message?: string }> {
  try {
    // Initialize SendGrid if not already done
    if (!initializeSendGrid()) {
      throw new Error('SendGrid not properly configured')
    }

    const msg = {
      to: emailData.to,
      from: emailData.from || process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      subject: emailData.subject,
      text: emailData.text,
      html: emailData.html,
    }

    if (process.env.NODE_ENV === 'development') console.log(`📧 Sending email to: ${emailData.to}`)
    if (process.env.NODE_ENV === 'development') console.log(`📧 Subject: ${emailData.subject}`)

    const response = await sgMail.send(msg)

    if (process.env.NODE_ENV === 'development') console.log('✅ Email sent successfully')
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.log(`📧 Response status: ${response[0].statusCode}`)

    return {
      success: true,
      message: 'Email sent successfully',
    }
  } catch (error: unknown) {
    if (process.env.NODE_ENV === 'development') console.error('❌ Email sending failed:', error)

    // Log detailed error information
    if ((error as Record<string, unknown>).response) {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error(
            'SendGrid Response:',
            ((error as Record<string, unknown>).response as Record<string, unknown>).body
          )
    }

    return {
      success: false,
      message: (error as Error).message || 'Failed to send email',
    }
  }
}

/**
 * Generate password reset email HTML template
 */
function generatePasswordResetEmailHTML(resetUrl: string, userName?: string): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Emynent Password</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-top: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #1E40AF; margin-bottom: 10px; }
        .title { font-size: 20px; color: #333; margin-bottom: 20px; }
        .content { margin-bottom: 30px; }
        .button { display: inline-block; background-color: #1E40AF; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 20px 0; }
        .button:hover { background-color: #1E3A8A; }
        .footer { font-size: 14px; color: #666; text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; }
        .warning { background-color: #FEF3C7; padding: 15px; border-left: 4px solid #F59E0B; margin: 20px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Emynent</div>
            <h1 class="title">Reset Your Password</h1>
        </div>
        
        <div class="content">
            <p>Hi${userName ? ` ${userName}` : ''},</p>
            
            <p>We received a request to reset your password for your Emynent account. If you made this request, click the button below to reset your password:</p>
            
            <div style="text-align: center;">
                <a href="${resetUrl}" class="button">Reset My Password</a>
            </div>
            
            <div class="warning">
                <strong>Important:</strong> This link will expire in 1 hour for security reasons.
            </div>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace;">${resetUrl}</p>
            
            <p>If you didn't request a password reset, you can safely ignore this email. Your password will not be changed.</p>
        </div>
        
        <div class="footer">
            <p>This email was sent from Emynent. Please don't reply to this email.</p>
            <p>If you have questions, contact our support team.</p>
        </div>
    </div>
</body>
</html>
  `
}

/**
 * Generate password reset email text version
 */
function generatePasswordResetEmailText(resetUrl: string, userName?: string): string {
  return `
Hi${userName ? ` ${userName}` : ''},

We received a request to reset your password for your Emynent account.

To reset your password, click this link: ${resetUrl}

Important: This link will expire in 1 hour for security reasons.

If you didn't request a password reset, you can safely ignore this email.

--
Emynent Team
  `.trim()
}

/**
 * Send password reset email using SendGrid
 */
export async function sendPasswordResetEmail({
  email,
  resetToken,
  userName,
}: PasswordResetEmailData): Promise<{ success: boolean; message?: string }> {
  try {
    // Validate email
    if (!isValidEmail(email)) {
      return {
        success: false,
        message: 'Invalid email address',
      }
    }

    // Construct reset URL (works for both local dev and production)
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const resetUrl = `${baseUrl}/reset-password?token=${resetToken}`

    if (process.env.NODE_ENV === 'development') console.log(`🔗 Generated reset URL: ${resetUrl}`)

    const emailData: EmailData = {
      to: email,
      subject: 'Reset Your Emynent Password',
      html: generatePasswordResetEmailHTML(resetUrl, userName),
      text: generatePasswordResetEmailText(resetUrl, userName),
    }

    return await sendEmail(emailData)
  } catch (error: unknown) {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('❌ Password reset email failed:', error)
    return {
      success: false,
      message: 'Failed to send password reset email',
    }
  }
}

/**
 * Test email connectivity (useful for debugging)
 */
export async function testEmailConnectivity(): Promise<{ success: boolean; message: string }> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      return {
        success: false,
        message: 'SENDGRID_API_KEY environment variable not set',
      }
    }

    if (!process.env.SENDGRID_FROM_EMAIL) {
      return {
        success: false,
        message: 'SENDGRID_FROM_EMAIL environment variable not set',
      }
    }

    return {
      success: true,
      message: 'SendGrid configuration looks good',
    }
  } catch (error: unknown) {
    return {
      success: false,
      message: (error as Error).message,
    }
  }
}
