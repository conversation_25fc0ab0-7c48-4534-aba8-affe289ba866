import { PrismaClient, Role, User } from '@prisma/client'

// Initialize Prisma client
const prisma = new PrismaClient()

/**
 * Service for user-related operations
 */
export class UserService {
  /**
   * Get a user by ID
   */
  async getUserById(userId: string): Promise<User | null> {
    try {
      return await prisma.user.findUnique({
        where: { id: userId },
      })
    } catch {
      // console.error('Error getting user by ID:', error)
      return null
    }
  }

  /**
   * Update a user's profile
   */
  async updateUser(
    userId: string,
    data: {
      name?: string
      email?: string
      title?: string
      jobTitle?: string
      department?: string
      phoneNumber?: string
      preferences?: Record<string, any>
    }
  ): Promise<{
    success: boolean
    message: string
    user?: User
  }> {
    try {
      // Verify user exists
      const existingUser = await prisma.user.findUnique({
        where: { id: userId },
      })

      if (!existingUser) {
        return { success: false, message: 'User not found' }
      }

      // Check if email is being changed and if it's already taken
      if (data.email && data.email !== existingUser.email) {
        const emailExists = await prisma.user.findUnique({
          where: { email: data.email },
        })

        if (emailExists) {
          return { success: false, message: 'Email already in use' }
        }
      }

      // Update user record
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          name: data.name,
          email: data.email,
          title: data.title,
          jobTitle: data.jobTitle,
          department: data.department,
          phoneNumber: data.phoneNumber,
          // Handle preferences as contextData JSON field
          ...(data.preferences && {
            contextData: JSON.stringify(data.preferences),
          }),
          updatedAt: new Date(),
        },
      })

      return {
        success: true,
        message: 'User updated successfully',
        user: updatedUser,
      }
    } catch {
      // console.error('Error updating user:', error)
      return { success: false, message: 'Failed to update user' }
    }
  }

  /**
   * Get users by company ID
   */
  async getUsersByCompany(companyId: string): Promise<User[]> {
    try {
      return await prisma.user.findMany({
        where: { companyId },
        orderBy: { createdAt: 'desc' },
      })
    } catch {
      // console.error('Error getting users by company:', error)
      return []
    }
  }

  /**
   * Impersonate a user by ID (for Developer Persona Mode)
   */
  async impersonateById(userId: string): Promise<{
    success: boolean
    message: string
    user?: User
  }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      })

      if (!user) {
        return { success: false, message: 'User not found' }
      }

      return {
        success: true,
        message: 'Impersonation successful',
        user,
      }
    } catch {
      // console.error('Error impersonating user by ID:', error)
      return { success: false, message: 'Failed to impersonate user' }
    }
  }

  /**
   * Impersonate a user by role (for Developer Persona Mode)
   */
  async impersonateByRole(
    role: string,
    companyId: string
  ): Promise<{
    success: boolean
    message: string
    user?: User
  }> {
    try {
      // Validate role
      if (!Object.values(Role).includes(role as Role)) {
        return { success: false, message: 'Invalid role' }
      }

      // Find a user with the specified role
      const user = await prisma.user.findFirst({
        where: {
          role: role as Role,
          companyId,
        },
      })

      if (!user) {
        return { success: false, message: `No user found with role ${role}` }
      }

      return {
        success: true,
        message: 'Impersonation successful',
        user,
      }
    } catch {
      // console.error('Error impersonating user by role:', error)
      return { success: false, message: 'Failed to impersonate user' }
    }
  }

  /**
   * Audit an impersonation event
   */
  async auditImpersonation(
    superadminId: string,
    targetUserId: string,
    targetRole: string,
    userAgent: string
  ): Promise<boolean> {
    try {
      await prisma.auditLog.create({
        data: {
          action: 'IMPERSONATION',
          userId: superadminId,
          resourceType: 'USER',
          resourceId: targetUserId,
          details: {
            targetRole,
            userAgent,
            timestamp: new Date().toISOString(),
          },
        },
      })

      return true
    } catch {
      // console.error('Error creating audit log:', error)
      return false
    }
  }
}
