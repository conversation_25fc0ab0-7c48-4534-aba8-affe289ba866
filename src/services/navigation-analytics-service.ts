import { PrismaClient } from '@prisma/client'

interface NavigationEvent {
  action: string
  section: string
  duration?: number
  sessionId: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  userId: string
  timestamp: number
  metadata?: Record<string, any>
}

export class NavigationAnalyticsService {
  constructor(private prisma: PrismaClient) {}

  async trackNavigationEvent(event: NavigationEvent): Promise<void> {
    try {
      await this.prisma.analyticsEvent.create({
        data: {
          userId: event.userId,
          eventType: event.action,
          eventData: {
            section: event.section,
            duration: event.duration,
            sessionId: event.sessionId,
            deviceType: event.deviceType,
            timestamp: event.timestamp,
            ...event.metadata,
          },
          timestamp: new Date(event.timestamp),
        },
      })
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to track navigation event:', error)
      // Don't throw - analytics failures shouldn't break user experience
    }
  }

  async getNavigationAnalytics(userId: string, timeRange: { start: Date; end: Date }) {
    try {
      const events = await this.prisma.analyticsEvent.findMany({
        where: {
          userId,
          timestamp: {
            gte: timeRange.start,
            lte: timeRange.end,
          },
          eventType: {
            in: ['section_time', 'navigation_click', 'page_view'],
          },
        },
        orderBy: {
          timestamp: 'desc',
        },
      })

      return events
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to get navigation analytics:', error)
      return []
    }
  }

  async getPopularSections(companyId?: string, limit: number = 10) {
    try {
      const whereClause = companyId
        ? {
            user: {
              companyId,
            },
            eventType: 'section_time',
          }
        : {
            eventType: 'section_time',
          }

      const sectionStats = await this.prisma.analyticsEvent.groupBy({
        by: ['eventData'],
        where: whereClause,
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: limit,
      })

      return sectionStats.map(stat => ({
        section: (stat.eventData as any)?.section || 'Unknown',
        visits: stat._count.id,
      }))
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to get popular sections:', error)
      return []
    }
  }
}
