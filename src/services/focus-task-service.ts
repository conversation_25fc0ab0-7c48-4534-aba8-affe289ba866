import { PrismaClient, FocusTask } from '@prisma/client'
import { prisma } from '@/lib/prisma'

/**
 * Input types for task operations
 */
export interface CreateTaskInput {
  title: string
  description?: string
  priority?: 'low' | 'medium' | 'high'
  dueDate?: Date
  estimatedMinutes?: number
  tags?: string[]
  userId: string
  companyId: string
}

export interface UpdateTaskInput {
  title?: string
  description?: string
  status?: 'pending' | 'in-progress' | 'completed' | 'cancelled'
  priority?: 'low' | 'medium' | 'high'
  dueDate?: Date
  completedAt?: Date
  estimatedMinutes?: number
  actualMinutes?: number
  tags?: string[]
  timeBlocks?: Array<{
    startTime: string
    endTime: string
    date?: string
  }>
  order?: number
}

export interface TaskFilters {
  status?: string
  priority?: string
  dueDateRange?: 'today' | 'week' | 'month' | 'overdue'
  tags?: string[]
}

export interface TaskSortOptions {
  field: 'createdAt' | 'dueDate' | 'priority' | 'order' | 'title'
  direction: 'asc' | 'desc'
}

/**
 * Service for focus task-related operations
 */
export class FocusTaskService {
  private prisma: PrismaClient

  constructor() {
    this.prisma = prisma
  }

  /**
   * Get all tasks for a user with optional filtering and sorting
   */
  async getUserTasks(
    userId: string,
    filters?: TaskFilters,
    sort?: TaskSortOptions
  ): Promise<FocusTask[]> {
    try {
      // Build where clause
      const where: any = { userId }

      if (filters?.status) {
        where.status = filters.status
      }

      if (filters?.priority) {
        where.priority = filters.priority
      }

      if (filters?.tags && filters.tags.length > 0) {
        where.tags = {
          hasSome: filters.tags,
        }
      }

      if (filters?.dueDateRange) {
        const now = new Date()
        switch (filters.dueDateRange) {
          case 'today':
            const today = new Date()
            today.setHours(0, 0, 0, 0)
            const tomorrow = new Date(today)
            tomorrow.setDate(today.getDate() + 1)
            where.dueDate = {
              gte: today,
              lt: tomorrow,
            }
            break
          case 'week':
            const weekStart = new Date()
            weekStart.setDate(now.getDate() - now.getDay())
            weekStart.setHours(0, 0, 0, 0)
            const weekEnd = new Date(weekStart)
            weekEnd.setDate(weekStart.getDate() + 7)
            where.dueDate = {
              gte: weekStart,
              lt: weekEnd,
            }
            break
          case 'overdue':
            where.dueDate = {
              lt: now,
            }
            where.status = {
              not: 'completed',
            }
            break
        }
      }

      // Build order clause
      const orderBy: any = {}
      if (sort?.field) {
        // Handle priority sorting with custom order
        if (sort.field === 'priority') {
          orderBy.priority = {
            _case: {
              high: 1,
              medium: 2,
              low: 3,
            },
          }
        } else {
          orderBy[sort.field] = sort.direction
        }
      } else {
        // Default sorting: order first, then created date
        orderBy.order = 'asc'
      }

      const tasks = await this.prisma.focusTask.findMany({
        where,
        orderBy,
      })

      return tasks
    } catch (error) {
      console.error('Error getting user tasks:', error)
      throw new Error('Failed to retrieve tasks')
    }
  }

  /**
   * Get a single task by ID
   */
  async getTaskById(taskId: string, userId: string): Promise<FocusTask | null> {
    try {
      const task = await this.prisma.focusTask.findFirst({
        where: {
          id: taskId,
          userId, // Ensure user can only access their own tasks
        },
      })

      return task
    } catch (error) {
      console.error('Error getting task by ID:', error)
      return null
    }
  }

  /**
   * Create a new task
   */
  async createTask(input: CreateTaskInput): Promise<FocusTask> {
    try {
      // Get the highest order value for this user to append new task at the end
      const lastTask = await this.prisma.focusTask.findFirst({
        where: { userId: input.userId },
        orderBy: { order: 'desc' },
        select: { order: true },
      })

      const newOrder = (lastTask?.order ?? -1) + 1

      const task = await this.prisma.focusTask.create({
        data: {
          title: input.title,
          description: input.description,
          priority: input.priority || 'medium',
          dueDate: input.dueDate,
          estimatedMinutes: input.estimatedMinutes,
          tags: input.tags || [],
          timeBlocks: [],
          userId: input.userId,
          companyId: input.companyId,
          order: newOrder,
        },
      })

      return task
    } catch (error) {
      console.error('Error creating task:', error)
      throw new Error('Failed to create task')
    }
  }

  /**
   * Update an existing task
   */
  async updateTask(taskId: string, input: UpdateTaskInput): Promise<FocusTask> {
    try {
      const task = await this.prisma.focusTask.update({
        where: { id: taskId },
        data: {
          ...(input.title !== undefined && { title: input.title }),
          ...(input.description !== undefined && { description: input.description }),
          ...(input.status !== undefined && { status: input.status }),
          ...(input.priority !== undefined && { priority: input.priority }),
          ...(input.dueDate !== undefined && { dueDate: input.dueDate }),
          ...(input.completedAt !== undefined && { completedAt: input.completedAt }),
          ...(input.estimatedMinutes !== undefined && { estimatedMinutes: input.estimatedMinutes }),
          ...(input.actualMinutes !== undefined && { actualMinutes: input.actualMinutes }),
          ...(input.tags !== undefined && { tags: input.tags }),
          ...(input.timeBlocks !== undefined && { timeBlocks: input.timeBlocks }),
          ...(input.order !== undefined && { order: input.order }),
          updatedAt: new Date(),
        },
      })

      return task
    } catch (error) {
      console.error('Error updating task:', error)
      throw new Error('Failed to update task')
    }
  }

  /**
   * Delete a task
   */
  async deleteTask(taskId: string): Promise<boolean> {
    try {
      await this.prisma.focusTask.delete({
        where: { id: taskId },
      })

      return true
    } catch (error) {
      console.error('Error deleting task:', error)
      return false
    }
  }

  /**
   * Reorder tasks for a user
   */
  async reorderTasks(userId: string, taskIds: string[]): Promise<boolean> {
    try {
      // Update each task with its new order
      const updatePromises = taskIds.map((taskId, index) =>
        this.prisma.focusTask.update({
          where: {
            id: taskId,
            userId, // Ensure user can only reorder their own tasks
          },
          data: { order: index },
        })
      )

      await Promise.all(updatePromises)
      return true
    } catch (error) {
      console.error('Error reordering tasks:', error)
      return false
    }
  }

  /**
   * Mark task as completed
   */
  async completeTask(taskId: string): Promise<FocusTask> {
    try {
      const task = await this.prisma.focusTask.update({
        where: { id: taskId },
        data: {
          status: 'completed',
          completedAt: new Date(),
          updatedAt: new Date(),
        },
      })

      return task
    } catch (error) {
      console.error('Error completing task:', error)
      throw new Error('Failed to complete task')
    }
  }

  /**
   * Get task statistics for a user
   */
  async getTaskStats(userId: string): Promise<{
    total: number
    completed: number
    pending: number
    overdue: number
  }> {
    try {
      const [total, completed, pending, overdue] = await Promise.all([
        this.prisma.focusTask.count({
          where: { userId },
        }),
        this.prisma.focusTask.count({
          where: { userId, status: 'completed' },
        }),
        this.prisma.focusTask.count({
          where: { userId, status: 'pending' },
        }),
        this.prisma.focusTask.count({
          where: {
            userId,
            status: { not: 'completed' },
            dueDate: { lt: new Date() },
          },
        }),
      ])

      return { total, completed, pending, overdue }
    } catch (error) {
      console.error('Error getting task stats:', error)
      return { total: 0, completed: 0, pending: 0, overdue: 0 }
    }
  }

  /**
   * Get today's tasks for a user
   */
  async getTodayTasks(userId: string): Promise<FocusTask[]> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(today.getDate() + 1)

      const tasks = await this.prisma.focusTask.findMany({
        where: {
          userId,
          OR: [
            {
              dueDate: {
                gte: today,
                lt: tomorrow,
              },
            },
            {
              status: 'in-progress',
            },
          ],
        },
        orderBy: [{ order: 'asc' }, { createdAt: 'asc' }],
      })

      return tasks
    } catch (error) {
      console.error('Error getting today tasks:', error)
      return []
    }
  }
}

// Export singleton instance
export const focusTaskService = new FocusTaskService()
