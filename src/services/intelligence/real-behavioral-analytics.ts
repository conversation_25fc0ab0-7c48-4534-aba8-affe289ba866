/**
 * Real Behavioral Analytics Service
 * Phase 3: Intelligence Layer - NO MOCKS, REAL FUNCTIONALITY
 *
 * This service analyzes real user behavior patterns from actual database events,
 * detects meaningful patterns, and stores insights for context-aware features.
 */

import prisma from '@/lib/prisma'
import {
  BehaviorAnalysis,
  BehaviorAnalysisParams,
  PatternDetectionParams,
  PatternDetectionResult,
  EngagementMetrics,
  NavigationPattern,
  FeatureUsagePattern,
  TimeBasedPattern,
  BehaviorPatterns,
  ContextEventData,
  PageVisit,
  PageTransition,
  FeatureUsage,
  BehaviorAnalysisError,
} from '@/types/intelligence'

export class RealBehavioralAnalyticsService {
  /**
   * Analyze real user behavior patterns from database events
   */
  async analyzeUserBehavior(params: BehaviorAnalysisParams): Promise<BehaviorAnalysis> {
    try {
      // Get real events from database based on timeframe
      const events = await this.getEventsForTimeframe(params.userId, params.timeframe)

      if (events.length === 0) {
        return {
          userId: params.userId,
          patterns: {},
          confidence: 0,
          analyzedAt: new Date(),
          recommendations: [],
        }
      }

      // Analyze different pattern types
      const patterns: BehaviorPatterns = {}
      let totalConfidence = 0
      let patternCount = 0

      for (const analysisType of params.analysisTypes) {
        switch (analysisType) {
          case 'navigation':
            patterns.navigation = await this.analyzeNavigationPattern(events)
            totalConfidence += patterns.navigation.confidence
            patternCount++
            break
          case 'feature_usage':
            patterns.featureUsage = await this.analyzeFeatureUsage(events)
            totalConfidence += patterns.featureUsage.confidence
            patternCount++
            break
          case 'time_based':
            patterns.timePatterns = await this.analyzeTimePatterns(events)
            totalConfidence += patterns.timePatterns.confidence
            patternCount++
            break
        }
      }

      // Calculate overall confidence
      const overallConfidence = patternCount > 0 ? totalConfidence / patternCount : 0

      // Store patterns in database
      await this.storeBehaviorPatterns(params.userId, patterns)

      // Generate recommendations if requested
      const recommendations = params.includeRecommendations
        ? await this.generateRecommendations(patterns)
        : []

      return {
        userId: params.userId,
        patterns,
        confidence: overallConfidence,
        analyzedAt: new Date(),
        recommendations,
      }
    } catch {
      // console.error('Error analyzing user behavior:', error);
      throw new BehaviorAnalysisError('Failed to analyze user behavior', {
        userId: params.userId,
        error: error.message,
      })
    }
  }

  /**
   * Detect patterns from a set of events
   */
  async detectPatterns(params: PatternDetectionParams): Promise<PatternDetectionResult> {
    try {
      const patterns: BehaviorPatterns = {}
      let totalConfidence = 0
      let patternCount = 0

      for (const patternType of params.patternTypes) {
        switch (patternType) {
          case 'navigation':
            patterns.navigation = await this.analyzeNavigationPattern(params.events)
            if (patterns.navigation.confidence >= (params.confidenceThreshold || 0.5)) {
              totalConfidence += patterns.navigation.confidence
              patternCount++
            }
            break
          case 'feature_usage':
            patterns.featureUsage = await this.analyzeFeatureUsage(params.events)
            if (patterns.featureUsage.confidence >= (params.confidenceThreshold || 0.5)) {
              totalConfidence += patterns.featureUsage.confidence
              patternCount++
            }
            break
          case 'time_based':
            patterns.timePatterns = await this.analyzeTimePatterns(params.events)
            if (patterns.timePatterns.confidence >= (params.confidenceThreshold || 0.5)) {
              totalConfidence += patterns.timePatterns.confidence
              patternCount++
            }
            break
        }
      }

      const overallConfidence = patternCount > 0 ? totalConfidence / patternCount : 0

      return {
        patterns,
        confidence: overallConfidence,
        detectedAt: new Date(),
      }
    } catch {
      // console.error('Error detecting patterns:', error);
      throw new BehaviorAnalysisError('Failed to detect patterns', {
        userId: params.userId,
        error: error.message,
      })
    }
  }

  /**
   * Get engagement metrics for a user
   */
  async getEngagementMetrics(userId: string): Promise<EngagementMetrics> {
    try {
      const events = await this.getEventsForTimeframe(userId, '7d')

      const eventCount = events.length
      const uniqueDays = new Set(events.map(event => event.timestamp.toDateString())).size

      const uniquePages = new Set(events.filter(event => event.pageUrl).map(event => event.pageUrl))
        .size

      // Calculate engagement score
      let score = 0
      score += Math.min(eventCount / 50, 1) * 40 // Activity frequency
      score += Math.min(uniqueDays / 7, 1) * 30 // Consistency
      score += Math.min(uniquePages / 10, 1) * 30 // Exploration

      // Determine engagement level
      let level: 'low' | 'medium' | 'high'
      if (score >= 70) level = 'high'
      else if (score >= 40) level = 'medium'
      else level = 'low'

      // Identify engagement factors
      const factors: string[] = []
      if (eventCount > 30) factors.push('high_activity')
      if (uniqueDays >= 5) factors.push('consistent_usage')
      if (uniquePages >= 8) factors.push('feature_exploration')
      if (events.some(e => e.type === 'collaboration_start')) factors.push('collaborative')

      return {
        level,
        score: Math.round(score),
        factors,
      }
    } catch {
      // console.error('Error calculating engagement metrics:', error);
      throw new BehaviorAnalysisError('Failed to calculate engagement metrics', {
        userId,
        error: error.message,
      })
    }
  }

  /**
   * Get events for a specific timeframe
   */
  private async getEventsForTimeframe(
    userId: string,
    timeframe: string
  ): Promise<ContextEventData[]> {
    const timeframeMs = this.parseTimeframe(timeframe)
    const startDate = new Date(Date.now() - timeframeMs)

    const events = await prisma.contextEvent.findMany({
      where: {
        userId,
        timestamp: {
          gte: startDate,
        },
      },
      orderBy: { timestamp: 'asc' },
    })

    return events.map(event => ({
      type: event.eventType,
      data: event.eventData as Record<string, any>,
      timestamp: event.timestamp,
      pageUrl: event.pageUrl || undefined,
      sessionId: event.sessionId || undefined,
      durationMs: event.durationMs || undefined,
    }))
  }

  /**
   * Analyze navigation patterns from real events
   */
  private async analyzeNavigationPattern(events: ContextEventData[]): Promise<NavigationPattern> {
    const pageViews = events.filter(e => e.type === 'page_view')
    const pageFrequency = new Map<string, number>()
    const transitions = new Map<string, number>()

    // Count real page visits
    pageViews.forEach(event => {
      const page = event.pageUrl || 'unknown'
      pageFrequency.set(page, (pageFrequency.get(page) || 0) + 1)
    })

    // Analyze real page transitions
    for (let i = 0; i < pageViews.length - 1; i++) {
      const from = pageViews[i].pageUrl || 'unknown'
      const to = pageViews[i + 1].pageUrl || 'unknown'
      const transition = `${from} -> ${to}`
      transitions.set(transition, (transitions.get(transition) || 0) + 1)
    }

    const mostVisitedPages: PageVisit[] = Array.from(pageFrequency.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([page, count]) => ({ page, count }))

    const commonTransitions: PageTransition[] = Array.from(transitions.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([transition, count]) => ({ transition, count }))

    // Calculate confidence based on data volume
    const confidence = Math.min(pageViews.length / 50, 1.0)

    return {
      mostVisitedPages,
      commonTransitions,
      confidence,
    }
  }

  /**
   * Analyze feature usage patterns from real events
   */
  private async analyzeFeatureUsage(events: ContextEventData[]): Promise<FeatureUsagePattern> {
    const featureEvents = events.filter(e => e.type === 'feature_use')
    const featureFrequency = new Map<string, { count: number; lastUsed: Date }>()

    // Count feature usage
    featureEvents.forEach(event => {
      const feature = event.data.feature || 'unknown'
      const existing = featureFrequency.get(feature)
      featureFrequency.set(feature, {
        count: (existing?.count || 0) + 1,
        lastUsed:
          event.timestamp > (existing?.lastUsed || new Date(0))
            ? event.timestamp
            : existing?.lastUsed || event.timestamp,
      })
    })

    const mostUsedFeatures: FeatureUsage[] = Array.from(featureFrequency.entries())
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 10)
      .map(([feature, data]) => ({
        feature,
        count: data.count,
        lastUsed: data.lastUsed,
      }))

    const usageFrequency: Record<string, number> = {}
    featureFrequency.forEach((data, feature) => {
      usageFrequency[feature] = data.count
    })

    // Calculate confidence based on data volume
    const confidence = Math.min(featureEvents.length / 30, 1.0)

    return {
      mostUsedFeatures,
      usageFrequency,
      confidence,
    }
  }

  /**
   * Analyze time-based patterns from real events
   */
  private async analyzeTimePatterns(events: ContextEventData[]): Promise<TimeBasedPattern> {
    const hourCounts = new Array(24).fill(0)
    const dayCounts = new Map<string, number>()
    const sessionDurations: number[] = []

    // Analyze time patterns
    events.forEach(event => {
      const hour = event.timestamp.getHours()
      hourCounts[hour]++

      const day = event.timestamp.toLocaleDateString('en-US', { weekday: 'long' })
      dayCounts.set(day, (dayCounts.get(day) || 0) + 1)

      if (event.durationMs) {
        sessionDurations.push(event.durationMs)
      }
    })

    // Find peak hours (top 3)
    const peakHours = hourCounts
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour)

    // Find active days (days with activity)
    const activeDays = Array.from(dayCounts.keys()).filter(day => dayCounts.get(day)! > 0)

    // Calculate average session duration
    const avgSessionDuration =
      sessionDurations.length > 0
        ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length
        : 0

    // Calculate confidence based on data volume and consistency
    const confidence = Math.min(events.length / 40, 1.0) * Math.min(activeDays.length / 5, 1.0)

    return {
      peakHours,
      activeDays,
      sessionDuration: avgSessionDuration,
      confidence,
    }
  }

  /**
   * Store behavior patterns in database
   */
  private async storeBehaviorPatterns(userId: string, patterns: BehaviorPatterns): Promise<void> {
    // Map object keys to database pattern types
    const patternTypeMap: Record<string, string> = {
      navigation: 'navigation',
      featureUsage: 'feature_usage',
      timePatterns: 'time_based',
    }

    for (const [objectKey, patternData] of Object.entries(patterns)) {
      if (patternData) {
        const patternType = patternTypeMap[objectKey] || objectKey
        await prisma.behavioralPattern.upsert({
          where: {
            userId_patternType: {
              userId,
              patternType,
            },
          },
          update: {
            patternData,
            confidenceScore: patternData.confidence || 0.0,
            lastUpdated: new Date(),
          },
          create: {
            userId,
            patternType,
            patternData,
            confidenceScore: patternData.confidence || 0.0,
          },
        })
      }
    }
  }

  /**
   * Generate recommendations based on patterns
   */
  private async generateRecommendations(patterns: BehaviorPatterns): Promise<string[]> {
    const recommendations: string[] = []

    // Navigation-based recommendations
    if (patterns.navigation && patterns.navigation.confidence > 0.7) {
      const topPage = patterns.navigation.mostVisitedPages[0]
      if (topPage && topPage.count > 10) {
        recommendations.push(
          `You frequently visit ${topPage.page}. Consider bookmarking it for quick access.`
        )
      }
    }

    // Feature usage recommendations
    if (patterns.featureUsage && patterns.featureUsage.confidence > 0.6) {
      const underusedFeatures = Object.entries(patterns.featureUsage.usageFrequency)
        .filter(([, count]) => count < 3)
        .map(([feature]) => feature)

      if (underusedFeatures.length > 0) {
        recommendations.push(
          `Consider exploring these features: ${underusedFeatures.slice(0, 3).join(', ')}`
        )
      }
    }

    // Time-based recommendations
    if (patterns.timePatterns && patterns.timePatterns.confidence > 0.5) {
      if (patterns.timePatterns.peakHours.length > 0) {
        const peakHour = patterns.timePatterns.peakHours[0]
        recommendations.push(
          `You're most active around ${peakHour}:00. Consider scheduling important tasks during this time.`
        )
      }
    }

    return recommendations
  }

  /**
   * Parse timeframe string to milliseconds
   */
  private parseTimeframe(timeframe: string): number {
    const match = timeframe.match(/^(\d+)([hdwmy])$/)
    if (!match) return 7 * 24 * 60 * 60 * 1000 // Default to 7 days

    const [, amount, unit] = match
    const num = parseInt(amount, 10)

    switch (unit) {
      case 'h':
        return num * 60 * 60 * 1000
      case 'd':
        return num * 24 * 60 * 60 * 1000
      case 'w':
        return num * 7 * 24 * 60 * 60 * 1000
      case 'm':
        return num * 30 * 24 * 60 * 60 * 1000
      case 'y':
        return num * 365 * 24 * 60 * 60 * 1000
      default:
        return 7 * 24 * 60 * 60 * 1000
    }
  }
}

// Export singleton instance
export const realBehavioralAnalyticsService = new RealBehavioralAnalyticsService()
