import { auth } from '@/lib/auth/config'
import { redis } from '@/lib/redis'
import { Role, PrismaClient, User as PrismaUser } from '@prisma/client'
import { hash, compare } from 'bcryptjs'
import { signJWT, signRefreshToken } from '@/lib/auth/jwt'
import { v4 as uuidv4 } from 'uuid'

// Device info interface since it doesn't exist in Prisma
interface DeviceInfo {
  userAgent?: string
  ip?: string
  type?: string
  browser?: string
  os?: string
}

// Auth user type based on actual Prisma User model
type AuthUser = PrismaUser & {
  company?: {
    id: string
    name: string
  }
}

// Initialize Prisma client
const prismaClient = new PrismaClient()

// Cache TTL in seconds
const USER_CACHE_TTL = 3600 // 1 hour

/**
 * Get the currently authenticated user with detailed profile information
 * Results are cached in Redis for performance
 */
export async function getCurrentUser() {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return null
    }

    const userId = session.user.id

    // Try to get from cache first
    const cacheKey = `user:profile:${userId}`
    const cachedUser = await redis.get(cacheKey)

    if (cachedUser) {
      return JSON.parse(cachedUser as string)
    }

    // If not in cache, fetch from database
    const user = await prismaClient.user.findUnique({
      where: { id: userId },
      include: {
        company: {
          select: {
            id: true,
            name: true,
            domains: true,
            subscriptionStatus: true,
          },
        },
      },
    })

    if (user) {
      // Cache the result
      await redis.set(cacheKey, JSON.stringify(user), 'EX', USER_CACHE_TTL)
    }

    return user
  } catch {
    // console.error('Error getting current user:', error);
    return null
  }
}

/**
 * Verify if the current user has a specific role
 */
export async function hasRole(role: Role | Role[]) {
  const session = await auth()

  if (!session?.user?.role) {
    return false
  }

  const userRole = session.user.role as Role

  if (Array.isArray(role)) {
    return role.includes(userRole)
  }

  return userRole === role
}

/**
 * Get the company ID for the current user
 */
export async function getCurrentCompanyId() {
  const session = await auth()
  return session?.user?.companyId || null
}

/**
 * Check if the current user is a superadmin
 */
export async function isSuperAdmin() {
  return hasRole(Role.SUPERADMIN)
}

/**
 * Check if a user exists by email
 */
export async function userExists(email: string) {
  const user = await prismaClient.user.findUnique({
    where: { email },
    select: { id: true },
  })

  return !!user
}

/**
 * Invalidate user cache when user data changes
 */
export async function invalidateUserCache(userId: string) {
  try {
    // Clear profile cache
    await redis.del(`user:profile:${userId}`)

    // Get user email to clear context cache as well
    const user = await prismaClient.user.findUnique({
      where: { id: userId },
      select: { email: true },
    })

    if (user?.email) {
      await redis.del(`user:context:${user.email}`)
    }
  } catch {
    // console.error('Error invalidating user cache:', error);
  }
}

/**
 * Context-aware authentication placeholder
 * This function will be expanded in the future to provide context-based authentication
 * using Gen AI for personalized security measures
 */
export async function getContextAwareUser() {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return null
    }

    // In the future, this will include additional context data
    // such as user preferences, recent activity, and behavioral patterns
    // that can be used by Gen AI for personalized security measures

    return {
      ...user,
      contextData: {
        lastActive: new Date().toISOString(),
        // Additional context data will be added here
      },
    }
  } catch {
    // console.error('Error getting context-aware user:', error);
    return null
  }
}

/**
 * Service for authentication-related operations
 */
export class AuthService {
  /**
   * Attempts to log in a user
   */
  async login(
    email: string,
    password: string,
    deviceInfo?: DeviceInfo
  ): Promise<{
    success: boolean
    message: string
    accessToken?: string
    refreshToken?: string
    user?: AuthUser
  }> {
    try {
      // Find user by email
      const user = await prismaClient.user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
          id: true,
          email: true,
          name: true,
          password: true,
          role: true,
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      })

      if (!user) {
        return {
          success: false,
          message: 'Invalid email or password',
        }
      }

      if (!user.password) {
        return {
          success: false,
          message: 'Please use social login or reset your password',
        }
      }

      // Verify password
      const isValidPassword = await compare(password, user.password)
      if (!isValidPassword) {
        return {
          success: false,
          message: 'Invalid email or password',
        }
      }

      // Generate session ID and fingerprint
      const sessionId = uuidv4()
      const fingerprint = deviceInfo?.userAgent
        ? uuidv4().substring(0, 32)
        : uuidv4().substring(0, 32)

      // Create session in database
      await prismaClient.session.create({
        data: {
          id: sessionId,
          sessionToken: uuidv4(),
          userId: user.id,
          expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days
        },
      })

      // Generate JWT tokens
      const accessToken = signJWT(
        {
          userId: user.id,
          email: user.email,
          role: user.role,
          sessionId,
          fingerprint,
          companyId: user.company?.id,
        },
        { expiresIn: '15m' }
      )

      const refreshToken = signRefreshToken(
        {
          sessionId,
          userId: user.id,
          fingerprint,
        },
        { expiresIn: '7d' }
      )

      return {
        success: true,
        message: 'Login successful',
        accessToken,
        refreshToken,
        user: user as AuthUser,
      }
    } catch {
      if (process.env.NODE_ENV === 'development') console.error('Login error:', error)
      return {
        success: false,
        message: 'An error occurred during login',
      }
    }
  }

  /**
   * Registers a new user
   */
  async register(
    email: string,
    password: string,
    name: string
  ): Promise<{
    success: boolean
    message: string
    userId?: string
  }> {
    try {
      // Check if user already exists
      const existingUser = await prismaClient.user.findUnique({
        where: { email: email.toLowerCase() },
      })

      if (existingUser) {
        return {
          success: false,
          message: 'Email already in use',
        }
      }

      // Hash password
      const hashedPassword = await hash(password, 12)

      // Create new user
      const newUser = await prismaClient.user.create({
        data: {
          email: email.toLowerCase(),
          password: hashedPassword,
          name,
          role: Role.EMPLOYEE, // Default role
        },
      })

      return {
        success: true,
        message: 'Registration successful',
        userId: newUser.id,
      }
    } catch {
      // console.error('AuthService register error:', error)
      return {
        success: false,
        message: 'An error occurred during registration',
      }
    }
  }

  /**
   * Logs out a user by invalidating their session
   */
  async logout(sessionId: string): Promise<boolean> {
    try {
      // Since Session model doesn't have isValid or revokedAt fields,
      // we'll delete the session to invalidate it
      await prismaClient.session.delete({
        where: { id: sessionId },
      })

      return true
    } catch {
      // console.error('AuthService logout error:', error)
      return false
    }
  }

  /**
   * Gets a user by their ID
   */
  async getUserById(userId: string): Promise<AuthUser | null> {
    try {
      const user = await prismaClient.user.findUnique({
        where: { id: userId },
        include: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      })

      return user as AuthUser
    } catch {
      // console.error('AuthService getUserById error:', error)
      return null
    }
  }

  /**
   * Verifies if a session is valid
   */
  async verifySession(sessionId: string): Promise<boolean> {
    try {
      const session = await prismaClient.session.findUnique({
        where: { id: sessionId },
      })

      if (!session) {
        return false
      }

      // Check if session exists and is not expired
      return session.expires > new Date()
    } catch {
      // console.error('AuthService verifySession error:', error)
      return false
    }
  }

  /**
   * Rotates the session tokens
   */
  async refreshSession(
    sessionId: string,
    userId: string,
    fingerprint: string
  ): Promise<{
    accessToken?: string
    refreshToken?: string
    error?: string
  }> {
    try {
      // Verify the session is valid
      const sessionValid = await this.verifySession(sessionId)

      if (!sessionValid) {
        return { error: 'Invalid session' }
      }

      // Update session expiry
      await prismaClient.session.update({
        where: { id: sessionId },
        data: {
          expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days
        },
      })

      // Get user data to include in the token
      const user = await this.getUserById(userId)

      if (!user) {
        return { error: 'User not found' }
      }

      // Generate new tokens
      const accessToken = signJWT(
        {
          userId: user.id,
          email: user.email,
          role: user.role,
          sessionId,
          fingerprint,
          companyId: user.company?.id,
        },
        { expiresIn: '15m' }
      )

      const refreshToken = signRefreshToken(
        {
          sessionId,
          userId: user.id,
          fingerprint,
        },
        { expiresIn: '7d' }
      )

      return {
        accessToken,
        refreshToken,
      }
    } catch {
      // console.error('AuthService refreshSession error:', error)
      return { error: 'Failed to refresh session' }
    }
  }
}
