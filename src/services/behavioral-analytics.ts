/**
 * Behavioral Analytics Service
 * Tracks and analyzes user behavior patterns for intelligence layer
 * Part of Phase 3: Intelligence Layer
 */

import { UserBehaviorEvent, BehaviorPattern, AnalyticsReport } from '@/types/intelligence'
import { hasFeature } from '@/lib/feature-flags'
import { logUserAction } from '@/services/context-service'

export interface BehaviorTrackingOptions {
  enableRealTime?: boolean
  batchSize?: number
  flushInterval?: number // in milliseconds
  enablePatternDetection?: boolean
}

export interface BehaviorAnalyticsConfig {
  userId: string
  companyId: string
  sessionId: string
  options?: BehaviorTrackingOptions
}

export interface PatternDetectionResult {
  patterns: BehaviorPattern[]
  confidence: number
  insights: string[]
  recommendations: string[]
}

/**
 * Core Behavioral Analytics Service
 * Tracks user interactions and detects behavioral patterns
 */
export class BehavioralAnalyticsService {
  private static instance: BehavioralAnalyticsService
  private eventQueue: UserBehaviorEvent[] = []
  private patternCache = new Map<string, BehaviorPattern[]>()
  private readonly DEFAULT_BATCH_SIZE = 50
  private readonly DEFAULT_FLUSH_INTERVAL = 30000 // 30 seconds
  private readonly PATTERN_CACHE_TTL = 1000 * 60 * 60 // 1 hour

  static getInstance(): BehavioralAnalyticsService {
    if (!BehavioralAnalyticsService.instance) {
      BehavioralAnalyticsService.instance = new BehavioralAnalyticsService()
    }
    return BehavioralAnalyticsService.instance
  }

  /**
   * Track a user behavior event
   */
  async trackEvent(
    config: BehaviorAnalyticsConfig,
    event: Omit<UserBehaviorEvent, 'id' | 'timestamp' | 'userId' | 'sessionId'>
  ): Promise<void> {
    try {
      // Check if behavioral analytics is enabled
      const analyticsEnabled = await hasFeature(config.companyId, 'behavioralAnalytics')
      if (!analyticsEnabled) {
        return
      }

      const fullEvent: UserBehaviorEvent = {
        id: this.generateEventId(),
        userId: config.userId,
        sessionId: config.sessionId,
        timestamp: new Date(),
        ...event,
      }

      // Add to queue
      this.eventQueue.push(fullEvent)

      // Real-time processing if enabled
      if (config.options?.enableRealTime) {
        await this.processEventRealTime(fullEvent, config)
      }

      // Batch processing
      const batchSize = config.options?.batchSize || this.DEFAULT_BATCH_SIZE
      if (this.eventQueue.length >= batchSize) {
        await this.flushEventQueue(config)
      }

      // Pattern detection if enabled
      if (config.options?.enablePatternDetection) {
        await this.detectPatternsForUser(config.userId, config.companyId)
      }
    } catch {
      // console.error('Error tracking behavior event:', error);
      // Don't throw - tracking failures shouldn't break user experience
    }
  }

  /**
   * Track page visit event
   */
  async trackPageVisit(
    config: BehaviorAnalyticsConfig,
    page: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.trackEvent(config, {
      type: 'page_visit',
      action: 'navigate',
      target: page,
      metadata: {
        page,
        referrer: typeof window !== 'undefined' ? document.referrer : undefined,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
        ...metadata,
      },
    })
  }

  /**
   * Track user interaction event
   */
  async trackInteraction(
    config: BehaviorAnalyticsConfig,
    element: string,
    action: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.trackEvent(config, {
      type: 'interaction',
      action,
      target: element,
      metadata: {
        element,
        timestamp: Date.now(),
        ...metadata,
      },
    })
  }

  /**
   * Track feature usage event
   */
  async trackFeatureUsage(
    config: BehaviorAnalyticsConfig,
    feature: string,
    action: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.trackEvent(config, {
      type: 'feature_usage',
      action,
      target: feature,
      metadata: {
        feature,
        duration: metadata?.duration,
        success: metadata?.success !== false,
        ...metadata,
      },
    })
  }

  /**
   * Track error or issue
   */
  async trackError(
    config: BehaviorAnalyticsConfig,
    error: string,
    context?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.trackEvent(config, {
      type: 'error',
      action: 'error_occurred',
      target: context || 'unknown',
      metadata: {
        error,
        context,
        stack: metadata?.stack,
        ...metadata,
      },
    })
  }

  /**
   * Get behavior patterns for a user
   */
  async getUserPatterns(
    userId: string,
    companyId: string,
    timeRange?: { start: Date; end: Date }
  ): Promise<BehaviorPattern[]> {
    try {
      // Check if behavioral analytics is enabled
      const analyticsEnabled = await hasFeature(companyId, 'behavioralAnalytics')
      if (!analyticsEnabled) {
        return []
      }

      // Check cache first
      const cacheKey = `patterns_${userId}_${timeRange?.start?.getTime()}_${timeRange?.end?.getTime()}`
      const cached = this.patternCache.get(cacheKey)
      if (cached) {
        return cached
      }

      // Detect patterns
      const patterns = await this.detectPatternsForUser(userId, companyId, timeRange)

      // Cache results
      this.patternCache.set(cacheKey, patterns)
      setTimeout(() => this.patternCache.delete(cacheKey), this.PATTERN_CACHE_TTL)

      return patterns
    } catch {
      // console.error('Error getting user patterns:', error);
      return []
    }
  }

  /**
   * Generate analytics report
   */
  async generateAnalyticsReport(
    userId: string,
    companyId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<AnalyticsReport> {
    try {
      // Check if behavioral analytics is enabled
      const analyticsEnabled = await hasFeature(companyId, 'behavioralAnalytics')
      if (!analyticsEnabled) {
        return this.getEmptyReport(userId, timeRange)
      }

      // Get user events for the time range
      const events = await this.getUserEvents(userId, timeRange)
      const patterns = await this.getUserPatterns(userId, companyId, timeRange)

      // Generate insights
      const insights = this.generateInsights(events, patterns)

      // Calculate metrics
      const metrics = this.calculateMetrics(events)

      const report: AnalyticsReport = {
        userId,
        timeRange,
        totalEvents: events.length,
        uniqueSessions: new Set(events.map(e => e.sessionId)).size,
        patterns,
        insights,
        metrics,
        topPages: this.getTopPages(events),
        topFeatures: this.getTopFeatures(events),
        errorRate: this.calculateErrorRate(events),
        engagementScore: this.calculateEngagementScore(events),
        generatedAt: new Date(),
      }

      return report
    } catch {
      // console.error('Error generating analytics report:', error);
      return this.getEmptyReport(userId, timeRange)
    }
  }

  /**
   * Detect behavioral patterns for a user
   */
  private async detectPatternsForUser(
    userId: string,
    companyId: string,
    timeRange?: { start: Date; end: Date }
  ): Promise<BehaviorPattern[]> {
    const patterns: BehaviorPattern[] = []

    try {
      // Get user events
      const events = await this.getUserEvents(userId, timeRange)
      if (events.length < 10) {
        return patterns // Need minimum events for pattern detection
      }

      // Detect navigation patterns
      patterns.push(...this.detectNavigationPatterns(events))

      // Detect usage patterns
      patterns.push(...this.detectUsagePatterns(events))

      // Detect time-based patterns
      patterns.push(...this.detectTimePatterns(events))

      // Detect error patterns
      patterns.push(...this.detectErrorPatterns(events))

      return patterns
    } catch {
      // console.error('Error detecting patterns:', error);
      return patterns
    }
  }

  /**
   * Detect navigation patterns
   */
  private detectNavigationPatterns(events: UserBehaviorEvent[]): BehaviorPattern[] {
    const patterns: BehaviorPattern[] = []
    const pageVisits = events.filter(e => e.type === 'page_visit')

    if (pageVisits.length < 3) return patterns

    // Find common navigation sequences
    const sequences = this.findNavigationSequences(pageVisits)

    sequences.forEach((sequence, index) => {
      if (sequence.frequency > 2) {
        patterns.push({
          id: `nav_pattern_${index}`,
          type: 'navigation',
          description: `User frequently navigates: ${sequence.path.join(' → ')}`,
          frequency: sequence.frequency,
          confidence: Math.min(0.95, (sequence.frequency / pageVisits.length) * 2),
          metadata: {
            sequence: sequence.path,
            averageTime: sequence.averageTime,
          },
        })
      }
    })

    return patterns
  }

  /**
   * Detect feature usage patterns
   */
  private detectUsagePatterns(events: UserBehaviorEvent[]): BehaviorPattern[] {
    const patterns: BehaviorPattern[] = []
    const featureEvents = events.filter(e => e.type === 'feature_usage')

    if (featureEvents.length < 5) return patterns

    // Group by feature
    const featureUsage = new Map<string, UserBehaviorEvent[]>()
    featureEvents.forEach(event => {
      const feature = event.target
      if (!featureUsage.has(feature)) {
        featureUsage.set(feature, [])
      }
      featureUsage.get(feature)!.push(event)
    })

    // Analyze each feature
    featureUsage.forEach((usage, feature) => {
      if (usage.length >= 3) {
        const avgDuration =
          usage
            .filter(e => e.metadata?.duration)
            .reduce((sum, e) => sum + (e.metadata?.duration || 0), 0) / usage.length

        patterns.push({
          id: `usage_pattern_${feature}`,
          type: 'feature_usage',
          description: `Regular user of ${feature} (${usage.length} times)`,
          frequency: usage.length,
          confidence: Math.min(0.9, (usage.length / featureEvents.length) * 3),
          metadata: {
            feature,
            averageDuration: avgDuration,
            successRate: usage.filter(e => e.metadata?.success !== false).length / usage.length,
          },
        })
      }
    })

    return patterns
  }

  /**
   * Detect time-based patterns
   */
  private detectTimePatterns(events: UserBehaviorEvent[]): BehaviorPattern[] {
    const patterns: BehaviorPattern[] = []

    // Group events by hour of day
    const hourlyActivity = new Array(24).fill(0)
    events.forEach(event => {
      const hour = event.timestamp.getHours()
      hourlyActivity[hour]++
    })

    // Find peak activity hours
    const maxActivity = Math.max(...hourlyActivity)
    const peakHours = hourlyActivity
      .map((activity, hour) => ({ hour, activity }))
      .filter(({ activity }) => activity > maxActivity * 0.7)
      .map(({ hour }) => hour)

    if (peakHours.length > 0) {
      patterns.push({
        id: 'time_pattern_peak_hours',
        type: 'temporal',
        description: `Most active during hours: ${peakHours.join(', ')}`,
        frequency: peakHours.reduce((sum, hour) => sum + hourlyActivity[hour], 0),
        confidence: 0.8,
        metadata: {
          peakHours,
          hourlyDistribution: hourlyActivity,
        },
      })
    }

    return patterns
  }

  /**
   * Detect error patterns
   */
  private detectErrorPatterns(events: UserBehaviorEvent[]): BehaviorPattern[] {
    const patterns: BehaviorPattern[] = []
    const errorEvents = events.filter(e => e.type === 'error')

    if (errorEvents.length === 0) return patterns

    // Group errors by type/context
    const errorGroups = new Map<string, UserBehaviorEvent[]>()
    errorEvents.forEach(event => {
      const key = event.metadata?.context || event.target
      if (!errorGroups.has(key)) {
        errorGroups.set(key, [])
      }
      errorGroups.get(key)!.push(event)
    })

    // Analyze error patterns
    errorGroups.forEach((errors, context) => {
      if (errors.length >= 2) {
        patterns.push({
          id: `error_pattern_${context}`,
          type: 'error',
          description: `Recurring errors in ${context} (${errors.length} times)`,
          frequency: errors.length,
          confidence: 0.9,
          metadata: {
            context,
            errorTypes: [...new Set(errors.map(e => e.metadata?.error))],
            lastOccurrence: Math.max(...errors.map(e => e.timestamp.getTime())),
          },
        })
      }
    })

    return patterns
  }

  /**
   * Helper methods
   */
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async processEventRealTime(
    event: UserBehaviorEvent,
    config: BehaviorAnalyticsConfig
  ): Promise<void> {
    // Real-time processing logic
    // console.log('Processing real-time event:', event);

    // Log user action for context building
    await logUserAction(config.userId, event.action, event.type, event.target)
  }

  private async flushEventQueue(config: BehaviorAnalyticsConfig): Promise<void> {
    if (this.eventQueue.length === 0) return

    try {
      // In production, this would batch insert to database
      // console.log(`Flushing ${this.eventQueue.length} events to storage`);

      // Clear the queue
      this.eventQueue = []
    } catch {
      // console.error('Error flushing event queue:', error);
    }
  }

  private async getUserEvents(
    userId: string,
    timeRange?: { start: Date; end: Date }
  ): Promise<UserBehaviorEvent[]> {
    // In production, this would query the database
    // For now, return mock events for demonstration
    return this.generateMockEvents(userId, timeRange)
  }

  private generateMockEvents(
    userId: string,
    timeRange?: { start: Date; end: Date }
  ): UserBehaviorEvent[] {
    const events: UserBehaviorEvent[] = []
    const now = new Date()
    const start = timeRange?.start || new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const end = timeRange?.end || now

    // Generate sample events
    const pages = ['/dashboard', '/settings', '/profile', '/reports']
    const features = ['theme_switch', 'export_data', 'user_search', 'notification_settings']

    for (let i = 0; i < 50; i++) {
      const timestamp = new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime())
      )

      events.push({
        id: `mock_event_${i}`,
        userId,
        sessionId: `session_${Math.floor(i / 10)}`,
        timestamp,
        type: Math.random() > 0.7 ? 'feature_usage' : 'page_visit',
        action: Math.random() > 0.5 ? 'click' : 'navigate',
        target:
          Math.random() > 0.5
            ? pages[Math.floor(Math.random() * pages.length)]
            : features[Math.floor(Math.random() * features.length)],
        metadata: {
          duration: Math.random() * 30000,
          success: Math.random() > 0.1,
        },
      })
    }

    return events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
  }

  private findNavigationSequences(pageVisits: UserBehaviorEvent[]): Array<{
    path: string[]
    frequency: number
    averageTime: number
  }> {
    const sequences = new Map<string, { count: number; times: number[] }>()

    // Look for sequences of 2-3 pages
    for (let i = 0; i < pageVisits.length - 1; i++) {
      const current = pageVisits[i].target
      const next = pageVisits[i + 1].target
      const timeDiff = pageVisits[i + 1].timestamp.getTime() - pageVisits[i].timestamp.getTime()

      if (timeDiff < 300000) {
        // Within 5 minutes
        const key = `${current}→${next}`
        if (!sequences.has(key)) {
          sequences.set(key, { count: 0, times: [] })
        }
        const seq = sequences.get(key)!
        seq.count++
        seq.times.push(timeDiff)
      }
    }

    return Array.from(sequences.entries()).map(([path, data]) => ({
      path: path.split('→'),
      frequency: data.count,
      averageTime: data.times.reduce((sum, time) => sum + time, 0) / data.times.length,
    }))
  }

  private generateInsights(events: UserBehaviorEvent[], patterns: BehaviorPattern[]): string[] {
    const insights: string[] = []

    // Activity level insights
    if (events.length > 100) {
      insights.push('High activity user - very engaged with the platform')
    } else if (events.length > 50) {
      insights.push('Moderate activity user - regular platform usage')
    } else {
      insights.push('Low activity user - may benefit from onboarding assistance')
    }

    // Pattern-based insights
    const navPatterns = patterns.filter(p => p.type === 'navigation')
    if (navPatterns.length > 0) {
      insights.push('Shows consistent navigation patterns - predictable user behavior')
    }

    const errorPatterns = patterns.filter(p => p.type === 'error')
    if (errorPatterns.length > 0) {
      insights.push('Experiencing recurring issues - may need support intervention')
    }

    return insights
  }

  private calculateMetrics(events: UserBehaviorEvent[]): Record<string, number> {
    const totalEvents = events.length
    const uniqueSessions = new Set(events.map(e => e.sessionId)).size
    const pageViews = events.filter(e => e.type === 'page_visit').length
    const interactions = events.filter(e => e.type === 'interaction').length
    const errors = events.filter(e => e.type === 'error').length

    return {
      totalEvents,
      uniqueSessions,
      pageViews,
      interactions,
      errors,
      avgEventsPerSession: totalEvents / uniqueSessions,
      interactionRate: interactions / totalEvents,
      errorRate: errors / totalEvents,
    }
  }

  private getTopPages(events: UserBehaviorEvent[]): Array<{ page: string; visits: number }> {
    const pageVisits = events.filter(e => e.type === 'page_visit')
    const pageCounts = new Map<string, number>()

    pageVisits.forEach(event => {
      const page = event.target
      pageCounts.set(page, (pageCounts.get(page) || 0) + 1)
    })

    return Array.from(pageCounts.entries())
      .map(([page, visits]) => ({ page, visits }))
      .sort((a, b) => b.visits - a.visits)
      .slice(0, 10)
  }

  private getTopFeatures(events: UserBehaviorEvent[]): Array<{ feature: string; usage: number }> {
    const featureEvents = events.filter(e => e.type === 'feature_usage')
    const featureCounts = new Map<string, number>()

    featureEvents.forEach(event => {
      const feature = event.target
      featureCounts.set(feature, (featureCounts.get(feature) || 0) + 1)
    })

    return Array.from(featureCounts.entries())
      .map(([feature, usage]) => ({ feature, usage }))
      .sort((a, b) => b.usage - a.usage)
      .slice(0, 10)
  }

  private calculateErrorRate(events: UserBehaviorEvent[]): number {
    const totalEvents = events.length
    const errorEvents = events.filter(e => e.type === 'error').length
    return totalEvents > 0 ? errorEvents / totalEvents : 0
  }

  private calculateEngagementScore(events: UserBehaviorEvent[]): number {
    // Simple engagement score based on activity diversity and frequency
    const uniqueTypes = new Set(events.map(e => e.type)).size
    const uniqueTargets = new Set(events.map(e => e.target)).size
    const totalEvents = events.length

    // Normalize to 0-1 scale
    const diversityScore = Math.min(1, (uniqueTypes * uniqueTargets) / 20)
    const activityScore = Math.min(1, totalEvents / 100)

    return (diversityScore + activityScore) / 2
  }

  private getEmptyReport(userId: string, timeRange: { start: Date; end: Date }): AnalyticsReport {
    return {
      userId,
      timeRange,
      totalEvents: 0,
      uniqueSessions: 0,
      patterns: [],
      insights: ['Analytics not available - feature may be disabled'],
      metrics: {},
      topPages: [],
      topFeatures: [],
      errorRate: 0,
      engagementScore: 0,
      generatedAt: new Date(),
    }
  }
}

// Export singleton instance
export const behavioralAnalyticsService = BehavioralAnalyticsService.getInstance()

// Export utility functions
export async function trackUserBehavior(
  config: BehaviorAnalyticsConfig,
  _event: Omit<UserBehaviorEvent, 'id' | 'timestamp' | 'userId' | 'sessionId'>
): Promise<void> {
  return behavioralAnalyticsService.trackEvent(config, event)
}

export async function getUserBehaviorPatterns(
  userId: string,
  companyId: string,
  timeRange?: { start: Date; end: Date }
): Promise<BehaviorPattern[]> {
  return behavioralAnalyticsService.getUserPatterns(userId, companyId, timeRange)
}

export async function generateUserAnalyticsReport(
  userId: string,
  companyId: string,
  timeRange: { start: Date; end: Date }
): Promise<AnalyticsReport> {
  return behavioralAnalyticsService.generateAnalyticsReport(userId, companyId, timeRange)
}
