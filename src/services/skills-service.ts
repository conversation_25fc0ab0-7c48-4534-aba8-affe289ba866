import { Skill, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@prisma/client'
import { prisma } from '@/lib/prisma'

/**
 * Input types for skill operations
 */
export interface CreateSkillInput {
  name: string
  description?: string
  category: SkillCategory
  currentLevel: SkillLevel
  targetLevel?: SkillLevel
  importance: number
  tags?: string[]
  userId: string
  companyId: string
}

export interface UpdateSkillInput {
  name?: string
  description?: string
  category?: SkillCategory
  currentLevel?: SkillLevel
  targetLevel?: SkillLevel
  importance?: number
  tags?: string[]
  lastAssessed?: Date
}

export interface CreateSkillAssessmentInput {
  skillId: string
  score: number
  level: SkillLevel
  notes?: string
  userId: string
}

export interface SkillFilters {
  category?: SkillCategory[]
  level?: SkillLevel[]
  importance?: number[]
}

/**
 * Skills Service - Business logic for skills management
 * Following TDD approach with real API calls and database operations
 */
export class SkillsService {
  /**
   * Create a new skill
   */
  static async createSkill(input: CreateSkillInput): Promise<Skill> {
    // Check for duplicate skill names for this user
    const existingSkill = await prisma.skill.findFirst({
      where: {
        name: input.name,
        userId: input.userId,
        companyId: input.companyId,
      },
    })

    if (existingSkill) {
      throw new Error('A skill with this name already exists')
    }

    const skill = await prisma.skill.create({
      data: {
        name: input.name,
        description: input.description,
        category: input.category,
        currentLevel: input.currentLevel,
        targetLevel: input.targetLevel,
        importance: input.importance,
        tags: input.tags || [],
        userId: input.userId,
        companyId: input.companyId,
      },
      include: {
        assessments: {
          orderBy: { assessmentDate: 'desc' },
          take: 5,
        },
      },
    })

    // Create initial assessment
    if (input.currentLevel) {
      await this.createSkillAssessment({
        skillId: skill.id,
        score: this.levelToScore(input.currentLevel),
        level: input.currentLevel,
        notes: 'Initial assessment',
        userId: input.userId,
      })
    }

    return skill
  }

  /**
   * Get skills for a user with filtering
   */
  static async getUserSkills(
    userId: string,
    companyId: string,
    filters?: SkillFilters
  ): Promise<Skill[]> {
    const where: any = {
      userId,
      companyId,
    }

    if (filters?.category?.length) {
      where.category = { in: filters.category }
    }

    if (filters?.level?.length) {
      where.currentLevel = { in: filters.level }
    }

    if (filters?.importance?.length) {
      where.importance = { in: filters.importance }
    }

    const skills = await prisma.skill.findMany({
      where,
      include: {
        assessments: {
          orderBy: { assessmentDate: 'desc' },
          take: 5,
        },
      },
      orderBy: [{ importance: 'desc' }, { name: 'asc' }],
    })

    return skills
  }

  /**
   * Get a specific skill by ID
   */
  static async getSkillById(
    skillId: string,
    userId: string,
    companyId: string
  ): Promise<Skill | null> {
    const skill = await prisma.skill.findFirst({
      where: {
        id: skillId,
        userId,
        companyId,
      },
      include: {
        assessments: {
          orderBy: { assessmentDate: 'desc' },
        },
      },
    })

    return skill
  }

  /**
   * Update an existing skill
   */
  static async updateSkill(
    skillId: string,
    userId: string,
    companyId: string,
    input: UpdateSkillInput
  ): Promise<Skill> {
    // Verify skill ownership
    const existingSkill = await prisma.skill.findFirst({
      where: {
        id: skillId,
        userId,
        companyId,
      },
    })

    if (!existingSkill) {
      throw new Error('Skill not found or access denied')
    }

    // Check for duplicate names if name is being updated
    if (input.name && input.name !== existingSkill.name) {
      const duplicateSkill = await prisma.skill.findFirst({
        where: {
          name: input.name,
          userId,
          companyId,
          id: { not: skillId },
        },
      })

      if (duplicateSkill) {
        throw new Error('A skill with this name already exists')
      }
    }

    const updatedSkill = await prisma.skill.update({
      where: { id: skillId },
      data: {
        ...input,
        lastAssessed: input.lastAssessed || new Date(),
      },
      include: {
        assessments: {
          orderBy: { assessmentDate: 'desc' },
          take: 5,
        },
      },
    })

    return updatedSkill
  }

  /**
   * Delete a skill
   */
  static async deleteSkill(skillId: string, userId: string, companyId: string): Promise<void> {
    // Verify skill ownership
    const existingSkill = await prisma.skill.findFirst({
      where: {
        id: skillId,
        userId,
        companyId,
      },
    })

    if (!existingSkill) {
      throw new Error('Skill not found or access denied')
    }

    // Delete assessments first (cascade should handle this, but explicit for clarity)
    await prisma.skillAssessment.deleteMany({
      where: { skillId },
    })

    await prisma.skill.delete({
      where: { id: skillId },
    })
  }

  /**
   * Create a skill assessment
   */
  static async createSkillAssessment(input: CreateSkillAssessmentInput): Promise<SkillAssessment> {
    const assessment = await prisma.skillAssessment.create({
      data: {
        skillId: input.skillId,
        score: input.score,
        level: input.level,
        notes: input.notes,
        userId: input.userId,
        assessmentDate: new Date(),
      },
    })

    // Update skill's current level based on latest assessment
    await prisma.skill.update({
      where: { id: input.skillId },
      data: {
        currentLevel: input.level,
        lastAssessed: new Date(),
      },
    })

    return assessment
  }

  /**
   * Get skills statistics
   */
  static async getSkillStatistics(userId: string, companyId: string) {
    const skills = await prisma.skill.findMany({
      where: { userId, companyId },
      include: { assessments: true },
    })

    const stats = {
      total: skills.length,
      beginner: skills.filter(s => s.currentLevel === 'BEGINNER').length,
      intermediate: skills.filter(s => s.currentLevel === 'INTERMEDIATE').length,
      advanced: skills.filter(s => s.currentLevel === 'ADVANCED').length,
      expert: skills.filter(s => s.currentLevel === 'EXPERT').length,
      needsAssessment: skills.filter(s => !s.lastAssessed || this.needsReassessment(s)).length,
      byCategory: {} as Record<string, number>,
      averageImportance:
        skills.length > 0
          ? Math.round(skills.reduce((sum, s) => sum + s.importance, 0) / skills.length)
          : 0,
    }

    skills.forEach(skill => {
      stats.byCategory[skill.category] = (stats.byCategory[skill.category] || 0) + 1
    })

    return stats
  }

  /**
   * Get skills that need development (have target levels)
   */
  static async getSkillsNeedingDevelopment(userId: string, companyId: string): Promise<Skill[]> {
    const skills = await prisma.skill.findMany({
      where: {
        userId,
        companyId,
        targetLevel: { not: null },
      },
      include: {
        assessments: {
          orderBy: { assessmentDate: 'desc' },
          take: 3,
        },
      },
      orderBy: [{ importance: 'desc' }],
    })

    // Filter to only include skills where current level is below target level
    return skills.filter(skill => {
      if (!skill.targetLevel) return false
      return this.levelToScore(skill.currentLevel) < this.levelToScore(skill.targetLevel)
    })
  }

  /**
   * Helper: Convert skill level to numeric score for comparison
   */
  private static levelToScore(level: SkillLevel): number {
    switch (level) {
      case 'BEGINNER':
        return 1
      case 'INTERMEDIATE':
        return 2
      case 'ADVANCED':
        return 3
      case 'EXPERT':
        return 4
      default:
        return 0
    }
  }

  /**
   * Helper: Check if skill needs reassessment
   */
  private static needsReassessment(skill: Skill): boolean {
    if (!skill.lastAssessed) return true

    const daysSinceAssessment = Math.floor(
      (Date.now() - skill.lastAssessed.getTime()) / (1000 * 60 * 60 * 24)
    )

    // Suggest reassessment every 90 days
    return daysSinceAssessment > 90
  }
}
