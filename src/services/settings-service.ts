import { auth } from '@/lib/auth/config'
import { PrismaClient } from '@prisma/client'
import { Redis } from 'ioredis'
import { cache } from 'react'
import { z } from 'zod'

// Types
export interface UserSettings {
  id: string
  userId: string
  preferences: {
    theme: string
    language: string
    timezone: string
    notifications: {
      email: boolean
      inApp: boolean
      weeklyDigest: boolean
    }
  }
  notifications: {
    system: boolean
    messages: boolean
    email: boolean
    events: boolean
    push: boolean
    sound: boolean
    doNotDisturb: boolean
    emailDigest: string
    quietHours: {
      enabled: boolean
      start: string
      end: string
    }
  }
  updatedAt: Date
}

export interface CompanySettings {
  id: string
  companyId: string
  name: string
  logo: string
  primaryColor: string
  secondaryColor: string
  maxUsers: number
  defaultLocale: string
  supportEmail: string
  adminEmails: string[]
  allowedEmailDomains: string[]
  isActive: boolean
  updatedAt: Date
}

export interface UserSetting {
  id: string
  userId: string
  section: string
  key: string
  value: unknown
  updatedAt: Date
}

// Validation schemas
const appearanceSettingsSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).optional(),
  fontSize: z.number().min(12).max(24).optional(),
  compactMode: z.boolean().optional(),
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  accentColor: z.string().optional(),
  borderRadius: z.enum(['none', 'small', 'medium', 'large', 'xl']).optional(),
  animationSpeed: z.enum(['none', 'slow', 'normal', 'fast']).optional(),
})

const notificationSettingsSchema = z.object({
  email: z.boolean().optional(),
  push: z.boolean().optional(),
  desktop: z.boolean().optional(),
  frequency: z.enum(['realtime', 'daily', 'weekly']).optional(),
})

const dashboardSettingsSchema = z.object({
  layout: z.string().optional(),
  showQuickActions: z.boolean().optional(),
  showRecentActivity: z.boolean().optional(),
  showAnalyticsSummary: z.boolean().optional(),
  showTaskList: z.boolean().optional(),
  chartType: z.string().optional(),
  showDataLabels: z.boolean().optional(),
  interactiveCharts: z.boolean().optional(),
  colorPalette: z.string().optional(),
  dateFormat: z.string().optional(),
  timeFormat: z.string().optional(),
  showRelativeDates: z.boolean().optional(),
  customMetrics: z.array(z.string()).optional(),
})

const _userSettingsSchema = z.object({
  appearance: appearanceSettingsSchema.optional(),
  notifications: notificationSettingsSchema.optional(),
  dashboard: dashboardSettingsSchema.optional(),
  language: z.string().optional(),
  timezone: z.string().optional(),
})

export const companySettingsSchema = z.object({
  name: z.string().min(2),
  logo: z.string().url().optional(),
  primaryColor: z.string(),
  secondaryColor: z.string(),
  maxUsers: z.number().int().positive(),
  defaultLocale: z.string(),
  supportEmail: z.string().email(),
  adminEmails: z.array(z.string().email()),
  allowedEmailDomains: z.array(z.string()),
  isActive: z.boolean(),
})

export class SettingsService {
  private readonly CACHE_TTL = 1800 // 30 minutes
  private readonly CACHE_PREFIX = 'settings:'

  constructor(
    private prisma: PrismaClient,
    private redis: Redis
  ) {}

  /**
   * Get user settings with caching
   */
  async getUserSettings(userId: string, section?: string): Promise<UserSetting[]> {
    const cacheKey = `settings:${userId}${section ? `:${section}` : ''}`

    try {
      // Try to get from cache first
      const cachedSettings = await this.redis.get(cacheKey)
      if (cachedSettings) {
        return JSON.parse(cachedSettings)
      }

      // If not in cache, fetch from database
      const where = section ? { userId, section } : { userId }

      const settings = await this.prisma.userSetting.findMany({
        where,
        orderBy: { updatedAt: 'desc' },
      })

      // Cache the results for 1 hour (3600 seconds)
      if (settings && settings.length > 0) {
        await this.redis.set(cacheKey, JSON.stringify(settings), 'EX', 3600)
      }

      return settings
    } catch {
      // console.error("Failed to fetch user settings:", error);
      return []
    }
  }

  /**
   * Update user settings
   */
  async updateUserSetting(
    userId: string,
    section: string,
    key: string,
    value: unknown
  ): Promise<UserSetting | null> {
    try {
      // Handle theme-related settings by updating User model directly
      if (section === 'appearance' && (key === 'theme' || key === 'colorScheme')) {
        const updateData: any = {}

        if (key === 'theme') {
          updateData.themeMode = value
        } else if (key === 'colorScheme') {
          updateData.colorScheme = value
        }

        // Update the User model directly for theme settings
        await this.prisma.user.update({
          where: { id: userId },
          data: updateData,
        })

        // Return a mock UserSetting for consistency
        return {
          id: `user-${userId}-${section}-${key}`,
          userId,
          section,
          key,
          value,
          updatedAt: new Date(),
        }
      }

      // For other settings, use the UserSetting table
      // Find existing setting or create new one
      const existingSetting = await this.prisma.userSetting.findFirst({
        where: { userId, section, key },
      })

      let updatedSetting

      if (existingSetting) {
        // Update existing setting
        updatedSetting = await this.prisma.userSetting.update({
          where: { id: existingSetting.id },
          data: { value, updatedAt: new Date() },
        })
      } else {
        // Create new setting
        updatedSetting = await this.prisma.userSetting.create({
          data: { userId, section, key, value },
        })
      }

      // Invalidate cache for both specific section and all user settings
      await this.redis.del(`settings:${userId}:${section}`)
      await this.redis.del(`settings:${userId}`)

      return updatedSetting
    } catch (error) {
      console.error('Failed to update user setting:', error)
      return null
    }
  }

  /**
   * Delete user settings
   */
  async deleteUserSetting(userId: string, section: string, key: string): Promise<boolean> {
    try {
      const existingSetting = await this.prisma.userSetting.findFirst({
        where: { userId, section, key },
      })

      if (!existingSetting) {
        return false
      }

      await this.prisma.userSetting.delete({
        where: { id: existingSetting.id },
      })

      // Invalidate cache for both specific section and all user settings
      await this.redis.del(`settings:${userId}:${section}`)
      await this.redis.del(`settings:${userId}`)

      return true
    } catch {
      // console.error("Failed to delete user setting:", error);
      return false
    }
  }

  /**
   * Get company settings with caching
   */
  async getCompanySettings(companyId: string) {
    const cacheKey = `${this.CACHE_PREFIX}company:${companyId}`

    // Try to get from cache first
    const cached = await this.redis.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    // Get from database
    const settings = await this.prisma.companySettings.findUnique({
      where: { companyId },
    })

    if (settings) {
      // Cache the settings
      await this.redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(settings))
    }

    return settings
  }

  /**
   * Update company settings
   */
  async updateCompanySettings(companyId: string, settings: unknown) {
    // Update in database
    const updated = await this.prisma.companySettings.upsert({
      where: { companyId },
      create: {
        companyId,
        ...settings,
      },
      update: settings,
    })

    // Invalidate cache
    await this.redis.del(`${this.CACHE_PREFIX}company:${companyId}`)

    return updated
  }

  /**
   * Get recently changed settings
   */
  async getRecentlyChangedSettings(userId: string, limit = 5) {
    return this.prisma.settingsChangeLog.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    })
  }

  /**
   * Log settings change
   */
  async logSettingsChange(
    userId: string,
    change: {
      type: 'user' | 'company'
      field: string
      oldValue: unknown
      newValue: unknown
    }
  ) {
    return this.prisma.settingsChangeLog.create({
      data: {
        userId,
        changeType: change.type,
        field: change.field,
        oldValue: JSON.stringify(change.oldValue),
        newValue: JSON.stringify(change.newValue),
      },
    })
  }

  // Context-aware settings suggestions
  async getSettingsSuggestions(userId: string): Promise<any[]> {
    const cacheKey = `settings:suggestions:${userId}`

    // Try cache first
    const cachedSuggestions = await this.redis.get(cacheKey)
    if (cachedSuggestions) {
      return JSON.parse(cachedSuggestions)
    }

    // Get user activity and preferences
    const userActivity = await this.prisma.userActivity.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 100,
    })

    // Generate suggestions based on user activity
    const suggestions = this.generateSuggestions(userActivity)

    // Cache for 1 hour
    await this.redis.set(cacheKey, JSON.stringify(suggestions), 'EX', 3600)

    return suggestions
  }

  private generateSuggestions(userActivity: unknown[]): unknown[] {
    // Placeholder for future Gen AI integration
    return []
  }
}

// Create a singleton instance
export const settingsService = new SettingsService(new PrismaClient(), new Redis())

// Cached version of getUserSettings for use in Server Components
export const getCachedUserSettings = cache(async (section?: string) => {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return []
    }

    return settingsService.getUserSettings(session.user.id, section)
  } catch {
    // console.error("Failed to fetch cached user settings:", error);
    return []
  }
})
