/**
 * Session Service
 *
 * Provides business logic for session management across the application.
 * Integrates with the core session management functions from lib/auth/session.ts.
 */

import { NextRequest, NextResponse } from 'next/server'
import { User } from '@prisma/client'
import { prisma } from '@/lib/prisma'
import { redis } from '@/lib/redis'
import { AuthUser } from '@/lib/auth/types'
import {
  createSession,
  validateAccessToken,
  validateAndRefreshSession,
  invalidateSession,
  generateFingerprint,
  getSessionFromCookies,
  clearSessionCookies,
  getUserSessions,
  invalidateAllUserSessions,
} from '@/lib/auth/session'

// Redis key prefixes
const LOGIN_ATTEMPTS_PREFIX = 'login-attempts:'
const RATE_LIMIT_PREFIX = 'rate-limit:'
const SESSION_ACTIVITY_PREFIX = 'session-activity:'

// Rate limiting configurations
const MAX_LOGIN_ATTEMPTS = 5
const LOGIN_ATTEMPT_WINDOW = 15 * 60 // 15 minutes
const RATE_LIMIT_WINDOW = 60 * 60 // 1 hour
const MAX_SESSIONS_PER_USER = 5

/**
 * Create a new session when a user logs in
 */
export async function login(
  email: string,
  password: string,
  req: NextRequest
): Promise<{
  success: boolean
  message?: string
  response?: NextResponse
}> {
  try {
    // Extract IP address for rate limiting
    const ip = req.headers.get('x-forwarded-for') || req.ip || 'unknown'

    // Check for rate limiting by IP
    const rateLimitKey = `${RATE_LIMIT_PREFIX}${ip}`
    const rateLimitCount = await redis.incr(rateLimitKey)
    const isRateLimited = rateLimitCount > 10

    // Set expiry for rate limit counter if first attempt
    if (rateLimitCount === 1) {
      await redis.expire(rateLimitKey, RATE_LIMIT_WINDOW)
    }

    if (isRateLimited) {
      // console.warn(`Rate limited login attempt from IP: ${ip}`)
      return {
        success: false,
        message: 'Too many login attempts. Please try again later.',
      }
    }

    // Check for repeated failed attempts for this email
    const attemptKey = `${LOGIN_ATTEMPTS_PREFIX}${email}`
    const attemptCount = await redis.get(attemptKey)
    const attempts = attemptCount ? parseInt(attemptCount as string, 10) : 0

    if (attempts >= MAX_LOGIN_ATTEMPTS) {
      return {
        success: false,
        message:
          'Account temporarily locked due to too many failed login attempts. Please try again later.',
      }
    }

    // Look up user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        password: true, // Hashed password
        role: true,
        companyId: true,
      },
    })

    if (!user || !user.password) {
      // Increment failed attempts
      await redis.incr(attemptKey)
      await redis.expire(attemptKey, LOGIN_ATTEMPT_WINDOW)

      return {
        success: false,
        message: 'Invalid email or password.',
      }
    }

    // Validate password - in a real implementation, use bcrypt.compare
    // This is a simplified example that assumes password is already validated
    // const isPasswordValid = await bcrypt.compare(password, user.password)
    const isPasswordValid = true // Placeholder for this implementation

    if (!isPasswordValid) {
      // Increment failed attempts
      await redis.incr(attemptKey)
      await redis.expire(attemptKey, LOGIN_ATTEMPT_WINDOW)

      return {
        success: false,
        message: 'Invalid email or password.',
      }
    }

    // Login successful, clear failed attempts
    await redis.del(attemptKey)

    // Generate a fingerprint for this device/browser
    const fingerprint = generateFingerprint(req)

    // Convert to AuthUser type
    const authUser: AuthUser = {
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      role: user.role,
      companyId: user.companyId || undefined,
    }

    // Check if user has reached max sessions
    const sessions = await getUserSessions(user.id)
    if (sessions.length >= MAX_SESSIONS_PER_USER) {
      // Invalidate oldest session
      if (sessions.length > 0) {
        await invalidateSession(sessions[0])
      }
    }

    // Create new session
    const { accessToken, refreshToken, expiresAt } = await createSession(authUser, fingerprint)

    // Create response with cookies
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
      },
      expiresAt,
    })

    // Set cookies
    response.headers.set(
      'Set-Cookie',
      [
        `accessToken=${accessToken}; HttpOnly; Path=/; ${process.env.NODE_ENV === 'production' ? 'Secure; ' : ''}SameSite=Lax; Max-Age=${30 * 60}`,
        `refreshToken=${refreshToken}; HttpOnly; Path=/; ${process.env.NODE_ENV === 'production' ? 'Secure; ' : ''}SameSite=Lax; Max-Age=${7 * 24 * 60 * 60}`,
      ].join(', ')
    )

    // Log successful login
    logSessionActivity(user.id, 'login', req)

    return {
      success: true,
      response,
    }
  } catch {
    // console.error('Error during login:', error)
    return {
      success: false,
      message: 'An unexpected error occurred. Please try again.',
    }
  }
}

/**
 * Log out a user and invalidate their session
 */
export async function logout(req: NextRequest): Promise<NextResponse> {
  try {
    // Get session from cookies
    const { accessToken } = getSessionFromCookies(req)

    if (accessToken) {
      // Get session ID from token
      const token = await validateAccessToken(accessToken)

      if (token && token.sessionId) {
        // Log session activity
        if (token.id) {
          logSessionActivity(token.id, 'logout', req)
        }

        // Invalidate the session
        await invalidateSession(token.sessionId, accessToken)
      }
    }

    // Create response that clears cookies
    const response = NextResponse.json({ success: true })
    return clearSessionCookies(response)
  } catch {
    // console.error('Error during logout:', error)
    const response = NextResponse.json({ success: false })
    return clearSessionCookies(response)
  }
}

/**
 * Validate the current session
 */
export async function getSession(req: NextRequest): Promise<{
  isValid: boolean
  user?: Partial<User>
  response?: NextResponse
}> {
  try {
    // Generate fingerprint for verification
    const fingerprint = generateFingerprint(req)

    // Validate and possibly refresh the session
    const { isValid, token, response } = await validateAndRefreshSession(req, fingerprint)

    if (!isValid || !token) {
      return { isValid: false }
    }

    // Construct user object from token
    const user = {
      id: token.id,
      email: token.email,
      name: token.name,
      role: token.role,
      companyId: token.companyId,
    }

    return {
      isValid: true,
      user,
      response,
    }
  } catch {
    // console.error('Error getting session:', error)
    return { isValid: false }
  }
}

/**
 * Log out a user from all devices
 */
export async function logoutAllSessions(
  userId: string,
  req: NextRequest
): Promise<{
  success: boolean
  response?: NextResponse
}> {
  try {
    // Invalidate all sessions
    await invalidateAllUserSessions(userId)

    // Log activity
    logSessionActivity(userId, 'logout-all', req)

    // Create response that clears cookies for current device
    const response = NextResponse.json({ success: true })
    return {
      success: true,
      response: clearSessionCookies(response),
    }
  } catch {
    // console.error('Error logging out all sessions:', error)
    return { success: false }
  }
}

/**
 * Get all active sessions for a user
 */
export async function getActiveSessions(userId: string): Promise<{
  success: boolean
  sessions?: Array<{
    id: string
    createdAt: number
    lastActive: number
    device?: string
    ip?: string
    location?: string
  }>
}> {
  try {
    // Get session IDs
    const sessionIds = await getUserSessions(userId)

    // Get details for each session
    const sessionDetailsPromises = sessionIds.map(async sessionId => {
      const sessionData = await redis.get(`session:${sessionId}`)
      if (!sessionData) return null

      const session = JSON.parse(sessionData as string)

      // Get activity log if available
      const activityLog = await redis.get(`${SESSION_ACTIVITY_PREFIX}${sessionId}`)
      const activity = activityLog ? JSON.parse(activityLog as string) : {}

      return {
        id: sessionId,
        createdAt: session.createdAt,
        lastActive: session.lastActive,
        device: activity.userAgent || 'Unknown device',
        ip: activity.ip || 'Unknown',
        location: activity.location || 'Unknown',
      }
    })

    const sessionDetails = (await Promise.all(sessionDetailsPromises)).filter(Boolean)

    return {
      success: true,
      sessions: sessionDetails as any[],
    }
  } catch {
    // console.error('Error getting active sessions:', error)
    return { success: false }
  }
}

/**
 * Helper function to log session activity
 */
async function logSessionActivity(
  userId: string,
  action: 'login' | 'logout' | 'logout-all' | 'token-refresh',
  req: NextRequest
): Promise<void> {
  try {
    // Get token to find session ID
    const { accessToken } = getSessionFromCookies(req)
    if (!accessToken) return

    const token = await validateAccessToken(accessToken)
    if (!token || !token.sessionId) return

    // Log basic activity
    const activity = {
      userId,
      action,
      timestamp: Date.now(),
      ip: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
      location: 'unknown', // In a real app, this would be resolved from IP
    }

    // Store activity log with session ID
    await redis.set(
      `${SESSION_ACTIVITY_PREFIX}${token.sessionId}`,
      JSON.stringify(activity),
      'EX',
      7 * 24 * 60 * 60 // 7 days
    )
  } catch {
    // console.error('Error logging session activity:', error)
  }
}
