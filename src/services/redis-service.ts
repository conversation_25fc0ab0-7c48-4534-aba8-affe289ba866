import Redis from 'ioredis'

// Connection options with fallback
const redisOptions = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT) : 6379,
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 100, 3000)
    // console.log(`Redis connection retry attempt ${times} with delay ${delay}ms`);
    return delay
  },
}

// Global connection instance
let redisClient: Redis | null = null
let usingFallback = false

/**
 * Service for Redis operations with connection management and error handling
 */
export class RedisService {
  private redis: Redis

  constructor() {
    if (!redisClient) {
      try {
        // Check if Redis is explicitly disabled for local development
        if (process.env.DISABLE_REDIS === 'true') {
          // console.info('Redis explicitly disabled. Using in-memory fallback.');
          redisClient = this.createFallbackClient()
          usingFallback = true
        } else {
          redisClient = new Redis(redisOptions)

          redisClient.on('error', error => {
            // console.error('Redis connection error:', error);
            // If we haven't already switched to fallback
            if (!usingFallback && redisClient) {
              // console.info('Switching to in-memory fallback due to Redis error');
              redisClient = this.createFallbackClient()
              usingFallback = true
              this.redis = redisClient
            }
          })

          redisClient.on('connect', () => {
            // console.info('Redis connected successfully');
            usingFallback = false
          })

          // Perform a connectivity test
          redisClient.ping().catch(err => {
            // console.error('Redis ping failed:', err);
            redisClient = this.createFallbackClient()
            usingFallback = true
            this.redis = redisClient
          })
        }
      } catch {
        // console.error('Failed to initialize Redis client:', error);
        // Create a mock Redis client for fallback
        redisClient = this.createFallbackClient()
        usingFallback = true
      }
    }

    this.redis = redisClient
  }

  /**
   * Creates a fallback client that uses in-memory storage
   */
  private createFallbackClient(): Redis {
    // console.info('Creating in-memory Redis fallback');

    // Create in-memory storage
    const storage = new Map<string, { value: string; expiry?: number }>()

    const mockRedis = {
      // Basic key operations
      get: async (key: string) => {
        const item = storage.get(key)
        if (!item) return null

        // Check if expired
        if (item.expiry && item.expiry < Date.now()) {
          storage.delete(key)
          return null
        }

        return item.value
      },

      set: async (key: string, value: string, exMode?: string, exValue?: number) => {
        let expiry: number | undefined

        // Handle expiration
        if (exMode && exValue) {
          if (exMode.toUpperCase() === 'EX') {
            expiry = Date.now() + exValue * 1000
          } else if (exMode.toUpperCase() === 'PX') {
            expiry = Date.now() + exValue
          }
        }

        storage.set(key, { value, expiry })
        return 'OK'
      },

      del: async (...keys: string[]) => {
        let count = 0
        for (const key of keys) {
          if (storage.delete(key)) {
            count++
          }
        }
        return count
      },

      exists: async (key: string) => (storage.has(key) ? 1 : 0),

      expire: async (key: string, seconds: number) => {
        const item = storage.get(key)
        if (!item) return 0

        item.expiry = Date.now() + seconds * 1000
        storage.set(key, item)
        return 1
      },

      ttl: async (key: string) => {
        const item = storage.get(key)
        if (!item) return -2
        if (!item.expiry) return -1

        const ttl = Math.ceil((item.expiry - Date.now()) / 1000)
        return ttl > 0 ? ttl : -2
      },

      keys: async (pattern: string) => {
        const result: string[] = []
        const regex = new RegExp(`^${pattern.replace(/\*/g, '.*')}$`)

        for (const key of storage.keys()) {
          if (regex.test(key)) {
            result.push(key)
          }
        }

        return result
      },

      ping: async () => 'PONG',

      // Event handlers
      on: () => mockRedis,
      once: () => mockRedis,

      // Connection management
      disconnect: () => {},
      quit: async () => 'OK',

      // Pipeline support (minimal)
      pipeline: () => {
        const commands: Array<{ cmd: string; args: unknown[] }> = []

        return {
          get: (key: string) => {
            commands.push({ cmd: 'get', args: [key] })
            return this
          },
          set: (key: string, value: string, ...args: unknown[]) => {
            commands.push({ cmd: 'set', args: [key, value, ...args] })
            return this
          },
          del: (...keys: string[]) => {
            commands.push({ cmd: 'del', args: keys })
            return this
          },
          exec: async () => {
            return commands.map(cmd => [null, 'OK'])
          },
        }
      },
    } as unknown as Redis

    return mockRedis
  }

  /**
   * Safely get a value from Redis with error handling
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.redis.get(key)
    } catch {
      // console.error(`Redis get error for key ${key}:`, error);
      return null
    }
  }

  /**
   * Safely set a value in Redis with TTL and error handling
   */
  async set(key: string, value: string, ttlSeconds: number = 3600): Promise<boolean> {
    try {
      const result = await this.redis.set(key, value, 'EX', ttlSeconds)
      return result === 'OK'
    } catch {
      // console.error(`Redis set error for key ${key}:`, error);
      return false
    }
  }

  /**
   * Safely delete key(s) from Redis with error handling
   */
  async del(...keys: string[]): Promise<number> {
    try {
      return await this.redis.del(...keys)
    } catch {
      // console.error(`Redis del error:`, error);
      return 0
    }
  }

  /**
   * Check if a key exists in Redis
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key)
      return result > 0
    } catch {
      // console.error(`Redis exists error for key ${key}:`, error);
      return false
    }
  }

  /**
   * Set key expiration time
   */
  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    try {
      const result = await this.redis.expire(key, ttlSeconds)
      return result === 1
    } catch {
      // console.error(`Redis expire error for key ${key}:`, error);
      return false
    }
  }

  /**
   * Get remaining TTL for a key
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key)
    } catch {
      // console.error(`Redis ttl error for key ${key}:`, error);
      return -2 // Key doesn't exist
    }
  }

  /**
   * Find keys matching a pattern
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.redis.keys(pattern)
    } catch {
      // console.error(`Redis keys error for pattern ${pattern}:`, error);
      return []
    }
  }

  /**
   * Clear cache for specific patterns
   * Used when data is updated and cache needs to be invalidated
   */
  async clearCache(pattern: string): Promise<number> {
    try {
      const keys = await this.keys(pattern)
      if (keys.length === 0) return 0

      return await this.del(...keys)
    } catch {
      // console.error(`Redis clearCache error for pattern ${pattern}:`, error);
      return 0
    }
  }

  /**
   * Clear cache for a specific company
   * Useful when company data changes
   */
  async clearCompanyCache(companyId: string): Promise<number> {
    return this.clearCache(`company:${companyId}:*`)
  }

  /**
   * Clear cache for a specific user
   * Useful when user data changes
   */
  async clearUserCache(userId: string): Promise<number> {
    return this.clearCache(`user:${userId}*`)
  }

  /**
   * Disconnect the Redis client
   */
  disconnect(): void {
    if (this.redis) {
      try {
        this.redis.disconnect()
      } catch {
        // console.error('Error disconnecting Redis:', error);
      }
    }
  }

  /**
   * Check if we're using the fallback implementation
   */
  isUsingFallback(): boolean {
    return usingFallback
  }
}
