import { PrismaClient, Prisma } from '@prisma/client'
import { prisma } from '@/lib/prisma'
import { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

/**
 * Types of resources that can be audited
 */
export enum AuditResourceType {
  USER = 'USER',
  COMPANY = 'COMPANY',
  FEATURE_FLAG = 'FEATURE_FLAG',
  ROLE = 'ROLE',
  DEPARTMENT = 'DEPARTMENT',
  SUB_DEPARTMENT = 'SUB_DEPARTMENT',
  PERMISSION = 'PERMISSION',
  SUBSCRIPTION = 'SUBSCRIPTION',
  SETTINGS = 'SETTINGS',
  DESIGN_SYSTEM = 'DESIGN_SYSTEM',
  COMPONENT = 'COMPONENT',
  ANALYTICS = 'ANALYTICS',
  ONBOARDING = 'ONBOARDING',
  CONTEXT = 'CONTEXT',
  OTHER = 'OTHER',
}

/**
 * Types of actions that can be audited
 */
export enum AuditAction {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  IMPERSONATE = 'IMPERSONATE',
  GRANT = 'GRANT',
  REVOKE = 'REVOKE',
  PUBLISH = 'PUBLISH',
  UNPUBLISH = 'UNPUBLISH',
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  VIEW = 'VIEW',
  DOWNLOAD = 'DOWNLOAD',
  UPLOAD = 'UPLOAD',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  OTHER = 'OTHER',
}

/**
 * Represents the data needed to create an audit log entry
 */
export interface AuditLogData {
  userId: string
  action: string
  details?: Record<string, unknown>
  resourceType?: string
  resourceId?: string
  ipAddress?: string
}

/**
 * Options for audit log retention
 */
export interface AuditRetentionOptions {
  days: number
  resourceTypes?: string[]
}

/**
 * Service for managing audit logs
 */
export class AuditService {
  private static instance: AuditService
  private prisma: PrismaClient
  private readonly DEFAULT_RETENTION_DAYS = 90

  private constructor() {
    this.prisma = prisma
  }

  /**
   * Get the singleton instance of AuditService
   */
  public static getInstance(): AuditService {
    if (!AuditService.instance) {
      AuditService.instance = new AuditService()
    }
    return AuditService.instance
  }

  /**
   * Create a new audit log entry
   */
  public async createLog(data: AuditLogData): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          details: data.details ? (data.details as any) : undefined,
          resourceType: data.resourceType,
          resourceId: data.resourceId,
          ipAddress: data.ipAddress,
        },
      })
    } catch {
      if (process.env.NODE_ENV === 'development') console.error('Error creating audit log:', error)
      // Don't throw, as audit logging should not block main operations
    }
  }

  /**
   * Create an audit log from a Next.js request
   */
  public async createLogFromRequest(
    req: NextRequest,
    action: string,
    resourceType?: string,
    resourceId?: string,
    details?: Record<string, unknown>
  ): Promise<void> {
    try {
      const token = await getToken({ req })
      if (!token || !token.sub) {
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.warn('Attempted to create audit log without user token')
        return
      }

      const ipAddress = req.ip || req.headers.get('x-forwarded-for') || 'unknown'

      await this.createLog({
        userId: token.sub,
        action,
        resourceType,
        resourceId,
        details,
        ipAddress: ipAddress as string,
      })
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Error creating audit log from request:', error)
    }
  }

  /**
   * Query audit logs with filtering and pagination
   */
  public async queryLogs(params: {
    userId?: string
    action?: string
    resourceType?: string
    resourceId?: string
    fromDate?: Date
    toDate?: Date
    companyId?: string
    page?: number
    limit?: number
  }): Promise<{ logs: unknown[]; total: number }> {
    const {
      userId,
      action,
      resourceType,
      resourceId,
      fromDate,
      toDate,
      companyId,
      page = 1,
      limit = 50,
    } = params

    const skip = (page - 1) * limit

    // Build the where clause for filtering
    const where: Prisma.AuditLogWhereInput = {}

    if (userId) {
      where.userId = userId
    }

    if (action) {
      where.action = action
    }

    if (resourceType) {
      where.resourceType = resourceType
    }

    if (resourceId) {
      where.resourceId = resourceId
    }

    // Date range filtering
    if (fromDate || toDate) {
      where.createdAt = {}

      if (fromDate) {
        where.createdAt.gte = fromDate
      }

      if (toDate) {
        where.createdAt.lte = toDate
      }
    }

    // Company filtering (filter by users in the company)
    if (companyId) {
      where.user = {
        UserCompany: {
          some: {
            companyId,
          },
        },
      }
    }

    // Execute the query with pagination
    const [logs, total] = await Promise.all([
      this.prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.auditLog.count({ where }),
    ])

    return { logs, total }
  }

  /**
   * Clean up old audit logs based on retention policy
   */
  public async cleanupOldLogs(
    options: AuditRetentionOptions = { days: this.DEFAULT_RETENTION_DAYS }
  ): Promise<number> {
    const { days, resourceTypes } = options

    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)

    const where: Prisma.AuditLogWhereInput = {
      createdAt: {
        lt: cutoffDate,
      },
    }

    // If specific resource types are specified, only delete those
    if (resourceTypes && resourceTypes.length > 0) {
      where.resourceType = {
        in: resourceTypes,
      }
    }

    // Delete old audit logs
    const result = await this.prisma.auditLog.deleteMany({
      where,
    })

    return result.count
  }

  /**
   * Get audit log statistics by action
   */
  public async getActionStatistics(companyId?: string): Promise<Record<string, number>> {
    const where: Prisma.AuditLogWhereInput = {}

    if (companyId) {
      where.user = {
        UserCompany: {
          some: {
            companyId,
          },
        },
      }
    }

    const actions = await this.prisma.auditLog.groupBy({
      by: ['action'],
      _count: {
        _all: true,
      },
      where,
    })

    return actions.reduce(
      (acc, curr) => {
        acc[curr.action] = curr._count._all
        return acc
      },
      {} as Record<string, number>
    )
  }

  /**
   * Get audit log statistics by resource type
   */
  public async getResourceStatistics(companyId?: string): Promise<Record<string, number>> {
    const where: Prisma.AuditLogWhereInput = {}

    if (companyId) {
      where.user = {
        UserCompany: {
          some: {
            companyId,
          },
        },
      }
    }

    const resources = await this.prisma.auditLog.groupBy({
      by: ['resourceType'],
      _count: {
        _all: true,
      },
      where,
    })

    return resources.reduce(
      (acc, curr) => {
        if (curr.resourceType) {
          acc[curr.resourceType] = curr._count._all
        }
        return acc
      },
      {} as Record<string, number>
    )
  }

  /**
   * Get audit logs for a specific user
   */
  public async getUserLogs(
    userId: string,
    page: number = 1,
    limit: number = 50
  ): Promise<{ logs: unknown[]; total: number }> {
    return this.queryLogs({ userId, page, limit })
  }

  /**
   * Get audit logs for a specific company
   */
  public async getCompanyLogs(
    companyId: string,
    page: number = 1,
    limit: number = 50
  ): Promise<{ logs: unknown[]; total: number }> {
    return this.queryLogs({ companyId, page, limit })
  }

  /**
   * Generate a scheduled cleanup job function
   */
  public getCleanupJob(
    options: AuditRetentionOptions = { days: this.DEFAULT_RETENTION_DAYS }
  ): () => Promise<number> {
    return async () => {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(`Running audit log cleanup job (retention: ${options.days} days)`)
      return this.cleanupOldLogs(options)
    }
  }
}

// Export singleton instance
export const auditService = AuditService.getInstance()

// Export default for simpler imports
export default auditService
