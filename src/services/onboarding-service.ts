import prisma from '@/lib/prisma'
import { auth } from '@/lib/auth/config'
import { OnboardingDataInput } from '@/lib/validators/onboarding'
import { redis } from '@/lib/redis'

// Cache TTL for onboarding data (24 hours)
const ONBOARDING_CACHE_TTL = 24 * 60 * 60

// Cache key prefix for onboarding data
const ONBOARDING_CACHE_PREFIX = 'onboarding:data:'

/**
 * Get the onboarding status of the current user
 * @returns The onboarding status or null if unauthenticated
 */
export async function getOnboardingStatus() {
  try {
    const session = await auth()
    if (!session || !session.user) return null

    // Try to get from cache first
    const cacheKey = `onboarding:status:${session.user.id}`
    const cachedStatus = await redis.get(cacheKey)

    if (cachedStatus) {
      return JSON.parse(cachedStatus)
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { onboardingCompleted: true },
    })

    const status = { completed: user?.onboardingCompleted || false }

    // Cache the status
    await redis.set(cacheKey, JSON.stringify(status), 'EX', ONBOARDING_CACHE_TTL)

    return status
  } catch {
    // console.error('Error fetching onboarding status:', error);
    return { completed: false }
  }
}

/**
 * Get onboarding data for the current user
 * @returns The onboarding data or empty object if none exists
 */
export async function getOnboardingData() {
  try {
    const session = await auth()
    if (!session || !session.user) return null

    // Try to get from cache first
    const cacheKey = `${ONBOARDING_CACHE_PREFIX}${session.user.id}`
    const cachedData = await redis.get(cacheKey)

    if (cachedData) {
      return JSON.parse(cachedData)
    }

    const onboardingData = await prisma.onboardingData.findUnique({
      where: { userId: session.user.id },
    })

    const data = onboardingData?.data || {}

    // Cache the data
    await redis.set(cacheKey, JSON.stringify(data), 'EX', ONBOARDING_CACHE_TTL)

    return data
  } catch {
    // console.error('Error fetching onboarding data:', error);
    return {}
  }
}

/**
 * Save onboarding step data for a user
 * @param userId The user ID
 * @param stepId The step ID
 * @param data The step data
 */
export async function saveOnboardingStep(userId: string, stepId: string, data: unknown) {
  try {
    // Get existing data first to merge it properly
    const existingRecord = await prisma.onboardingData.findUnique({
      where: { userId },
    })

    const existingData = existingRecord?.data || {}

    const updatedData = { ...existingData, ...data }

    // Store the updated data
    await prisma.onboardingData.upsert({
      where: { userId },
      update: {
        data: updatedData,
        lastStep: stepId,
        lastUpdated: new Date(),
      },
      create: {
        userId,
        data: updatedData,
        lastStep: stepId,
        lastUpdated: new Date(),
        createdAt: new Date(),
      },
    })

    // Update cache
    const cacheKey = `${ONBOARDING_CACHE_PREFIX}${userId}`
    await redis.set(cacheKey, JSON.stringify(updatedData), 'EX', ONBOARDING_CACHE_TTL)

    return { success: true }
  } catch {
    // console.error('Error saving onboarding step:', error);
    return { success: false, error: 'Failed to save step data' }
  }
}

/**
 * Map job title/role to Role enum
 * @param roleString The role string from onboarding
 * @returns Mapped Role enum value
 */
function mapRoleToEnum(roleString: string): string {
  if (!roleString) return 'EMPLOYEE'

  const roleLower = roleString.toLowerCase()

  // Check for management/leadership roles
  if (
    roleLower.includes('manager') ||
    roleLower.includes('lead') ||
    roleLower.includes('head') ||
    roleLower.includes('supervisor')
  ) {
    return 'MANAGER'
  }

  // Check for director/VP roles
  if (
    roleLower.includes('director') ||
    roleLower.includes('vp') ||
    roleLower.includes('vice president') ||
    roleLower.includes('chief')
  ) {
    return 'DIRECTOR'
  }

  // Check for admin roles (HR, IT Admin, etc.)
  if (
    roleLower.includes('admin') ||
    roleLower.includes('administrator') ||
    roleLower.includes('operations manager')
  ) {
    return 'ADMIN'
  }

  // Default to EMPLOYEE for all other roles
  return 'EMPLOYEE'
}

/**
 * Complete the onboarding process for a user
 * @param userId The user ID
 * @param data The complete onboarding data
 */
export async function completeOnboarding(userId: string, data: OnboardingDataInput) {
  try {
    // Create a transaction to ensure both operations succeed or fail together
    await prisma.$transaction([
      // Update the onboarding data record
      prisma.onboardingData.upsert({
        where: { userId },
        update: {
          data: data,
          lastStep: 'completed',
          lastUpdated: new Date(),
        },
        create: {
          userId,
          data: data,
          lastStep: 'completed',
          lastUpdated: new Date(),
          createdAt: new Date(),
        },
      }),

      // Update the user record to mark onboarding as completed
      prisma.user.update({
        where: { id: userId },
        data: {
          onboardingCompleted: true,
          // Combine firstName and lastName into the name field
          ...(data.firstName &&
            data.lastName && {
              name: `${data.firstName} ${data.lastName}`,
            }),
          // Map job title to proper Role enum
          ...(data.role && {
            role: mapRoleToEnum(data.role) as any,
          }),
          // Store the actual job title in jobTitle field
          ...(data.role && {
            jobTitle: data.role,
          }),
          // Update any other user fields from onboarding data
          ...(data.email && { email: data.email }),
          ...(data.team && { department: data.team }),
          updatedAt: new Date(),
        },
      }),
    ])

    // Update cache for both status and data
    const statusCacheKey = `onboarding:status:${userId}`
    await redis.set(statusCacheKey, JSON.stringify({ completed: true }), 'EX', ONBOARDING_CACHE_TTL)

    const dataCacheKey = `${ONBOARDING_CACHE_PREFIX}${userId}`
    await redis.set(dataCacheKey, JSON.stringify(data), 'EX', ONBOARDING_CACHE_TTL)

    return { success: true }
  } catch {
    // console.error('Error completing onboarding:', error);
    return { success: false, error: 'Failed to complete onboarding' }
  }
}

/**
 * Reset the onboarding process for a user (useful for testing/development)
 * @param userId The user ID
 */
export async function resetOnboarding(userId: string) {
  try {
    await prisma.$transaction([
      // Update the onboarding data record
      prisma.onboardingData
        .delete({
          where: { userId },
        })
        .catch(() => null), // Ignore if it doesn't exist

      // Reset the user's onboarding status
      prisma.user.update({
        where: { id: userId },
        data: {
          onboardingCompleted: false,
          updatedAt: new Date(),
        },
      }),
    ])

    // Clear cache
    const statusCacheKey = `onboarding:status:${userId}`
    await redis.del(statusCacheKey)

    const dataCacheKey = `${ONBOARDING_CACHE_PREFIX}${userId}`
    await redis.del(dataCacheKey)

    return { success: true }
  } catch {
    // console.error('Error resetting onboarding:', error);
    return { success: false, error: 'Failed to reset onboarding' }
  }
}
