/**
 * Performance Monitoring Service for User Context API
 * Tracks response times, cache hit/miss ratios, and performance metrics
 */

import { RedisService } from './redis-service'

export interface PerformanceMetric {
  endpoint: string
  userId: string
  responseTime: number
  cacheHit: boolean
  timestamp: number
  userAgent?: string
  ip?: string
}

export interface PerformanceStats {
  totalRequests: number
  averageResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  cacheHitRatio: number
  slowRequestsCount: number
}

class PerformanceMonitoringService {
  private redisService: RedisService
  private metrics: PerformanceMetric[] = []
  private readonly SLOW_REQUEST_THRESHOLD = 100 // ms
  private readonly METRICS_RETENTION_HOURS = 24
  private readonly MAX_METRICS_IN_MEMORY = 1000

  constructor() {
    this.redisService = new RedisService()
  }

  /**
   * Record a performance metric for an API request
   */
  async recordMetric(metric: PerformanceMetric): Promise<void> {
    try {
      // Add to in-memory array for real-time calculations
      this.metrics.push(metric)

      // Keep only the most recent metrics in memory
      if (this.metrics.length > this.MAX_METRICS_IN_MEMORY) {
        this.metrics = this.metrics.slice(-this.MAX_METRICS_IN_MEMORY)
      }

      // Store in Redis for persistence with TTL
      const key = `performance:${metric.endpoint}:${Date.now()}`
      await this.redisService.set(key, JSON.stringify(metric), this.METRICS_RETENTION_HOURS * 3600)

      // Update aggregate stats
      await this.updateAggregateStats(metric)

      // Check for performance issues and alert if necessary
      if (metric.responseTime > this.SLOW_REQUEST_THRESHOLD) {
        await this.alertSlowRequest(metric)
      }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to record performance metric:', _error)
      // Don't throw - monitoring failures shouldn't break the app
    }
  }

  /**
   * Get real-time performance statistics
   */
  async getPerformanceStats(endpoint?: string): Promise<PerformanceStats> {
    try {
      let relevantMetrics = this.metrics

      if (endpoint) {
        relevantMetrics = this.metrics.filter(m => m.endpoint === endpoint)
      }

      if (relevantMetrics.length === 0) {
        return {
          totalRequests: 0,
          averageResponseTime: 0,
          p95ResponseTime: 0,
          p99ResponseTime: 0,
          cacheHitRatio: 0,
          slowRequestsCount: 0,
        }
      }

      const responseTimes = relevantMetrics.map(m => m.responseTime).sort((a, b) => a - b)
      const cacheHits = relevantMetrics.filter(m => m.cacheHit).length
      const slowRequests = relevantMetrics.filter(
        m => m.responseTime > this.SLOW_REQUEST_THRESHOLD
      ).length

      const totalRequests = relevantMetrics.length
      const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / totalRequests
      const p95Index = Math.floor(totalRequests * 0.95)
      const p99Index = Math.floor(totalRequests * 0.99)

      return {
        totalRequests,
        averageResponseTime: Math.round(averageResponseTime * 100) / 100,
        p95ResponseTime: responseTimes[p95Index] || 0,
        p99ResponseTime: responseTimes[p99Index] || 0,
        cacheHitRatio: Math.round((cacheHits / totalRequests) * 10000) / 100, // Percentage with 2 decimals
        slowRequestsCount: slowRequests,
      }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to calculate performance stats:', _error)
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        cacheHitRatio: 0,
        slowRequestsCount: 0,
      }
    }
  }

  /**
   * Get historical performance data from Redis
   */
  async getHistoricalStats(endpoint: string, hours: number = 24): Promise<PerformanceMetric[]> {
    try {
      const cutoffTime = Date.now() - hours * 60 * 60 * 1000
      const pattern = `performance:${endpoint}:*`
      const keys = await this.redisService.keys(pattern)

      const metrics: PerformanceMetric[] = []

      for (const key of keys) {
        const timestamp = parseInt(key.split(':').pop() || '0')
        if (timestamp >= cutoffTime) {
          const data = await this.redisService.get(key)
          if (data) {
            metrics.push(JSON.parse(data))
          }
        }
      }

      return metrics.sort((a, b) => a.timestamp - b.timestamp)
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to get historical stats:', _error)
      return []
    }
  }

  /**
   * Update aggregate statistics in Redis
   */
  private async updateAggregateStats(metric: PerformanceMetric): Promise<void> {
    try {
      const key = `aggregate:${metric.endpoint}:${new Date().toISOString().split('T')[0]}` // Daily aggregates

      const existing = await this.redisService.get(key)
      const aggregate = existing
        ? JSON.parse(existing)
        : {
            totalRequests: 0,
            totalResponseTime: 0,
            cacheHits: 0,
            slowRequests: 0,
            responseTimes: [],
          }

      aggregate.totalRequests++
      aggregate.totalResponseTime += metric.responseTime
      if (metric.cacheHit) aggregate.cacheHits++
      if (metric.responseTime > this.SLOW_REQUEST_THRESHOLD) aggregate.slowRequests++

      // Keep track of response times for percentile calculations
      aggregate.responseTimes.push(metric.responseTime)

      // Limit the stored response times to prevent memory issues
      if (aggregate.responseTimes.length > 10000) {
        aggregate.responseTimes = aggregate.responseTimes.slice(-5000)
      }

      await this.redisService.set(key, JSON.stringify(aggregate), 86400 * 7) // Keep for 7 days
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to update aggregate stats:', _error)
    }
  }

  /**
   * Alert for slow requests (could integrate with external alerting systems)
   */
  private async alertSlowRequest(metric: PerformanceMetric): Promise<void> {
    try {
      const alertKey = `alert:slow_request:${metric.endpoint}:${Math.floor(Date.now() / 60000)}` // Per minute

      // Check if we've already alerted for this endpoint in the last minute
      const existingAlert = await this.redisService.get(alertKey)
      if (existingAlert) {
        return // Don't spam alerts
      }

      // Set alert flag for 1 minute
      await this.redisService.set(alertKey, '1', 60)

      // Log the slow request (in production, this could send to Slack, email, etc.)
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.warn(`🚨 SLOW REQUEST ALERT: ${metric.endpoint}`, {
            userId: metric.userId,
            responseTime: `${metric.responseTime}ms`,
            threshold: `${this.SLOW_REQUEST_THRESHOLD}ms`,
            cacheHit: metric.cacheHit,
            timestamp: new Date(metric.timestamp).toISOString(),
          })

      // In production, you might want to:
      // - Send to Sentry
      // - Send to Slack webhook
      // - Send email notification
      // - Trigger PagerDuty incident
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to send slow request alert:', _error)
    }
  }

  /**
   * Create a performance wrapper for API routes
   */
  createPerformanceWrapper(endpoint: string) {
    return {
      start: (userId: string, userAgent?: string, ip?: string) => {
        const startTime = performance.now()

        return {
          end: async (cacheHit: boolean = false) => {
            const endTime = performance.now()
            const responseTime = Math.round((endTime - startTime) * 100) / 100 // Round to 2 decimals

            await this.recordMetric({
              endpoint,
              userId,
              responseTime,
              cacheHit,
              timestamp: Date.now(),
              userAgent,
              ip,
            })

            return responseTime
          },
        }
      },
    }
  }

  /**
   * Clean up old metrics from memory and Redis
   */
  async cleanupOldMetrics(): Promise<void> {
    try {
      const cutoffTime = Date.now() - this.METRICS_RETENTION_HOURS * 60 * 60 * 1000

      // Clean up in-memory metrics
      this.metrics = this.metrics.filter(m => m.timestamp >= cutoffTime)

      // Clean up Redis keys (this should be done sparingly as it's expensive)
      const allKeys = await this.redisService.keys('performance:*')
      const oldKeys = allKeys.filter(key => {
        const timestamp = parseInt(key.split(':').pop() || '0')
        return timestamp < cutoffTime
      })

      if (oldKeys.length > 0) {
        await this.redisService.del(...oldKeys)
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.log(`Cleaned up ${oldKeys.length} old performance metrics`)
      }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to cleanup old metrics:', _error)
    }
  }

  /**
   * Get cache performance statistics
   */
  async getCachePerformance(): Promise<{
    hitRatio: number
    totalRequests: number
    avgHitResponseTime: number
    avgMissResponseTime: number
  }> {
    try {
      const cacheHits = this.metrics.filter(m => m.cacheHit)
      const cacheMisses = this.metrics.filter(m => !m.cacheHit)

      const totalRequests = this.metrics.length
      const hitRatio = totalRequests > 0 ? (cacheHits.length / totalRequests) * 100 : 0

      const avgHitResponseTime =
        cacheHits.length > 0
          ? cacheHits.reduce((sum, m) => sum + m.responseTime, 0) / cacheHits.length
          : 0

      const avgMissResponseTime =
        cacheMisses.length > 0
          ? cacheMisses.reduce((sum, m) => sum + m.responseTime, 0) / cacheMisses.length
          : 0

      return {
        hitRatio: Math.round(hitRatio * 100) / 100,
        totalRequests,
        avgHitResponseTime: Math.round(avgHitResponseTime * 100) / 100,
        avgMissResponseTime: Math.round(avgMissResponseTime * 100) / 100,
      }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Failed to get cache performance:', _error)
      return {
        hitRatio: 0,
        totalRequests: 0,
        avgHitResponseTime: 0,
        avgMissResponseTime: 0,
      }
    }
  }
}

// Export singleton instance
export const performanceMonitoring = new PerformanceMonitoringService()
