/**
 * AI Recommendations Service - Placeholder for future Gen AI integration
 *
 * This service will be implemented post-MVP to provide personalized insights,
 * recommendations, and coaching based on user context across all entry points
 * (e.g., onboarding, settings, dashboards, Superadmin Panel).
 */

import { hasFeature } from '@/lib/feature-flags'
import { getUserContext } from '@/services/context-service'
import { UserContext, RecommendationType, AIRecommendation } from '@/types/intelligence'

/**
 * Types for AI-generated recommendations
 */
export interface Recommendation {
  id?: string
  userId: string
  type: 'skill' | 'resource' | 'mentor' | 'course' | 'project' | 'task' | 'insight'
  content: string
  priority: 'high' | 'medium' | 'low'
  completed?: boolean
  createdAt?: Date
  updatedAt?: Date
}

/**
 * Generate personalized onboarding recommendations
 * Placeholder for future Gen AI implementation
 *
 * @param userId The user ID to generate recommendations for
 * @param companyId The company ID for feature flag check
 * @returns Array of recommendations or empty array if unavailable
 */
export async function generateOnboardingRecommendations(
  userId: string,
  companyId: string
): Promise<Recommendation[]> {
  try {
    // Check if context-awareness feature is enabled
    const isEnabled = await hasFeature(companyId, 'contextAwareness')
    if (!isEnabled) {
      return []
    }

    // Get user context
    const context = await getUserContext(userId, companyId)
    if (!context) {
      return []
    }

    // In the future, this would:
    // 1. Process user context with Gen AI
    // 2. Generate personalized onboarding recommendations
    // 3. Store recommendations in CoachingHistory

    // Placeholder implementation with mock recommendations
    const mockRecommendations: Recommendation[] = [
      {
        userId,
        type: 'resource',
        content: `Welcome to Emynent! Based on your ${context.role} role, we recommend exploring the ${context.role} dashboard first.`,
        priority: 'high',
        createdAt: new Date(),
      },
      {
        userId,
        type: 'task',
        content: 'Complete your profile to help us personalize your experience.',
        priority: 'high',
        createdAt: new Date(),
      },
    ]

    return mockRecommendations
  } catch {
    // console.error(`Error generating onboarding recommendations for user ${userId}:`, error);
    return []
  }
}

/**
 * Generate skill development recommendations
 * Placeholder for future Gen AI implementation
 *
 * @param userId The user ID to generate recommendations for
 * @param companyId The company ID for feature flag check
 * @returns Array of recommendations or empty array if unavailable
 */
export async function generateSkillRecommendations(
  userId: string,
  companyId: string
): Promise<Recommendation[]> {
  try {
    // Check if context-awareness feature is enabled
    const isEnabled = await hasFeature(companyId, 'contextAwareness')
    if (!isEnabled) {
      return []
    }

    // Get user context
    const context = await getUserContext(userId, companyId)
    if (!context) {
      return []
    }

    // In the future, this would:
    // 1. Analyze skill gaps from context
    // 2. Process performance reviews with Gen AI
    // 3. Generate personalized skill recommendations

    // Placeholder implementation with mock recommendations
    const mockRecommendations: Recommendation[] = [
      {
        userId,
        type: 'skill',
        content: 'Based on your recent activities, we recommend improving your leadership skills.',
        priority: 'medium',
        createdAt: new Date(),
      },
      {
        userId,
        type: 'course',
        content:
          'Consider taking our "Effective Communication" course to enhance your collaboration skills.',
        priority: 'medium',
        createdAt: new Date(),
      },
    ]

    return mockRecommendations
  } catch {
    // console.error(`Error generating skill recommendations for user ${userId}:`, error);
    return []
  }
}

/**
 * Store recommendation in coaching history
 * Placeholder for future Gen AI implementation
 *
 * @param recommendation The recommendation to store
 * @returns The stored recommendation ID or null if failed
 */
export async function storeRecommendation(recommendation: Recommendation): Promise<string | null> {
  try {
    // In the future, this would:
    // 1. Store the recommendation in CoachingHistory table
    // 2. Link it to relevant entities (skills, courses, etc.)

    // Placeholder implementation
    // console.log('Storing recommendation:', recommendation);

    return 'placeholder-recommendation-id'
  } catch {
    // console.error('Error storing recommendation:', error);
    return null
  }
}

/**
 * Mark recommendation as completed
 * Placeholder for future Gen AI implementation
 *
 * @param recommendationId The recommendation ID to mark as completed
 * @param userId The user ID who completed the recommendation
 * @returns Success status
 */
export async function completeRecommendation(
  recommendationId: string,
  userId: string
): Promise<boolean> {
  try {
    // In the future, this would:
    // 1. Update the recommendation status in CoachingHistory
    // 2. Trigger refreshing of recommendations
    // 3. Log the completion for future context

    // Placeholder implementation
    // console.log(`Marking recommendation ${recommendationId} as completed for user ${userId}`);

    return true
  } catch {
    // console.error(`Error completing recommendation ${recommendationId}:`, error);
    return false
  }
}

/**
 * AI Recommendation System
 * Processes user context to generate personalized recommendations
 * Part of Phase 3: Intelligence Layer
 */

export interface RecommendationRequest {
  userId: string
  companyId: string
  context: UserContext
  type?: RecommendationType
  limit?: number
}

export interface RecommendationResponse {
  recommendations: AIRecommendation[]
  confidence: number
  reasoning: string
  metadata: {
    processingTime: number
    contextFactors: string[]
    fallbackUsed: boolean
  }
}

/**
 * Core AI Recommendation Service
 * Generates context-aware recommendations for users
 */
export class AIRecommendationService {
  private static instance: AIRecommendationService
  private cache = new Map<string, RecommendationResponse>()
  private readonly CACHE_TTL = 1000 * 60 * 30 // 30 minutes

  static getInstance(): AIRecommendationService {
    if (!AIRecommendationService.instance) {
      AIRecommendationService.instance = new AIRecommendationService()
    }
    return AIRecommendationService.instance
  }

  /**
   * Clear the cache - useful for testing
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Generate personalized recommendations based on user context
   */
  async generateRecommendations(request: RecommendationRequest): Promise<RecommendationResponse> {
    const startTime = Date.now()

    try {
      // Check cache first BEFORE any other operations
      const cacheKey = this.getCacheKey(request)
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }

      // Check if AI recommendations are enabled for this company
      let aiEnabled = false
      try {
        aiEnabled = await hasFeature(request.companyId, 'aiRecommendations')
      } catch {
        // console.error('Feature flag check failed:', error);
        // If feature flag check fails, use fallback
        const fallbackResponse = this.getFallbackRecommendations(request, startTime)
        this.setCache(cacheKey, fallbackResponse)
        return fallbackResponse
      }

      if (!aiEnabled) {
        const fallbackResponse = this.getFallbackRecommendations(request, startTime)
        // Cache fallback responses too
        this.setCache(cacheKey, fallbackResponse)
        return fallbackResponse
      }

      // Validate context
      if (!request.context) {
        return this.getFallbackRecommendations(request, startTime)
      }

      // Process user context and generate recommendations
      const recommendations = await this.processContextAndGenerate(request)

      // Calculate overall confidence
      const confidence = this.calculateConfidence(request.context, recommendations)

      const response: RecommendationResponse = {
        recommendations,
        confidence,
        reasoning: this.generateReasoning(request.context, recommendations),
        metadata: {
          processingTime: Date.now() - startTime,
          contextFactors: this.extractContextFactors(request.context),
          fallbackUsed: false,
        },
      }

      // Cache the response
      this.setCache(cacheKey, response)

      return response
    } catch {
      // console.error('AI Recommendation generation failed:', error);
      const fallbackResponse = this.getFallbackRecommendations(request, startTime)
      // Don't cache error responses to allow retry
      return fallbackResponse
    }
  }

  /**
   * Process user context and generate AI-driven recommendations
   */
  private async processContextAndGenerate(
    request: RecommendationRequest
  ): Promise<AIRecommendation[]> {
    const { context, type, limit = 5 } = request

    try {
      // Validate context - if this fails, it will throw and trigger fallback
      const validatedContext = await this.validateContext(context)

      const recommendations: AIRecommendation[] = []

      // Role-based recommendations
      if (!type || type === 'role_based') {
        recommendations.push(...(await this.generateRoleBasedRecommendations(validatedContext)))
      }

      // Skill development recommendations
      if (!type || type === 'skill_development') {
        recommendations.push(...(await this.generateSkillRecommendations(validatedContext)))
      }

      // Career path recommendations
      if (!type || type === 'career_path') {
        recommendations.push(...(await this.generateCareerRecommendations(validatedContext)))
      }

      // UI/UX personalization recommendations
      if (!type || type === 'ui_personalization') {
        recommendations.push(...(await this.generateUIRecommendations(validatedContext)))
      }

      // Content recommendations
      if (!type || type === 'content') {
        recommendations.push(...(await this.generateContentRecommendations(validatedContext)))
      }

      // Sort by priority and confidence, limit results
      return recommendations
        .sort((a, b) => b.priority * b.confidence - a.priority * a.confidence)
        .slice(0, limit)
    } catch {
      // If context processing fails, throw error to trigger fallback in main method
      throw new Error(`Context processing failed: ${error.message}`)
    }
  }

  /**
   * Validate user context (simplified approach)
   */
  private async validateContext(context: UserContext): Promise<UserContext> {
    try {
      // Only validate if context-awareness feature is enabled
      let isEnabled = false
      try {
        isEnabled = await hasFeature(context.companyId, 'contextAwareness')
      } catch {
        // console.error('Context awareness feature flag check failed:', error);
        // If feature flag check fails, return original context
        return context
      }

      if (!isEnabled) {
        return context
      }

      // Import getUserContext from the correct context service
      const { getUserContext } = await import('./context-service')

      // Try to get fresh context data
      let freshContext = null
      try {
        freshContext = await getUserContext(context.userId, context.companyId)
      } catch {
        // console.error('Context service call failed:', error);
        // If context service fails, throw to trigger fallback
        throw new Error(`Context service failed: ${error.message}`)
      }

      // If fresh context is null, treat it as a validation failure
      if (freshContext === null) {
        throw new Error('Context service returned null - user context unavailable')
      }

      // If fresh context is available, merge it with provided context
      if (freshContext) {
        const mergedContext = {
          ...context,
          // Preserve the provided context role instead of overriding with fresh context
          role: context.role || freshContext.role,
          preferences: { ...freshContext.preferences, ...context.preferences },
          recentActions: context.recentActions || freshContext.recentActions || [],
          historicalData: { ...freshContext.historicalData, ...context.historicalData },
        }
        return mergedContext
      }

      return context
    } catch {
      // If validation fails, throw to trigger fallback in the main method
      throw new Error(`Context validation failed: ${error.message}`)
    }
  }

  /**
   * Generate role-based recommendations
   */
  private async generateRoleBasedRecommendations(
    context: UserContext
  ): Promise<AIRecommendation[]> {
    const recommendations: AIRecommendation[] = []

    // Manager-specific recommendations
    if (context.role === 'MANAGER') {
      recommendations.push({
        id: `role_manager_${Date.now()}`,
        type: 'role_based',
        title: 'Review Team Performance',
        description: "Check your team's recent performance metrics and provide feedback",
        action: {
          type: 'navigate',
          target: '/dashboard/team',
          label: 'View Team Dashboard',
        },
        priority: 0.8,
        confidence: 0.9,
        reasoning: 'Managers should regularly review team performance',
        metadata: {
          category: 'management',
          tags: ['team', 'performance', 'management'],
        },
      })

      recommendations.push({
        id: `role_manager_meetings_${Date.now()}`,
        type: 'role_based',
        title: 'Schedule Team Check-ins',
        description: 'Set up regular one-on-one meetings with team members',
        action: {
          type: 'navigate',
          target: '/calendar',
          label: 'Schedule Meetings',
        },
        priority: 0.7,
        confidence: 0.85,
        reasoning: 'Regular check-ins improve team communication',
        metadata: {
          category: 'management',
          tags: ['meetings', 'communication', 'management'],
        },
      })
    }

    // Superadmin-specific recommendations
    if (context.role === 'SUPERADMIN') {
      recommendations.push({
        id: `role_superadmin_${Date.now()}`,
        type: 'role_based',
        title: 'Review System Health',
        description: 'Check system performance metrics and user activity',
        action: {
          type: 'navigate',
          target: '/superadmin/audit-logs',
          label: 'View System Metrics',
        },
        priority: 0.9,
        confidence: 0.95,
        reasoning: 'Superadmins should monitor system health regularly',
        metadata: {
          category: 'administration',
          tags: ['system', 'monitoring', 'administration'],
        },
      })

      recommendations.push({
        id: `role_superadmin_users_${Date.now()}`,
        type: 'role_based',
        title: 'Manage User Access',
        description: 'Review and update user permissions and access levels',
        action: {
          type: 'navigate',
          target: '/superadmin/panel',
          label: 'Manage Users',
        },
        priority: 0.8,
        confidence: 0.9,
        reasoning: 'Regular user access reviews maintain security',
        metadata: {
          category: 'administration',
          tags: ['users', 'security', 'administration'],
        },
      })
    }

    // General recommendations for all roles
    recommendations.push({
      id: `role_general_${Date.now()}`,
      type: 'role_based',
      title: 'Update Your Profile',
      description: 'Keep your profile information current and complete',
      action: {
        type: 'navigate',
        target: '/settings/profile',
        label: 'Edit Profile',
      },
      priority: 0.3,
      confidence: 0.7,
      reasoning: 'Updated profiles improve collaboration',
      metadata: {
        category: 'general',
        tags: ['profile', 'settings'],
      },
    })

    return recommendations
  }

  /**
   * Generate skill development recommendations
   */
  private async generateSkillRecommendations(context: UserContext): Promise<AIRecommendation[]> {
    const recommendations: AIRecommendation[] = []

    // Analyze recent activities for skill gaps
    if (context.recentActions?.length) {
      const skillGaps = this.analyzeSkillGaps(context)

      skillGaps.forEach((skill, index) => {
        recommendations.push({
          id: `skill_${skill.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}_${index}`,
          type: 'skill_development',
          title: `Improve ${skill} Skills`,
          description: `Based on your recent activities, developing ${skill} skills could enhance your performance`,
          action: {
            type: 'navigate',
            target: `/learning/skills/${skill.toLowerCase().replace(/\s+/g, '-')}`,
            label: `Learn ${skill}`,
          },
          priority: 0.7,
          confidence: 0.8,
          reasoning: `Recent activity patterns suggest ${skill} development would be beneficial`,
          metadata: {
            category: 'skill_development',
            tags: ['learning', 'skills', skill.toLowerCase()],
          },
        })
      })
    }

    return recommendations
  }

  /**
   * Generate career path recommendations
   */
  private async generateCareerRecommendations(context: UserContext): Promise<AIRecommendation[]> {
    const recommendations: AIRecommendation[] = []

    // Career progression recommendations based on role
    const nextRoles = this.getNextCareerSteps(context.role)

    nextRoles.forEach((role, index) => {
      recommendations.push({
        id: `career_${role.toLowerCase()}_${Date.now()}_${index}`,
        type: 'career_path',
        title: `Path to ${role}`,
        description: `Explore the skills and experience needed to advance to ${role}`,
        action: {
          type: 'navigate',
          target: `/career-paths/${role.toLowerCase()}`,
          label: `Explore ${role} Path`,
        },
        priority: 0.6,
        confidence: 0.75,
        reasoning: `Natural career progression from ${context.role}`,
        metadata: {
          category: 'career_development',
          tags: ['career', 'progression', role.toLowerCase()],
        },
      })
    })

    return recommendations
  }

  /**
   * Generate UI personalization recommendations
   */
  private async generateUIRecommendations(context: UserContext): Promise<AIRecommendation[]> {
    const recommendations: AIRecommendation[] = []

    // Theme recommendations based on usage patterns - ONLY for system theme users
    // Do NOT recommend theme changes for users who have already set a specific theme
    if (context.preferences?.theme === 'system') {
      recommendations.push({
        id: `ui_theme_${Date.now()}`,
        type: 'ui_personalization',
        title: 'Optimize Your Theme',
        description: 'Set a specific theme preference for a more consistent experience',
        action: {
          type: 'navigate',
          target: '/settings/platform/appearance',
          label: 'Customize Theme',
        },
        priority: 0.4,
        confidence: 0.6,
        reasoning: 'System theme can cause inconsistent appearance across different times of day',
        metadata: {
          category: 'personalization',
          tags: ['theme', 'appearance', 'settings'],
        },
      })
    }
    // Explicitly: No theme recommendations for users with specific themes (light, dark, etc.)

    // Dashboard layout recommendations (for all users)
    if (context.recentActions && context.recentActions.length > 5) {
      recommendations.push({
        id: `ui_dashboard_${Date.now()}`,
        type: 'ui_personalization',
        title: 'Customize Dashboard Layout',
        description: 'Arrange your dashboard widgets based on your usage patterns',
        action: {
          type: 'navigate',
          target: '/dashboard',
          label: 'Customize Dashboard',
        },
        priority: 0.5,
        confidence: 0.7,
        reasoning: 'Active users benefit from personalized dashboard layouts',
        metadata: {
          category: 'personalization',
          tags: ['dashboard', 'layout', 'productivity'],
        },
      })
    }

    return recommendations
  }

  /**
   * Generate content recommendations
   */
  private async generateContentRecommendations(context: UserContext): Promise<AIRecommendation[]> {
    const recommendations: AIRecommendation[] = []

    // Onboarding completion recommendations - ONLY for users who haven't completed onboarding
    // Only show onboarding recommendations if explicitly NOT completed (false or undefined)
    if (context.preferences?.onboardingCompleted !== true) {
      recommendations.push({
        id: `content_onboarding_${Date.now()}`,
        type: 'content',
        title: 'Complete Your Profile Setup',
        description: 'Finish setting up your profile to unlock personalized features',
        action: {
          type: 'navigate',
          target: '/onboarding',
          label: 'Complete Setup',
        },
        priority: 0.9,
        confidence: 0.95,
        reasoning: 'Completing onboarding improves the overall user experience',
        metadata: {
          category: 'onboarding',
          tags: ['setup', 'profile', 'getting-started'],
        },
      })
    }
    // Explicitly: No onboarding recommendations for users who have completed onboarding (onboardingCompleted === true)

    // Resource discovery recommendations (for all users)
    if (context.recentActions && context.recentActions.length > 0) {
      const hasVisitedResources = context.recentActions.some(
        action =>
          action.data?.page?.includes('/resources') || action.data?.page?.includes('/content')
      )

      if (!hasVisitedResources) {
        recommendations.push({
          id: `content_resources_${Date.now()}`,
          type: 'content',
          title: 'Explore Learning Resources',
          description: 'Discover training materials and documentation relevant to your role',
          action: {
            type: 'navigate',
            target: '/settings/content/resources',
            label: 'Browse Resources',
          },
          priority: 0.6,
          confidence: 0.8,
          reasoning:
            "Users who haven't explored resources may benefit from discovering available content",
          metadata: {
            category: 'learning',
            tags: ['resources', 'training', 'documentation'],
          },
        })
      }
    }

    return recommendations
  }

  /**
   * Generate fallback recommendations when AI is disabled or fails
   */
  private getFallbackRecommendations(
    request: RecommendationRequest,
    startTime: number
  ): RecommendationResponse {
    const fallbackRecommendations: AIRecommendation[] = [
      {
        id: `fallback_dashboard_${Date.now()}`,
        type: 'role_based',
        title: 'Explore Your Dashboard',
        description: 'Check out your personalized dashboard for updates and insights',
        action: {
          type: 'navigate',
          target: '/dashboard',
          label: 'Go to Dashboard',
        },
        priority: 0.5,
        confidence: 0.7,
        reasoning: 'Dashboard provides overview of your current status',
        metadata: {
          category: 'general',
          tags: ['dashboard', 'overview'],
        },
      },
      {
        id: `fallback_settings_${Date.now()}`,
        type: 'ui_personalization',
        title: 'Customize Your Experience',
        description: 'Personalize your settings to match your preferences',
        action: {
          type: 'navigate',
          target: '/settings',
          label: 'Open Settings',
        },
        priority: 0.4,
        confidence: 0.6,
        reasoning: 'Settings allow for basic personalization',
        metadata: {
          category: 'personalization',
          tags: ['settings', 'customization'],
        },
      },
    ]

    return {
      recommendations: fallbackRecommendations,
      confidence: 0.6,
      reasoning: 'Using fallback recommendations due to AI service unavailability',
      metadata: {
        processingTime: Date.now() - startTime,
        contextFactors: ['fallback_mode'],
        fallbackUsed: true,
      },
    }
  }

  /**
   * Helper methods
   */
  private getCacheKey(request: RecommendationRequest): string {
    // Create a stable cache key that doesn't include timestamps or random data
    const contextKey = {
      userId: request.context.userId,
      role: request.context.role,
      companyId: request.context.companyId,
      theme: request.context.preferences?.theme,
      onboardingCompleted: request.context.preferences?.onboardingCompleted,
      recentActionsCount: request.context.recentActions?.length || 0,
      hasHistoricalData: !!request.context.historicalData,
    }

    return `ai_rec_${request.userId}_${request.type || 'all'}_${JSON.stringify(contextKey)}`
  }

  private getFromCache(key: string): RecommendationResponse | null {
    const cached = this.cache.get(key)
    if (cached) {
      // Check if cache entry is still valid based on cache timestamp
      const cacheTimestamp = cached.metadata.cacheTimestamp || Date.now()
      const cacheAge = Date.now() - cacheTimestamp
      if (cacheAge < this.CACHE_TTL) {
        // Return a clean copy without the cacheTimestamp to avoid comparison issues
        const cleanMetadata = { ...cached.metadata }
        delete cleanMetadata.cacheTimestamp
        return {
          ...cached,
          metadata: cleanMetadata,
        }
      }
      // Remove expired cache entry
      this.cache.delete(key)
    }
    return null
  }

  private setCache(key: string, response: RecommendationResponse): void {
    // Store the current timestamp for cache validation
    const responseWithTimestamp = {
      ...response,
      metadata: {
        ...response.metadata,
        cacheTimestamp: Date.now(),
      },
    }
    this.cache.set(key, responseWithTimestamp)
  }

  private calculateConfidence(context: UserContext, recommendations: AIRecommendation[]): number {
    // Handle edge case of no recommendations
    if (!recommendations || recommendations.length === 0) {
      return 0.3 // Low confidence when no recommendations
    }

    // Calculate overall confidence based on context completeness and recommendation quality
    const contextCompleteness = this.assessContextCompleteness(context)
    const avgRecommendationConfidence =
      recommendations.reduce((sum, rec) => sum + rec.confidence, 0) / recommendations.length

    // Ensure we don't get NaN
    const finalConfidence = (contextCompleteness + avgRecommendationConfidence) / 2
    return Math.min(0.95, Math.max(0.1, finalConfidence)) // Ensure bounds between 0.1 and 0.95
  }

  private generateReasoning(context: UserContext, recommendations: AIRecommendation[]): string {
    const factors = this.extractContextFactors(context)
    return `Generated ${recommendations.length} recommendations based on: ${factors.join(', ')}`
  }

  private extractContextFactors(context: UserContext): string[] {
    const factors: string[] = []

    if (context.role) factors.push(`role: ${context.role}`)
    if (context.recentActions?.length)
      factors.push(`recent activity: ${context.recentActions.length} actions`)
    if (context.preferences) factors.push('user preferences')
    if (context.historicalData) factors.push('historical data')

    return factors
  }

  private assessContextCompleteness(context: UserContext): number {
    let score = 0
    const maxScore = 6 // Increased to include more factors

    if (context.role) score += 1
    if (context.recentActions?.length) score += 1
    if (context.preferences && Object.keys(context.preferences).length > 0) score += 1
    if (context.historicalData && Object.keys(context.historicalData).length > 0) score += 1
    if (context.companyId) score += 1
    if (context.userId) score += 1

    return score / maxScore
  }

  private analyzeSkillGaps(context: UserContext): string[] {
    // Simplified skill gap analysis based on role and activities
    const skillGaps: string[] = []

    if (context.role === 'MANAGER' || context.role === 'DIRECTOR') {
      skillGaps.push('Leadership', 'Team Management', 'Strategic Planning')
    }

    if (context.role === 'EMPLOYEE') {
      skillGaps.push('Technical Skills', 'Communication', 'Problem Solving')
    }

    return skillGaps.slice(0, 2) // Limit to top 2 skill gaps
  }

  private getNextCareerSteps(currentRole: string): string[] {
    const careerPaths: Record<string, string[]> = {
      EMPLOYEE: ['MANAGER', 'SPECIALIST'],
      MANAGER: ['DIRECTOR', 'SENIOR_MANAGER'],
      DIRECTOR: ['VP', 'EXECUTIVE'],
      ADMIN: ['SENIOR_ADMIN', 'DIRECTOR'],
      SUPERADMIN: ['CTO', 'VP_ENGINEERING'],
    }

    return careerPaths[currentRole] || []
  }
}

// Export singleton instance
export const aiRecommendationService = AIRecommendationService.getInstance()

// Export utility functions
export async function generateUserRecommendations(
  userId: string,
  companyId: string,
  context: UserContext,
  type?: RecommendationType,
  limit?: number
): Promise<RecommendationResponse> {
  return aiRecommendationService.generateRecommendations({
    userId,
    companyId,
    context,
    type,
    limit,
  })
}
