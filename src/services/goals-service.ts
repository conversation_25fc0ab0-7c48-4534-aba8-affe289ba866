import { Goal, KeyResult, GoalType, GoalStatus, GoalCategory, GoalTimeframe } from '@prisma/client'
import { prisma } from '@/lib/prisma'

/**
 * Input types for goal operations
 */
export interface CreateGoalInput {
  title: string
  description?: string
  type?: GoalType
  category?: GoalCategory
  timeframe?: GoalTimeframe
  targetValue?: number
  unit?: string
  targetDate?: Date
  priority?: 'low' | 'medium' | 'high'
  isPublic?: boolean
  tags?: string[]
  parentGoalId?: string
  userId: string
  companyId: string
}

export interface UpdateGoalInput {
  title?: string
  description?: string
  status?: GoalStatus
  progress?: number
  currentValue?: number
  priority?: 'low' | 'medium' | 'high'
  isPublic?: boolean
  tags?: string[]
  targetDate?: Date
}

export interface CreateKeyResultInput {
  title: string
  description?: string
  targetValue: number
  unit: string
  dueDate?: Date
  goalId: string
}

export interface UpdateKeyResultInput {
  title?: string
  description?: string
  currentValue?: number
  status?: string
  dueDate?: Date
}

export interface GoalFilters {
  status?: GoalStatus[]
  category?: GoalCategory[]
  timeframe?: GoalTimeframe[]
  userId?: string
  isPublic?: boolean
}

/**
 * Goals Service - Business logic for goals and OKR management
 */
export class GoalsService {
  /**
   * Create a new goal
   */
  static async createGoal(input: CreateGoalInput): Promise<Goal> {
    const goal = await prisma.goal.create({
      data: {
        title: input.title,
        description: input.description,
        type: input.type || 'OBJECTIVE',
        category: input.category || 'PERSONAL',
        timeframe: input.timeframe || 'QUARTERLY',
        targetValue: input.targetValue,
        unit: input.unit,
        targetDate: input.targetDate,
        priority: input.priority || 'medium',
        isPublic: input.isPublic || false,
        tags: input.tags || [],
        parentGoalId: input.parentGoalId,
        userId: input.userId,
        companyId: input.companyId,
      },
      include: {
        keyResults: true,
        subGoals: true,
        goalUpdates: true,
      },
    })

    return goal
  }

  /**
   * Get goals for a user with filtering
   */
  static async getUserGoals(
    userId: string,
    companyId: string,
    filters?: GoalFilters
  ): Promise<Goal[]> {
    const where: any = {
      userId,
      companyId,
    }

    if (filters?.status?.length) {
      where.status = { in: filters.status }
    }

    if (filters?.category?.length) {
      where.category = { in: filters.category }
    }

    if (filters?.timeframe?.length) {
      where.timeframe = { in: filters.timeframe }
    }

    const goals = await prisma.goal.findMany({
      where,
      include: {
        keyResults: true,
        subGoals: {
          include: {
            keyResults: true,
          },
        },
        goalUpdates: {
          orderBy: { createdAt: 'desc' },
          take: 3,
        },
        parentGoal: true,
      },
      orderBy: [{ priority: 'desc' }, { createdAt: 'desc' }],
    })

    return goals
  }

  /**
   * Get a specific goal by ID
   */
  static async getGoalById(
    goalId: string,
    userId: string,
    companyId: string
  ): Promise<Goal | null> {
    const goal = await prisma.goal.findFirst({
      where: {
        id: goalId,
        userId,
        companyId,
      },
      include: {
        keyResults: {
          orderBy: { createdAt: 'asc' },
        },
        subGoals: {
          include: {
            keyResults: true,
          },
        },
        goalUpdates: {
          orderBy: { createdAt: 'desc' },
        },
        parentGoal: true,
        goalCollaborators: true,
      },
    })

    return goal
  }

  /**
   * Update an existing goal
   */
  static async updateGoal(
    goalId: string,
    userId: string,
    companyId: string,
    input: UpdateGoalInput
  ): Promise<Goal> {
    // Verify goal ownership
    const existingGoal = await prisma.goal.findFirst({
      where: {
        id: goalId,
        userId,
        companyId,
      },
    })

    if (!existingGoal) {
      throw new Error('Goal not found or access denied')
    }

    const updatedGoal = await prisma.goal.update({
      where: { id: goalId },
      data: {
        ...input,
        updatedAt: new Date(),
      },
      include: {
        keyResults: true,
        subGoals: true,
        goalUpdates: true,
      },
    })

    return updatedGoal
  }

  /**
   * Delete a goal and its related data
   */
  static async deleteGoal(goalId: string, userId: string, companyId: string): Promise<void> {
    // Verify goal ownership
    const existingGoal = await prisma.goal.findFirst({
      where: {
        id: goalId,
        userId,
        companyId,
      },
    })

    if (!existingGoal) {
      throw new Error('Goal not found or access denied')
    }

    await prisma.goal.delete({
      where: { id: goalId },
    })
  }

  /**
   * Create a key result for a goal
   */
  static async createKeyResult(input: CreateKeyResultInput): Promise<KeyResult> {
    const keyResult = await prisma.keyResult.create({
      data: {
        title: input.title,
        description: input.description,
        targetValue: input.targetValue,
        unit: input.unit,
        dueDate: input.dueDate,
        goalId: input.goalId,
      },
    })

    return keyResult
  }

  /**
   * Update a key result
   */
  static async updateKeyResult(
    keyResultId: string,
    input: UpdateKeyResultInput
  ): Promise<KeyResult> {
    const updatedKeyResult = await prisma.keyResult.update({
      where: { id: keyResultId },
      data: {
        ...input,
        updatedAt: new Date(),
      },
    })

    return updatedKeyResult
  }

  /**
   * Update goal progress based on key results
   */
  static async updateGoalProgress(goalId: string): Promise<Goal> {
    const goal = await prisma.goal.findUnique({
      where: { id: goalId },
      include: { keyResults: true },
    })

    if (!goal) {
      throw new Error('Goal not found')
    }

    let totalProgress = 0
    let completedKeyResults = 0

    goal.keyResults.forEach(kr => {
      const progress = kr.targetValue > 0 ? (kr.currentValue / kr.targetValue) * 100 : 0
      totalProgress += Math.min(progress, 100)
      if (progress >= 100) completedKeyResults++
    })

    const averageProgress = goal.keyResults.length > 0 ? totalProgress / goal.keyResults.length : 0

    // Auto-update status based on progress
    let newStatus: GoalStatus = goal.status
    if (averageProgress >= 100) {
      newStatus = 'COMPLETED'
    } else if (averageProgress >= 70) {
      newStatus = 'ON_TRACK'
    } else if (averageProgress >= 30) {
      newStatus = 'IN_PROGRESS'
    } else if (averageProgress < 30 && goal.targetDate && new Date() > goal.targetDate) {
      newStatus = 'AT_RISK'
    }

    const updatedGoal = await prisma.goal.update({
      where: { id: goalId },
      data: {
        progress: Math.round(averageProgress),
        status: newStatus,
        completedAt: newStatus === 'COMPLETED' ? new Date() : null,
      },
      include: {
        keyResults: true,
        subGoals: true,
        goalUpdates: true,
      },
    })

    return updatedGoal
  }

  /**
   * Get goal statistics for a user
   */
  static async getGoalStatistics(userId: string, companyId: string) {
    const goals = await prisma.goal.findMany({
      where: { userId, companyId },
      include: { keyResults: true },
    })

    const stats = {
      total: goals.length,
      completed: goals.filter(g => g.status === 'COMPLETED').length,
      inProgress: goals.filter(g => g.status === 'IN_PROGRESS').length,
      atRisk: goals.filter(g => g.status === 'AT_RISK').length,
      averageProgress:
        goals.length > 0
          ? Math.round(goals.reduce((sum, g) => sum + g.progress, 0) / goals.length)
          : 0,
      byCategory: {} as Record<string, number>,
      byTimeframe: {} as Record<string, number>,
    }

    goals.forEach(goal => {
      stats.byCategory[goal.category] = (stats.byCategory[goal.category] || 0) + 1
      stats.byTimeframe[goal.timeframe] = (stats.byTimeframe[goal.timeframe] || 0) + 1
    })

    return stats
  }
}
