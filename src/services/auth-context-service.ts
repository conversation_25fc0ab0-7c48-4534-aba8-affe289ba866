/**
 * Authentication Context Service
 *
 * Centralized service for managing authentication state across the application.
 * Integrates with session management and RBAC systems to provide a unified
 * authentication interface for all application components.
 */

import { redis } from '@/lib/redis'
import { prisma } from '@/lib/prisma'
import { validateAccessToken, revokeSession, revokeAllUserSessions } from '@/lib/auth/session'
import { rbacService } from '@/services/rbac-service'
import { hasFeature } from '@/lib/features'
import { Role } from '@prisma/client'
import { AuthUser } from '@/lib/auth/types'

// Redis cache configuration
const AUTH_CONTEXT_PREFIX = 'auth:context:'
const CONTEXT_CACHE_TTL = 60 * 10 // 10 minutes

/**
 * Authentication context interface
 */
export interface AuthContext {
  user: AuthUser | null
  isAuthenticated: boolean
  roles: Role[]
  permissions: string[]
  companyId?: string
  sessionId?: string
  deviceInfo?: {
    userAgent?: string
    ip?: string
    deviceId?: string
  }
  lastActive?: Date
  // Placeholder for future context-aware Gen AI enhancements
  contextData?: {
    recentActivity?: string[]
    preferences?: Record<string, any>
    behavioralTrends?: Record<string, any>
  }
}

/**
 * Login credentials interface
 */
export interface LoginCredentials {
  email: string
  password: string
  deviceInfo?: {
    userAgent?: string
    ip?: string
    deviceId?: string
  }
  rememberMe?: boolean
}

/**
 * Authentication context service for managing auth state across the application
 */
class AuthContextService {
  /**
   * Get authentication context for a user
   *
   * @param userId User ID
   * @param sessionId Current session ID
   * @returns Authentication context
   */
  async getAuthContext(userId: string, sessionId?: string): Promise<AuthContext> {
    try {
      // Check cache first
      const cacheKey = `${AUTH_CONTEXT_PREFIX}${userId}`
      const cachedContext = await redis.get(cacheKey)

      if (cachedContext) {
        const parsedContext = JSON.parse(cachedContext as string) as AuthContext

        // Don't use cache if sessionId is provided and doesn't match
        if (!sessionId || parsedContext.sessionId === sessionId) {
          return parsedContext
        }
      }

      // Cache miss or session mismatch, get from database
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          companyId: true,
        },
      })

      if (!user) {
        return {
          user: null,
          isAuthenticated: false,
          roles: [],
          permissions: [],
        }
      }

      // Get session info if sessionId is provided
      let session = null
      if (sessionId) {
        session = await prisma.session.findUnique({
          where: { id: sessionId },
          select: {
            id: true,
            lastActive: true,
            deviceInfo: true,
          },
        })
      }

      // Get permissions for the user's role
      const permissions = await rbacService.getUserPermissions(userId)

      // Build the auth context
      const authContext: AuthContext = {
        user: {
          id: user.id,
          email: user.email,
          name: user.name || undefined,
          role: user.role,
        },
        isAuthenticated: true,
        roles: [user.role], // Primary role
        permissions: permissions.map(p => p.permission),
        companyId: user.companyId || undefined,
        sessionId: session?.id,
        lastActive: session?.lastActive,
        deviceInfo: session?.deviceInfo as any,
      }

      // Check if context-awareness is enabled for the company
      const contextAwarenessEnabled = user.companyId
        ? await hasFeature(user.companyId, 'contextAwareness')
        : false

      // Add context data if enabled
      if (contextAwarenessEnabled) {
        const userContext = await prisma.userContext.findUnique({
          where: { userId: user.id },
        })

        if (userContext) {
          authContext.contextData = {
            recentActivity: (userContext.recentActions as any)?.activities || [],
            preferences: userContext.preferences as any,
            behavioralTrends: (userContext.historicalData as any)?.trends || {},
          }
        }
      }

      // Cache the context
      await redis.set(cacheKey, JSON.stringify(authContext), 'EX', CONTEXT_CACHE_TTL)

      return authContext
    } catch {
      // console.error('Error getting auth context:', error)

      // Return unauthenticated context on error
      return {
        user: null,
        isAuthenticated: false,
        roles: [],
        permissions: [],
      }
    }
  }

  /**
   * Login user with credentials
   *
   * @param credentials Login credentials
   * @returns Authentication result with tokens and context
   */
  async login(credentials: LoginCredentials): Promise<{
    success: boolean
    message: string
    accessToken?: string
    refreshToken?: string
    context?: AuthContext
  }> {
    try {
      const { email, password, deviceInfo, rememberMe } = credentials

      // TODO: Fix authentication integration
      // const authResult = await sessionLogin(email, password, req)
      const authResult = { success: false, message: 'Authentication temporarily disabled' }

      if (!authResult.success) {
        return {
          success: false,
          message: authResult.message || 'Authentication failed',
        }
      }

      // Get auth context for the authenticated user
      const context = await this.getAuthContext(authResult.userId as string, authResult.sessionId)

      return {
        success: true,
        message: 'Authentication successful',
        accessToken: authResult.accessToken,
        refreshToken: authResult.refreshToken,
        context,
      }
    } catch {
      // console.error('Login error:', error)
      return {
        success: false,
        message: 'An error occurred during login',
      }
    }
  }

  /**
   * Logout user from current session
   *
   * @param accessToken Current access token
   * @returns Logout result
   */
  async logout(accessToken: string): Promise<{
    success: boolean
    message: string
  }> {
    try {
      // Validate the token first
      const tokenPayload = await validateAccessToken(accessToken)

      if (!tokenPayload) {
        return {
          success: false,
          message: 'Invalid token',
        }
      }

      // Revoke the session
      await revokeSession(tokenPayload.sessionId)

      // Invalidate cache
      const cacheKey = `${AUTH_CONTEXT_PREFIX}${tokenPayload.id}`
      await redis.del(cacheKey)

      return {
        success: true,
        message: 'Logout successful',
      }
    } catch {
      // console.error('Logout error:', error)
      return {
        success: false,
        message: 'An error occurred during logout',
      }
    }
  }

  /**
   * Logout user from all sessions
   *
   * @param userId User ID
   * @returns Logout result
   */
  async logoutAll(userId: string): Promise<{
    success: boolean
    message: string
    sessionsRevoked?: number
  }> {
    try {
      // Revoke all sessions for the user
      const result = await revokeAllUserSessions(userId)

      // Invalidate cache
      const cacheKey = `${AUTH_CONTEXT_PREFIX}${userId}`
      await redis.del(cacheKey)

      return {
        success: true,
        message: 'All sessions have been terminated',
        sessionsRevoked: result.count,
      }
    } catch {
      // console.error('Logout all error:', error)
      return {
        success: false,
        message: 'An error occurred while terminating sessions',
      }
    }
  }

  /**
   * Validate access token and return authentication context
   *
   * @param accessToken Access token to validate
   * @returns Authentication context if token is valid
   */
  async validateToken(accessToken: string): Promise<{
    valid: boolean
    context?: AuthContext
    message?: string
  }> {
    try {
      const tokenPayload = await validateAccessToken(accessToken)

      if (!tokenPayload) {
        return {
          valid: false,
          message: 'Invalid or expired token',
        }
      }

      // Get auth context for the user
      const context = await this.getAuthContext(tokenPayload.id, tokenPayload.sessionId)

      return {
        valid: true,
        context,
      }
    } catch {
      // console.error('Token validation error:', error)
      return {
        valid: false,
        message: 'Error validating token',
      }
    }
  }

  /**
   * Check if a user has a specific permission
   *
   * @param userId User ID
   * @param permission Permission to check
   * @returns Whether the user has the permission
   */
  async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const context = await this.getAuthContext(userId)

      if (!context.isAuthenticated || !context.permissions) {
        return false
      }

      return context.permissions.includes(permission)
    } catch {
      // console.error('Permission check error:', error)
      return false
    }
  }

  /**
   * Check if user has context-aware permission
   *
   * @param userId User ID
   * @param resource Resource to access
   * @param action Action to perform
   * @param contextData Additional context data
   * @returns Whether the user has context-aware permission
   */
  async hasContextPermission(
    userId: string,
    resource: string,
    action: string,
    contextData?: unknown
  ): Promise<{
    allowed: boolean
    message: string
  }> {
    try {
      // Use RBAC service for context-aware permission check
      return await rbacService.checkContextPermission(userId, resource, action, contextData)
    } catch {
      // console.error('Context permission check error:', error)
      return {
        allowed: false,
        message: 'Error checking context permission',
      }
    }
  }

  /**
   * Update user's recent activity for context-awareness
   *
   * @param userId User ID
   * @param activity Activity to record
   */
  async recordActivity(userId: string, activity: string): Promise<void> {
    try {
      // Check if context-awareness is enabled for the user
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true },
      })

      if (!user?.companyId) return

      const contextAwarenessEnabled = await hasFeature(user.companyId, 'contextAwareness')

      if (!contextAwarenessEnabled) return

      // Get or create user context
      let userContext = await prisma.userContext.findUnique({
        where: { userId },
      })

      if (!userContext) {
        // Create new user context
        userContext = await prisma.userContext.create({
          data: {
            userId,
            companyId: user.companyId,
            recentActions: { activities: [activity] },
          },
        })
      } else {
        // Update existing user context
        const recentActions = (userContext.recentActions as any) || { activities: [] }
        const activities = recentActions.activities || []

        // Add new activity and keep only the most recent ones
        activities.unshift({
          activity,
          timestamp: new Date().toISOString(),
        })

        // Keep only last 20 activities
        if (activities.length > 20) {
          activities.pop()
        }

        await prisma.userContext.update({
          where: { userId },
          data: {
            recentActions: { ...recentActions, activities },
            updatedAt: new Date(),
          },
        })
      }

      // Invalidate cache
      const cacheKey = `${AUTH_CONTEXT_PREFIX}${userId}`
      await redis.del(cacheKey)
    } catch {
      // console.error('Error recording activity:', error)
    }
  }

  /**
   * Invalidate auth context cache for a user
   *
   * @param userId User ID
   */
  async invalidateCache(userId: string): Promise<void> {
    try {
      const cacheKey = `${AUTH_CONTEXT_PREFIX}${userId}`
      await redis.del(cacheKey)
    } catch {
      // console.error('Error invalidating auth context cache:', error)
    }
  }
}

export const authContextService = new AuthContextService()
