/**
 * Analytics Service - High-Performance Behavioral Analytics
 *
 * Features:
 * - Real-time event tracking (25ms write latency target)
 * - Batch processing for aggregations
 * - AI training data preparation
 * - Performance monitoring integration
 * - Sentry integration for error tracking
 */

import { PrismaClient } from '@prisma/client'
// import * as Sentry from '@sentry/nextjs' // TODO: Add Sentry integration
import { Redis } from 'ioredis'

// Types for analytics events
export interface AnalyticsEvent {
  userId: string
  companyId: string
  sessionId: string
  eventType: string
  action: string
  target: string
  context: Record<string, unknown>
  payload: Record<string, unknown>
  timestamp?: Date
}

export interface PerformanceMetric {
  endpoint: string
  method: string
  userId?: string
  companyId?: string
  responseTime: number
  statusCode: number
  payloadSize?: number
  queryTime?: number
  cacheHit: boolean
  errorType?: string
}

export interface SessionData {
  userId: string
  companyId: string
  sessionId: string
  deviceInfo: Record<string, unknown>
  startTime?: Date
  pageCount?: number
  eventCount?: number
  engagementScore?: number
}

export interface MLFeatureVector {
  userId: string
  companyId: string
  featureSet: string
  features: Record<string, number>
  labels?: Record<string, unknown>
}

// Analytics client singleton
let analyticsClient: PrismaClient | null = null
let redisClient: Redis | null = null

function getAnalyticsClient(): PrismaClient {
  if (!analyticsClient) {
    const analyticsUrl = process.env.ANALYTICS_DATABASE_URL || process.env.DATABASE_URL
    if (!analyticsUrl) {
      throw new Error('ANALYTICS_DATABASE_URL or DATABASE_URL must be set')
    }

    analyticsClient = new PrismaClient({
      datasources: {
        db: { url: analyticsUrl },
      },
    })
  }
  return analyticsClient
}

function getRedisClient(): Redis {
  if (!redisClient) {
    redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379')
  }
  return redisClient
}

export class AnalyticsService {
  private client: PrismaClient
  private redis: Redis
  private batchQueue: AnalyticsEvent[] = []
  private batchSize = 100
  private batchTimeout = 5000 // 5 seconds
  private batchTimer: NodeJS.Timeout | null = null

  constructor() {
    this.client = getAnalyticsClient()
    this.redis = getRedisClient()
    this.startBatchProcessor()
  }

  /**
   * Track a single event - Real-time processing for critical events
   * Target: <25ms write latency
   */
  async trackEvent(event: AnalyticsEvent): Promise<void> {
    const startTime = Date.now()

    try {
      // Validate required fields
      this.validateEvent(event)

      // Determine processing strategy
      const isCriticalEvent = this.isCriticalEvent(event.eventType)

      if (isCriticalEvent) {
        // Real-time processing for critical events
        await this.processEventRealTime(event)
      } else {
        // Batch processing for analytics events
        this.addToBatch(event)
      }

      // Track performance
      const responseTime = Date.now() - startTime
      this.trackPerformanceMetric({
        endpoint: 'analytics/track-event',
        method: 'POST',
        userId: event.userId,
        companyId: event.companyId,
        responseTime,
        statusCode: 200,
        cacheHit: false,
      })

      // Log success if response time exceeds target
      if (responseTime > 25) {
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.warn(
              `[ANALYTICS_PERFORMANCE] Event tracking exceeded 25ms target: ${responseTime}ms`,
              {
                eventType: event.eventType,
                target: '25ms',
                actual: `${responseTime}ms`,
              }
            )
      }
    } catch {
      const responseTime = Date.now() - startTime

      // Track error performance
      this.trackPerformanceMetric({
        endpoint: 'analytics/track-event',
        method: 'POST',
        userId: event.userId,
        companyId: event.companyId,
        responseTime,
        statusCode: 500,
        cacheHit: false,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
      })

      // Log to Sentry
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] trackEvent error:', error, {
            event: this.sanitizeEventForLogging(event),
            responseTime,
          })

      throw error
    }
  }

  /**
   * Track multiple events in batch - Optimized for high throughput
   */
  async trackEventsBatch(events: AnalyticsEvent[]): Promise<void> {
    const startTime = Date.now()

    try {
      // Validate all events
      events.forEach(event => this.validateEvent(event))

      // Process in batches to avoid memory issues
      const batchSize = 1000
      for (let i = 0; i < events.length; i += batchSize) {
        const batch = events.slice(i, i + batchSize)
        await this.client.eventStream.createMany({
          data: batch.map(event => ({
            ...event,
            timestamp: event.timestamp || new Date(),
          })),
        })
      }

      const responseTime = Date.now() - startTime
      const eventsPerMinute = (events.length / responseTime) * 60000

      // Track batch performance
      this.trackPerformanceMetric({
        endpoint: 'analytics/track-events-batch',
        method: 'POST',
        responseTime,
        statusCode: 200,
        cacheHit: false,
        payloadSize: events.length,
      })

      // Log performance metrics
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(`[ANALYTICS_BATCH] Processed ${events.length} events in ${responseTime}ms`, {
            eventsPerMinute: Math.round(eventsPerMinute),
            target: '10000+/minute',
            performance: eventsPerMinute > 10000 ? 'GOOD' : 'NEEDS_OPTIMIZATION',
          })
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] trackEventsBatch error:', error, {
            eventCount: events.length,
          })
      throw error
    }
  }

  /**
   * Track performance metrics for API optimization
   */
  async trackPerformanceMetric(metric: PerformanceMetric): Promise<void> {
    try {
      await this.client.performanceMetric.create({
        data: {
          ...metric,
          timestamp: new Date(),
        },
      })

      // Cache recent metrics for real-time monitoring
      const cacheKey = `perf:${metric.endpoint}:recent`
      await this.redis.lpush(cacheKey, JSON.stringify(metric))
      await this.redis.ltrim(cacheKey, 0, 99) // Keep last 100 metrics
      await this.redis.expire(cacheKey, 3600) // 1 hour TTL
    } catch {
      // Don't throw on performance tracking errors to avoid cascading failures
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_PERFORMANCE] Failed to track performance metric:', error)
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] trackPerformanceMetric error:', error)
    }
  }

  /**
   * Start or update user session
   */
  async updateSession(sessionData: SessionData): Promise<void> {
    try {
      await this.client.userSession.upsert({
        where: { sessionId: sessionData.sessionId },
        create: {
          ...sessionData,
          startTime: sessionData.startTime || new Date(),
          isActive: true,
        },
        update: {
          pageCount: sessionData.pageCount,
          eventCount: sessionData.eventCount,
          engagementScore: sessionData.engagementScore,
          endTime: null, // Reset end time if session is active
          isActive: true,
        },
      })
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] updateSession error:', error)
      throw error
    }
  }

  /**
   * End user session
   */
  async endSession(sessionId: string): Promise<void> {
    try {
      const session = await this.client.userSession.findUnique({
        where: { sessionId },
      })

      if (session && session.startTime) {
        const duration = Math.floor((Date.now() - session.startTime.getTime()) / 1000)

        await this.client.userSession.update({
          where: { sessionId },
          data: {
            endTime: new Date(),
            duration,
            isActive: false,
          },
        })
      }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] endSession error:', error)
      throw error
    }
  }

  /**
   * Generate ML feature vectors for AI training
   */
  async generateFeatureVector(data: MLFeatureVector): Promise<void> {
    try {
      await this.client.mLFeatureVector.create({
        data: {
          ...data,
          timestamp: new Date(),
        },
      })
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] generateFeatureVector error:', error)
      throw error
    }
  }

  /**
   * Get performance insights for API optimization
   */
  async getPerformanceInsights(
    companyId: string,
    timeRange: 'hour' | 'day' | 'week' = 'hour'
  ): Promise<Record<string, unknown>> {
    try {
      const since = new Date()
      switch (timeRange) {
        case 'hour':
          since.setHours(since.getHours() - 1)
          break
        case 'day':
          since.setDate(since.getDate() - 1)
          break
        case 'week':
          since.setDate(since.getDate() - 7)
          break
      }

      const metrics = await this.client.performanceMetric.findMany({
        where: {
          companyId,
          timestamp: { gte: since },
        },
        orderBy: { timestamp: 'desc' },
      })

      // Aggregate insights
      const insights = this.analyzePerformanceMetrics(metrics)
      return insights
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] getPerformanceInsights error:', error)
      throw error
    }
  }

  /**
   * Get user behavior patterns for AI training
   */
  async getUserBehaviorPatterns(userId: string, limit: number = 100): Promise<unknown[]> {
    try {
      const events = await this.client.eventStream.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' },
        take: limit,
      })

      // Generate behavioral patterns
      const patterns = this.analyzeBehaviorPatterns(events)
      return patterns
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ANALYTICS_SERVICE] getUserBehaviorPatterns error:', error)
      throw error
    }
  }

  // Private methods

  private validateEvent(event: AnalyticsEvent): void {
    const required = ['userId', 'companyId', 'sessionId', 'eventType', 'action', 'target']
    for (const field of required) {
      if (!event[field as keyof AnalyticsEvent]) {
        throw new Error(`Missing required field: ${field}`)
      }
    }
  }

  private isCriticalEvent(eventType: string): boolean {
    const criticalEvents = [
      'error',
      'security_event',
      'payment',
      'user_signup',
      'user_login',
      'feature_flag_change',
    ]
    return criticalEvents.includes(eventType)
  }

  private async processEventRealTime(event: AnalyticsEvent): Promise<void> {
    await this.client.eventStream.create({
      data: {
        ...event,
        timestamp: event.timestamp || new Date(),
      },
    })
  }

  private addToBatch(event: AnalyticsEvent): void {
    this.batchQueue.push(event)

    if (this.batchQueue.length >= this.batchSize) {
      this.processBatch()
    }
  }

  private startBatchProcessor(): void {
    this.batchTimer = setInterval(() => {
      if (this.batchQueue.length > 0) {
        this.processBatch()
      }
    }, this.batchTimeout)
  }

  private async processBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return

    const batch = [...this.batchQueue]
    this.batchQueue = []

    try {
      await this.client.eventStream.createMany({
        data: batch.map(event => ({
          ...event,
          timestamp: event.timestamp || new Date(),
        })),
      })
    } catch {
      // Re-queue failed events
      this.batchQueue.unshift(...batch)
      throw error
    }
  }

  private sanitizeEventForLogging(event: AnalyticsEvent): Record<string, unknown> {
    // Remove sensitive data for logging
    const sanitized = { ...event }
    if (sanitized.payload?.password) delete sanitized.payload.password
    if (sanitized.payload?.token) delete sanitized.payload.token
    return sanitized
  }

  private analyzePerformanceMetrics(metrics: unknown[]): Record<string, unknown> {
    if (metrics.length === 0) return {}

    const byEndpoint = metrics.reduce((acc, metric) => {
      if (!acc[metric.endpoint]) {
        acc[metric.endpoint] = []
      }
      acc[metric.endpoint].push(metric)
      return acc
    }, {})

    const insights = {}
    for (const [endpoint, endpointMetrics] of Object.entries(byEndpoint)) {
      const responseTimes = (endpointMetrics as any[]).map(m => m.responseTime)
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      const p95ResponseTime = responseTimes.sort((a, b) => a - b)[
        Math.floor(responseTimes.length * 0.95)
      ]

      insights[endpoint] = {
        avgResponseTime: Math.round(avgResponseTime),
        p95ResponseTime: Math.round(p95ResponseTime),
        requestCount: (endpointMetrics as any[]).length,
        errorRate:
          (endpointMetrics as any[]).filter(m => m.statusCode >= 400).length /
          (endpointMetrics as any[]).length,
        cacheHitRate:
          (endpointMetrics as any[]).filter(m => m.cacheHit).length /
          (endpointMetrics as any[]).length,
        needsOptimization: avgResponseTime > 100 || p95ResponseTime > 200,
      }
    }

    return insights
  }

  private analyzeBehaviorPatterns(events: unknown[]): unknown[] {
    // Simple pattern analysis - can be enhanced with ML
    const patterns = []

    // Page navigation patterns
    const pageEvents = events.filter(e => e.eventType === 'page_visit')
    if (pageEvents.length > 1) {
      const sequence = pageEvents.slice(0, 5).map(e => e.target)
      patterns.push({
        type: 'navigation',
        pattern: sequence,
        frequency: 1,
        confidence: 0.8,
      })
    }

    // Feature usage patterns
    const featureEvents = events.filter(e => e.eventType === 'feature_usage')
    const featureUsage = featureEvents.reduce((acc, event) => {
      acc[event.target] = (acc[event.target] || 0) + 1
      return acc
    }, {})

    if (Object.keys(featureUsage).length > 0) {
      patterns.push({
        type: 'feature_usage',
        pattern: featureUsage,
        frequency: featureEvents.length,
        confidence: 0.9,
      })
    }

    return patterns
  }

  // Cleanup
  async disconnect(): Promise<void> {
    if (this.batchTimer) {
      clearInterval(this.batchTimer)
    }

    // Process remaining batch
    if (this.batchQueue.length > 0) {
      await this.processBatch()
    }

    await this.client.$disconnect()
    this.redis.disconnect()
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService()
