/**
 * Enhanced Analytics Service - High-Performance Behavioral Analytics
 *
 * Features:
 * - Real-time event tracking (25-50ms write latency target)
 * - Batch processing for high-volume events (10,000+ events/minute)
 * - Time-series data optimized for analytics queries
 * - Data retention and compliance management
 * - AI training data preparation
 */

import { getAnalyticsDb, executeQuery, initAnalyticsDb } from '@/lib/analytics/analytics-db'
import { EventBus } from '@/lib/analytics/event-bus'
import { Redis } from 'ioredis'

// Types for analytics events
export interface AnalyticsEvent {
  userId: string
  companyId: string
  sessionId: string
  eventType: string
  action: string
  target: string
  context: Record<string, unknown>
  payload: Record<string, unknown>
  timestamp?: Date
}

export interface PerformanceMetric {
  endpoint: string
  method: string
  userId?: string
  companyId?: string
  responseTime: number
  statusCode: number
  payloadSize?: number
  queryTime?: number
  cacheHit: boolean
  errorType?: string
}

export interface SessionData {
  userId: string
  companyId: string
  sessionId: string
  deviceInfo: Record<string, unknown>
  startTime?: Date
  endTime?: Date
  pageCount?: number
  eventCount?: number
  engagementScore?: number
  isActive?: boolean
}

export interface PageNavigationData {
  userId: string
  companyId: string
  sessionId: string
  fromPage?: string
  toPage: string
  referrer?: string
  duration?: number
}

export interface FeatureInteractionData {
  userId: string
  companyId: string
  sessionId: string
  feature: string
  action: string
  element: string
  value?: string
  context: Record<string, unknown>
  outcome?: string
  duration?: number
}

// Redis client for caching
let redisClient: Redis | null = null

function getRedisClient(): Redis {
  if (!redisClient) {
    redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379')
  }
  return redisClient
}

// Initialize analytics database
initAnalyticsDb().catch(error => {
  if (process.env.NODE_ENV === 'development')
    if (process.env.NODE_ENV === 'development')
      console.error('[ENHANCED_ANALYTICS] Failed to initialize analytics database:', error)
})

export class EnhancedAnalyticsService {
  /**
   * Track a single event with 25-50ms target latency
   * @param event Analytics event to track
   */
  async trackEvent(event: AnalyticsEvent): Promise<void> {
    const startTime = Date.now()

    try {
      // Validate required fields
      this.validateEvent(event)

      // Get analytics client
      const client = getAnalyticsDb()

      // Determine if this is a critical event that needs immediate processing
      const isCriticalEvent = this.isCriticalEvent(event.eventType)

      if (isCriticalEvent) {
        // Process critical events immediately
        await executeQuery(
          () =>
            client.eventStream.create({
              data: {
                userId: event.userId,
                companyId: event.companyId,
                sessionId: event.sessionId,
                eventType: event.eventType,
                action: event.action,
                target: event.target,
                context: event.context,
                payload: event.payload,
                timestamp: event.timestamp || new Date(),
              },
            }),
          { label: 'track_critical_event' }
        )
      } else {
        // Use event bus for non-critical events (async processing)
        await EventBus.publish('analytics:event', event)
      }

      // Track performance of this operation
      const responseTime = Date.now() - startTime

      // Log if response time exceeds target
      if (responseTime > 50) {
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.warn(
              `[ENHANCED_ANALYTICS] Event tracking exceeded 50ms target: ${responseTime}ms`,
              {
                eventType: event.eventType,
                target: '50ms',
                actual: `${responseTime}ms`,
              }
            )
      }
    } catch {
      const responseTime = Date.now() - startTime
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] trackEvent error:', error, {
            event: this.sanitizeEventForLogging(event),
            responseTime,
          })
      throw error
    }
  }

  /**
   * Track multiple events in batch - optimized for high throughput
   * @param events Array of analytics events
   */
  async trackEventsBatch(events: AnalyticsEvent[]): Promise<void> {
    const startTime = Date.now()

    try {
      // Validate all events
      events.forEach(event => this.validateEvent(event))

      // Get analytics client
      const client = getAnalyticsDb()

      // Process in batches to avoid memory issues
      const batchSize = 1000
      for (let i = 0; i < events.length; i += batchSize) {
        const batch = events.slice(i, i + batchSize)

        await executeQuery(
          () =>
            client.eventStream.createMany({
              data: batch.map(event => ({
                userId: event.userId,
                companyId: event.companyId,
                sessionId: event.sessionId,
                eventType: event.eventType,
                action: event.action,
                target: event.target,
                context: event.context,
                payload: event.payload,
                timestamp: event.timestamp || new Date(),
              })),
            }),
          { label: 'track_events_batch' }
        )
      }

      const responseTime = Date.now() - startTime
      const eventsPerMinute = (events.length / responseTime) * 60000

      // Log performance metrics
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(
            `[ENHANCED_ANALYTICS] Processed ${events.length} events in ${responseTime}ms`,
            {
              eventsPerMinute: Math.round(eventsPerMinute),
              target: '10000+/minute',
              performance: eventsPerMinute > 10000 ? 'GOOD' : 'NEEDS_OPTIMIZATION',
            }
          )
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] trackEventsBatch error:', error, {
            eventCount: events.length,
          })
      throw error
    }
  }

  /**
   * Track performance metrics for API optimization
   * @param metric Performance metric to track
   */
  async trackPerformanceMetric(metric: PerformanceMetric): Promise<void> {
    try {
      const client = getAnalyticsDb()

      await executeQuery(
        () =>
          client.performanceMetric.create({
            data: {
              endpoint: metric.endpoint,
              method: metric.method,
              userId: metric.userId,
              companyId: metric.companyId,
              responseTime: metric.responseTime,
              statusCode: metric.statusCode,
              payloadSize: metric.payloadSize,
              queryTime: metric.queryTime,
              cacheHit: metric.cacheHit,
              errorType: metric.errorType,
            },
          }),
        { label: 'track_performance_metric' }
      )
    } catch {
      // Don't throw on performance tracking errors to avoid cascading failures
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] Failed to track performance metric:', error)
    }
  }

  /**
   * Start or update user session
   * @param sessionData Session data to update
   */
  async updateSession(sessionData: SessionData): Promise<void> {
    try {
      const client = getAnalyticsDb()

      await executeQuery(
        () =>
          client.userSession.upsert({
            where: {
              sessionId: sessionData.sessionId,
            },
            update: {
              endTime: sessionData.endTime,
              pageCount: sessionData.pageCount,
              eventCount: sessionData.eventCount,
              engagementScore: sessionData.engagementScore,
              isActive: sessionData.isActive ?? true,
            },
            create: {
              userId: sessionData.userId,
              companyId: sessionData.companyId,
              sessionId: sessionData.sessionId,
              deviceInfo: sessionData.deviceInfo,
              startTime: sessionData.startTime || new Date(),
              pageCount: sessionData.pageCount || 0,
              eventCount: sessionData.eventCount || 0,
              engagementScore: sessionData.engagementScore,
              isActive: sessionData.isActive ?? true,
            },
          }),
        { label: 'update_session' }
      )
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] updateSession error:', error)
      throw error
    }
  }

  /**
   * End a user session
   * @param sessionId Session ID to end
   */
  async endSession(sessionId: string): Promise<void> {
    try {
      const client = getAnalyticsDb()
      const now = new Date()

      const session = await executeQuery(
        () =>
          client.userSession.findUnique({
            where: { sessionId },
          }),
        { label: 'find_session' }
      )

      if (!session) {
        throw new Error(`Session ${sessionId} not found`)
      }

      // Calculate duration in seconds
      const duration = Math.floor((now.getTime() - session.startTime.getTime()) / 1000)

      await executeQuery(
        () =>
          client.userSession.update({
            where: { sessionId },
            data: {
              endTime: now,
              duration,
              isActive: false,
            },
          }),
        { label: 'end_session' }
      )
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] endSession error:', error)
      throw error
    }
  }

  /**
   * Track page navigation
   * @param navigationData Page navigation data
   */
  async trackPageNavigation(navigationData: PageNavigationData): Promise<void> {
    try {
      const client = getAnalyticsDb()

      await executeQuery(
        () =>
          client.pageNavigation.create({
            data: {
              userId: navigationData.userId,
              companyId: navigationData.companyId,
              sessionId: navigationData.sessionId,
              fromPage: navigationData.fromPage,
              toPage: navigationData.toPage,
              referrer: navigationData.referrer,
              duration: navigationData.duration,
            },
          }),
        { label: 'track_page_navigation' }
      )

      // Update session page count
      await executeQuery(
        () =>
          client.userSession.updateMany({
            where: { sessionId: navigationData.sessionId },
            data: {
              pageCount: {
                increment: 1,
              },
            },
          }),
        { label: 'update_session_page_count' }
      )
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] trackPageNavigation error:', error)
      throw error
    }
  }

  /**
   * Track feature interaction
   * @param interactionData Feature interaction data
   */
  async trackFeatureInteraction(interactionData: FeatureInteractionData): Promise<void> {
    try {
      const client = getAnalyticsDb()

      await executeQuery(
        () =>
          client.featureInteraction.create({
            data: {
              userId: interactionData.userId,
              companyId: interactionData.companyId,
              sessionId: interactionData.sessionId,
              feature: interactionData.feature,
              action: interactionData.action,
              element: interactionData.element,
              value: interactionData.value,
              context: interactionData.context,
              outcome: interactionData.outcome,
              duration: interactionData.duration,
            },
          }),
        { label: 'track_feature_interaction' }
      )
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] trackFeatureInteraction error:', error)
      throw error
    }
  }

  /**
   * Track error event
   * @param userId User ID
   * @param companyId Company ID
   * @param sessionId Session ID
   * @param errorType Error type
   * @param message Error message
   * @param stack Error stack trace
   * @param context Error context
   * @param severity Error severity
   */
  async trackError(
    userId: string | null,
    companyId: string | null,
    sessionId: string | null,
    errorType: string,
    message: string,
    stack?: string,
    context: Record<string, unknown> = {},
    severity: string = 'medium'
  ): Promise<void> {
    try {
      const client = getAnalyticsDb()

      await executeQuery(
        () =>
          client.errorEvent.create({
            data: {
              userId,
              companyId,
              sessionId,
              errorType,
              message,
              stack,
              context,
              severity,
            },
          }),
        { label: 'track_error' }
      )

      // Update error count in session if session ID is provided
      if (sessionId) {
        await executeQuery(
          () =>
            client.userSession.updateMany({
              where: { sessionId },
              data: {
                errorCount: {
                  increment: 1,
                },
              },
            }),
          { label: 'update_session_error_count' }
        )
      }
    } catch {
      // Log but don't throw to prevent cascading failures
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] trackError failed:', error)
    }
  }

  /**
   * Generate daily aggregates for reporting
   * @param date Date to aggregate
   * @param companyId Company ID
   */
  async generateDailyAggregates(date: Date, companyId: string): Promise<void> {
    try {
      const client = getAnalyticsDb()
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)

      const endOfDay = new Date(date)
      endOfDay.setHours(23, 59, 59, 999)

      // Aggregate event counts by type
      const eventCounts = await executeQuery(
        () =>
          client.eventStream.groupBy({
            by: ['eventType'],
            where: {
              companyId,
              timestamp: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
            _count: {
              id: true,
            },
          }),
        { label: 'aggregate_events' }
      )

      // Aggregate unique users
      const uniqueUsers = await executeQuery(
        () =>
          client.eventStream.groupBy({
            by: ['companyId'],
            where: {
              companyId,
              timestamp: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
            _count: {
              _all: true,
            },
            _distinct: ['userId'],
          }),
        { label: 'aggregate_users' }
      )

      // Create aggregates for each event type
      for (const eventCount of eventCounts) {
        await executeQuery(
          () =>
            client.dailyAggregate.upsert({
              where: {
                date_companyId_metricType: {
                  date: startOfDay,
                  companyId,
                  metricType: `event_count_${eventCount.eventType}`,
                },
              },
              update: {
                value: eventCount._count.id,
              },
              create: {
                date: startOfDay,
                companyId,
                metricType: `event_count_${eventCount.eventType}`,
                value: eventCount._count.id,
              },
            }),
          { label: 'create_daily_aggregate' }
        )
      }

      // Create aggregate for unique users
      if (uniqueUsers.length > 0) {
        const distinctUserCount = await executeQuery(
          () => client.$queryRaw`
            SELECT COUNT(DISTINCT "userId") 
            FROM "event_stream" 
            WHERE "companyId" = ${companyId} 
            AND "timestamp" >= ${startOfDay} 
            AND "timestamp" <= ${endOfDay}
          `,
          { label: 'count_distinct_users' }
        )

        await executeQuery(
          () =>
            client.dailyAggregate.upsert({
              where: {
                date_companyId_metricType: {
                  date: startOfDay,
                  companyId,
                  metricType: 'unique_users',
                },
              },
              update: {
                value: Number(distinctUserCount[0].count),
              },
              create: {
                date: startOfDay,
                companyId,
                metricType: 'unique_users',
                value: Number(distinctUserCount[0].count),
              },
            }),
          { label: 'create_user_aggregate' }
        )
      }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] generateDailyAggregates error:', error)
      throw error
    }
  }

  /**
   * Purge old analytics data based on retention policy
   * @param retentionDays Number of days to retain data
   */
  async purgeOldData(retentionDays: number = 90): Promise<void> {
    try {
      const client = getAnalyticsDb()
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

      // Purge old events
      const deletedEvents = await executeQuery(
        () =>
          client.eventStream.deleteMany({
            where: {
              timestamp: {
                lt: cutoffDate,
              },
            },
          }),
        { label: 'purge_events', timeout: 30000 }
      )

      // Purge old sessions
      const deletedSessions = await executeQuery(
        () =>
          client.userSession.deleteMany({
            where: {
              startTime: {
                lt: cutoffDate,
              },
            },
          }),
        { label: 'purge_sessions', timeout: 30000 }
      )

      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(
            `[ENHANCED_ANALYTICS] Purged old data: ${deletedEvents.count} events, ${deletedSessions.count} sessions`
          )
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] purgeOldData error:', error)
      throw error
    }
  }

  /**
   * Anonymize user data for GDPR compliance
   * @param userId User ID to anonymize
   */
  async anonymizeUserData(userId: string): Promise<void> {
    try {
      const client = getAnalyticsDb()
      const anonymousId = `anon_${Date.now()}_${Math.floor(Math.random() * 1000)}`

      // Anonymize events
      await executeQuery(
        () =>
          client.eventStream.updateMany({
            where: { userId },
            data: {
              userId: anonymousId,
              context: {}, // Clear context data
              payload: {}, // Clear payload data
            },
          }),
        { label: 'anonymize_events', timeout: 30000 }
      )

      // Anonymize sessions
      await executeQuery(
        () =>
          client.userSession.updateMany({
            where: { userId },
            data: {
              userId: anonymousId,
              deviceInfo: {}, // Clear device info
            },
          }),
        { label: 'anonymize_sessions', timeout: 30000 }
      )

      // Anonymize page navigation
      await executeQuery(
        () =>
          client.pageNavigation.updateMany({
            where: { userId },
            data: {
              userId: anonymousId,
            },
          }),
        { label: 'anonymize_navigation', timeout: 30000 }
      )

      // Anonymize feature interactions
      await executeQuery(
        () =>
          client.featureInteraction.updateMany({
            where: { userId },
            data: {
              userId: anonymousId,
              value: null,
              context: {}, // Clear context data
            },
          }),
        { label: 'anonymize_interactions', timeout: 30000 }
      )

      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(`[ENHANCED_ANALYTICS] Anonymized data for user ${userId}`)
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('[ENHANCED_ANALYTICS] anonymizeUserData error:', error)
      throw error
    }
  }

  /**
   * Validate event data
   * @param event Event to validate
   */
  private validateEvent(event: AnalyticsEvent): void {
    if (
      !event.userId ||
      !event.companyId ||
      !event.sessionId ||
      !event.eventType ||
      !event.action ||
      !event.target
    ) {
      throw new Error('Missing required event fields')
    }
  }

  /**
   * Determine if an event is critical and needs immediate processing
   * @param eventType Event type
   * @returns True if critical event
   */
  private isCriticalEvent(eventType: string): boolean {
    const criticalEventTypes = [
      'error',
      'security',
      'purchase',
      'conversion',
      'session_start',
      'session_end',
      'user_feedback',
    ]

    return criticalEventTypes.includes(eventType.toLowerCase())
  }

  /**
   * Sanitize event data for logging
   * @param event Event to sanitize
   * @returns Sanitized event
   */
  private sanitizeEventForLogging(event: AnalyticsEvent): Record<string, unknown> {
    return {
      userId: event.userId ? `${event.userId.substring(0, 4)}...` : null,
      companyId: event.companyId,
      sessionId: event.sessionId ? `${event.sessionId.substring(0, 4)}...` : null,
      eventType: event.eventType,
      action: event.action,
      target: event.target,
      hasContext: !!event.context && Object.keys(event.context).length > 0,
      hasPayload: !!event.payload && Object.keys(event.payload).length > 0,
    }
  }
}

// Export singleton instance
export const enhancedAnalyticsService = new EnhancedAnalyticsService()
