import { prisma } from '@/lib/prisma'
import { sendPasswordResetEmail, isValidEmail } from './email-service'
import bcrypt from 'bcryptjs'
import crypto from 'crypto'

export interface ForgotPasswordRequest {
  email: string
  ipAddress?: string
  userAgent?: string
}

export interface ResetPasswordRequest {
  token: string
  newPassword: string
  ipAddress?: string
  userAgent?: string
}

export interface ForgotPasswordResponse {
  success: boolean
  message: string
}

export interface ResetPasswordResponse {
  success: boolean
  message: string
}

/**
 * Password reset service
 */
export class PasswordResetService {
  /**
   * <PERSON><PERSON> forgot password request
   */
  static async forgotPassword({
    email,
    ipAddress,
    userAgent,
  }: ForgotPasswordRequest): Promise<ForgotPasswordResponse> {
    try {
      // Validate email format
      if (!isValidEmail(email)) {
        return {
          success: false,
          message: 'Please enter a valid email address',
        }
      }

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: { id: true, email: true, name: true, password: true },
      })

      // Always return success for security (don't reveal if email exists)
      if (!user) {
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.log(`Password reset requested for non-existent email: ${email}`)
        return {
          success: true,
          message:
            'If an account with that email exists, you will receive a password reset link shortly.',
        }
      }

      // Check if user has a password (not OAuth-only)
      if (!user.password) {
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.log(`Password reset requested for OAuth-only account: ${email}`)
        return {
          success: true,
          message:
            'If an account with that email exists, you will receive a password reset link shortly.',
        }
      }

      // Clean up expired tokens for this email
      await this.cleanupExpiredTokens(email)

      // Check for recent token requests (rate limiting)
      const recentToken = await prisma.passwordResetToken.findFirst({
        where: {
          email: email.toLowerCase(),
          createdAt: {
            gte: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
          },
        },
      })

      if (recentToken) {
        return {
          success: false,
          message:
            'A password reset email was already sent recently. Please check your email or wait a few minutes before trying again.',
        }
      }

      // Generate secure token
      const token = crypto.randomBytes(32).toString('hex')
      const expires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

      // Save token to database
      await prisma.passwordResetToken.create({
        data: {
          email: email.toLowerCase(),
          token,
          expires,
          ipAddress,
          userAgent,
        },
      })

      // Send reset email
      const emailResult = await sendPasswordResetEmail({
        email: email.toLowerCase(),
        resetToken: token,
        userName: user.name || undefined,
      })

      if (!emailResult.success) {
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.error('Failed to send password reset email:', emailResult.message)
        // Clean up token if email failed
        await prisma.passwordResetToken.deleteMany({
          where: { token },
        })

        return {
          success: false,
          message: 'Failed to send password reset email. Please try again later.',
        }
      }

      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(`Password reset email sent to: ${email}`)

      return {
        success: true,
        message:
          'If an account with that email exists, you will receive a password reset link shortly.',
      }
    } catch {
      if (process.env.NODE_ENV === 'development') console.error('Error in forgotPassword:', _error)
      return {
        success: false,
        message: 'An error occurred while processing your request. Please try again later.',
      }
    }
  }

  /**
   * Handle password reset with token
   */
  static async resetPassword({
    token,
    newPassword,
    ipAddress,
    userAgent,
  }: ResetPasswordRequest): Promise<ResetPasswordResponse> {
    try {
      // Validate inputs
      if (!token || !newPassword) {
        return {
          success: false,
          message: 'Invalid request. Missing token or password.',
        }
      }

      // Validate password strength
      if (newPassword.length < 8) {
        return {
          success: false,
          message: 'Password must be at least 8 characters long.',
        }
      }

      // Find valid token
      const resetToken = await prisma.passwordResetToken.findUnique({
        where: { token },
        include: {
          // We don't have a direct relation, so we'll fetch the user separately
        },
      })

      if (!resetToken) {
        return {
          success: false,
          message: 'Invalid or expired reset token.',
        }
      }

      // Check if token is expired
      if (resetToken.expires < new Date()) {
        // Clean up expired token
        await prisma.passwordResetToken.delete({
          where: { id: resetToken.id },
        })

        return {
          success: false,
          message: 'Reset token has expired. Please request a new password reset.',
        }
      }

      // Check if token was already used
      if (resetToken.used) {
        return {
          success: false,
          message: 'This reset token has already been used.',
        }
      }

      // Find the user
      const user = await prisma.user.findUnique({
        where: { email: resetToken.email },
        select: { id: true, email: true, password: true },
      })

      if (!user) {
        return {
          success: false,
          message: 'User account not found.',
        }
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12)

      // Update password and mark token as used
      await prisma.$transaction([
        // Update user password
        prisma.user.update({
          where: { id: user.id },
          data: { password: hashedPassword },
        }),
        // Mark token as used
        prisma.passwordResetToken.update({
          where: { id: resetToken.id },
          data: {
            used: true,
            updatedAt: new Date(),
          },
        }),
      ])

      // Clean up all other tokens for this email
      await prisma.passwordResetToken.deleteMany({
        where: {
          email: resetToken.email,
          id: { not: resetToken.id },
        },
      })

      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(`Password successfully reset for user: ${user.email}`)

      return {
        success: true,
        message:
          'Your password has been successfully reset. You can now sign in with your new password.',
      }
    } catch {
      if (process.env.NODE_ENV === 'development') console.error('Error in resetPassword:', _error)
      return {
        success: false,
        message: 'An error occurred while resetting your password. Please try again later.',
      }
    }
  }

  /**
   * Validate reset token without using it
   */
  static async validateResetToken(token: string): Promise<{ valid: boolean; email?: string }> {
    try {
      const resetToken = await prisma.passwordResetToken.findUnique({
        where: { token },
      })

      if (!resetToken || resetToken.used || resetToken.expires < new Date()) {
        return { valid: false }
      }

      return { valid: true, email: resetToken.email }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Error validating reset token:', _error)
      return { valid: false }
    }
  }

  /**
   * Clean up expired tokens for an email
   */
  private static async cleanupExpiredTokens(email: string): Promise<void> {
    try {
      await prisma.passwordResetToken.deleteMany({
        where: {
          email: email.toLowerCase(),
          OR: [{ expires: { lt: new Date() } }, { used: true }],
        },
      })
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Error cleaning up expired tokens:', _error)
    }
  }

  /**
   * Clean up all expired tokens (for scheduled cleanup)
   */
  static async cleanupAllExpiredTokens(): Promise<number> {
    try {
      const result = await prisma.passwordResetToken.deleteMany({
        where: {
          OR: [{ expires: { lt: new Date() } }, { used: true }],
        },
      })
      return result.count
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Error cleaning up all expired tokens:', _error)
      return 0
    }
  }
}
