import { LucideIcon } from 'lucide-react'

export interface NavigationLink {
  href: string
  label: string
  icon?: LucideIcon
  active?: boolean
}

export interface NavigationItem extends NavigationLink {
  subItems?: NavigationLink[]
}

export interface NavigationZone {
  zone: string
  items: NavigationItem[]
}

// These are for the AI-First sidebar, which we will preserve.
export interface SmartRecommendation extends NavigationLink {
  score: number
  reason: string
}

export interface UserContextData {
  role: string
  companyId: string
  recentActions?: string[]
  preferences?: Record<string, any>
}

export interface BehaviorPattern {
  actionType: string
  frequency: number
  lastOccurrence: Date
  context: Record<string, any>
}

// Sidebar configuration interface
export interface SidebarConfig {
  zones: NavigationZone[]
  collapsed?: boolean
  onCollapseChange?: (collapsed: boolean) => void
  className?: string
  userContext?: UserContextData
  recommendations?: SmartRecommendation[]
  onBehaviorTrack?: (event: BehaviorPattern) => void
}
