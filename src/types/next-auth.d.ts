import { DefaultSession } from 'next-auth'

declare module 'next-auth' {
  /**
   * Extends the built-in session type to include companyId for multi-tenant support
   * CRITICAL: This fixes the 401 authentication errors by ensuring session includes companyId
   */
  interface Session {
    user: {
      id: string
      role: string
      companyId: string // CRITICAL: Required for multi-tenant authentication
    } & DefaultSession['user']
  }

  /**
   * Extends the built-in user type to include companyId
   */
  interface User {
    id: string
    role: string
    companyId?: string // Optional during OAuth signup, required after onboarding
  }
}

declare module 'next-auth/jwt' {
  /**
   * Extends the built-in JWT type to include companyId
   * CRITICAL: This ensures JWT tokens carry companyId for session reconstruction
   */
  interface JWT {
    id: string
    role: string
    companyId?: string // Optional to handle edge cases during signup
  }
}
