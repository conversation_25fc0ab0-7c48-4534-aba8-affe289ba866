/**
 * 🎨 AI-First Theme System Types
 *
 * Comprehensive type definitions for the sophisticated theme system
 * with AI-driven personalization and behavioral analytics.
 */

export type ThemeMode = 'light' | 'dark' | 'system'

export type ColorScheme =
  | 'emynent-light'
  | 'emynent-dark'
  | 'nightowl'
  | 'draculapro'
  | 'syntaxlight'
  | 'mintygreen'
  | 'sandybeige'
  | 'lavender-light'
  | 'coral-light'
  | 'aqua-light'
  | 'twilight-light'
  | 'github-dark'
  | 'blue-dark'
  | 'green-dark'
  | 'slate-dark'
  | 'twilight'

export interface CustomColor {
  id: string
  name: string
  value: string
  element?: 'primary' | 'secondary' | 'accent' | 'background' | 'foreground'
  createdAt?: Date
  usageCount?: number
}

export interface GradientSettings {
  enabled: boolean
  type: 'linear' | 'radial' | 'conic'
  angle?: number
  stops?: Array<{
    color: string
    position: number
  }>
}

export interface ThemeCustomizations {
  colors: CustomColor[]
  gradients: GradientSettings
  typography?: {
    fontFamily?: string
    fontSize?: number
    lineHeight?: number
  }
  spacing?: {
    scale?: number
    customValues?: Record<string, string>
  }
  borderRadius?: {
    scale?: number
    customValues?: Record<string, string>
  }
}

export interface CurrentTheme {
  mode: ThemeMode
  colorScheme: ColorScheme
  appliedAt: Date
  source: 'user_selection' | 'ai_recommendation' | 'company_default' | 'system_default'
}

export interface ThemeUsagePattern {
  colorScheme: ColorScheme
  usage: number // Percentage of time used
  satisfaction: number // 0-10 score
  contexts: Array<{
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night'
    dayOfWeek: 'weekday' | 'weekend'
    usage: number
  }>
}

export interface AIThemeContext {
  preferredColorSchemes: ColorScheme[]
  usagePatterns: Record<ColorScheme, ThemeUsagePattern>
  satisfactionScores: Record<ColorScheme, number>
  behaviorTriggers: Array<{
    trigger: string
    preferredTheme: ColorScheme
    confidence: number
  }>
  learningMetadata: {
    dataPoints: number
    lastUpdated: Date
    confidenceLevel: number
  }
}

export interface ThemePreferences {
  userId: string
  currentTheme: CurrentTheme
  aiContext: AIThemeContext
  customizations: ThemeCustomizations
  companyRestrictions?: {
    allowedSchemes?: ColorScheme[]
    forbiddenSchemes?: ColorScheme[]
    customizationLimits?: {
      maxCustomColors?: number
      allowGradients?: boolean
      allowTypographyChanges?: boolean
    }
  }
  metadata: {
    createdAt: Date
    updatedAt: Date
    version: string
  }
}

export interface ThemeInteraction {
  action:
    | 'theme_switch'
    | 'color_customize'
    | 'gradient_adjust'
    | 'export_theme'
    | 'import_theme'
    | 'reset_theme'
  from?: string
  to?: string
  color?: string
  element?: string
  timestamp: number
  context?: {
    timeOfDay?: number
    deviceType?: 'desktop' | 'mobile' | 'tablet'
    location?: string
    sessionDuration?: number
  }
}

export interface ThemeSatisfactionFeedback {
  colorScheme: ColorScheme
  satisfaction: number // 0-10
  aspects: {
    readability: number
    aesthetics: number
    productivity: number
    eyeStrain?: number
    mood?: number
  }
  feedback?: string
  timestamp: Date
}

export interface AIThemeRecommendation {
  type: 'color_scheme' | 'custom_color' | 'gradient' | 'time_based' | 'mood_based'
  suggestion: string
  confidence: number // 0-1
  reasoning: string
  expectedBenefit: string
  metadata: {
    basedOn: string[]
    algorithm: string
    version: string
  }
}

export interface ThemeRecommendationResponse {
  userId: string
  recommendations: AIThemeRecommendation[]
  insights: {
    usagePatterns: Record<string, any>
    preferencesTrends: Record<string, any>
    satisfactionMetrics: Record<string, any>
  }
  contextFactors?: Array<{
    factor: string
    influence: number
    description: string
  }>
  generatedAt: Date
}

export interface ThemeAnalytics {
  userId: string
  interactions: ThemeInteraction[]
  patterns: {
    switchFrequency: number
    preferredTimes: Record<string, number>
    mostUsedSchemes: Array<{
      scheme: ColorScheme
      percentage: number
    }>
    customizationActivity: {
      colorsAdded: number
      gradientsUsed: number
      exportsCount: number
    }
  }
  satisfaction: Record<
    ColorScheme,
    {
      overall: number
      aspects: Record<string, number>
      sampleSize: number
      trend: 'improving' | 'declining' | 'stable'
    }
  >
  metadata: {
    periodStart: Date
    periodEnd: Date
    dataPoints: number
  }
}

export interface UserThemeContext {
  userId: string
  role: string
  companyId: string
  preferences: Record<string, any>
  recentActions: Array<{
    action: string
    timestamp: number
    data: Record<string, any>
  }>
  historicalData: Record<string, any>
}

export interface ThemeUpdateResult {
  success: boolean
  preferences?: ThemePreferences
  errors?: Array<{
    field: string
    message: string
    code: string
  }>
  warnings?: Array<{
    message: string
    suggestion: string
  }>
}

export interface ContextualRecommendationOptions {
  time?: {
    hour: number
    timezone: string
  }
  environment?: {
    lighting: 'bright' | 'dim' | 'natural'
    location: 'office' | 'home' | 'mobile'
  }
  activity?: {
    type: 'focused_work' | 'collaboration' | 'reading' | 'creative'
    duration: number
  }
  mood?: {
    energy: number // 1-10
    stress: number // 1-10
    creativity: number // 1-10
  }
}

export interface WebSocketThemeHandler {
  broadcast: (userId: string, message: any) => void
  emit: (event: string, data: any) => void
}

// Company-level theme configuration
export interface CompanyThemeConfig {
  id: string
  name: string
  defaultTheme: {
    mode: ThemeMode
    colorScheme: ColorScheme
  }
  allowedCustomizations: {
    colorSchemes: ColorScheme[]
    customColors: boolean
    gradients: boolean
    typography: boolean
  }
  brandColors: {
    primary: string
    secondary: string
    accent: string
    logo?: string
  }
  aiFeatures: {
    recommendationsEnabled: boolean
    behaviorTrackingEnabled: boolean
    crossCompanyLearning: boolean
  }
}

// Theme validation and security
export interface ThemeValidationRule {
  field: string
  validator: (value: any) => boolean
  message: string
  severity: 'error' | 'warning'
}

export interface ThemeSecurityPolicy {
  allowCustomCSS: boolean
  maxCustomColors: number
  allowedColorFormats: string[]
  forbiddenPatterns: RegExp[]
  sanitizationRules: ThemeValidationRule[]
}
