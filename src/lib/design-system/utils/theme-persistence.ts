'use server'

import { cookies } from 'next/headers'
import { redis } from '@/lib/redis'
import { prisma } from '@/lib/prisma'
import { isValidTheme } from './theme-validator'

// Redis cache TTL in seconds (1 hour)
const CACHE_TTL = 3600

/**
 * Saves the user's theme preference to the database and Redis cache
 * @param userId The user's ID
 * @param theme The theme preference to save
 */
export async function saveThemePreference(userId: string, theme: string): Promise<void> {
  // Validate theme before saving
  if (!isValidTheme(theme)) {
    // console.error(`Invalid theme: ${theme}`);
    return
  }

  try {
    // Save to database
    await prisma.userPreference.upsert({
      where: { userId },
      update: { theme },
      create: { userId, theme },
    })

    // Cache in Redis
    const cacheKey = `theme:${userId}`
    await redis.set(cacheKey, theme, 'EX', CACHE_TTL)

    // Set in cookie for faster access on client
    cookies().set('theme', theme, {
      maxAge: 60 * 60 * 24 * 30, // 30 days
      path: '/',
    })
  } catch {
    // console.error('Failed to save theme preference:', error);
    // Set cookie as fallback even if Redis/DB fails
    cookies().set('theme', theme, {
      maxAge: 60 * 60 * 24 * 30, // 30 days
      path: '/',
    })
  }
}

/**
 * Gets the user's theme preference from Redis cache or database
 * @param userId The user's ID
 * @returns The theme preference or null if not found
 */
export async function getThemePreference(userId: string): Promise<string | null> {
  try {
    // Check Redis cache first
    const cacheKey = `theme:${userId}`
    const cachedTheme = await redis.get(cacheKey)

    if (cachedTheme) {
      return cachedTheme
    }

    // Fall back to database
    const userPreference = await prisma.userPreference.findUnique({
      where: { userId },
      select: { theme: true },
    })

    if (userPreference?.theme) {
      // Update cache for next time
      await redis.set(cacheKey, userPreference.theme, 'EX', CACHE_TTL)
      return userPreference.theme
    }

    // Check cookie as last resort
    const cookieTheme = cookies().get('theme')?.value
    return cookieTheme || null
  } catch {
    // console.error('Failed to get theme preference:', error);

    // Try to get from cookie if DB or Redis fails
    const cookieTheme = cookies().get('theme')?.value
    return cookieTheme || null
  }
}

/**
 * Clears the user's theme preference cache
 * @param userId The user's ID
 */
export async function clearThemePreferenceCache(userId: string): Promise<void> {
  try {
    const cacheKey = `theme:${userId}`
    await redis.del(cacheKey)
  } catch {
    // console.error('Failed to clear theme preference cache:', error);
  }
}

/**
 * Handles theme fallback when preferences cannot be loaded
 * Defaults to system mode + Emynent Default
 */
export async function getDefaultTheme(): Promise<string> {
  return 'system'
}

/**
 * Gets a toast message for theme loading errors
 */
export async function getThemeErrorMessage(): Promise<string> {
  return 'Theme preferences could not be loaded. Using system default.'
}
