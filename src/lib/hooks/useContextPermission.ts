'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { hasPermission } from '@/lib/auth/permissions'
import { PermissionContextData } from '@/services/rbac-service'

interface UseContextPermissionProps {
  /**
   * Resource being accessed (e.g., 'users', 'reports')
   */
  resource: string

  /**
   * Action being performed (e.g., 'create', 'delete')
   */
  action: string

  /**
   * Additional context data for the permission check
   */
  contextData?: PermissionContextData

  /**
   * Whether to skip the permission check
   */
  skip?: boolean
}

interface UseContextPermissionResult {
  /**
   * Whether the user has permission for the action
   */
  allowed: boolean

  /**
   * Whether the permission check is loading
   */
  loading: boolean

  /**
   * Error message if permission check failed
   */
  error?: string

  /**
   * Message explaining the permission decision
   */
  message?: string

  /**
   * Refetch the permission check
   */
  refetch: () => void
}

/**
 * Hook for checking context-aware permissions in client components
 * Falls back to basic permission checking if context-aware permissions are not available
 *
 * @param props Permission check properties
 * @returns Permission check result
 */
export function useContextPermission(props: UseContextPermissionProps): UseContextPermissionResult {
  const { resource, action, contextData, skip = false } = props
  const { data: session, status } = useSession()
  const [allowed, setAllowed] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(!skip)
  const [error, setError] = useState<string | undefined>(undefined)
  const [message, setMessage] = useState<string | undefined>(undefined)

  const sessionLoading = status === 'loading'

  // Function to check permissions
  const checkPermission = useCallback(async () => {
    if (skip || !resource || !action) return

    // Reset states
    setLoading(true)
    setError(undefined)
    setMessage(undefined)

    try {
      if (!session?.user) {
        setAllowed(false)
        setMessage('No active session')
        return
      }

      // For basic permission checks without context data
      if (!contextData) {
        // Use client-side permission checking from permissions.ts
        const hasAccess = hasPermission(session.user.role, `${resource}:${action}`)
        setAllowed(hasAccess)
        setMessage(hasAccess ? 'Permission granted' : 'Permission denied')
        return
      }

      // For context-aware permission checks
      const response = await fetch('/api/auth/context-permission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.id,
          resource,
          action,
          contextData,
        }),
        credentials: 'include', // Important to include cookies for session
      })

      if (!response.ok) {
        throw new Error(`Permission check failed: ${response.status}`)
      }

      const data = await response.json()
      setAllowed(data.allowed)
      setMessage(data.message || (data.allowed ? 'Permission granted' : 'Permission denied'))

      // Optional fields from context-aware permission checks
      if (data.explanation) {
        // Future: Handle Gen AI-generated explanations
        setMessage(data.explanation)
      }
    } catch {
      setAllowed(false)
      setError(err instanceof Error ? err.message : 'Failed to check permissions')
      setMessage('Permission check error')
    } finally {
      setLoading(false)
    }
  }, [resource, action, contextData, session, skip])

  // Check permissions when dependencies change
  useEffect(() => {
    if (!sessionLoading) {
      checkPermission()
    }
  }, [checkPermission, sessionLoading])

  return {
    allowed,
    loading: loading || sessionLoading,
    error,
    message,
    refetch: checkPermission,
  }
}
