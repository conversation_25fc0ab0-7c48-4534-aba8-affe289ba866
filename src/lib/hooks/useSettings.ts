import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

interface Settings {
  appearance?: {
    theme?: 'light' | 'dark' | 'system'
    fontSize?: number
    compactMode?: boolean
    primaryColor?: string
    secondaryColor?: string
    accentColor?: string
    borderRadius?: 'none' | 'small' | 'medium' | 'large' | 'xl'
    animationSpeed?: 'none' | 'slow' | 'normal' | 'fast'
  }
  notifications?: {
    email?: boolean
    push?: boolean
    desktop?: boolean
    frequency?: 'realtime' | 'daily' | 'weekly'
  }
  dashboard?: {
    layout?: string
    showQuickActions?: boolean
    showRecentActivity?: boolean
    showAnalyticsSummary?: boolean
    showTaskList?: boolean
    chartType?: string
    showDataLabels?: boolean
    interactiveCharts?: boolean
    colorPalette?: string
    dateFormat?: string
    timeFormat?: string
    showRelativeDates?: boolean
    customMetrics?: string[]
  }
  language?: string
  timezone?: string
}

export function useSettings() {
  const { data: session } = useSession()
  const [settings, setSettings] = useState<Settings | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch settings on mount
  useEffect(() => {
    if (session?.user?.id) {
      fetchSettings()
    }
  }, [session?.user?.id])

  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/settings')
      if (!response.ok) {
        throw new Error('Failed to fetch settings')
      }

      const data = await response.json()
      setSettings(data)
    } catch {
      setError(err instanceof Error ? err.message : 'An error occurred')
      toast.error('Failed to load settings')
    } finally {
      setIsLoading(false)
    }
  }

  const updateSettings = async (newSettings: Partial<Settings>) => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      })

      if (!response.ok) {
        throw new Error('Failed to update settings')
      }

      const data = await response.json()
      setSettings(data)
      toast.success('Settings updated successfully')
    } catch {
      setError(err instanceof Error ? err.message : 'An error occurred')
      toast.error('Failed to update settings')
    } finally {
      setIsLoading(false)
    }
  }

  const updateAppearance = async (appearance: Settings['appearance']) => {
    await updateSettings({ appearance })
  }

  const updateNotifications = async (notifications: Settings['notifications']) => {
    await updateSettings({ notifications })
  }

  const updateDashboard = async (dashboard: Settings['dashboard']) => {
    await updateSettings({ dashboard })
  }

  const updateLanguage = async (language: string) => {
    await updateSettings({ language })
  }

  const updateTimezone = async (timezone: string) => {
    await updateSettings({ timezone })
  }

  return {
    settings,
    isLoading,
    error,
    updateSettings,
    updateAppearance,
    updateNotifications,
    updateDashboard,
    updateLanguage,
    updateTimezone,
    refreshSettings: fetchSettings,
  }
}
