import { Redis } from '@upstash/redis'

// Define Redis interface to ensure compatibility
interface RedisLike {
  get(key: string): Promise<string | null>
  set(key: string, value: string, expCommand?: string, expValue?: number): Promise<string>
  del(key: string): Promise<number>
  setex(key: string, seconds: number, value: string): Promise<string>
  incr(key: string): Promise<number>
  expire(key: string, seconds: number): Promise<number>
  ttl(key: string): Promise<number>
  exists(key: string): Promise<number>
  getex?(key: string, command?: string, value?: number): Promise<string | null>
  hget?(key: string, field: string): Promise<string | null>
  hset?(key: string, field: string, value: string): Promise<number>
  ping?(): Promise<string>
  keys?(pattern: string): Promise<string[]>
  quit?(): Promise<void>
  flushdb?(): Promise<string>
}

// This class provides a fallback for local development or when Redis isn't configured
class LocalRedisCache implements RedisLike {
  private cache = new Map<string, { value: string; expiry?: number }>()

  async ping(): Promise<string> {
    return 'PONG'
  }

  async flushdb(): Promise<string> {
    this.cache.clear()
    return 'OK'
  }

  async keys(pattern: string): Promise<string[]> {
    // Simple pattern matching for test purposes
    const allKeys = Array.from(this.cache.keys())
    if (pattern === '*') return allKeys

    // Convert Redis pattern to regex
    const regexPattern = pattern.replace(/\*/g, '.*').replace(/\?/g, '.')
    const regex = new RegExp(`^${regexPattern}$`)

    return allKeys.filter(key => regex.test(key))
  }

  async quit(): Promise<void> {
    // For local cache, just clear it
    this.cache.clear()
  }

  async get(key: string): Promise<string | null> {
    const item = this.cache.get(key)

    if (!item) return null

    // Check if item has expired
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(key)
      return null
    }

    return item.value
  }

  async set(key: string, value: string, expCommand?: string, expValue?: number): Promise<string> {
    let expiry: number | undefined

    // Handle expiration (EX seconds, PX milliseconds)
    if (expCommand && expValue) {
      if (expCommand.toUpperCase() === 'EX') {
        // seconds
        expiry = Date.now() + expValue * 1000
      } else if (expCommand.toUpperCase() === 'PX') {
        // milliseconds
        expiry = Date.now() + expValue
      }
    }

    this.cache.set(key, { value, expiry })
    return 'OK'
  }

  async del(...keys: string[]): Promise<number> {
    let deleted = 0
    for (const key of keys) {
      if (this.cache.delete(key)) {
        deleted++
      }
    }
    return deleted
  }

  async setex(key: string, seconds: number, value: string): Promise<string> {
    // console.log(`🔴 LocalRedisCache.setex called: ${key}, ${seconds}s, ${value}`);
    const expiry = Date.now() + seconds * 1000
    this.cache.set(key, { value, expiry })
    return 'OK'
  }

  async getex(key: string, command?: string, value?: number): Promise<string | null> {
    return this.get(key)
  }

  async hget(key: string, field: string): Promise<string | null> {
    const item = this.cache.get(`${key}:${field}`)
    if (!item) return null
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(`${key}:${field}`)
      return null
    }
    return item.value
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    this.cache.set(`${key}:${field}`, { value })
    return 1
  }

  async incr(key: string): Promise<number> {
    const current = await this.get(key)
    const newValue = (current ? parseInt(current) : 0) + 1
    await this.set(key, newValue.toString())
    return newValue
  }

  async expire(key: string, seconds: number): Promise<number> {
    const item = this.cache.get(key)
    if (!item) return 0

    const expiry = Date.now() + seconds * 1000
    this.cache.set(key, { ...item, expiry })
    return 1
  }

  async ttl(key: string): Promise<number> {
    const item = this.cache.get(key)
    if (!item) return -2 // Key doesn't exist
    if (!item.expiry) return -1 // Key exists but no expiry

    const remaining = Math.max(0, Math.floor((item.expiry - Date.now()) / 1000))
    return remaining
  }

  async exists(key: string): Promise<number> {
    const item = this.cache.get(key)
    if (!item) return 0

    // Check if item has expired
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(key)
      return 0
    }

    return 1
  }
}

// Initialize Redis client with fallback for local development
let redis: RedisLike

try {
  // Only create a real Redis client if credentials are provided
  if (process.env.REDIS_URL && process.env.REDIS_TOKEN) {
    redis = new Redis({
      url: process.env.REDIS_URL,
      token: process.env.REDIS_TOKEN,
    }) as RedisLike
  } else {
    // For local development, use a Map-based cache
    redis = new LocalRedisCache()
    // console.warn('Using local Redis cache. For production, set REDIS_URL and REDIS_TOKEN environment variables.')
  }
} catch {
  // console.error('Failed to initialize Redis client, falling back to local cache:', error)
  redis = new LocalRedisCache()
}

export { redis }
