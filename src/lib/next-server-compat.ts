/**
 * Next.js Server Compatibility Layer for Test Environment
 * Provides mock implementations of Next.js server functions for Vitest
 */

// Mock getServerSession for test environment
export async function getServerSession(authConfig?: unknown) {
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('This mock should only be used in test environment')
  }

  // Return null for unauthenticated requests (this is what the real function does)
  return null
}

// Create mock NextRequest and NextResponse for test environment
class MockNextRequest {
  url: string
  method: string
  headers: Map<string, string>
  private _body: string | null

  constructor(url: string, init?: RequestInit) {
    this.url = url
    this.method = init?.method || 'GET'
    this.headers = new Map()
    this._body = (init?.body as string) || null

    // Add headers from init if provided
    if (init?.headers) {
      if (init.headers instanceof Headers) {
        init.headers.forEach((value, key) => {
          this.headers.set(key, _value)
        })
      } else if (Array.isArray(init.headers)) {
        init.headers.forEach(([key, value]) => {
          this.headers.set(key, _value)
        })
      } else {
        Object.entries(init.headers).forEach(([key, value]) => {
          this.headers.set(key, _value)
        })
      }
    }
  }

  async json() {
    if (this._body) {
      return JSON.parse(this._body)
    }
    return {}
  }
}

class MockNextResponse {
  static json(data: unknown, init?: ResponseInit) {
    return {
      json: async () => data,
      status: init?.status || 200,
      headers: new Map(),
    }
  }
}

// Export the appropriate implementations based on environment
export const NextRequest =
  process.env.NODE_ENV === 'test' ? (MockNextRequest as any) : require('next/server').NextRequest

export const NextResponse =
  process.env.NODE_ENV === 'test' ? (MockNextResponse as any) : require('next/server').NextResponse
