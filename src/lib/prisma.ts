import { PrismaClient } from '@prisma/client'
import { Redis } from '@upstash/redis'
import { withCache, CacheConfig as _CacheConfig, CACHE_TTL as _CACHE_TTL } from './cache-middleware'

// Initialize Redis client for caching if available
const _redis = process.env.UPSTASH_REDIS_URL
  ? new Redis({
      url: process.env.UPSTASH_REDIS_URL,
      token: process.env.UPSTASH_REDIS_TOKEN || '',
    })
  : null

// Create global Prisma client
// Based on https://www.prisma.io/docs/guides/other/troubleshooting-orm/help-articles/nextjs-prisma-client-dev-practices
const globalForPrisma = global as unknown as {
  prisma: PrismaClient
  cachedPrisma: PrismaClient
  auditedPrisma: PrismaClient
}

// Initialize the base Prisma client without middleware
const basePrisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = basePrisma
}

// Create cached version with cache middleware
const cachedPrisma = withCache(basePrisma, {
  enabled: process.env.NODE_ENV === 'production',
  ttl: 300, // 5 minutes default
  prefix: 'prisma-cache:',
})

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.cachedPrisma = cachedPrisma
}

// Export the base prisma client for use
export const prisma = basePrisma

// Export cached version for performance-critical operations
export const cachedPrismaClient = cachedPrisma

// Export utility function for applying audit middleware when needed
export function createAuditedPrisma() {
  // Only apply audit middleware if not already applied
  if (globalForPrisma.auditedPrisma) {
    return globalForPrisma.auditedPrisma
  }

  // Apply audit middleware after base client is initialized
  const { applyAuditMiddleware } = require('./audit-middleware')
  const auditedClient = applyAuditMiddleware(basePrisma)

  if (process.env.NODE_ENV !== 'production') {
    globalForPrisma.auditedPrisma = auditedClient
  }

  return auditedClient
}

// Export TenantIsolationManager
export { TenantIsolationManager } from './tenant-isolation-manager'

export default basePrisma
