/**
 * Phase 4.2: AI-Powered Personalization Engine
 * Core personalization engine that adapts UI and content based on user behavior
 */

import { prisma } from '@/lib/prisma'
import { redis } from '@/lib/redis'
import { logger } from '@/lib/logger'

export interface UserProfile {
  id: string
  userId: string
  preferences: {
    theme: string
    layout: string
    features: string[]
    notifications: boolean
  }
  behaviorProfile: {
    mostUsedFeatures: string[]
    navigationPatterns: string[]
    timeOfDayUsage: Record<string, number>
    sessionDuration: number
    clickPatterns: Record<string, number>
  }
  recommendations: Recommendation[]
  uiConfiguration: {
    dashboardLayout: string
    widgetOrder: string[]
    hiddenElements: string[]
    shortcuts: Record<string, string>
  }
  learningData: {
    interactions: number
    lastUpdated: Date
    confidence: number
    adaptationHistory: AdaptationEvent[]
  }
}

export interface Recommendation {
  id: string
  type: 'feature' | 'content' | 'workflow' | 'ui'
  title: string
  description: string
  confidence: number
  priority: 'low' | 'medium' | 'high'
  metadata: Record<string, any>
  createdAt: Date
  expiresAt?: Date
  status: 'active' | 'dismissed' | 'completed'
}

export interface AdaptationEvent {
  timestamp: Date
  type: string
  change: string
  reason: string
  confidence: number
}

export interface PersonalizationContext {
  userId: string
  companyId: string
  sessionId: string
  currentPage: string
  timeOfDay: number
  dayOfWeek: number
  deviceType: string
  userAgent: string
}

export class PersonalizationEngine {
  private static instance: PersonalizationEngine
  private cache = new Map<string, UserProfile>()
  private readonly CACHE_TTL = 3600 // 1 hour

  private constructor() {}

  public static getInstance(): PersonalizationEngine {
    if (!PersonalizationEngine.instance) {
      PersonalizationEngine.instance = new PersonalizationEngine()
    }
    return PersonalizationEngine.instance
  }

  /**
   * Get or create user personalization profile
   */
  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      // Check cache first
      const cacheKey = `profile:${userId}`
      const cached = this.cache.get(cacheKey)
      if (cached) {
        return cached
      }

      // Check Redis cache
      const redisData = await redis.get(cacheKey)
      if (redisData) {
        const profile = JSON.parse(redisData)
        this.cache.set(cacheKey, profile)
        return profile
      }

      // Get from database
      let dbProfile = await prisma.userPersonalizationProfile.findUnique({
        where: { userId },
      })

      if (!dbProfile) {
        // Create default profile
        dbProfile = await this.createDefaultProfile(userId)
      }

      const profile: UserProfile = {
        id: dbProfile.id,
        userId: dbProfile.userId,
        preferences: (dbProfile.preferences as any) || this.getDefaultPreferences(),
        behaviorProfile: (dbProfile.behaviorProfile as any) || this.getDefaultBehaviorProfile(),
        recommendations: (dbProfile.recommendations as any) || [],
        uiConfiguration: (dbProfile.uiConfiguration as any) || this.getDefaultUIConfiguration(),
        learningData: (dbProfile.learningData as any) || this.getDefaultLearningData(),
      }

      // Cache the profile
      this.cache.set(cacheKey, profile)
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(profile))

      return profile
    } catch {
      logger.error('Error getting user profile:', _error)
      return this.getDefaultProfile(userId)
    }
  }

  /**
   * Update user profile with new data
   */
  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const currentProfile = await this.getUserProfile(userId)
      const updatedProfile = { ...currentProfile, ...updates }

      // Update database
      await prisma.userPersonalizationProfile.upsert({
        where: { userId },
        update: {
          preferences: updatedProfile.preferences,
          behaviorProfile: updatedProfile.behaviorProfile,
          recommendations: updatedProfile.recommendations,
          uiConfiguration: updatedProfile.uiConfiguration,
          learningData: updatedProfile.learningData,
          lastUpdated: new Date(),
        },
        create: {
          userId,
          preferences: updatedProfile.preferences,
          behaviorProfile: updatedProfile.behaviorProfile,
          recommendations: updatedProfile.recommendations,
          uiConfiguration: updatedProfile.uiConfiguration,
          learningData: updatedProfile.learningData,
        },
      })

      // Update caches
      const cacheKey = `profile:${userId}`
      this.cache.set(cacheKey, updatedProfile)
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(updatedProfile))

      return updatedProfile
    } catch {
      logger.error('Error updating user profile:', _error)
      throw error
    }
  }

  /**
   * Generate personalized recommendations
   */
  async generateRecommendations(
    userId: string,
    context: PersonalizationContext
  ): Promise<Recommendation[]> {
    try {
      const profile = await this.getUserProfile(userId)
      const recommendations: Recommendation[] = []

      // Feature recommendations based on usage patterns
      const featureRecs = await this.generateFeatureRecommendations(profile, _context)
      recommendations.push(...featureRecs)

      // UI optimization recommendations
      const uiRecs = await this.generateUIRecommendations(profile, _context)
      recommendations.push(...uiRecs)

      // Workflow recommendations
      const workflowRecs = await this.generateWorkflowRecommendations(profile, _context)
      recommendations.push(...workflowRecs)

      // Content recommendations
      const contentRecs = await this.generateContentRecommendations(profile, _context)
      recommendations.push(...contentRecs)

      // Sort by priority and confidence
      recommendations.sort((a, b) => {
        const priorityWeight = { high: 3, medium: 2, low: 1 }
        const aPriority = priorityWeight[a.priority] * a.confidence
        const bPriority = priorityWeight[b.priority] * b.confidence
        return bPriority - aPriority
      })

      // Update profile with new recommendations
      await this.updateUserProfile(userId, {
        recommendations: recommendations.slice(0, 10), // Keep top 10
      })

      return recommendations
    } catch {
      logger.error('Error generating recommendations:', _error)
      return []
    }
  }

  /**
   * Get personalized UI configuration
   */
  async getPersonalizedUI(userId: string, context: PersonalizationContext): Promise<unknown> {
    try {
      const profile = await this.getUserProfile(userId)
      const baseConfig = profile.uiConfiguration

      // Adapt based on time of day
      const timeAdaptations = this.getTimeBasedAdaptations(context.timeOfDay)

      // Adapt based on usage patterns
      const usageAdaptations = this.getUsageBasedAdaptations(profile.behaviorProfile)

      // Adapt based on device type
      const deviceAdaptations = this.getDeviceBasedAdaptations(context.deviceType)

      return {
        ...baseConfig,
        ...timeAdaptations,
        ...usageAdaptations,
        ...deviceAdaptations,
        personalizedAt: new Date().toISOString(),
      }
    } catch {
      logger.error('Error getting personalized UI:', _error)
      return this.getDefaultUIConfiguration()
    }
  }

  /**
   * Track user interaction for learning
   */
  async trackInteraction(
    userId: string,
    interaction: {
      type: string
      element: string
      action: string
      context: PersonalizationContext
      metadata?: Record<string, any>
    }
  ): Promise<boolean> {
    try {
      // Validate input
      if (!userId || !interaction.type || !interaction.element || !interaction.action) {
        return false
      }

      const profile = await this.getUserProfile(userId)

      // Update behavior profile
      const updatedBehavior = this.updateBehaviorProfile(profile.behaviorProfile, interaction)

      // Generate new adaptations if needed
      const adaptations = await this.generateAdaptations(profile, interaction)

      // Update learning data
      const updatedLearning = {
        ...profile.learningData,
        interactions: profile.learningData.interactions + 1,
        lastUpdated: new Date(),
        adaptationHistory: [
          ...profile.learningData.adaptationHistory.slice(-50), // Keep last 50
          ...adaptations,
        ],
      }

      await this.updateUserProfile(userId, {
        behaviorProfile: updatedBehavior,
        learningData: updatedLearning,
      })

      // Track in analytics
      await this.trackPersonalizationEvent(userId, interaction)

      return true
    } catch {
      logger.error('Error tracking interaction:', _error)
      return false
    }
  }

  /**
   * Get smart navigation suggestions
   */
  async getNavigationSuggestions(
    userId: string,
    context: PersonalizationContext
  ): Promise<Array<{ path: string; label: string; confidence: number; reason: string }>> {
    try {
      const profile = await this.getUserProfile(userId)
      const suggestions = []

      // Add null safety checks for behaviorProfile
      const behaviorProfile = profile.behaviorProfile || this.getDefaultBehaviorProfile()

      // Based on time patterns
      const timeBasedSuggestions = this.getTimeBasedNavigationSuggestions(
        behaviorProfile.timeOfDayUsage || {},
        context.timeOfDay
      )
      suggestions.push(...timeBasedSuggestions)

      // Based on navigation patterns
      const patternBasedSuggestions = this.getPatternBasedNavigationSuggestions(
        behaviorProfile.navigationPatterns || [],
        context.currentPage
      )
      suggestions.push(...patternBasedSuggestions)

      // Based on most used features
      const featureBasedSuggestions = this.getFeatureBasedNavigationSuggestions(
        behaviorProfile.mostUsedFeatures || []
      )
      suggestions.push(...featureBasedSuggestions)

      return suggestions.sort((a, b) => b.confidence - a.confidence).slice(0, 5)
    } catch {
      logger.error('Error getting navigation suggestions:', _error)
      return []
    }
  }

  /**
   * Private helper methods
   */
  private async createDefaultProfile(userId: string) {
    return await prisma.userPersonalizationProfile.create({
      data: {
        userId,
        preferences: this.getDefaultPreferences(),
        behaviorProfile: this.getDefaultBehaviorProfile(),
        recommendations: [],
        uiConfiguration: this.getDefaultUIConfiguration(),
        learningData: this.getDefaultLearningData(),
      },
    })
  }

  private getDefaultProfile(userId: string): UserProfile {
    return {
      id: `default-${userId}`,
      userId,
      preferences: this.getDefaultPreferences(),
      behaviorProfile: this.getDefaultBehaviorProfile(),
      recommendations: [],
      uiConfiguration: this.getDefaultUIConfiguration(),
      learningData: this.getDefaultLearningData(),
    }
  }

  private getDefaultPreferences() {
    return {
      theme: 'system',
      layout: 'default',
      features: [],
      notifications: true,
    }
  }

  private getDefaultBehaviorProfile() {
    return {
      mostUsedFeatures: [],
      navigationPatterns: [],
      timeOfDayUsage: {},
      sessionDuration: 0,
      clickPatterns: {},
    }
  }

  private getDefaultUIConfiguration() {
    return {
      layout: 'grid',
      dashboardLayout: 'grid',
      widgets: ['overview', 'recent', 'quick-actions'],
      widgetOrder: ['overview', 'recent', 'quick-actions'],
      theme: 'system',
      hiddenElements: [],
      shortcuts: {},
    }
  }

  private getDefaultLearningData() {
    return {
      interactions: 0,
      lastUpdated: new Date(),
      confidence: 0,
      adaptationHistory: [],
    }
  }

  private async generateFeatureRecommendations(
    profile: UserProfile,
    context: PersonalizationContext
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = []

    // Recommend underused features - add null safety check
    const allFeatures = ['theme-customization', 'dashboard-widgets', 'shortcuts', 'notifications']
    const mostUsedFeatures = profile.behaviorProfile?.mostUsedFeatures || []
    const unusedFeatures = allFeatures.filter(feature => !mostUsedFeatures.includes(feature))

    for (const feature of unusedFeatures.slice(0, 3)) {
      recommendations.push({
        id: `feature-${feature}-${Date.now()}`,
        type: 'feature',
        title: `Try ${feature.replace('-', ' ')}`,
        description: `Based on your usage patterns, you might find ${feature} helpful`,
        confidence: 0.7,
        priority: 'medium',
        metadata: { feature, reason: 'underused' },
        createdAt: new Date(),
        status: 'active',
      })
    }

    return recommendations
  }

  private async generateUIRecommendations(
    profile: UserProfile,
    context: PersonalizationContext
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = []

    // Recommend layout changes based on usage - add null safety check
    const mostUsedFeatures = profile.behaviorProfile?.mostUsedFeatures || []
    if (mostUsedFeatures.length > 5) {
      recommendations.push({
        id: `ui-layout-${Date.now()}`,
        type: 'ui',
        title: 'Optimize your dashboard layout',
        description: 'Rearrange widgets based on your most used features',
        confidence: 0.8,
        priority: 'high',
        metadata: { type: 'layout', features: mostUsedFeatures },
        createdAt: new Date(),
        status: 'active',
      })
    }

    return recommendations
  }

  private async generateWorkflowRecommendations(
    profile: UserProfile,
    context: PersonalizationContext
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = []

    // Recommend shortcuts based on patterns - add null safety check
    const navigationPatterns = profile.behaviorProfile?.navigationPatterns || []
    if (navigationPatterns.length > 3) {
      recommendations.push({
        id: `workflow-shortcuts-${Date.now()}`,
        type: 'workflow',
        title: 'Create shortcuts for common tasks',
        description: 'Set up keyboard shortcuts for your frequent actions',
        confidence: 0.6,
        priority: 'low',
        metadata: { patterns: navigationPatterns },
        createdAt: new Date(),
        status: 'active',
      })
    }

    return recommendations
  }

  private async generateContentRecommendations(
    profile: UserProfile,
    context: PersonalizationContext
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = []

    // Time-based content recommendations
    const hour = context.timeOfDay
    if (hour >= 9 && hour <= 17) {
      recommendations.push({
        id: `content-work-hours-${Date.now()}`,
        type: 'content',
        title: 'Focus mode available',
        description: 'Enable focus mode to minimize distractions during work hours',
        confidence: 0.5,
        priority: 'low',
        metadata: { timeContext: 'work-hours' },
        createdAt: new Date(),
        status: 'active',
      })
    }

    return recommendations
  }

  private getTimeBasedAdaptations(timeOfDay: number) {
    if (timeOfDay >= 18 || timeOfDay <= 6) {
      return {
        theme: 'dark',
        reducedAnimations: true,
      }
    }
    return {}
  }

  private getUsageBasedAdaptations(behaviorProfile: UserProfile['behaviorProfile']) {
    const adaptations: unknown = {}

    // Promote frequently used features - add null safety check
    if (
      behaviorProfile &&
      behaviorProfile.mostUsedFeatures &&
      behaviorProfile.mostUsedFeatures.length > 0
    ) {
      adaptations.quickActions = behaviorProfile.mostUsedFeatures.slice(0, 5)
    }

    return adaptations
  }

  private getDeviceBasedAdaptations(deviceType: string) {
    if (deviceType === 'mobile') {
      return {
        compactMode: true,
        touchOptimized: true,
      }
    }
    return {}
  }

  private updateBehaviorProfile(
    current: UserProfile['behaviorProfile'],
    interaction: unknown
  ): UserProfile['behaviorProfile'] {
    // Ensure we have a valid behavior profile
    const updated = current ? { ...current } : this.getDefaultBehaviorProfile()

    // Update most used features
    if (interaction.type === 'feature_use') {
      const feature = interaction.element
      const currentFeatures = updated.mostUsedFeatures || []
      const index = currentFeatures.indexOf(feature)

      if (index > -1) {
        // Move to front
        currentFeatures.splice(index, 1)
        currentFeatures.unshift(feature)
      } else {
        // Add to front
        currentFeatures.unshift(feature)
        if (currentFeatures.length > 10) {
          currentFeatures.pop()
        }
      }
      updated.mostUsedFeatures = currentFeatures
    }

    // Update time of day usage
    const hour = new Date().getHours()
    updated.timeOfDayUsage = updated.timeOfDayUsage || {}
    updated.timeOfDayUsage[hour] = (updated.timeOfDayUsage[hour] || 0) + 1

    // Update click patterns
    if (interaction.element) {
      updated.clickPatterns = updated.clickPatterns || {}
      updated.clickPatterns[interaction.element] =
        (updated.clickPatterns[interaction.element] || 0) + 1
    }

    return updated
  }

  private async generateAdaptations(
    profile: UserProfile,
    interaction: unknown
  ): Promise<AdaptationEvent[]> {
    const adaptations: AdaptationEvent[] = []

    // Generate adaptations based on interaction patterns
    if (interaction.type === 'navigation' && profile.learningData.interactions > 10) {
      adaptations.push({
        timestamp: new Date(),
        type: 'navigation',
        change: 'Updated navigation suggestions',
        reason: 'Frequent navigation pattern detected',
        confidence: 0.7,
      })
    }

    return adaptations
  }

  private async trackPersonalizationEvent(userId: string, interaction: unknown): Promise<void> {
    try {
      await prisma.contextEvent.create({
        data: {
          userId,
          companyId: interaction.context.companyId,
          eventType: 'personalization_interaction',
          eventData: {
            type: interaction.type,
            element: interaction.element,
            action: interaction.action,
            metadata: interaction.metadata,
          },
          sessionId: interaction.context.sessionId,
          timestamp: new Date(),
        },
      })
    } catch {
      logger.error('Error tracking personalization event:', _error)
    }
  }

  private getTimeBasedNavigationSuggestions(
    timeUsage: Record<string, number>,
    currentHour: number
  ) {
    const suggestions = []
    const similarHours = Object.entries(timeUsage)
      .filter(([hour, count]) => Math.abs(parseInt(hour) - currentHour) <= 2)
      .sort(([, a], [, b]) => b - a)

    if (similarHours.length > 0) {
      suggestions.push({
        path: '/dashboard',
        label: 'Dashboard',
        confidence: 0.8,
        reason: 'Frequently accessed at this time',
      })
    }

    return suggestions
  }

  private getPatternBasedNavigationSuggestions(patterns: string[], currentPage: string) {
    const suggestions = []

    // Find common next pages
    const nextPages = patterns
      .filter(pattern => pattern.includes(currentPage))
      .map(pattern => {
        const pages = pattern.split(' -> ')
        const currentIndex = pages.indexOf(currentPage)
        return pages[currentIndex + 1]
      })
      .filter(Boolean)

    const uniqueNextPages = [...new Set(nextPages)]

    for (const page of uniqueNextPages.slice(0, 3)) {
      suggestions.push({
        path: page,
        label: page.replace('/', '').replace('-', ' '),
        confidence: 0.6,
        reason: 'Common navigation pattern',
      })
    }

    return suggestions
  }

  private getFeatureBasedNavigationSuggestions(mostUsedFeatures: string[]) {
    const featurePageMap: Record<string, string> = {
      'theme-customization': '/settings/appearance',
      'dashboard-widgets': '/dashboard',
      'user-management': '/settings/users',
      analytics: '/analytics',
    }

    return mostUsedFeatures
      .slice(0, 3)
      .map(feature => ({
        path: featurePageMap[feature] || `/${feature}`,
        label: feature.replace('-', ' '),
        confidence: 0.7,
        reason: 'Frequently used feature',
      }))
      .filter(suggestion => suggestion.path !== '/undefined')
  }
}

// Export singleton instance
export const personalizationEngine = PersonalizationEngine.getInstance()
