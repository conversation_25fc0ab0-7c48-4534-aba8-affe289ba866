/**
 * API utility functions for handling URLs in different environments
 * Follows first principles: URLs should work in browser, test, and server environments
 */

/**
 * Get the base URL for API calls
 * - In browser: use relative URLs (empty string)
 * - In test environment: use localhost with current port
 * - In server environment: use localhost with current port
 */
export function getApiBaseUrl(): string {
  // In test environment, always use absolute URLs
  if (process.env.NODE_ENV === 'test') {
    const port = process.env.PORT || '3000'
    return `http://localhost:${port}`
  }

  // In browser environment, use relative URLs
  if (typeof window !== 'undefined') {
    return ''
  }

  // In Node.js environment (SSR), use absolute URL
  const port = process.env.PORT || '3000'
  return `http://localhost:${port}`
}

/**
 * Create a full API URL that works in all environments
 */
export function createApiUrl(path: string): string {
  const baseUrl = getApiBaseUrl()
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

/**
 * Fetch wrapper that handles URLs properly in all environments
 */
export async function apiFetch(path: string, options?: RequestInit): Promise<Response> {
  const url = createApiUrl(path)
  return fetch(url, options)
}
