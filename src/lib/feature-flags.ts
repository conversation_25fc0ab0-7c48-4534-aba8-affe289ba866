import { PrismaClient } from '@prisma/client'
import { RedisService } from '@/services/redis-service'
import { cache } from 'react'

// Initialize services
const prisma = new PrismaClient()
const redisService = new RedisService()

// Cache TTL in seconds (10 minutes)
const CACHE_TTL = 60 * 10

// Default feature flags for MVP - Updated for Enhanced MVP
const DEFAULT_FEATURES: Record<string, boolean> = {
  // Basic features - always enabled
  teamDashboard: true,
  settings: true,
  notifications: true,

  // Enhanced MVP features - now enabled
  contextAwareness: true, // Enable basic context awareness
  smartRecommendations: true, // Enable basic recommendations
  behavioralTracking: true, // Enable user behavior tracking
  personalizedUI: true, // Enable basic personalization
  intelligentDefaults: true, // Enable smart defaults

  // Advanced features - still deferred
  advancedMLPredictions: false,
  complexSimulations: false,
  fullGenAI: false,

  // Company-specific features
  advancedAnalytics: true,
  multiLanguage: false,
}

/**
 * Feature flags utility for controlling feature availability.
 * This implementation includes Redis caching with a 10-minute TTL.
 */

/**
 * Check if a feature is enabled for a specific company or user.
 *
 * Cache strategy:
 * - Results are cached in Redis with a 10-minute TTL
 * - If Redis is unavailable, falls back to database query
 * - If all fails, defaults to false (feature disabled)
 *
 * @param companyId The company ID to check the feature for
 * @param featureKey The feature key to check
 * @returns Boolean indicating if the feature is enabled
 */
export const hasFeature = cache(async (companyId: string, featureKey: string): Promise<boolean> => {
  try {
    // First check company-specific override from database/Redis
    const redis = await redisService.getClient()

    if (redis) {
      const cacheKey = `feature:${companyId}:${featureKey}`
      const cached = await redis.get(cacheKey)

      if (cached !== null) {
        return cached === 'true'
      }
    }

    // Check database for company-specific feature flag
    try {
      const featureFlag = await prisma.featureFlag.findFirst({
        where: {
          companyId: parseInt(companyId),
          key: featureKey,
        },
      })

      if (featureFlag) {
        const result = featureFlag.enabled

        // Cache the result
        if (redis) {
          await redis.setex(`feature:${companyId}:${featureKey}`, CACHE_TTL, result.toString())
        }

        return result
      }
    } catch {
      // console.error(`Database error checking feature flag ${featureKey}:`, dbError)
      // Continue to default fallback
    }

    // Fall back to default features for Enhanced MVP
    const defaultValue = DEFAULT_FEATURES[featureKey] ?? false

    // Cache the default value
    if (redis) {
      await redis.setex(`feature:${companyId}:${featureKey}`, CACHE_TTL, defaultValue.toString())
    }

    return defaultValue
  } catch {
    // console.error(`Error checking feature flag ${featureKey}:`, error)
    // On error, use safe defaults
    return DEFAULT_FEATURES[featureKey] ?? false
  }
})

/**
 * Check if multiple features are enabled for a company.
 *
 * @param companyId The company ID to check
 * @param featureKeys Array of feature keys to check
 * @returns Object with feature keys mapped to boolean values
 */
export const hasFeatures = cache(
  async (companyId: string, featureKeys: string[]): Promise<Record<string, boolean>> => {
    const results: Record<string, boolean> = {}

    await Promise.all(
      featureKeys.map(async key => {
        results[key] = await hasFeature(companyId, key)
      })
    )

    return results
  }
)

/**
 * Get all enabled features for a company.
 *
 * @param companyId The company ID to check
 * @returns Array of enabled feature keys
 */
export const getEnabledFeatures = cache(async (companyId: string): Promise<string[]> => {
  try {
    const features = await prisma.featureFlag.findMany({
      where: {
        companyId,
        enabled: true,
      },
      select: {
        key: true,
      },
    })

    return features.map(f => f.key)
  } catch {
    // console.error(`Error getting enabled features for company ${companyId}:`, error);
    return []
  }
})

/**
 * Set the state of a feature flag for a company
 * Updates cache and database
 */
export async function setFeature(
  companyId: string,
  featureKey: string,
  isEnabled: boolean
): Promise<boolean> {
  if (!companyId || !featureKey) {
    return false
  }

  try {
    // Update the database
    await prisma.featureFlag.upsert({
      where: {
        companyId_key: {
          companyId,
          key: featureKey,
        },
      },
      update: {
        enabled: isEnabled,
        updatedAt: new Date(),
      },
      create: {
        companyId,
        key: featureKey,
        enabled: isEnabled,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })

    // Update the cache
    const cacheKey = `feature:${companyId}:${featureKey}`
    await redisService.set(cacheKey, isEnabled.toString(), CACHE_TTL)

    return true
  } catch {
    // console.error(`Error setting feature flag ${featureKey} for company ${companyId}:`, error)
    return false
  }
}

/**
 * Get all feature flags for a company
 * Unlike individual flags, this is not cached to ensure accuracy for admin panels
 */
export async function getAllFeatures(companyId: string): Promise<
  {
    key: string
    enabled: boolean
    updatedAt: Date
  }[]
> {
  if (!companyId) {
    return []
  }

  try {
    const features = await prisma.featureFlag.findMany({
      where: { companyId },
      select: {
        key: true,
        enabled: true,
        updatedAt: true,
      },
      orderBy: { key: 'asc' },
    })

    return features
  } catch {
    // console.error(`Error getting all features for company ${companyId}:`, error)
    return []
  }
}

/**
 * Clear the feature flag cache for a company
 * Useful after bulk updates to ensure fresh data
 */
export async function clearFeatureCache(companyId: string): Promise<boolean> {
  try {
    // Get all keys matching the pattern
    const keys = await redisService.keys(`feature:${companyId}:*`)

    if (keys.length > 0) {
      await redisService.del(...keys)
    }

    return true
  } catch {
    // console.error(`Error clearing feature cache for company ${companyId}:`, error)
    return false
  }
}
