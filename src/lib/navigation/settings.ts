import { NavigationZone } from '@/types/navigation'
import {
  Activity,
  Bell,
  BookOpen,
  Brain,
  Building,
  Database,
  Eye,
  FileText,
  Globe,
  Monitor,
  Palette,
  Settings,
  Shield,
  Target,
  User,
  Users,
} from 'lucide-react'

export const settingsNavigationZones: NavigationZone[] = [
  {
    zone: 'Personal Settings',
    items: [
      {
        href: '/settings/profile',
        label: 'Profile',
        icon: User,
        subItems: [
          { href: '/settings/profile/personal-info', label: 'Personal Info', icon: User },
          { href: '/settings/profile/security', label: 'Security', icon: Shield },
          { href: '/settings/profile/notifications', label: 'Notifications', icon: Bell },
          { href: '/settings/profile/activity', label: 'Activity', icon: Activity },
          { href: '/settings/profile/privacy', label: 'Privacy', icon: Eye },
          {
            href: '/settings/profile/connected-accounts',
            label: 'Connected Accounts',
            icon: Monitor,
          },
        ],
      },
    ],
  },
  {
    zone: 'Platform Settings',
    items: [
      {
        href: '/settings/platform/appearance',
        label: 'Appearance',
        icon: Palette,
      },
      {
        href: '/settings/platform/accessibility',
        label: 'Accessibility',
        icon: Eye,
      },
      {
        href: '/settings/platform/language',
        label: 'Language & Region',
        icon: Globe,
      },
      {
        href: '/settings/platform/notifications',
        label: 'Notifications',
        icon: Bell,
      },
      {
        href: '/settings/platform/dashboard',
        label: 'Dashboard',
        icon: Monitor,
      },
      {
        href: '/settings/platform/ai-models',
        label: 'AI Models',
        icon: Brain,
      },
    ],
  },
  {
    zone: 'Team Settings',
    items: [
      {
        href: '/settings/team',
        label: 'Team',
        icon: Users,
      },
    ],
  },
  {
    zone: 'Access Control',
    items: [
      {
        href: '/settings/access-control/users',
        label: 'User Management',
        icon: Users,
      },
      {
        href: '/settings/access-control/departments',
        label: 'Departments',
        icon: Building,
      },
      {
        href: '/settings/access-control/sub-departments',
        label: 'Sub-Departments',
        icon: Building,
      },
      {
        href: '/settings/access-control/audit',
        label: 'Audit Trail',
        icon: FileText,
      },
    ],
  },
  {
    zone: 'Content',
    items: [
      {
        href: '/settings/content/career-paths',
        label: 'Career Paths',
        icon: Target,
      },
      {
        href: '/settings/content/resources',
        label: 'Resource Library',
        icon: BookOpen,
      },
    ],
  },
  {
    zone: 'Advanced',
    items: [
      {
        href: '/settings/advanced',
        label: 'Advanced Settings',
        icon: Settings,
      },
      {
        href: '/settings/advanced/data',
        label: 'Data Management',
        icon: Database,
      },
    ],
  },
]
