import {
  Activity,
  Award,
  BarChart3,
  BookOpen,
  Briefcase,
  // Additional icons for sub-navigation
  Clock,
  Compass,
  Eye,
  GraduationCap,
  // Icons for the 9 navigation areas
  LayoutDashboard,
  MessageCircle,
  // Superadmin icons
  Settings,
  Target,
  TrendingUp as TrendingUpIcon,
  Users,
} from 'lucide-react'

// Define the navigation groups
export const PERSONAL_GROWTH_ZONE = 'Personal'
export const TEAM_CONNECTION_ZONE = 'Team'
export const ORGANIZATION_IMPACT_ZONE = 'Organisation'
export const SUPERADMIN_ZONE = 'Superadmin'
export const OTHER_ZONE = 'Other'

// Sub-navigation item interface
interface SubNavigationItem {
  id: string
  name: string
  href: string
  icon: React.ElementType
  description: string
}

// Navigation item interface
interface NavigationItem {
  id: string
  name: string
  href: string
  icon: React.ElementType
  description: string
  group: string
  features: string[]
  priority: number
  relevanceScore?: number
  isRecommended?: boolean
  notification?: number
  newActivity?: boolean
  aiSuggestion?: string
  subItems?: SubNavigationItem[]
  expanded?: boolean
}

// Define sub-navigation items
const focusSubItems: SubNavigationItem[] = [
  {
    id: 'today',
    name: 'Today',
    href: '/focus/today',
    icon: Clock,
    description: "Today's priorities and tasks",
  },
  {
    id: 'goals',
    name: 'Goals',
    href: '/focus/goals',
    icon: Target,
    description: 'Your personal and professional goals',
  },
  {
    id: 'skills',
    name: 'Skills',
    href: '/focus/skills',
    icon: TrendingUpIcon,
    description: 'Skill development tracking',
  },
  {
    id: 'performance',
    name: 'Performance',
    href: '/focus/performance',
    icon: BarChart3,
    description: 'Performance metrics and insights',
  },
  {
    id: 'insights',
    name: 'Insights',
    href: '/focus/insights',
    icon: Eye,
    description: 'AI-powered insights and recommendations',
  },
]

const growSubItems: SubNavigationItem[] = [
  {
    id: 'learning',
    name: 'Learning',
    href: '/grow/learning',
    icon: GraduationCap,
    description: 'Learning paths and courses',
  },
  {
    id: 'skills',
    name: 'Skills',
    href: '/grow/skills',
    icon: TrendingUpIcon,
    description: 'Skill assessments and development',
  },
  {
    id: 'career',
    name: 'Career',
    href: '/grow/career',
    icon: Briefcase,
    description: 'Career planning and progression',
  },
  {
    id: 'development',
    name: 'Development',
    href: '/grow/development',
    icon: BookOpen,
    description: 'Personal development resources',
  },
]

// Define the 9 AI-first navigation items with sub-navigation
export const baseNavigationItems: NavigationItem[] = [
  // Personal Zone
  {
    id: 'focus',
    name: 'Focus',
    href: '/focus',
    icon: LayoutDashboard,
    description: 'Your daily command center',
    group: PERSONAL_GROWTH_ZONE,
    features: ['daily-planning', 'goal-tracking', 'productivity'],
    priority: 10,
    subItems: focusSubItems,
    expanded: false,
  },
  {
    id: 'grow',
    name: 'Grow',
    href: '/grow',
    icon: BookOpen,
    description: 'Your learning journey',
    group: PERSONAL_GROWTH_ZONE,
    features: ['learning', 'skills', 'development', 'career-path'],
    priority: 7,
    subItems: growSubItems,
    expanded: false,
  },
  {
    id: 'customize',
    name: 'Customize',
    href: '/settings',
    icon: Settings,
    description: 'System settings and preferences',
    group: PERSONAL_GROWTH_ZONE,
    features: ['settings', 'preferences', 'profile', 'appearance'],
    priority: 5,
  },

  // Team Connection Zone
  {
    id: 'team',
    name: 'Team',
    href: '/team',
    icon: Users,
    description: 'Your squad management',
    group: TEAM_CONNECTION_ZONE,
    features: ['team-management', 'leadership', 'delegation'],
    priority: 8,
  },
  {
    id: 'connect',
    name: 'Connect',
    href: '/connect',
    icon: MessageCircle,
    description: 'Communication & feedback',
    group: TEAM_CONNECTION_ZONE,
    features: ['feedback', 'communication', 'collaboration'],
    priority: 6,
  },
  {
    id: 'celebrate',
    name: 'Celebrate',
    href: '/celebrate',
    icon: Award,
    description: 'Recognition & community',
    group: TEAM_CONNECTION_ZONE,
    features: ['recognition', 'rewards', 'culture'],
    priority: 5,
  },

  // Organization Impact Zone
  {
    id: 'vision',
    name: 'Vision',
    href: '/vision',
    icon: Target,
    description: 'Company strategy & alignment',
    group: ORGANIZATION_IMPACT_ZONE,
    features: ['strategy', 'goals', 'okrs', 'alignment'],
    priority: 7,
  },
  {
    id: 'explore',
    name: 'Explore',
    href: '/explore',
    icon: Compass,
    description: 'Internal opportunities',
    group: ORGANIZATION_IMPACT_ZONE,
    features: ['opportunities', 'projects', 'growth'],
    priority: 6,
  },
  {
    id: 'pulse',
    name: 'Pulse',
    href: '/pulse',
    icon: Activity,
    description: 'Organizational insights',
    group: ORGANIZATION_IMPACT_ZONE,
    features: ['analytics', 'insights', 'trends', 'reporting'],
    priority: 7,
  },
]

// Superadmin-only navigation items
export const superadminNavigationItems: NavigationItem[] = [
  {
    id: 'superadmin',
    name: 'Superadmin',
    href: '/superadmin',
    icon: Settings,
    description: 'System administration panel',
    group: SUPERADMIN_ZONE,
    features: ['system-admin', 'user-management', 'system-config'],
    priority: 10,
  },
]
