import { NavigationZone } from '@/types/navigation'
import {
  Activity,
  BarChart3,
  Bell,
  Building,
  Code,
  Crown,
  Database,
  FileText,
  Flag,
  Globe,
  Key,
  LayoutDashboard,
  Mail,
  Palette,
  Settings,
  Shield,
  Users,
} from 'lucide-react'

export const superadminNavigationZones: NavigationZone[] = [
  {
    zone: 'Overview',
    items: [
      {
        href: '/superadmin/dashboard',
        label: 'Dashboard',
        icon: LayoutDashboard,
        subItems: [
          {
            href: '/superadmin/dashboard/overview',
            label: 'System Overview',
            icon: LayoutDashboard,
          },
          { href: '/superadmin/dashboard/metrics', label: 'Key Metrics', icon: BarChart3 },
          { href: '/superadmin/dashboard/health', label: 'System Health', icon: Activity },
        ],
      },
      {
        href: '/superadmin/analytics',
        label: 'Analytics',
        icon: BarChart3,
        subItems: [
          { href: '/superadmin/analytics/usage', label: 'Usage Analytics', icon: BarChart3 },
          { href: '/superadmin/analytics/performance', label: 'Performance', icon: Activity },
          { href: '/superadmin/analytics/trends', label: 'Trends & Insights', icon: BarChart3 },
        ],
      },
    ],
  },
  {
    zone: 'Company Management',
    items: [
      {
        href: '/superadmin/companies',
        label: 'Companies',
        icon: Building,
        subItems: [
          { href: '/superadmin/companies/list', label: 'All Companies', icon: Building },
          { href: '/superadmin/companies/add', label: 'Add Company', icon: Building },
          { href: '/superadmin/companies/billing', label: 'Billing Overview', icon: FileText },
        ],
      },
      {
        href: '/superadmin/users',
        label: 'User Management',
        icon: Users,
        subItems: [
          { href: '/superadmin/users/all', label: 'All Users', icon: Users },
          { href: '/superadmin/users/admins', label: 'Company Admins', icon: Crown },
          { href: '/superadmin/users/activity', label: 'User Activity', icon: Activity },
        ],
      },
    ],
  },
  {
    zone: 'Platform Configuration',
    items: [
      {
        href: '/superadmin/design-system',
        label: 'Design System',
        icon: Palette,
        subItems: [
          { href: '/superadmin/design-system/components', label: 'Component Library', icon: Code },
          { href: '/superadmin/design-system/themes', label: 'Theme Management', icon: Palette },
          {
            href: '/superadmin/design-system/preview',
            label: 'Live Preview',
            icon: LayoutDashboard,
          },
        ],
      },
      {
        href: '/superadmin/feature-flags',
        label: 'Feature Flags',
        icon: Flag,
        subItems: [
          { href: '/superadmin/feature-flags/manage', label: 'Manage Flags', icon: Flag },
          { href: '/superadmin/feature-flags/rollout', label: 'Feature Rollout', icon: Activity },
          { href: '/superadmin/feature-flags/analytics', label: 'Flag Analytics', icon: BarChart3 },
        ],
      },
      {
        href: '/superadmin/email-validation',
        label: 'Email Validation',
        icon: Mail,
        subItems: [
          { href: '/superadmin/email-validation/domains', label: 'Domain Whitelist', icon: Globe },
          {
            href: '/superadmin/email-validation/verification',
            label: 'Verification Queue',
            icon: Mail,
          },
          {
            href: '/superadmin/email-validation/settings',
            label: 'Validation Settings',
            icon: Settings,
          },
        ],
      },
    ],
  },
  {
    zone: 'System Administration',
    items: [
      {
        href: '/superadmin/security',
        label: 'Security',
        icon: Shield,
        subItems: [
          { href: '/superadmin/security/audit-logs', label: 'Audit Logs', icon: FileText },
          { href: '/superadmin/security/access-control', label: 'Access Control', icon: Key },
          { href: '/superadmin/security/monitoring', label: 'Security Monitoring', icon: Shield },
        ],
      },
      {
        href: '/superadmin/system',
        label: 'System Settings',
        icon: Settings,
        subItems: [
          {
            href: '/superadmin/system/configuration',
            label: 'System Configuration',
            icon: Settings,
          },
          { href: '/superadmin/system/database', label: 'Database Management', icon: Database },
          { href: '/superadmin/system/notifications', label: 'System Notifications', icon: Bell },
        ],
      },
    ],
  },
]
