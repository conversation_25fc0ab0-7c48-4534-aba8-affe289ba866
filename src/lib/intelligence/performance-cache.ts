/**
 * Intelligence Layer Performance Cache
 * Handles caching and performance optimization for context processing, AI recommendations, and analytics
 * Part of Phase 3: Intelligence Layer Implementation - Task 3.5
 */

import { redis } from '@/lib/redis'
import { hasFeature } from '@/lib/feature-flags'

export interface CacheConfig {
  ttl: number // Time to live in seconds
  prefix: string
  compression?: boolean
  serialization?: 'json' | 'msgpack'
}

export interface PerformanceMetrics {
  cacheHits: number
  cacheMisses: number
  avgResponseTime: number
  totalRequests: number
  errorRate: number
}

export class IntelligenceCache {
  private static instance: IntelligenceCache
  private metrics: Map<string, PerformanceMetrics> = new Map()

  // Cache configurations for different data types
  private readonly cacheConfigs: Record<string, CacheConfig> = {
    context: { ttl: 3600, prefix: 'intel:context:', compression: true }, // 1 hour
    recommendations: { ttl: 1800, prefix: 'intel:rec:', compression: true }, // 30 minutes
    analytics: { ttl: 900, prefix: 'intel:analytics:', compression: false }, // 15 minutes
    insights: { ttl: 7200, prefix: 'intel:insights:', compression: true }, // 2 hours
    patterns: { ttl: 14400, prefix: 'intel:patterns:', compression: true }, // 4 hours
  }

  private constructor() {}

  static getInstance(): IntelligenceCache {
    if (!IntelligenceCache.instance) {
      IntelligenceCache.instance = new IntelligenceCache()
    }
    return IntelligenceCache.instance
  }

  /**
   * Get cached data with performance tracking
   */
  async get<T>(
    type: keyof typeof this.cacheConfigs,
    key: string,
    companyId: string
  ): Promise<T | null> {
    const startTime = Date.now()
    const config = this.cacheConfigs[type]
    const cacheKey = `${config.prefix}${companyId}:${key}`

    try {
      // Check if intelligence caching is enabled for this company
      if (!(await hasFeature(companyId, 'intelligenceCaching'))) {
        this.updateMetrics(type, false, Date.now() - startTime)
        return null
      }

      const cached = await redis.get(cacheKey)

      if (cached) {
        this.updateMetrics(type, true, Date.now() - startTime)
        return this.deserialize<T>(cached, config)
      }

      this.updateMetrics(type, false, Date.now() - startTime)
      return null
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error(`Cache get error for ${cacheKey}:`, error)
      this.updateMetrics(type, false, Date.now() - startTime, true)
      return null
    }
  }

  /**
   * Set cached data with compression and TTL
   */
  async set<T>(
    type: keyof typeof this.cacheConfigs,
    key: string,
    value: T,
    companyId: string,
    customTtl?: number
  ): Promise<boolean> {
    const config = this.cacheConfigs[type]
    const cacheKey = `${config.prefix}${companyId}:${key}`
    const ttl = customTtl || config.ttl

    try {
      // Check if intelligence caching is enabled for this company
      if (!(await hasFeature(companyId, 'intelligenceCaching'))) {
        return false
      }

      const serialized = this.serialize(value, config)
      await redis.setex(cacheKey, ttl, serialized)
      return true
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error(`Cache set error for ${cacheKey}:`, error)
      return false
    }
  }

  /**
   * Delete cached data
   */
  async delete(
    type: keyof typeof this.cacheConfigs,
    key: string,
    companyId: string
  ): Promise<boolean> {
    const config = this.cacheConfigs[type]
    const cacheKey = `${config.prefix}${companyId}:${key}`

    try {
      const result = await redis.del(cacheKey)
      return result > 0
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error(`Cache delete error for ${cacheKey}:`, error)
      return false
    }
  }

  /**
   * Invalidate all cache entries for a specific type and company
   */
  async invalidateByType(type: keyof typeof this.cacheConfigs, companyId: string): Promise<number> {
    const config = this.cacheConfigs[type]
    const pattern = `${config.prefix}${companyId}:*`

    try {
      const keys = await redis.keys(pattern)
      if (keys.length === 0) return 0

      const result = await redis.del(...keys)
      return result
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error(`Cache invalidation error for pattern ${pattern}:`, error)
      return 0
    }
  }

  /**
   * Get or set with automatic caching
   */
  async getOrSet<T>(
    type: keyof typeof this.cacheConfigs,
    key: string,
    companyId: string,
    fetcher: () => Promise<T>,
    customTtl?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(type, key, companyId)
    if (cached !== null) {
      return cached
    }

    // Fetch fresh data
    const fresh = await fetcher()

    // Cache the result
    await this.set(type, key, fresh, companyId, customTtl)

    return fresh
  }

  /**
   * Batch get multiple cache entries
   */
  async batchGet<T>(
    type: keyof typeof this.cacheConfigs,
    keys: string[],
    companyId: string
  ): Promise<Map<string, T | null>> {
    const config = this.cacheConfigs[type]
    const cacheKeys = keys.map(key => `${config.prefix}${companyId}:${key}`)
    const results = new Map<string, T | null>()

    try {
      if (!(await hasFeature(companyId, 'intelligenceCaching'))) {
        keys.forEach(key => results.set(key, null))
        return results
      }

      const pipeline = redis.pipeline()
      cacheKeys.forEach(cacheKey => pipeline.get(cacheKey))

      const responses = await pipeline.exec()

      keys.forEach((key, index) => {
        const response = responses?.[index]
        if (response && response[1]) {
          results.set(key, this.deserialize<T>(response[1] as string, config))
        } else {
          results.set(key, null)
        }
      })

      return results
    } catch {
      if (process.env.NODE_ENV === 'development') console.error('Batch cache get error:', error)
      keys.forEach(key => results.set(key, null))
      return results
    }
  }

  /**
   * Get performance metrics for monitoring
   */
  getMetrics(
    type?: keyof typeof this.cacheConfigs
  ): PerformanceMetrics | Map<string, PerformanceMetrics> {
    if (type) {
      return (
        this.metrics.get(type) || {
          cacheHits: 0,
          cacheMisses: 0,
          avgResponseTime: 0,
          totalRequests: 0,
          errorRate: 0,
        }
      )
    }
    return new Map(this.metrics)
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(type?: keyof typeof this.cacheConfigs): void {
    if (type) {
      this.metrics.delete(type)
    } else {
      this.metrics.clear()
    }
  }

  /**
   * Get cache hit ratio for monitoring
   */
  getCacheHitRatio(type: keyof typeof this.cacheConfigs): number {
    const metrics = this.metrics.get(type)
    if (!metrics || metrics.totalRequests === 0) return 0

    return metrics.cacheHits / metrics.totalRequests
  }

  /**
   * Serialize data based on configuration
   */
  private serialize<T>(value: T, config: CacheConfig): string {
    const json = JSON.stringify(value)

    // Add compression if enabled (simple base64 for now, could use gzip)
    if (config.compression && json.length > 1000) {
      return Buffer.from(json).toString('base64')
    }

    return json
  }

  /**
   * Deserialize data based on configuration
   */
  private deserialize<T>(data: string, config: CacheConfig): T {
    try {
      // Check if data is base64 encoded (compressed)
      if (config.compression && !data.startsWith('{') && !data.startsWith('[')) {
        const decoded = Buffer.from(data, 'base64').toString('utf-8')
        return JSON.parse(decoded)
      }

      return JSON.parse(data)
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Cache deserialization error:', error)
      throw error
    }
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(
    type: keyof typeof this.cacheConfigs,
    hit: boolean,
    responseTime: number,
    error: boolean = false
  ): void {
    const current = this.metrics.get(type) || {
      cacheHits: 0,
      cacheMisses: 0,
      avgResponseTime: 0,
      totalRequests: 0,
      errorRate: 0,
    }

    current.totalRequests++

    if (error) {
      current.errorRate =
        (current.errorRate * (current.totalRequests - 1) + 1) / current.totalRequests
    } else {
      if (hit) {
        current.cacheHits++
      } else {
        current.cacheMisses++
      }
    }

    // Update average response time
    current.avgResponseTime =
      (current.avgResponseTime * (current.totalRequests - 1) + responseTime) / current.totalRequests

    this.metrics.set(type, current)
  }
}

// Export singleton instance
export const intelligenceCache = IntelligenceCache.getInstance()

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static timers: Map<string, number> = new Map()

  static startTimer(operation: string): void {
    this.timers.set(operation, Date.now())
  }

  static endTimer(operation: string): number {
    const start = this.timers.get(operation)
    if (!start) return 0

    const duration = Date.now() - start
    this.timers.delete(operation)
    return duration
  }

  static async measureAsync<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<{ result: T; duration: number }> {
    const start = Date.now()
    const result = await fn()
    const duration = Date.now() - start

    return { result, duration }
  }

  static measure<T>(operation: string, fn: () => T): { result: T; duration: number } {
    const start = Date.now()
    const result = fn()
    const duration = Date.now() - start

    return { result, duration }
  }
}

/**
 * Cache warming utilities for preloading frequently accessed data
 */
export class CacheWarmer {
  private static instance: CacheWarmer
  private cache = intelligenceCache

  static getInstance(): CacheWarmer {
    if (!CacheWarmer.instance) {
      CacheWarmer.instance = new CacheWarmer()
    }
    return CacheWarmer.instance
  }

  /**
   * Warm up context cache for active users
   */
  async warmContextCache(companyId: string, userIds: string[]): Promise<void> {
    // console.log(`Warming context cache for ${userIds.length} users in company ${companyId}`);

    // This would typically fetch and cache user contexts
    // Implementation depends on your context service
    for (const userId of userIds) {
      try {
        // Placeholder for context warming logic
        await this.cache.set('context', `user:${userId}`, { warmed: true }, companyId, 3600)
      } catch {
        // console.error(`Failed to warm context cache for user ${userId}:`, error);
      }
    }
  }

  /**
   * Warm up recommendations cache
   */
  async warmRecommendationsCache(companyId: string, userIds: string[]): Promise<void> {
    // console.log(`Warming recommendations cache for ${userIds.length} users in company ${companyId}`);

    for (const userId of userIds) {
      try {
        // Placeholder for recommendations warming logic
        await this.cache.set('recommendations', `user:${userId}`, { warmed: true }, companyId, 1800)
      } catch {
        // console.error(`Failed to warm recommendations cache for user ${userId}:`, error);
      }
    }
  }
}

export const cacheWarmer = CacheWarmer.getInstance()
