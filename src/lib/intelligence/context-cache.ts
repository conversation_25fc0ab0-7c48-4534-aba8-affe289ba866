/**
 * Context Cache
 * Handles caching of processed context data with TTL support
 */

export class ContextCache {
  private cache: Map<string, { data: unknown; expiry: number }> = new Map()
  private defaultTTL = 3600000 // 1 hour in milliseconds

  /**
   * Cache processed context data
   */
  async cacheContext(userId: string, context: unknown, ttl?: number): Promise<void> {
    try {
      const expiryTime = Date.now() + (ttl || this.defaultTTL)

      this.cache.set(userId, {
        data: context,
        expiry: expiryTime,
      })

      // In production, this would use Redis
      // console.log(`Context cached for user ${userId} with TTL ${ttl || this.defaultTTL}ms`);
    } catch {
      // console.error('Error caching context:', error);
      throw error
    }
  }

  /**
   * Retrieve cached context data
   */
  async getContext(userId: string): Promise<any | null> {
    try {
      const cached = this.cache.get(userId)

      if (!cached) {
        return null
      }

      // Check if expired
      if (Date.now() > cached.expiry) {
        this.cache.delete(userId)
        return null
      }

      return cached.data
    } catch {
      // console.error('Error retrieving cached context:', error);
      return null
    }
  }

  /**
   * Invalidate cached context
   */
  async invalidateContext(userId: string): Promise<void> {
    try {
      this.cache.delete(userId)
      // console.log(`Context invalidated for user ${userId}`);
    } catch {
      // console.error('Error invalidating context:', error);
    }
  }

  /**
   * Clear all cached contexts
   */
  async clearAll(): Promise<void> {
    try {
      this.cache.clear()
      // console.log('All cached contexts cleared');
    } catch {
      // console.error('Error clearing cache:', error);
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{ size: number; keys: string[] }> {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    }
  }

  /**
   * Clean up expired entries
   */
  async cleanup(): Promise<void> {
    try {
      const now = Date.now()
      const expiredKeys: string[] = []

      for (const [key, value] of this.cache.entries()) {
        if (now > value.expiry) {
          expiredKeys.push(key)
        }
      }

      expiredKeys.forEach(key => this.cache.delete(key))

      if (expiredKeys.length > 0) {
        // console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
      }
    } catch {
      // console.error('Error during cache cleanup:', error);
    }
  }
}
