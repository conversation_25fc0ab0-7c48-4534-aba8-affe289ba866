/**
 * Context Analyzer
 * Analyzes user context data to generate insights and patterns
 */

import { UserContextData, ContextInsights } from './types'

export class ContextAnalyzer {
  /**
   * Generate insights from user context data
   */
  async generateInsights(context: UserContextData | any): Promise<ContextInsights> {
    try {
      const usagePatterns = this.analyzeUsagePatterns(context)
      const engagementScore = this.calculateEngagementScore(context)
      const preferredFeatures = this.identifyPreferredFeatures(context)
      const skillGapAnalysis = this.analyzeSkillGaps(context)
      const behaviorPatterns = this.analyzeBehaviorPatterns(context)

      return {
        usagePatterns,
        engagementScore,
        preferredFeatures,
        skillGapAnalysis,
        behaviorPatterns,
      }
    } catch {
      // console.error('Error generating insights:', error);

      // Return default insights on error
      return {
        usagePatterns: [],
        engagementScore: 0,
        preferredFeatures: [],
        skillGapAnalysis: [],
        behaviorPatterns: [],
      }
    }
  }

  /**
   * Analyze usage patterns from user actions
   */
  private analyzeUsagePatterns(context: unknown): string[] {
    const patterns: string[] = []

    // Safely handle recentActions - check if it's an array
    if (
      context.recentActions &&
      Array.isArray(context.recentActions) &&
      context.recentActions.length > 0
    ) {
      const dashboardVisits = context.recentActions.filter(
        (action: unknown) => action.entityType === 'dashboard'
      ).length

      if (dashboardVisits > 0) {
        patterns.push('dashboard-focused')
      }

      const reportUsage = context.recentActions.filter(
        (action: unknown) => action.entityType === 'reports'
      ).length

      if (reportUsage > 0) {
        patterns.push('analytics-oriented')
      }
    }

    if (context.historicalData?.mostUsedFeatures?.includes('dashboard')) {
      patterns.push('dashboard-focused')
    }

    return patterns
  }

  /**
   * Calculate user engagement score (0-1)
   */
  private calculateEngagementScore(context: unknown): number {
    let score = 0

    // Base score from session time
    if (context.historicalData?.avgSessionTime) {
      score += Math.min(context.historicalData.avgSessionTime / 3600, 0.5) // Max 0.5 for session time
    }

    // Score from feature usage diversity
    if (context.historicalData?.mostUsedFeatures?.length) {
      score += Math.min(context.historicalData.mostUsedFeatures.length / 10, 0.3) // Max 0.3 for diversity
    }

    // Score from recent activity
    if (context.recentActions?.length) {
      score += Math.min(context.recentActions.length / 20, 0.2) // Max 0.2 for activity
    }

    return Math.min(score, 1) // Cap at 1.0
  }

  /**
   * Identify user's preferred features
   */
  private identifyPreferredFeatures(context: unknown): string[] {
    const features: string[] = []

    if (context.historicalData?.mostUsedFeatures) {
      features.push(...context.historicalData.mostUsedFeatures)
    }

    // Add features from recent actions
    if (context.recentActions && Array.isArray(context.recentActions)) {
      const recentFeatures = context.recentActions
        .map((action: unknown) => action.entityType)
        .filter((feature: string) => feature && !features.includes(feature))

      features.push(...recentFeatures)
    }

    return features.slice(0, 5) // Return top 5 features
  }

  /**
   * Analyze skill gaps
   */
  private analyzeSkillGaps(context: unknown): string[] {
    if (context.skillGaps && Array.isArray(context.skillGaps)) {
      return context.skillGaps
    }

    return []
  }

  /**
   * Analyze behavior patterns
   */
  private analyzeBehaviorPatterns(context: unknown): string[] {
    const patterns: string[] = []

    if (context.historicalData?.loginFrequency === 'daily') {
      patterns.push('highly-engaged')
    }

    if (context.historicalData?.avgSessionTime > 1800) {
      // > 30 minutes
      patterns.push('deep-user')
    }

    if (context.recentActions?.length > 10) {
      patterns.push('active-user')
    }

    return patterns
  }
}
