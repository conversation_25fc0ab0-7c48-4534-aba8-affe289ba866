/**
 * Context Processor
 * Main orchestrator for context processing, aggregation, analysis, and caching
 */

import {
  UserContextData,
  ProcessedContext,
  ContextUpdateEvent,
  ContextSubscriber,
  ValidationResult,
} from './types'
import { ContextAggregator } from './context-aggregator'
import { ContextAnalyzer } from './context-analyzer'
import { ContextCache } from './context-cache'

export class ContextProcessor {
  private aggregator: ContextAggregator
  private analyzer: ContextAnalyzer
  private cache: ContextCache
  private subscribers: Map<string, ContextSubscriber[]> = new Map()

  constructor() {
    this.aggregator = new ContextAggregator()
    this.analyzer = new ContextAnalyzer()
    this.cache = new ContextCache()
  }

  /**
   * Process complete user context pipeline
   */
  async processUserContext(userId: string, companyId: string): Promise<ProcessedContext | null> {
    try {
      // Check cache first
      const cached = await this.cache.getContext(userId)
      if (cached) {
        return cached
      }

      // Aggregate context data
      const contextData = await this.aggregator.aggregateUserContext(userId, companyId)

      // Process for AI consumption
      const processedContext = await this.processContextForAI(contextData)

      if (processedContext) {
        // Cache the processed context
        await this.cache.cacheContext(userId, processedContext)
      }

      return processedContext
    } catch {
      // console.error('Error processing user context:', error);
      return null
    }
  }

  /**
   * Process context data for AI consumption
   */
  async processContextForAI(context: unknown): Promise<ProcessedContext | null> {
    try {
      if (!(await this.validateContext(context))) {
        return null
      }

      // Sanitize sensitive data
      const sanitizedContext = await this.sanitizeContext(context)

      // Generate insights
      const insights = await this.analyzer.generateInsights(sanitizedContext)

      // Create context vector (simplified for now)
      const contextVector = this.generateContextVector(sanitizedContext, insights)

      // Generate basic recommendations
      const recommendations = this.generateRecommendations(insights)

      return {
        userId: sanitizedContext.userId,
        companyId: sanitizedContext.companyId,
        contextVector,
        insights,
        recommendations,
        lastProcessed: new Date(),
      }
    } catch {
      // console.error('Error processing context for AI:', error);
      return null
    }
  }

  /**
   * Update context data in real-time
   */
  async updateContext(userId: string, updateData: Record<string, any>): Promise<void> {
    try {
      // Get current cached context
      const currentContext = await this.cache.getContext(userId)

      if (currentContext) {
        // Merge update data properly
        const updatedContext = {
          ...currentContext,
          ...updateData,
          lastProcessed: new Date(),
        }

        // Update cache
        await this.cache.cacheContext(userId, updatedContext)
      } else {
        // If no cached context, create a new one with the update data
        const newContext = {
          userId,
          ...updateData,
          lastProcessed: new Date(),
        }
        await this.cache.cacheContext(userId, newContext)
      }

      // Notify subscribers
      await this.notifySubscribers(userId, {
        userId,
        type: 'context_update',
        data: updateData,
        timestamp: new Date(),
      })
    } catch {
      // console.error('Error updating context:', error);
    }
  }

  /**
   * Subscribe to context changes
   */
  subscribe(userId: string, callback: ContextSubscriber): void {
    if (!this.subscribers.has(userId)) {
      this.subscribers.set(userId, [])
    }

    this.subscribers.get(userId)!.push(callback)
  }

  /**
   * Unsubscribe from context changes
   */
  unsubscribe(userId: string, callback: ContextSubscriber): void {
    const userSubscribers = this.subscribers.get(userId)
    if (userSubscribers) {
      const index = userSubscribers.indexOf(callback)
      if (index > -1) {
        userSubscribers.splice(index, 1)
      }
    }
  }

  /**
   * Validate context data structure
   */
  async validateContext(context: unknown): Promise<boolean> {
    try {
      // Basic validation
      if (!context || typeof context !== 'object') {
        return false
      }

      // Required fields - check for null/undefined values specifically
      const requiredFields = ['userId', 'companyId']
      for (const field of requiredFields) {
        if (context[field] === null || context[field] === undefined || context[field] === '') {
          return false
        }
      }

      return true
    } catch {
      // console.error('Error validating context:', error);
      return false
    }
  }

  /**
   * Sanitize sensitive data before processing
   */
  async sanitizeContext(context: unknown): Promise<unknown> {
    try {
      const sanitized = { ...context }

      // Remove sensitive fields
      const sensitiveFields = [
        'sensitiveField',
        'personalInfo',
        'ssn',
        'creditCard',
        'password',
        'token',
      ]

      sensitiveFields.forEach(field => {
        delete sanitized[field]
      })

      return sanitized
    } catch {
      // console.error('Error sanitizing context:', error);
      return context
    }
  }

  /**
   * Generate context vector for AI processing
   */
  private generateContextVector(context: unknown, insights: unknown): number[] {
    // Simplified context vector generation
    // In production, this would use more sophisticated ML techniques

    const vector: number[] = []

    // Engagement score
    vector.push(insights.engagementScore || 0)

    // Feature usage diversity
    vector.push((insights.preferredFeatures?.length || 0) / 10)

    // Skill gap count
    vector.push((insights.skillGapAnalysis?.length || 0) / 5)

    // Pad to fixed length
    while (vector.length < 10) {
      vector.push(0)
    }

    return vector.slice(0, 10) // Fixed length of 10
  }

  /**
   * Generate basic recommendations
   */
  private generateRecommendations(insights: unknown): string[] {
    const recommendations: string[] = []

    if (insights.skillGapAnalysis?.length > 0) {
      recommendations.push(`Focus on developing: ${insights.skillGapAnalysis.join(', ')}`)
    }

    if (insights.engagementScore < 0.5) {
      recommendations.push('Consider exploring more platform features')
    }

    if (insights.usagePatterns?.includes('dashboard-focused')) {
      recommendations.push('Try using the reports feature for deeper insights')
    }

    return recommendations
  }

  /**
   * Notify subscribers of context changes
   */
  private async notifySubscribers(userId: string, event: ContextUpdateEvent): Promise<void> {
    try {
      const userSubscribers = this.subscribers.get(userId)
      if (userSubscribers) {
        userSubscribers.forEach(callback => {
          try {
            callback(event)
          } catch {
            // console.error('Error notifying subscriber:', error);
          }
        })
      }
    } catch {
      // console.error('Error notifying subscribers:', error);
    }
  }
}
