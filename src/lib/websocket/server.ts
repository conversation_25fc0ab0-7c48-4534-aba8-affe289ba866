import { WebSocketServer, WebSocket } from 'ws'
import { IncomingMessage, Server } from 'http'
import { parse } from 'url'
import { verify } from 'jsonwebtoken'
import { collaborationEngine } from '../collaboration/real-time-engine'
import {
  WebSocketMessage,
  ComponentUpdateMessage,
  _UserPresenceMessage,
  _ValidationResultMessage,
  _WebSocketMessageType,
} from '@/types/design-system'

interface AuthenticatedWebSocket extends WebSocket {
  userId?: string
  userName?: string
  companyId?: string
  isAlive?: boolean
  subscribedComponents?: Set<string>
  collaborationSessions?: Set<string> // Track collaboration sessions
}

interface _ConnectionInfo {
  userId: string
  userName: string
  companyId: string
  connectedAt: Date
  lastActivity: Date
}

/**
 * Real WebSocket Server for Live Design System and Collaboration
 * Handles real-time component updates, user presence, and collaboration
 */
export class DesignSystemWebSocketServer {
  private wss: WebSocketServer | null = null
  private connections: Map<string, AuthenticatedWebSocket> = new Map()
  private userConnections: Map<string, Set<string>> = new Map() // userId -> Set of connection IDs
  private componentSubscriptions: Map<string, Set<string>> = new Map() // componentId -> Set of connection IDs
  private collaborationSessions: Map<string, Set<string>> = new Map() // sessionId -> Set of connection IDs
  private heartbeatInterval: NodeJS.Timeout | null = null
  private isRunning: boolean = false
  private actualPort: number = 0

  constructor(private serverOrPort: Server | number = 8080) {}

  /**
   * Check if server is already running
   */
  public isServerRunning(): boolean {
    return this.isRunning && this.wss !== null
  }

  /**
   * Get the actual port the server is running on
   */
  public getPort(): number {
    return this.actualPort
  }

  /**
   * Find an available port starting from the specified port
   */
  private async findAvailablePort(startPort: number): Promise<number> {
    const net = await import('net')

    return new Promise((resolve, reject) => {
      const server = net.createServer()

      server.listen(startPort, () => {
        const port = (server.address() as any)?.port
        server.close(() => {
          resolve(port)
        })
      })

      server.on('error', (_err: unknown) => {
        if (err.code === 'EADDRINUSE') {
          // Try next port
          this.findAvailablePort(startPort + 1)
            .then(resolve)
            .catch(reject)
        } else {
          reject(err)
        }
      })
    })
  }

  /**
   * Initialize and start the WebSocket server
   */
  public async start(): Promise<void> {
    if (this.isServerRunning()) {
      // console.log(`🔌 WebSocket server already running on port ${this.actualPort}`);
      return
    }

    try {
      // Check if we received an HTTP server instance or a port number
      if (typeof this.serverOrPort === 'number') {
        // Original behavior - create server on specific port
        this.actualPort = await this.findAvailablePort(this.serverOrPort)

        if (this.actualPort !== this.serverOrPort) {
          // console.log(`🔄 Port ${this.serverOrPort} was busy, using port ${this.actualPort} instead`);
        }

        // Debug environment variables
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.log('🔧 WebSocket server environment check:', {
              hasNextAuthSecret: !!process.env.NEXTAUTH_SECRET,
              secretLength: process.env.NEXTAUTH_SECRET?.length || 0,
              nodeEnv: process.env.NODE_ENV,
              targetPort: this.serverOrPort,
              actualPort: this.actualPort,
            })

        this.wss = new WebSocketServer({
          port: this.actualPort,
          verifyClient: this.verifyClient.bind(this),
        })
      } else {
        // New behavior - attach to existing HTTP server
        const httpServer = this.serverOrPort
        const address = httpServer.address()
        this.actualPort = typeof address === 'object' && address ? address.port : 8080

        // console.log('🔧 WebSocket server attaching to HTTP server on port:', this.actualPort);

        this.wss = new WebSocketServer({
          server: httpServer,
          verifyClient: this.verifyClient.bind(this),
        })
      }

      this.wss.on('connection', this.handleConnection.bind(this))
      this.wss.on('error', this.handleServerError.bind(this))
      this.startHeartbeat()
      this.isRunning = true

      // console.log(`🚀 Design System WebSocket server started on port ${this.actualPort}`);
    } catch {
      // console.error('❌ Failed to start WebSocket server:', _error);
      await this.cleanup()
      throw error
    }
  }

  /**
   * Handle server-level errors
   */
  private handleServerError(error: Error): void {
    // console.error('❌ WebSocket server error:', _error);

    // If it's a port conflict, try to restart on a different port
    if ((error as any).code === 'EADDRINUSE') {
      // console.log('🔄 Port conflict detected, attempting to restart on different port...');
      this.restart().catch(restartError => {
        // console.error('❌ Failed to restart server:', _restartError);
      })
    }
  }

  /**
   * Restart the server on a different port
   */
  private async restart(): Promise<void> {
    await this.stop()
    this.serverOrPort = this.actualPort + 1
    await this.start()
  }

  /**
   * Stop the WebSocket server
   */
  public async stop(): Promise<void> {
    // console.log('🛑 Stopping WebSocket server...');

    await this.cleanup()
    // console.log('🛑 Design System WebSocket server stopped');
  }

  /**
   * Clean up all resources
   */
  private async cleanup(): Promise<void> {
    this.isRunning = false

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    // Close all connections gracefully
    this.connections.forEach((connection, _connectionId) => {
      try {
        connection.close(1000, 'Server shutdown')
      } catch {
        // console.warn(`⚠️ Error closing connection ${connectionId}:`, _error);
      }
    })

    if (this.wss) {
      await new Promise<void>(resolve => {
        this.wss!.close(() => {
          resolve()
        })
      })
      this.wss = null
    }

    this.connections.clear()
    this.userConnections.clear()
    this.componentSubscriptions.clear()
    this.collaborationSessions.clear()
    this.actualPort = 0
  }

  /**
   * Verify client authentication before allowing connection
   */
  private verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): boolean {
    try {
      const url = parse(info.req.url || '', true)
      const token = url.query.token as string

      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log('🔍 WebSocket connection attempt:', {
            hasToken: !!token,
            tokenLength: token?.length || 0,
            tokenPreview: token?.substring(0, 50) + '...',
            fullUrl: info.req.url?.substring(0, 100) + '...',
            origin: info.origin,
            userAgent: info.req.headers['user-agent']?.substring(0, 100),
          })

      // For testing, allow connections without token
      if (!token && process.env.NODE_ENV === 'test') {
        // console.log('✅ WebSocket connection allowed for testing (no token required)');
        return true
      }

      if (!token) {
        // console.log('❌ WebSocket connection rejected: No token provided');
        return false
      }

      // Verify JWT token
      const secret = process.env.NEXTAUTH_SECRET
      if (!secret) {
        // console.error('❌ NEXTAUTH_SECRET not configured');
        return false
      }

      try {
        const decoded = verify(token, secret) as any
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.log('✅ JWT token verified successfully:', {
              userId: decoded.sub,
              email: decoded.email,
              role: decoded.role,
              companyId: decoded.companyId,
              audience: decoded.aud,
              issuer: decoded.iss,
              expiresAt: new Date(decoded.exp * 1000).toISOString(),
            })

        // Check if token is for WebSocket access
        if (decoded.aud !== 'websocket-design-system') {
          // console.log('❌ WebSocket connection rejected: Invalid audience');
          return false
        }

        // Check if user has required role (SUPERADMIN)
        if (decoded.role !== 'SUPERADMIN') {
          // console.log(`❌ WebSocket connection rejected: Insufficient role (${decoded.role})`);
          return false
        }

        // Store user info for this connection
        ;(info.req as any).user = {
          id: decoded.sub,
          email: decoded.email,
          role: decoded.role,
          companyId: decoded.companyId,
        }

        // console.log('✅ WebSocket connection authorized for user:', decoded.email);
        return true
      } catch {
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.log('❌ WebSocket connection rejected: Invalid JWT token:', {
              error: jwtError.message,
              tokenPreview: token?.substring(0, 50) + '...',
            })
        return false
      }
    } catch {
      // console.error('❌ Error during WebSocket client verification:', _error);
      return false
    }
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: AuthenticatedWebSocket, req: IncomingMessage): void {
    const connectionId = this.generateConnectionId()
    const url = parse(req.url || '', true)
    const token = url.query.token as string

    try {
      // For testing, use default user info if no token
      if (!token && process.env.NODE_ENV === 'test') {
        ws.userId = 'test-user-default'
        ws.userName = 'Test User'
        ws.companyId = 'test-company'
      } else {
        // Extract user info from token
        const secret = process.env.NEXTAUTH_SECRET || 'fallback-secret'
        const decoded = verify(token, secret) as any

        ws.userId = decoded.sub
        ws.userName = decoded.name || decoded.email
        ws.companyId = decoded.companyId
      }

      ws.isAlive = true
      ws.subscribedComponents = new Set()
      ws.collaborationSessions = new Set()

      // Store connection
      this.connections.set(connectionId, ws)

      // Track user connections
      if (!this.userConnections.has(ws.userId!)) {
        this.userConnections.set(ws.userId!, new Set())
      }
      this.userConnections.get(ws.userId!)!.add(connectionId)

      // console.log(`🔌 New WebSocket connection: ${ws.userName} (${ws.userId})`);

      // Set up event handlers
      ws.on('message', data => this.handleMessage(connectionId, data))
      ws.on('close', () => this.handleDisconnection(connectionId))
      ws.on('error', error => this.handleError(connectionId, _error))
      ws.on('pong', () => {
        ws.isAlive = true
      })

      // Send welcome message
      this.sendToConnection(connectionId, {
        type: 'SYSTEM_MESSAGE',
        timestamp: new Date(),
        userId: 'system',
        data: {
          message: 'Connected to Design System WebSocket server',
          connectionId,
          serverTime: new Date().toISOString(),
        },
      })

      // Broadcast user presence
      this.broadcastUserPresence(ws.userId!, ws.userName!, 'join')
    } catch {
      // console.error('❌ Error setting up WebSocket connection:', _error);
      ws.close(1008, 'Authentication failed')
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(connectionId: string, data: Buffer): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    try {
      const message: unknown = JSON.parse(data.toString())

      // console.log(`📨 WebSocket message from ${connection.userName}: ${message.type}`);

      switch (message.type) {
        case 'COMPONENT_UPDATE':
          this.handleComponentUpdate(connectionId, message as ComponentUpdateMessage)
          break
        case 'USER_PRESENCE':
          this.handleUserPresence(connectionId, message as _UserPresenceMessage)
          break
        case 'COMPONENT_CREATE':
        case 'COMPONENT_DELETE':
        case 'COMPONENT_PUBLISH':
          this.broadcastToSubscribers(message.data.componentId, message)
          break
        // Collaboration message types
        case 'register':
          this.handleCollaborationRegister(connectionId, message).catch(error => {
            // console.error('Error handling collaboration register:', _error);
          })
          break
        case 'cursor_update':
          this.handleCollaborationCursorUpdate(connectionId, message).catch(error => {
            // console.error('Error handling cursor update:', _error);
          })
          break
        case 'content_change':
          this.handleCollaborationContentChange(connectionId, message).catch(error => {
            // console.error('Error handling content change:', _error);
          })
          break
        default:
        // console.log(`⚠️ Unknown message type: ${message.type}`);
      }

      // Update last activity
      connection.isAlive = true
    } catch {
      // console.error('❌ Error handling WebSocket message:', _error);
      this.sendToConnection(connectionId, {
        type: 'SYSTEM_MESSAGE',
        timestamp: new Date(),
        userId: 'system',
        data: {
          error: 'Invalid message format',
          message: 'Failed to parse message',
        },
      })
    }
  }

  /**
   * Handle component update messages
   */
  private handleComponentUpdate(connectionId: string, message: ComponentUpdateMessage): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    // Validate component access (company-scoped)
    // In production, verify user has access to this component

    // Broadcast to all subscribers of this component
    this.broadcastToSubscribers(message.data.componentId, message, connectionId)

    // If no subscribers, broadcast to all connections except sender (for testing and general updates)
    const subscribers = this.componentSubscriptions.get(message.data.componentId)
    if (!subscribers || subscribers.size === 0) {
      // console.log(`📡 No subscribers for ${message.data.componentId}, broadcasting to all connections`);
      this.connections.forEach((conn, connId) => {
        if (connId !== connectionId) {
          this.sendToConnection(connId, message)
        }
      })
    }

    // console.log(`🔄 Component update broadcasted: ${message.data.componentId}`);
  }

  /**
   * Handle user presence messages
   */
  private handleUserPresence(connectionId: string, message: _UserPresenceMessage): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { action, componentId } = message.data

    if (action === 'editing' && componentId) {
      // Subscribe to component updates
      connection.subscribedComponents!.add(componentId)

      if (!this.componentSubscriptions.has(componentId)) {
        this.componentSubscriptions.set(componentId, new Set())
      }
      this.componentSubscriptions.get(componentId)!.add(connectionId)
    }

    // Broadcast presence update
    this.broadcastUserPresence(connection.userId!, connection.userName!, action, componentId)
  }

  /**
   * Handle collaboration registration
   */
  private async handleCollaborationRegister(connectionId: string, message: unknown): Promise<void> {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { sessionId, userId, userName } = message

    // Register connection with collaboration engine
    collaborationEngine.registerConnection(userId || connection.userId!, connection)

    // Join the collaboration session
    const session = await collaborationEngine.joinSession(
      sessionId,
      userId || connection.userId!,
      userName || connection.userName!
    )

    if (session) {
      // Register connection for collaboration session
      connection.collaborationSessions!.add(sessionId)

      if (!this.collaborationSessions.has(sessionId)) {
        this.collaborationSessions.set(sessionId, new Set())
      }
      this.collaborationSessions.get(sessionId)!.add(connectionId)

      // Send registration confirmation
      this.sendToConnection(connectionId, {
        type: 'registered',
        sessionId,
        userId: connection.userId,
        userName: connection.userName,
        timestamp: new Date(),
      })

      // console.log(`🤝 User ${connection.userName} registered for collaboration session ${sessionId}`);
    } else {
      // Send error if session doesn't exist
      this.sendToConnection(connectionId, {
        type: 'error',
        message: 'Session not found',
        timestamp: new Date(),
      })
    }
  }

  /**
   * Handle collaboration cursor updates
   */
  private async handleCollaborationCursorUpdate(
    connectionId: string,
    message: unknown
  ): Promise<void> {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { sessionId, x, y } = message

    // Update cursor in collaboration engine
    const success = await collaborationEngine.updateCursor(sessionId, connection.userId!, x, y)

    if (success) {
      // Broadcast cursor update to all participants in the session
      this.broadcastToCollaborationSession(sessionId, message, connectionId)
      // console.log(`👆 Cursor update from ${connection.userName} in session ${sessionId}`);
    }
  }

  /**
   * Handle collaboration content changes
   */
  private async handleCollaborationContentChange(
    connectionId: string,
    message: unknown
  ): Promise<void> {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { sessionId, changes } = message

    // Handle content change in collaboration engine
    const success = await collaborationEngine.handleContentChange(
      sessionId,
      connection.userId!,
      changes
    )

    if (success) {
      // Broadcast content change to all participants in the session
      this.broadcastToCollaborationSession(sessionId, message, connectionId)
      // console.log(`📝 Content change from ${connection.userName} in session ${sessionId}`);
    }
  }

  /**
   * Handle connection disconnection
   */
  private handleDisconnection(connectionId: string): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    // console.log(`🔌 WebSocket disconnection: ${connection.userName} (${connection.userId})`);

    // Clean up subscriptions
    if (connection.subscribedComponents) {
      connection.subscribedComponents.forEach(componentId => {
        const subscribers = this.componentSubscriptions.get(componentId)
        if (subscribers) {
          subscribers.delete(connectionId)
          if (subscribers.size === 0) {
            this.componentSubscriptions.delete(componentId)
          }
        }
      })
    }

    // Clean up collaboration sessions
    if (connection.collaborationSessions && connection.userId) {
      // Handle user disconnection in collaboration engine
      collaborationEngine.handleUserDisconnection(connection.userId).catch(error => {
        // console.error('Error handling user disconnection:', _error);
      })

      connection.collaborationSessions.forEach(sessionId => {
        const participants = this.collaborationSessions.get(sessionId)
        if (participants) {
          participants.delete(connectionId)
          if (participants.size === 0) {
            this.collaborationSessions.delete(sessionId)
          }
        }
      })
    }

    // Clean up user connections
    if (connection.userId) {
      const userConnections = this.userConnections.get(connection.userId)
      if (userConnections) {
        userConnections.delete(connectionId)
        if (userConnections.size === 0) {
          this.userConnections.delete(connection.userId)
          // Broadcast user left
          this.broadcastUserPresence(connection.userId, connection.userName!, 'leave')
        }
      }
    }

    // Remove connection
    this.connections.delete(connectionId)
  }

  /**
   * Handle connection errors
   */
  private handleError(connectionId: string, error: Error): void {
    const connection = this.connections.get(connectionId)
    // console.error(`❌ WebSocket error for ${connection?.userName || connectionId}:`, _error);
  }

  /**
   * Broadcast message to all subscribers of a component
   */
  private broadcastToSubscribers(
    componentId: string,
    message: WebSocketMessage,
    excludeConnectionId?: string
  ): void {
    const subscribers = this.componentSubscriptions.get(componentId)
    if (!subscribers) return

    subscribers.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        this.sendToConnection(connectionId, message)
      }
    })
  }

  /**
   * Broadcast message to all participants in a collaboration session
   */
  private broadcastToCollaborationSession(
    sessionId: string,
    message: unknown,
    excludeConnectionId?: string
  ): void {
    const participants = this.collaborationSessions.get(sessionId)
    if (!participants) return

    participants.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        this.sendToConnection(connectionId, message)
      }
    })
  }

  /**
   * Public method to broadcast to collaboration session (for external use)
   */
  public broadcastToSession(sessionId: string, message: unknown): void {
    this.broadcastToCollaborationSession(sessionId, message)
  }

  /**
   * Broadcast user presence to all connections
   */
  private broadcastUserPresence(
    userId: string,
    userName: string,
    action: 'join' | 'leave' | 'editing',
    componentId?: string
  ): void {
    const presenceMessage: _UserPresenceMessage = {
      type: 'USER_PRESENCE',
      timestamp: new Date(),
      userId: 'system',
      data: {
        userId,
        userName,
        action,
        componentId,
      },
    }

    this.broadcast(presenceMessage)
  }

  /**
   * Send message to all connected clients
   */
  private broadcast(message: WebSocketMessage): void {
    this.connections.forEach((connection, _connectionId) => {
      this.sendToConnection(connectionId, message)
    })
  }

  /**
   * Send message to specific connection
   */
  private sendToConnection(connectionId: string, message: WebSocketMessage): void {
    const connection = this.connections.get(connectionId)
    if (!connection || connection.readyState !== WebSocket.OPEN) {
      return
    }

    try {
      connection.send(JSON.stringify(message))
    } catch {
      // console.error(`❌ Failed to send message to ${connectionId}:`, _error);
      this.handleDisconnection(connectionId)
    }
  }

  /**
   * Start heartbeat to detect dead connections
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.connections.forEach((connection, _connectionId) => {
        if (!connection.isAlive) {
          // console.log(`💔 Terminating dead connection: ${connectionId}`);
          connection.terminate()
          this.handleDisconnection(connectionId)
          return
        }

        connection.isAlive = false
        connection.ping()
      })
    }, 30000) // 30 seconds
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get server statistics
   */
  public getStats() {
    return {
      totalConnections: this.connections.size,
      activeUsers: this.userConnections.size,
      componentSubscriptions: this.componentSubscriptions.size,
      uptime: process.uptime(),
    }
  }
}

// Singleton instance
let serverInstance: DesignSystemWebSocketServer | null = null

/**
 * Get or create WebSocket server instance
 */
export function getWebSocketServer(port?: number): DesignSystemWebSocketServer {
  if (!serverInstance) {
    serverInstance = new DesignSystemWebSocketServer(port)
  } else if (port && !serverInstance.isServerRunning()) {
    // If a specific port is requested and server is not running, create new instance
    serverInstance = new DesignSystemWebSocketServer(port)
  }
  return serverInstance
}

/**
 * Start the WebSocket server
 */
export async function startWebSocketServer(port?: number): Promise<DesignSystemWebSocketServer> {
  const server = getWebSocketServer(port)

  if (server.isServerRunning()) {
    // console.log(`🔌 WebSocket server already running on port ${server.getPort()}`);
    return server
  }

  await server.start()
  return server
}

/**
 * Stop the WebSocket server
 */
export async function stopWebSocketServer(): Promise<void> {
  if (serverInstance) {
    await serverInstance.stop()
    serverInstance = null
  }
}

/**
 * Get server status
 */
export function getServerStatus(): { isRunning: boolean; port: number; stats?: unknown } {
  if (!serverInstance) {
    return { isRunning: false, port: 0 }
  }

  return {
    isRunning: serverInstance.isServerRunning(),
    port: serverInstance.getPort(),
    stats: serverInstance.isServerRunning() ? serverInstance.getStats() : undefined,
  }
}
