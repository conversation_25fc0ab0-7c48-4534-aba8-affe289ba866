import { useEffect, useRef, useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import {
  WebSocketMessage,
  ComponentUpdateMessage,
  _UserPresenceMessage,
} from '@/types/design-system'

type ConnectionStatus = 'Connecting' | 'Connected' | 'Disconnected' | 'Error' | 'Offline'

interface _UseWebSocketReturn {
  sendMessage: (message: WebSocketMessage) => void
  lastMessage: WebSocketMessage | null
  connectionStatus: ConnectionStatus
  isConnected: boolean
}

interface WebSocketOptions {
  url: string
  onMessage?: (data: unknown) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (_error: Event) => void
  reconnectAttempts?: number
  reconnectDelay?: number
}

interface WebSocketState {
  isConnected: boolean
  isConnecting: boolean
  error: string | null
  lastMessage: unknown
}

// Enhanced error interface for better debugging
interface WebSocketErrorDetails {
  type: string
  message: string
  code?: number
  reason?: string
  timestamp: string
  readyState: number
  url?: string
  connectionAttempt: number
  // Additional debugging fields
  errorEvent?: {
    type: string
    message: string
    filename?: string
    lineno?: number
    colno?: number
  }
  networkInfo?: {
    onLine: boolean
    effectiveType?: string
    downlink?: number
  }
  browserInfo?: {
    userAgent: string
    language: string
  }
  additionalInfo?: unknown
}

// Circuit breaker pattern for connection stability
interface CircuitBreakerState {
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN'
  failureCount: number
  lastFailureTime: number
  successCount: number
}

// Dynamic port discovery function
async function discoverWebSocketPort(): Promise<{ port: number; url: string }> {
  try {
    // console.log('🔍 Discovering WebSocket server port...');

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000)

    const response = await fetch('/api/websocket/port', {
      method: 'GET',
      headers: { Accept: 'application/json' },
      signal: controller.signal,
    })

    clearTimeout(timeoutId)

    if (response.ok) {
      const data = await response.json()
      // console.log('📊 WebSocket port discovery result:', data);

      if (data.serverRunning) {
        return { port: data.port, url: data.url }
      } else {
        throw new Error(`WebSocket server not running on port ${data.port}`)
      }
    }

    throw new Error(`Port discovery failed with status ${response.status}`)
  } catch (error: unknown) {
    // console.warn('⚠️ Port discovery failed, falling back to default port 8080:', error.message);
    return { port: 8080, url: 'ws://localhost:8080' }
  }
}

export function useWebSocket({
  url,
  onMessage,
  onConnect,
  onDisconnect,
  onError,
  reconnectAttempts = 3,
  reconnectDelay = 1000,
}: WebSocketOptions) {
  const ws = useRef<WebSocket | null>(null)
  const reconnectTimeoutId = useRef<NodeJS.Timeout | null>(null)
  const reconnectCount = useRef(0)
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastMessage: null,
  })

  const connect = useCallback(() => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      return
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }))

    try {
      ws.current = new WebSocket(url)

      ws.current.onopen = () => {
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null,
        }))
        reconnectCount.current = 0
        onConnect?.()
      }

      ws.current.onmessage = event => {
        try {
          const data = JSON.parse(event.data)
          setState(prev => ({ ...prev, lastMessage: data }))
          onMessage?.(data)
        } catch {
          // console.error('Failed to parse WebSocket message:', _error);
        }
      }

      ws.current.onclose = () => {
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
        }))
        onDisconnect?.()

        // Attempt to reconnect with exponential backoff
        if (reconnectCount.current < reconnectAttempts) {
          reconnectCount.current++
          const delay = reconnectDelay * Math.pow(2, reconnectCount.current - 1)
          reconnectTimeoutId.current = setTimeout(() => {
            connect()
          }, delay)
        }
      }

      ws.current.onerror = error => {
        setState(prev => ({
          ...prev,
          error: 'WebSocket connection error',
          isConnecting: false,
        }))
        onError?.(error)
      }
    } catch {
      setState(prev => ({
        ...prev,
        error: 'Failed to create WebSocket connection',
        isConnecting: false,
      }))
    }
  }, [url, onMessage, onConnect, onDisconnect, onError, reconnectAttempts, reconnectDelay])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutId.current) {
      clearTimeout(reconnectTimeoutId.current)
      reconnectTimeoutId.current = null
    }

    if (ws.current) {
      ws.current.close()
      ws.current = null
    }

    reconnectCount.current = 0
    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
    }))
  }, [])

  const sendMessage = useCallback((data: unknown) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      try {
        ws.current.send(JSON.stringify(data))
        return true
      } catch {
        // console.error('Failed to send WebSocket message:', _error);
        return false
      }
    }
    return false
  }, [])

  useEffect(() => {
    connect()

    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  return {
    ...state,
    connect,
    disconnect,
    sendMessage,
  }
}

// Design System specific WebSocket hook with enhanced error handling and circuit breaker
export function useDesignSystemWebSocket() {
  const { data: session, status: sessionStatus } = useSession()
  const [componentUpdates, setComponentUpdates] = useState<any[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [lastMessage, setLastMessage] = useState<any>(null)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const [connectionDetails, setConnectionDetails] = useState<WebSocketErrorDetails | null>(null)
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const connectionAttemptRef = useRef(0)
  const [shouldConnect, setShouldConnect] = useState(false)

  // Circuit breaker state
  const circuitBreakerRef = useRef<CircuitBreakerState>({
    state: 'CLOSED',
    failureCount: 0,
    lastFailureTime: 0,
    successCount: 0,
  })

  const maxReconnectAttempts = 5
  const baseReconnectDelay = 5000 // 5 seconds base delay
  const connectionTimeout = 15000 // 15 seconds
  const circuitBreakerOpenDuration = 60000 // 1 minute
  const maxFailuresBeforeOpen = 3

  // Circuit breaker logic
  const canAttemptConnection = useCallback((): boolean => {
    const breaker = circuitBreakerRef.current
    const now = Date.now()

    switch (breaker.state) {
      case 'CLOSED':
        return true
      case 'OPEN':
        if (now - breaker.lastFailureTime > circuitBreakerOpenDuration) {
          breaker.state = 'HALF_OPEN'
          breaker.successCount = 0
          // console.log('🔄 Circuit breaker moving to HALF_OPEN state');
          return true
        }
        return false
      case 'HALF_OPEN':
        return true
      default:
        return false
    }
  }, [])

  const recordSuccess = useCallback(() => {
    const breaker = circuitBreakerRef.current
    breaker.failureCount = 0
    breaker.successCount++

    if (breaker.state === 'HALF_OPEN' && breaker.successCount >= 2) {
      breaker.state = 'CLOSED'
      // console.log('✅ Circuit breaker reset to CLOSED state');
    }
  }, [])

  const recordFailure = useCallback(() => {
    const breaker = circuitBreakerRef.current
    breaker.failureCount++
    breaker.lastFailureTime = Date.now()

    if (breaker.failureCount >= maxFailuresBeforeOpen) {
      breaker.state = 'OPEN'
      // console.log('🚫 Circuit breaker opened due to excessive failures');
    }
  }, [])

  // Enhanced server availability check
  const checkServerAvailability = useCallback(async (): Promise<boolean> => {
    try {
      // console.log('🔍 Checking WebSocket server availability...');

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      const response = await fetch('/api/websocket/design-system', {
        method: 'GET',
        headers: { Accept: 'application/json' },
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const data = await response.json()
        // console.log('📊 WebSocket server status:', data);
        return data.status === 'running' || data.status === 'ready'
      }

      // console.warn('⚠️ WebSocket server status check failed:', response.status);
      return false
    } catch (error: unknown) {
      if (error.name === 'AbortError') {
        // console.error('❌ Server availability check timed out');
      } else {
        // console.error('❌ Failed to check WebSocket server availability:', _error);
      }
      return false
    }
  }, [])

  // Get authentication token for WebSocket with enhanced error handling
  const getAuthToken = useCallback(async (): Promise<string> => {
    if (!session?.user) {
      throw new Error('No authenticated session found')
    }

    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.log('🎫 Requesting WebSocket token for user:', {
          userId: session.user.id,
          email: session.user.email,
          role: session.user.role,
          companyId: session.user.companyId || 'undefined',
          sessionStatus,
        })

    try {
      const requestBody = {
        email: session.user.email,
        role: session.user.role,
        companyId: session.user.companyId || null,
      }

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000)

      const tokenResponse = await fetch('/api/auth/websocket-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        credentials: 'include',
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!tokenResponse.ok) {
        let errorData
        let errorMessage = `HTTP ${tokenResponse.status}`

        try {
          errorData = await tokenResponse.json()
          errorMessage = errorData.error || errorData.message || errorMessage
        } catch {
          try {
            const responseText = await tokenResponse.text()
            errorMessage = responseText || errorMessage
          } catch {
            // console.warn('Could not parse error response:', textError);
          }
        }

        // Enhanced error logging for token request failures
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.error('❌ WebSocket token request failed:', {
              status: tokenResponse.status,
              statusText: tokenResponse.statusText,
              errorMessage,
              requestBody: {
                email: requestBody.email,
                role: requestBody.role,
                companyId: requestBody.companyId,
              },
              responseHeaders: Object.fromEntries(tokenResponse.headers.entries()),
            })

        throw new Error(`Failed to get WebSocket token: ${errorMessage}`)
      }

      const responseData = await tokenResponse.json()

      if (!responseData.token) {
        // console.error('❌ Token response missing token field:', responseData);
        throw new Error('No token received from server')
      }

      // console.log('✅ WebSocket token received successfully');
      return responseData.token
    } catch (error: unknown) {
      if (error.name === 'AbortError') {
        // console.error('❌ WebSocket token request timed out after 10 seconds');
        throw new Error('Token request timed out - server may be overloaded')
      }

      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('❌ WebSocket token request error:', {
            errorName: error.name,
            errorMessage: error.message,
            stack: error.stack?.substring(0, 500),
          })
      throw error
    }
  }, [session, sessionStatus])

  // Enhanced connection function with circuit breaker and better error handling
  const connect = useCallback(async () => {
    // Check circuit breaker
    if (!canAttemptConnection()) {
      const timeUntilReset = Math.ceil(
        (circuitBreakerOpenDuration - (Date.now() - circuitBreakerRef.current.lastFailureTime)) /
          1000
      )
      // console.log(`🚫 Circuit breaker is open, waiting ${timeUntilReset}s before retry`);
      setConnectionError(
        `Connection temporarily disabled due to repeated failures. Retry in ${timeUntilReset}s`
      )
      return
    }

    // Increment connection attempt counter
    connectionAttemptRef.current += 1
    const currentAttempt = connectionAttemptRef.current

    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.log(`🔍 WebSocket connection attempt #${currentAttempt} - Session debug:`, {
          hasSession: !!session,
          hasUser: !!session?.user,
          userEmail: session?.user?.email,
          userRole: session?.user?.role,
          userId: session?.user?.id,
          sessionStatus,
          circuitBreakerState: circuitBreakerRef.current.state,
          currentUrl: typeof window !== 'undefined' ? window.location.href : 'server-side',
        })

    // Wait for session to be loaded
    if (sessionStatus === 'loading') {
      // console.log('⏳ Waiting for session to load...');
      return
    }

    // Enhanced session validation
    if (sessionStatus === 'unauthenticated' || !session) {
      const authError: WebSocketErrorDetails = {
        type: 'AUTHENTICATION_REQUIRED',
        message:
          'User session is not authenticated - WebSocket connection requires valid authentication',
        timestamp: new Date().toISOString(),
        readyState: -1,
        connectionAttempt: currentAttempt,
      }
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('❌ WebSocket connection failed: No authenticated session', {
            sessionStatus,
            hasSession: !!session,
            hasUser: !!session?.user,
          })
      setConnectionDetails(authError)
      setConnectionError(authError.message)
      setIsConnecting(false)
      recordFailure()
      return
    }

    if (!session.user) {
      const userError: WebSocketErrorDetails = {
        type: 'USER_INFO_MISSING',
        message: 'User information is missing from session - cannot establish WebSocket connection',
        timestamp: new Date().toISOString(),
        readyState: -1,
        connectionAttempt: currentAttempt,
      }
      // console.error('❌ WebSocket connection failed: No user in session', _session);
      setConnectionDetails(userError)
      setConnectionError(userError.message)
      setIsConnecting(false)
      recordFailure()
      return
    }

    // Check user role before attempting connection
    if (session.user.role !== 'SUPERADMIN') {
      const roleError: WebSocketErrorDetails = {
        type: 'INSUFFICIENT_PERMISSIONS',
        message: `WebSocket access requires SUPERADMIN role. Current role: ${session.user.role}`,
        timestamp: new Date().toISOString(),
        readyState: -1,
        connectionAttempt: currentAttempt,
      }
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('❌ WebSocket connection failed: Insufficient role', {
            userRole: session.user.role,
            requiredRole: 'SUPERADMIN',
          })
      setConnectionDetails(roleError)
      setConnectionError(roleError.message)
      setIsConnecting(false)
      recordFailure()
      return
    }

    // Check if already connecting or connected
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      // console.log('⚠️ WebSocket already connected');
      return
    }

    if (wsRef.current && wsRef.current.readyState === WebSocket.CONNECTING) {
      // console.log('⚠️ WebSocket connection already in progress');
      return
    }

    // Clear any existing connection
    if (wsRef.current) {
      // console.log('🧹 Cleaning up existing WebSocket connection');
      wsRef.current.close()
      wsRef.current = null
    }

    setIsConnecting(true)
    setConnectionError(null)
    setConnectionDetails(null)

    try {
      // Check server availability first
      const serverAvailable = await checkServerAvailability()
      if (!serverAvailable) {
        throw new Error('WebSocket server is not available or not running')
      }

      // console.log('🎫 Getting authentication token...');
      const token = await getAuthToken()

      // Discover the actual WebSocket server port dynamically
      // console.log('🔍 Discovering WebSocket server port...');
      const { port, url: wsBaseUrl } = await discoverWebSocketPort()

      const wsUrl = `${wsBaseUrl}?token=${encodeURIComponent(token)}`

      // console.log('🔌 Connecting to Design System WebSocket server...');
      // console.log(`📊 Connection attempt #${currentAttempt} of ${maxReconnectAttempts + 1}`);
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(`📡 WebSocket URL: ${wsUrl.replace(/token=[^&]+/, 'token=***')}`) // Hide token in logs
      // console.log(`🌐 Network status: ${navigator.onLine ? 'Online' : 'Offline'}`);
      // console.log(`🔧 WebSocket constructor about to be called...`);

      const ws = new WebSocket(wsUrl)
      wsRef.current = ws

      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.log(`✅ WebSocket instance created successfully:`, {
            readyState: ws.readyState,
            url: ws.url.replace(/token=[^&]+/, 'token=***'),
            protocol: ws.protocol,
            extensions: ws.extensions,
          })

      // Enhanced connection timeout
      const connectionTimeoutId = setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          // console.log('⏰ WebSocket connection timeout after 15 seconds');
          const timeoutError: WebSocketErrorDetails = {
            type: 'CONNECTION_TIMEOUT',
            message: 'Connection timeout - server may be unavailable or overloaded',
            timestamp: new Date().toISOString(),
            readyState: ws.readyState,
            url: wsBaseUrl,
            connectionAttempt: currentAttempt,
            networkInfo: {
              onLine: navigator.onLine,
            },
            browserInfo: {
              userAgent: navigator.userAgent,
              language: navigator.language,
            },
          }
          setConnectionDetails(timeoutError)
          setConnectionError(timeoutError.message)
          recordFailure()
          ws.close()
        }
      }, connectionTimeout)

      ws.onopen = () => {
        clearTimeout(connectionTimeoutId)
        // console.log('🚀 Connected to Design System WebSocket server');
        setIsConnected(true)
        setIsConnecting(false)
        setConnectionError(null)
        setConnectionDetails(null)
        setReconnectAttempts(0)
        connectionAttemptRef.current = 0
        recordSuccess()
      }

      ws.onmessage = event => {
        try {
          const message = JSON.parse(event.data)
          // console.log('📨 WebSocket message received:', message.type);

          setLastMessage(message)

          if (message.type === 'COMPONENT_UPDATE') {
            setComponentUpdates(prev => [...prev, message])
          }
        } catch {
          // console.error('❌ Failed to parse WebSocket message:', _error);
        }
      }

      ws.onerror = error => {
        clearTimeout(connectionTimeoutId)

        // Enhanced error details extraction
        let errorMessage = 'WebSocket connection error'
        let errorCode = 'WEBSOCKET_ERROR'
        let additionalInfo: unknown = {}

        if (error instanceof ErrorEvent) {
          errorMessage = error.message || 'Network error occurred'
          errorCode = 'NETWORK_ERROR'
          additionalInfo = {
            type: error.type,
            filename: error.filename,
            lineno: error.lineno,
            colno: error.colno,
            timeStamp: error.timeStamp,
          }
        } else if (error instanceof Event) {
          errorMessage = `WebSocket error event: ${error.type}`
          errorCode = 'WEBSOCKET_EVENT_ERROR'
          additionalInfo = {
            type: error.type,
            target: error.target ? 'WebSocket' : 'unknown',
            timeStamp: error.timeStamp,
          }
        }

        const errorDetails: WebSocketErrorDetails = {
          type: errorCode,
          message: errorMessage,
          timestamp: new Date().toISOString(),
          readyState: ws.readyState,
          url: wsBaseUrl,
          connectionAttempt: currentAttempt,
          additionalInfo,
        }

        // console.error('❌ WebSocket error details:', errorDetails);
        // console.error('❌ Raw error object:', _error);
        // console.error('❌ WebSocket ready state:', ws.readyState, 'Expected:', WebSocket.CONNECTING);
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.error('❌ WebSocket URL used:', wsUrl.replace(/token=[^&]+/, 'token=***')) // Hide token in logs

        // Add connection troubleshooting info
        if (process.env.NODE_ENV === 'development')
          if (process.env.NODE_ENV === 'development')
            console.error('🔧 Connection troubleshooting info:', {
              serverHealthCheck: `curl http://localhost:${port}/health`,
              tokenEndpoint: 'POST /api/auth/websocket-token',
              expectedReadyStates: {
                '0': 'CONNECTING',
                '1': 'OPEN',
                '2': 'CLOSING',
                '3': 'CLOSED',
              },
              actualReadyState: ws.readyState,
              networkOnline: navigator.onLine,
              secureContext: isSecureContext,
            })

        setConnectionDetails(errorDetails)
        setConnectionError(errorDetails.message)
        setIsConnecting(false)

        // Only retry if we haven't exceeded max attempts
        if (currentAttempt < maxReconnectAttempts) {
          // Use the same reconnection logic as in onclose handler
          const nextAttempt = reconnectAttempts + 1
          const delay = baseReconnectDelay * Math.pow(2, nextAttempt - 1) // Exponential backoff

          reconnectTimeoutRef.current = setTimeout(() => {
            setReconnectAttempts(nextAttempt)
            connect()
          }, delay)
        } else {
          // console.error('❌ Max WebSocket connection attempts reached, disabling automatic reconnection');
          setConnectionStatus('offline')
        }
      }

      ws.onclose = event => {
        clearTimeout(connectionTimeoutId)

        const closeDetails: WebSocketErrorDetails = {
          type: 'CONNECTION_CLOSED',
          message: `Connection closed with code ${event.code}`,
          code: event.code,
          reason: event.reason,
          timestamp: new Date().toISOString(),
          readyState: ws.readyState,
          url: wsBaseUrl,
          connectionAttempt: currentAttempt,
        }

        // Determine if this was a normal closure or error
        const isNormalClosure = event.code === 1000
        const isServerShutdown = event.code === 1001
        const isAbnormalClosure = event.code === 1006

        if (isAbnormalClosure) {
          closeDetails.type = 'ABNORMAL_CLOSURE'
          closeDetails.message =
            'Connection closed abnormally - server may have crashed or network issues'
          recordFailure()
        } else if (!isNormalClosure && !isServerShutdown) {
          recordFailure()
        }

        // console.log(`🔌 WebSocket connection closed:`, closeDetails);

        setIsConnected(false)
        setIsConnecting(false)
        setConnectionDetails(closeDetails)
        setConnectionError(closeDetails.message)

        // Only attempt reconnection if we haven't exceeded max attempts and circuit breaker allows it
        if (
          reconnectAttempts < maxReconnectAttempts &&
          canAttemptConnection() &&
          !isNormalClosure
        ) {
          const nextAttempt = reconnectAttempts + 1
          const delay = baseReconnectDelay * Math.pow(2, nextAttempt - 1) // Exponential backoff

          // console.log(`🔄 Scheduling reconnection attempt ${nextAttempt}/${maxReconnectAttempts} in ${delay}ms`);

          reconnectTimeoutRef.current = setTimeout(() => {
            setReconnectAttempts(nextAttempt)
            connect()
          }, delay)
        } else if (reconnectAttempts >= maxReconnectAttempts) {
          // console.log('❌ Max reconnection attempts reached, giving up');
          setConnectionError(
            'Max reconnection attempts reached. Please refresh the page or check your connection.'
          )
        }
      }
    } catch (error: unknown) {
      // console.error('❌ Failed to initiate WebSocket connection:', _error);
      setIsConnecting(false)
      setConnectionError(error.message)
      recordFailure()

      // Enhanced error reporting
      const connectionError: WebSocketErrorDetails = {
        type: 'CONNECTION_INIT_ERROR',
        message: error.message || 'Failed to initialize WebSocket connection',
        timestamp: new Date().toISOString(),
        readyState: -1,
        connectionAttempt: currentAttempt,
      }

      setConnectionDetails(connectionError)
    }
  }, [
    session,
    sessionStatus,
    reconnectAttempts,
    checkServerAvailability,
    getAuthToken,
    canAttemptConnection,
    recordSuccess,
    recordFailure,
  ])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    // console.log('🔌 Manually disconnecting WebSocket...');

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Client disconnect')
      wsRef.current = null
    }

    setIsConnected(false)
    setIsConnecting(false)
    setReconnectAttempts(0)
    connectionAttemptRef.current = 0
    setConnectionError(null)
    setConnectionDetails(null)
  }, [])

  // Send message to WebSocket server
  const sendMessage = useCallback(
    (message: unknown) => {
      if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
        // console.warn('⚠️ WebSocket not connected, cannot send message');
        return false
      }

      try {
        const fullMessage = {
          ...message,
          timestamp: new Date(),
          userId: session?.user?.id || 'unknown-user',
        }

        wsRef.current.send(JSON.stringify(fullMessage))
        // console.log('📨 WebSocket message sent:', message.type);
        return true
      } catch {
        // console.error('❌ Failed to send WebSocket message:', _error);
        return false
      }
    },
    [session]
  )

  // Component-specific methods
  const updateComponent = useCallback(
    (componentId: string, componentData: unknown) => {
      return sendMessage({
        type: 'COMPONENT_UPDATE',
        data: {
          componentId,
          updates: componentData,
          changeType: 'style',
        },
      })
    },
    [sendMessage]
  )

  const subscribeToComponent = useCallback(
    (componentId: string) => {
      return sendMessage({
        type: 'USER_PRESENCE',
        data: {
          userId: session?.user?.id || 'unknown-user',
          userName: session?.user?.name || session?.user?.email || 'Unknown User',
          action: 'editing',
          componentId,
        },
      })
    },
    [sendMessage, session]
  )

  const unsubscribeFromComponent = useCallback(
    (componentId: string) => {
      return sendMessage({
        type: 'USER_PRESENCE',
        data: {
          userId: session?.user?.id || 'unknown-user',
          userName: session?.user?.name || session?.user?.email || 'Unknown User',
          action: 'leave',
          componentId,
        },
      })
    },
    [sendMessage, session]
  )

  // Manual retry function with enhanced state reset
  const retry = useCallback(() => {
    // console.log('🔄 Manual retry requested - resetting connection state');

    // Clear any existing timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    // Close existing connection if any
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }

    // Reset all state
    setReconnectAttempts(0)
    connectionAttemptRef.current = 0
    setConnectionError(null)
    setConnectionDetails(null)
    setIsConnected(false)
    setIsConnecting(false)

    // Wait a moment then trigger a fresh connection
    setTimeout(() => {
      // console.log('🔄 Starting fresh connection attempt...');
      setShouldConnect(true)
    }, 500)
  }, []) // Remove connect dependency to prevent loops

  // Connect on mount when session is ready, disconnect on unmount
  useEffect(() => {
    let isActive = true // Prevent state updates after unmount

    if (sessionStatus !== 'loading' && isActive) {
      setShouldConnect(true) // Trigger connection
    }

    return () => {
      isActive = false
      setShouldConnect(false)
      disconnect()
    }
  }, [sessionStatus]) // Remove connect and disconnect from dependencies to prevent loops

  // Separate effect to handle connection when shouldConnect changes
  useEffect(() => {
    if (shouldConnect && sessionStatus !== 'loading') {
      connect()
    }
  }, [shouldConnect, sessionStatus, connect])

  // Manual connect function for external use
  const manualConnect = useCallback(() => {
    setShouldConnect(true)
  }, [])

  // Manual disconnect function for external use
  const manualDisconnect = useCallback(() => {
    setShouldConnect(false)
    disconnect()
  }, [])

  return {
    isConnected,
    isConnecting,
    lastMessage,
    componentUpdates,
    connectionError,
    connectionDetails,
    reconnectAttempts,
    maxReconnectAttempts,
    sendMessage,
    updateComponent,
    subscribeToComponent,
    unsubscribeFromComponent,
    connect: manualConnect,
    disconnect: manualDisconnect,
    retry,
  }
}

// Hook for component-specific updates
export function useComponentUpdates(componentId: string) {
  const [wsUrl, setWsUrl] = useState<string>('ws://localhost:8080') // Fallback
  const [isDiscovering, setIsDiscovering] = useState(true)

  // Discover WebSocket port on mount
  useEffect(() => {
    let isMounted = true

    const discoverPort = async () => {
      try {
        const { url } = await discoverWebSocketPort()
        if (isMounted) {
          setWsUrl(url)
          setIsDiscovering(false)
        }
      } catch {
        // console.warn('⚠️ Port discovery failed for useComponentUpdates, using fallback:', error);
        if (isMounted) {
          setIsDiscovering(false)
        }
      }
    }

    discoverPort()

    return () => {
      isMounted = false
    }
  }, [])

  const { sendMessage, lastMessage, isConnected } = useWebSocket({
    url: wsUrl,
  })
  const [latestUpdate, setLatestUpdate] = useState<ComponentUpdateMessage | null>(null)

  // Filter messages for this specific component
  useEffect(() => {
    if (lastMessage?.type === 'COMPONENT_UPDATE') {
      const updateMessage = lastMessage as ComponentUpdateMessage
      if (updateMessage.data.componentId === componentId) {
        setLatestUpdate(updateMessage)
      }
    }
  }, [lastMessage, componentId])

  const updateComponent = useCallback(
    (updates: unknown) => {
      return sendMessage({
        type: 'COMPONENT_UPDATE',
        data: {
          componentId,
          updates,
          changeType: 'style',
        },
      })
    },
    [sendMessage, componentId]
  )

  return {
    latestUpdate,
    updateComponent,
    isConnected: isConnected && !isDiscovering,
  }
}
