/**
 * Time & Energy Management Service
 * Task 5.13: AI-first time blocking and energy tracking system
 * Provides intelligent time management and energy optimization features
 */

import { prisma } from '@/lib/db'
import { type User } from '@prisma/client'

// Types for time blocks
export interface TimeBlock {
  id: string
  title: string
  description?: string
  startTime: Date
  endTime: Date
  type: 'focus' | 'meeting' | 'break' | 'administrative' | 'creative' | 'learning'
  energyLevel?: 'low' | 'medium' | 'high'
  userId: string
  source?: 'manual' | 'calendar' | 'ai_suggested'
  completed?: boolean
  actualProductivity?: number
  tags?: string[]
  createdAt: Date
  updatedAt: Date
}

// Types for energy tracking
export interface EnergyLevel {
  id: string
  level: number // 1-5 scale
  mood: string
  context?: string
  timestamp: Date
  userId: string
  factors?: string[] // e.g., ['caffeine', 'sleep', 'exercise']
}

// AI insights types
export interface ProductivityInsights {
  peakProductivityHours: string[]
  averageEnergyLevel: number
  productivityScore: number
  recommendations: string[]
  weeklyTrends: Record<string, { productivity: number; energy: number }>
  energyPatterns: {
    morning: number
    afternoon: number
    evening: number
  }
}

export interface WorkLifeBalanceMetrics {
  workHoursPerDay: number
  breakTimePerDay: number
  overtimeHours: number
  workLifeBalanceScore: number
  recommendations: string[]
  weeklyBalance: {
    workTime: number
    personalTime: number
    restTime: number
  }
  burnoutRisk: 'low' | 'medium' | 'high'
}

export interface OptimalTimeBlockSuggestion {
  suggestedTime: string
  type: TimeBlock['type']
  reason: string
  confidence: number
  estimatedProductivity: number
  energyRequirement: 'low' | 'medium' | 'high'
}

export class TimeEnergyService {
  /**
   * Create a new time block
   */
  static async createTimeBlock(data: {
    title: string
    description?: string
    startTime: Date
    endTime: Date
    type: TimeBlock['type']
    userId: string
    energyLevel?: TimeBlock['energyLevel']
    tags?: string[]
  }): Promise<TimeBlock> {
    try {
      const timeBlock = await prisma.timeBlock.create({
        data: {
          ...data,
          source: 'manual',
          completed: false,
        },
      })

      // Track analytics
      await this.trackTimeBlockAnalytics(timeBlock.id, 'created')

      return timeBlock as TimeBlock
    } catch (error) {
      console.error('Error creating time block:', error)
      throw new Error('Failed to create time block')
    }
  }

  /**
   * Get time blocks for a user with optional filters
   */
  static async getTimeBlocks(
    userId: string,
    filters?: {
      startDate?: Date
      endDate?: Date
      type?: TimeBlock['type']
      completed?: boolean
    }
  ): Promise<TimeBlock[]> {
    try {
      const whereClause: any = { userId }

      if (filters?.startDate && filters?.endDate) {
        whereClause.startTime = {
          gte: filters.startDate,
          lte: filters.endDate,
        }
      }

      if (filters?.type) {
        whereClause.type = filters.type
      }

      if (filters?.completed !== undefined) {
        whereClause.completed = filters.completed
      }

      const timeBlocks = await prisma.timeBlock.findMany({
        where: whereClause,
        orderBy: { startTime: 'asc' },
      })

      return timeBlocks as TimeBlock[]
    } catch (error) {
      console.error('Error fetching time blocks:', error)
      throw new Error('Failed to fetch time blocks')
    }
  }

  /**
   * Update a time block
   */
  static async updateTimeBlock(
    id: string,
    data: Partial<Omit<TimeBlock, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>
  ): Promise<TimeBlock> {
    try {
      const timeBlock = await prisma.timeBlock.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date(),
        },
      })

      // Track completion analytics
      if (data.completed) {
        await this.trackTimeBlockAnalytics(id, 'completed')
      }

      return timeBlock as TimeBlock
    } catch (error) {
      console.error('Error updating time block:', error)
      throw new Error('Failed to update time block')
    }
  }

  /**
   * Delete a time block
   */
  static async deleteTimeBlock(id: string): Promise<void> {
    try {
      await prisma.timeBlock.delete({
        where: { id },
      })

      await this.trackTimeBlockAnalytics(id, 'deleted')
    } catch (error) {
      console.error('Error deleting time block:', error)
      throw new Error('Failed to delete time block')
    }
  }

  /**
   * Record energy level
   */
  static async recordEnergyLevel(data: {
    level: number
    mood: string
    context?: string
    userId: string
    factors?: string[]
    timestamp?: Date
  }): Promise<EnergyLevel> {
    try {
      const energyLevel = await prisma.energyLevel.create({
        data: {
          ...data,
          timestamp: data.timestamp || new Date(),
        },
      })

      // Update AI learning model with new data
      await this.updateEnergyPatternModel(data.userId)

      return energyLevel as EnergyLevel
    } catch (error) {
      console.error('Error recording energy level:', error)
      throw new Error('Failed to record energy level')
    }
  }

  /**
   * Get energy levels for a user
   */
  static async getEnergyLevels(
    userId: string,
    filters?: {
      startDate?: Date
      endDate?: Date
      limit?: number
    }
  ): Promise<EnergyLevel[]> {
    try {
      const whereClause: any = { userId }

      if (filters?.startDate && filters?.endDate) {
        whereClause.timestamp = {
          gte: filters.startDate,
          lte: filters.endDate,
        }
      }

      const energyLevels = await prisma.energyLevel.findMany({
        where: whereClause,
        orderBy: { timestamp: 'desc' },
        take: filters?.limit || 100,
      })

      return energyLevels as EnergyLevel[]
    } catch (error) {
      console.error('Error fetching energy levels:', error)
      throw new Error('Failed to fetch energy levels')
    }
  }

  /**
   * Get AI-powered productivity insights
   */
  static async getProductivityInsights(userId: string): Promise<ProductivityInsights> {
    try {
      // Get recent data for analysis
      const timeBlocks = await this.getTimeBlocks(userId, {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        endDate: new Date(),
      })

      const energyLevels = await this.getEnergyLevels(userId, {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        endDate: new Date(),
      })

      // AI analysis
      const insights = await this.analyzeProductivityPatterns(timeBlocks, energyLevels)

      return insights
    } catch (error) {
      console.error('Error generating productivity insights:', error)
      throw new Error('Failed to generate productivity insights')
    }
  }

  /**
   * Get work-life balance metrics
   */
  static async getWorkLifeBalanceMetrics(userId: string): Promise<WorkLifeBalanceMetrics> {
    try {
      const timeBlocks = await this.getTimeBlocks(userId, {
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        endDate: new Date(),
      })

      const metrics = await this.calculateWorkLifeBalance(timeBlocks)

      return metrics
    } catch (error) {
      console.error('Error calculating work-life balance:', error)
      throw new Error('Failed to calculate work-life balance')
    }
  }

  /**
   * Predict optimal time blocks using AI
   */
  static async predictOptimalTimeBlocks(userId: string): Promise<OptimalTimeBlockSuggestion[]> {
    try {
      // Get historical data
      const timeBlocks = await this.getTimeBlocks(userId, {
        startDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // Last 60 days
        endDate: new Date(),
      })

      const energyLevels = await this.getEnergyLevels(userId, {
        startDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
        endDate: new Date(),
      })

      // AI prediction model
      const suggestions = await this.generateOptimalTimeBlockSuggestions(timeBlocks, energyLevels)

      return suggestions
    } catch (error) {
      console.error('Error predicting optimal time blocks:', error)
      throw new Error('Failed to predict optimal time blocks')
    }
  }

  // Private helper methods

  private static async trackTimeBlockAnalytics(timeBlockId: string, action: string): Promise<void> {
    try {
      await prisma.analytics.create({
        data: {
          eventType: 'time_block',
          eventAction: action,
          entityId: timeBlockId,
          timestamp: new Date(),
        },
      })
    } catch (error) {
      console.error('Error tracking analytics:', error)
      // Don't throw - analytics shouldn't break core functionality
    }
  }

  private static async updateEnergyPatternModel(userId: string): Promise<void> {
    // Update AI model with new energy data
    // This would integrate with your AI learning pipeline
    console.log(`Updating energy pattern model for user ${userId}`)
  }

  private static async analyzeProductivityPatterns(
    timeBlocks: TimeBlock[],
    energyLevels: EnergyLevel[]
  ): Promise<ProductivityInsights> {
    // AI analysis logic
    const totalProductivity = timeBlocks.reduce(
      (sum, block) => sum + (block.actualProductivity || 0),
      0
    )
    const averageProductivity = totalProductivity / Math.max(timeBlocks.length, 1)

    const averageEnergyLevel =
      energyLevels.reduce((sum, level) => sum + level.level, 0) / Math.max(energyLevels.length, 1)

    // Find peak productivity hours
    const hourlyProductivity = this.calculateHourlyProductivity(timeBlocks)
    const peakHours = Object.entries(hourlyProductivity)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 2)
      .map(([hour]) => `${hour}:00-${parseInt(hour) + 1}:00`)

    // Generate AI recommendations
    const recommendations = this.generateProductivityRecommendations(timeBlocks, energyLevels)

    // Calculate weekly trends
    const weeklyTrends = this.calculateWeeklyTrends(timeBlocks, energyLevels)

    return {
      peakProductivityHours: peakHours,
      averageEnergyLevel,
      productivityScore: Math.round(averageProductivity * 100),
      recommendations,
      weeklyTrends,
      energyPatterns: {
        morning: this.calculateEnergyByTimeOfDay(energyLevels, 'morning'),
        afternoon: this.calculateEnergyByTimeOfDay(energyLevels, 'afternoon'),
        evening: this.calculateEnergyByTimeOfDay(energyLevels, 'evening'),
      },
    }
  }

  private static calculateWorkLifeBalance(timeBlocks: TimeBlock[]): WorkLifeBalanceMetrics {
    const workBlocks = timeBlocks.filter(block =>
      ['focus', 'meeting', 'administrative'].includes(block.type)
    )
    const breakBlocks = timeBlocks.filter(block => block.type === 'break')

    const workHours = workBlocks.reduce((sum, block) => {
      const duration = (block.endTime.getTime() - block.startTime.getTime()) / (1000 * 60 * 60)
      return sum + duration
    }, 0)

    const breakHours = breakBlocks.reduce((sum, block) => {
      const duration = (block.endTime.getTime() - block.startTime.getTime()) / (1000 * 60 * 60)
      return sum + duration
    }, 0)

    const workHoursPerDay = workHours / 7
    const breakTimePerDay = breakHours / 7
    const overtimeHours = Math.max(0, workHours - 40) // Assuming 40 hour work week

    // Calculate balance score (0-100)
    const idealWorkHours = 8 * 5 // 40 hours per week
    const workBalance = Math.max(0, 100 - Math.abs(workHours - idealWorkHours) * 2)
    const breakBalance = Math.min(100, (breakHours / (workHours * 0.15)) * 100) // 15% break time ideal
    const workLifeBalanceScore = Math.round((workBalance + breakBalance) / 2)

    return {
      workHoursPerDay,
      breakTimePerDay,
      overtimeHours,
      workLifeBalanceScore,
      recommendations: this.generateWorkLifeRecommendations(
        workHoursPerDay,
        breakTimePerDay,
        overtimeHours
      ),
      weeklyBalance: {
        workTime: workHours,
        personalTime: 168 - workHours - 56, // 168 hours/week - work - sleep (8h/day)
        restTime: 56, // Assuming 8 hours sleep per day
      },
      burnoutRisk: this.calculateBurnoutRisk(workHoursPerDay, overtimeHours, breakTimePerDay),
    }
  }

  private static async generateOptimalTimeBlockSuggestions(
    timeBlocks: TimeBlock[],
    energyLevels: EnergyLevel[]
  ): Promise<OptimalTimeBlockSuggestion[]> {
    // AI suggestion logic based on historical patterns
    const suggestions: OptimalTimeBlockSuggestion[] = []

    // Analyze energy patterns
    const morningEnergy = this.calculateEnergyByTimeOfDay(energyLevels, 'morning')
    const afternoonEnergy = this.calculateEnergyByTimeOfDay(energyLevels, 'afternoon')

    if (morningEnergy > 3.5) {
      suggestions.push({
        suggestedTime: '09:00-11:00',
        type: 'focus',
        reason: 'High energy period based on your patterns',
        confidence: 0.89,
        estimatedProductivity: 85,
        energyRequirement: 'high',
      })
    }

    if (afternoonEnergy > 3.0) {
      suggestions.push({
        suggestedTime: '14:00-15:30',
        type: 'meeting',
        reason: 'Good time for meetings based on team availability',
        confidence: 0.76,
        estimatedProductivity: 72,
        energyRequirement: 'medium',
      })
    }

    return suggestions
  }

  // Additional helper methods
  private static calculateHourlyProductivity(timeBlocks: TimeBlock[]): Record<string, number> {
    const hourlyData: Record<string, { total: number; count: number }> = {}

    timeBlocks.forEach(block => {
      if (block.actualProductivity) {
        const hour = block.startTime.getHours().toString()
        if (!hourlyData[hour]) {
          hourlyData[hour] = { total: 0, count: 0 }
        }
        hourlyData[hour].total += block.actualProductivity
        hourlyData[hour].count += 1
      }
    })

    const hourlyProductivity: Record<string, number> = {}
    Object.entries(hourlyData).forEach(([hour, data]) => {
      hourlyProductivity[hour] = data.total / data.count
    })

    return hourlyProductivity
  }

  private static generateProductivityRecommendations(
    timeBlocks: TimeBlock[],
    energyLevels: EnergyLevel[]
  ): string[] {
    const recommendations: string[] = []

    const morningEnergy = this.calculateEnergyByTimeOfDay(energyLevels, 'morning')
    if (morningEnergy > 3.5) {
      recommendations.push('Schedule demanding tasks during 9-11 AM when your energy is highest')
    }

    const afternoonEnergy = this.calculateEnergyByTimeOfDay(energyLevels, 'afternoon')
    if (afternoonEnergy < 2.5) {
      recommendations.push('Take breaks during low-energy periods around 1-2 PM')
    }

    const avgMeetingLength = this.calculateAverageMeetingLength(timeBlocks)
    if (avgMeetingLength > 60) {
      recommendations.push('Consider shorter meeting blocks to maintain focus')
    }

    return recommendations
  }

  private static calculateWeeklyTrends(
    timeBlocks: TimeBlock[],
    energyLevels: EnergyLevel[]
  ): Record<string, { productivity: number; energy: number }> {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    const trends: Record<string, { productivity: number; energy: number }> = {}

    days.forEach(day => {
      const dayIndex = days.indexOf(day)
      const dayBlocks = timeBlocks.filter(block => block.startTime.getDay() === dayIndex)
      const dayEnergy = energyLevels.filter(level => level.timestamp.getDay() === dayIndex)

      const productivity =
        dayBlocks.reduce((sum, block) => sum + (block.actualProductivity || 0), 0) /
        Math.max(dayBlocks.length, 1)
      const energy =
        dayEnergy.reduce((sum, level) => sum + level.level, 0) / Math.max(dayEnergy.length, 1)

      trends[day] = {
        productivity: Math.round(productivity * 100) || 75, // Default values for demo
        energy: Math.round(energy * 10) / 10 || 3.5,
      }
    })

    return trends
  }

  private static calculateEnergyByTimeOfDay(
    energyLevels: EnergyLevel[],
    timeOfDay: 'morning' | 'afternoon' | 'evening'
  ): number {
    const timeRanges = {
      morning: [6, 12],
      afternoon: [12, 18],
      evening: [18, 24],
    }

    const [start, end] = timeRanges[timeOfDay]
    const relevantLevels = energyLevels.filter(level => {
      const hour = level.timestamp.getHours()
      return hour >= start && hour < end
    })

    if (relevantLevels.length === 0) return 3.0 // Default value

    return relevantLevels.reduce((sum, level) => sum + level.level, 0) / relevantLevels.length
  }

  private static generateWorkLifeRecommendations(
    workHoursPerDay: number,
    breakTimePerDay: number,
    overtimeHours: number
  ): string[] {
    const recommendations: string[] = []

    if (workHoursPerDay > 9) {
      recommendations.push('Consider shorter work blocks to avoid burnout')
    }

    if (breakTimePerDay < 1) {
      recommendations.push('Take more frequent breaks during high-intensity periods')
    }

    if (overtimeHours > 5) {
      recommendations.push('Reduce overtime hours to maintain sustainable productivity')
    }

    return recommendations
  }

  private static calculateBurnoutRisk(
    workHoursPerDay: number,
    overtimeHours: number,
    breakTimePerDay: number
  ): 'low' | 'medium' | 'high' {
    let riskScore = 0

    if (workHoursPerDay > 10) riskScore += 30
    else if (workHoursPerDay > 8) riskScore += 15

    if (overtimeHours > 10) riskScore += 25
    else if (overtimeHours > 5) riskScore += 10

    if (breakTimePerDay < 0.5) riskScore += 20
    else if (breakTimePerDay < 1) riskScore += 10

    if (riskScore >= 50) return 'high'
    if (riskScore >= 25) return 'medium'
    return 'low'
  }

  private static calculateAverageMeetingLength(timeBlocks: TimeBlock[]): number {
    const meetings = timeBlocks.filter(block => block.type === 'meeting')
    if (meetings.length === 0) return 0

    const totalDuration = meetings.reduce((sum, meeting) => {
      return sum + (meeting.endTime.getTime() - meeting.startTime.getTime())
    }, 0)

    return totalDuration / meetings.length / (1000 * 60) // Convert to minutes
  }
}
