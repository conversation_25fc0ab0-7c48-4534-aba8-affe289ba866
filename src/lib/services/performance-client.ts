'use client'

import { toast } from 'sonner'

// Types for performance metrics
export interface PerformanceMetric {
  id: string
  name: string
  value: number
  unit: string
  trend: 'up' | 'down' | 'stable'
  trendValue: number
  category: 'productivity' | 'skills' | 'goals' | 'engagement'
  description?: string
  icon?: string
}

export interface PerformanceStats {
  totalTasks: number
  completedTasks: number
  completionRate: number
  averageTaskTime: number
  goalsAchieved: number
  skillsImproved: number
  productivityScore: number
  engagementScore: number
}

export interface PerformanceInsight {
  id: string
  title: string
  description: string
  category: 'strength' | 'improvement' | 'suggestion'
  relatedMetricId?: string
  actionItems?: string[]
}

export interface PerformanceEvaluation {
  id: string
  title: string
  evaluationDate: Date
  evaluationType: 'self' | 'peer' | 'manager' | '360'
  metrics: PerformanceMetric[]
  strengths: string[]
  areasForImprovement: string[]
  rating: number
  feedback: string
  goals: string[]
  status: 'draft' | 'submitted' | 'completed'
}

export interface PerformanceResponse {
  stats: PerformanceStats
  metrics: PerformanceMetric[]
  insights: PerformanceInsight[]
  recentEvaluations?: PerformanceEvaluation[]
}

export interface CreateEvaluationInput {
  title: string
  evaluationType: 'self' | 'peer' | 'manager' | '360'
  metrics: { name: string; value: number; category: string }[]
  strengths: string[]
  areasForImprovement: string[]
  rating: number
  feedback: string
  goals: string[]
}

export interface UpdateEvaluationInput {
  title?: string
  metrics?: { name: string; value: number; category: string }[]
  strengths?: string[]
  areasForImprovement?: string[]
  rating?: number
  feedback?: string
  goals?: string[]
  status?: 'draft' | 'submitted' | 'completed'
}

// Client-side service for performance metrics
export const performanceClient = {
  // Get performance data for a specific time period
  async getPerformanceData(period: string = '30d'): Promise<PerformanceResponse> {
    try {
      const response = await fetch(`/api/performance?period=${period}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch performance data')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch performance data'
      toast.error(errorMessage)
      console.error('Error fetching performance data:', error)
      return {
        stats: {
          totalTasks: 0,
          completedTasks: 0,
          completionRate: 0,
          averageTaskTime: 0,
          goalsAchieved: 0,
          skillsImproved: 0,
          productivityScore: 0,
          engagementScore: 0,
        },
        metrics: [],
        insights: [],
      }
    }
  },

  // Get performance metrics by category
  async getMetricsByCategory(
    category: string,
    period: string = '30d'
  ): Promise<PerformanceMetric[]> {
    try {
      const response = await fetch(
        `/api/performance/metrics?category=${category}&period=${period}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch performance metrics')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch performance metrics'
      toast.error(errorMessage)
      console.error('Error fetching performance metrics:', error)
      return []
    }
  },

  // Get performance insights
  async getPerformanceInsights(period: string = '30d'): Promise<PerformanceInsight[]> {
    try {
      const response = await fetch(`/api/performance/insights?period=${period}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch performance insights')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch performance insights'
      toast.error(errorMessage)
      console.error('Error fetching performance insights:', error)
      return []
    }
  },

  // Get performance trend over time
  async getPerformanceTrend(
    metricId: string,
    timeframe: string = '6m'
  ): Promise<{ labels: string[]; data: number[] }> {
    try {
      const response = await fetch(`/api/performance/trends/${metricId}?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch performance trend')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch performance trend'
      toast.error(errorMessage)
      console.error('Error fetching performance trend:', error)
      return { labels: [], data: [] }
    }
  },

  // Get performance evaluations
  async getPerformanceEvaluations(): Promise<PerformanceEvaluation[]> {
    try {
      const response = await fetch('/api/performance/evaluations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch performance evaluations')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch performance evaluations'
      toast.error(errorMessage)
      console.error('Error fetching performance evaluations:', error)
      return []
    }
  },

  // Get a specific performance evaluation
  async getEvaluationById(evaluationId: string): Promise<PerformanceEvaluation> {
    try {
      const response = await fetch(`/api/performance/evaluations/${evaluationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch performance evaluation')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch performance evaluation'
      toast.error(errorMessage)
      console.error('Error fetching performance evaluation:', error)
      throw error
    }
  },

  // Create a new performance evaluation
  async createEvaluation(input: CreateEvaluationInput): Promise<PerformanceEvaluation> {
    try {
      const response = await fetch('/api/performance/evaluations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create performance evaluation')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to create performance evaluation'
      toast.error(errorMessage)
      console.error('Error creating performance evaluation:', error)
      throw error
    }
  },

  // Update an existing performance evaluation
  async updateEvaluation(
    evaluationId: string,
    input: UpdateEvaluationInput
  ): Promise<PerformanceEvaluation> {
    try {
      const response = await fetch(`/api/performance/evaluations/${evaluationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to update performance evaluation')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update performance evaluation'
      toast.error(errorMessage)
      console.error('Error updating performance evaluation:', error)
      throw error
    }
  },

  // Delete a performance evaluation
  async deleteEvaluation(evaluationId: string): Promise<void> {
    try {
      const response = await fetch(`/api/performance/evaluations/${evaluationId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to delete performance evaluation')
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to delete performance evaluation'
      toast.error(errorMessage)
      console.error('Error deleting performance evaluation:', error)
      throw error
    }
  },
}
