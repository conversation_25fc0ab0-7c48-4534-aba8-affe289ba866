/**
 * Feature Flag Service
 *
 * Manages feature flags for companies and users to control rollout
 * of new features and AI capabilities.
 */

interface FeatureFlag {
  key: string
  enabled: boolean
  companyId?: string
  userId?: string
  rolloutPercentage?: number
  conditions?: Record<string, any>
}

// Cache for feature flags to avoid repeated API calls
const featureFlagCache = new Map<string, { value: boolean; expiry: number }>()
const CACHE_TTL = 10 * 60 * 1000 // 10 minutes

/**
 * Check if a feature is enabled for a company
 */
export async function hasFeature(companyId: string, featureKey: string): Promise<boolean> {
  const cacheKey = `${companyId}:${featureKey}`
  const cached = featureFlagCache.get(cacheKey)

  // Return cached value if still valid
  if (cached && cached.expiry > Date.now()) {
    return cached.value
  }

  try {
    const response = await fetch('/api/feature/check', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ companyId, featureKey }),
    })

    if (!response.ok) {
      // Default to false if API fails
      return false
    }

    const data = await response.json()
    const enabled = data.enabled || false

    // Cache the result
    featureFlagCache.set(cacheKey, {
      value: enabled,
      expiry: Date.now() + CACHE_TTL,
    })

    return enabled
  } catch {
    if (process.env.NODE_ENV === 'development') console.error('Feature flag check failed:', error)
    return false
  }
}

/**
 * Check if a feature is enabled for a specific user
 */
export async function hasUserFeature(
  userId: string,
  companyId: string,
  featureKey: string
): Promise<boolean> {
  const cacheKey = `${userId}:${companyId}:${featureKey}`
  const cached = featureFlagCache.get(cacheKey)

  if (cached && cached.expiry > Date.now()) {
    return cached.value
  }

  try {
    const response = await fetch('/api/feature/check', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId, companyId, featureKey }),
    })

    if (!response.ok) {
      return false
    }

    const data = await response.json()
    const enabled = data.enabled || false

    featureFlagCache.set(cacheKey, {
      value: enabled,
      expiry: Date.now() + CACHE_TTL,
    })

    return enabled
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('User feature flag check failed:', error)
    return false
  }
}

/**
 * Clear feature flag cache
 */
export function clearFeatureFlagCache(): void {
  featureFlagCache.clear()
}

/**
 * Get all enabled features for a company
 */
export async function getEnabledFeatures(companyId: string): Promise<string[]> {
  try {
    const response = await fetch(`/api/feature/list?companyId=${companyId}`)

    if (!response.ok) {
      return []
    }

    const data = await response.json()
    return data.features || []
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('Failed to get enabled features:', error)
    return []
  }
}

// Default feature flags for testing and development
export const DEFAULT_FEATURES = {
  contextAwareness: false, // AI-driven context awareness
  aiRecommendations: true, // AI-powered recommendations
  behavioralTracking: true, // User behavior tracking
  multiProviderAI: true, // Multiple AI provider support
  advancedAnalytics: false, // Advanced analytics features
  realTimeCollaboration: false, // Real-time collaboration features
} as const

/**
 * Synchronous feature check using defaults (for testing)
 */
export function hasFeatureSync(
  companyId: string,
  featureKey: keyof typeof DEFAULT_FEATURES
): boolean {
  // In development/test, use defaults
  if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
    return DEFAULT_FEATURES[featureKey] ?? false
  }

  // In production, this should not be used - use async hasFeature instead
  if (process.env.NODE_ENV === 'development')
    if (process.env.NODE_ENV === 'development')
      console.warn('hasFeatureSync should not be used in production')
  return false
}
