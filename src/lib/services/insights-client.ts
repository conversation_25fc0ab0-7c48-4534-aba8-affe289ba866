/**
 * Insights Client
 * Client-side service for fetching insights data from the API
 * Part of Task 5.5: Develop Insights with AI-powered Suggestions
 */

import {
  FocusAnalyticsData,
  ProductivityInsight,
  GoalInsight,
  SkillInsight,
} from './focus-analytics-service'

/**
 * Client for interacting with the insights API
 */
export class InsightsClient {
  /**
   * Fetch all insights data
   */
  async getInsights(): Promise<FocusAnalyticsData> {
    try {
      const response = await fetch('/api/intelligence/insights', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch insights: ${response.status} ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching insights:', error)
      // Return empty data structure on error
      return {
        productivityInsights: [],
        goalInsights: [],
        skillInsights: [],
        isContextAware: false,
      }
    }
  }

  /**
   * Submit feedback on an insight
   */
  async submitFeedback(
    feedback: 'helpful' | 'not_helpful',
    insightType?: string
  ): Promise<boolean> {
    try {
      const response = await fetch('/api/intelligence/insights', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          feedback,
          insightType,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to submit feedback: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data.success
    } catch (error) {
      console.error('Error submitting insight feedback:', error)
      return false
    }
  }
}
