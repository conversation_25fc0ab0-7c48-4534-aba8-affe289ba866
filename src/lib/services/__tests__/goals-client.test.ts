import { describe, it, expect, vi, beforeEach } from 'vitest'
import { goalsClient } from '../goals-client'

// Mock fetch for testing
global.fetch = vi.fn()

describe('goalsClient', () => {
  beforeEach(() => {
    vi.resetAllMocks()
    vi.clearAllMocks()
  })

  describe('getGoals', () => {
    it('should fetch goals successfully', async () => {
      // Arrange
      const mockGoals = [
        {
          id: '1',
          title: 'Quarterly OKR',
          description: 'Improve team productivity',
          type: 'OBJECTIVE',
          status: 'IN_PROGRESS',
          progress: 40,
          targetValue: null,
          currentValue: null,
          unit: null,
          category: 'PROFESSIONAL',
          timeframe: 'QUARTERLY',
          startDate: '2025-01-01',
          dueDate: '2025-03-31',
        },
      ]

      // Mock the fetch implementation
      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockGoals,
      } as Response)

      // Act
      const result = await goalsClient.getGoals()

      // Assert
      expect(global.fetch).toHaveBeenCalledWith('/api/goals', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      expect(result).toEqual(mockGoals)
    })

    it('should throw an error when fetch fails', async () => {
      // Arrange
      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      } as Response)

      // Act & Assert
      await expect(goalsClient.getGoals()).rejects.toThrow('Failed to fetch goals')
    })
  })

  describe('getGoal', () => {
    it('should fetch a single goal by ID', async () => {
      // Arrange
      const mockGoal = {
        id: '1',
        title: 'Quarterly OKR',
        description: 'Improve team productivity',
        type: 'OBJECTIVE',
        status: 'IN_PROGRESS',
        progress: 40,
      }

      // Mock the fetch implementation
      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockGoal,
      } as Response)

      // Act
      const result = await goalsClient.getGoal('1')

      // Assert
      expect(global.fetch).toHaveBeenCalledWith('/api/goals/1', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      expect(result).toEqual(mockGoal)
    })
  })

  describe('createGoal', () => {
    it('should create a goal successfully', async () => {
      // Arrange
      const goalData = {
        title: 'New Goal',
        description: 'New goal description',
        type: 'OBJECTIVE',
        category: 'PROFESSIONAL',
        timeframe: 'QUARTERLY',
        priority: 'MEDIUM',
      }

      const mockResponse = {
        id: 'new-goal-id',
        ...goalData,
        status: 'NOT_STARTED',
        progress: 0,
        createdAt: '2025-01-15T12:00:00.000Z',
      }

      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response)

      // Act
      const result = await goalsClient.createGoal(goalData)

      // Assert
      expect(global.fetch).toHaveBeenCalledWith('/api/goals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(goalData),
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('updateGoal', () => {
    it('should update a goal successfully', async () => {
      // Arrange
      const goalId = '1'
      const updateData = {
        title: 'Updated Goal',
        progress: 60,
      }

      const mockResponse = {
        id: goalId,
        title: 'Updated Goal',
        description: 'Original description',
        type: 'OBJECTIVE',
        status: 'IN_PROGRESS',
        progress: 60,
        category: 'PROFESSIONAL',
        timeframe: 'QUARTERLY',
        priority: 'MEDIUM',
        updatedAt: '2025-01-15T12:00:00.000Z',
      }

      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response)

      // Act
      const result = await goalsClient.updateGoal(goalId, updateData)

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(`/api/goals/${goalId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('deleteGoal', () => {
    it('should delete a goal successfully', async () => {
      // Arrange
      const goalId = '1'

      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response)

      // Act
      const result = await goalsClient.deleteGoal(goalId)

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(`/api/goals/${goalId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      expect(result).toEqual({ success: true })
    })
  })

  describe('createKeyResult', () => {
    it('should create a key result for an objective', async () => {
      // Arrange
      const objectiveId = 'obj-1'
      const keyResultData = {
        title: 'New Key Result',
        description: 'Description for key result',
        targetValue: 100,
        currentValue: 0,
        unit: 'percent',
      }

      const mockResponse = {
        id: 'kr-1',
        ...keyResultData,
        status: 'NOT_STARTED',
        parentGoalId: objectiveId,
        createdAt: '2025-01-15T12:00:00.000Z',
      }

      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response)

      // Act
      const result = await goalsClient.createKeyResult(objectiveId, keyResultData)

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(`/api/goals/${objectiveId}/key-results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(keyResultData),
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('updateKeyResult', () => {
    it('should update a key result successfully', async () => {
      // Arrange
      const keyResultId = 'kr-1'
      const updateData = {
        currentValue: 50,
        status: 'IN_PROGRESS',
      }

      const mockResponse = {
        id: keyResultId,
        title: 'Key Result',
        description: 'Description',
        targetValue: 100,
        currentValue: 50,
        unit: 'percent',
        status: 'IN_PROGRESS',
        parentGoalId: 'obj-1',
        updatedAt: '2025-01-15T12:00:00.000Z',
      }

      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response)

      // Act
      const result = await goalsClient.updateKeyResult(keyResultId, updateData)

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(`/api/goals/key-results/${keyResultId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe('getGoalsByTimeframe', () => {
    it('should fetch goals filtered by timeframe', async () => {
      // Arrange
      const timeframe = 'QUARTERLY'
      const mockGoals = [
        {
          id: '1',
          title: 'Quarterly OKR 1',
          timeframe: 'QUARTERLY',
        },
        {
          id: '2',
          title: 'Quarterly OKR 2',
          timeframe: 'QUARTERLY',
        },
      ]

      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockGoals,
      } as Response)

      // Act
      const result = await goalsClient.getGoalsByTimeframe(timeframe)

      // Assert
      expect(global.fetch).toHaveBeenCalledWith(`/api/goals?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      expect(result).toEqual(mockGoals)
    })
  })

  describe('getAnalytics', () => {
    it('should fetch goal analytics data', async () => {
      // Arrange
      const mockAnalytics = {
        totalGoals: 10,
        completedGoals: 4,
        inProgressGoals: 5,
        atRiskGoals: 1,
        byTimeframe: {
          DAILY: 2,
          WEEKLY: 3,
          MONTHLY: 2,
          QUARTERLY: 2,
          YEARLY: 1,
        },
        byCategory: {
          PERSONAL: 3,
          PROFESSIONAL: 5,
          LEARNING: 2,
        },
      }

      vi.mocked(global.fetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAnalytics,
      } as Response)

      // Act
      const result = await goalsClient.getAnalytics()

      // Assert
      expect(global.fetch).toHaveBeenCalledWith('/api/goals/analytics', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      expect(result).toEqual(mockAnalytics)
    })
  })
})
