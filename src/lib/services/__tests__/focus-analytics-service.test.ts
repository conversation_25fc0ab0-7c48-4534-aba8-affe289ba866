/**
 * FocusAnalyticsService Tests
 * TDD implementation testing the analytics service with real data
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { FocusAnalyticsService } from '../focus-analytics-service'
import { prisma } from '@/lib/prisma'

describe('FocusAnalyticsService - TDD Implementation', () => {
  // Test data
  const testUser = {
    id: 'test-analytics-user-id',
    name: 'Test Analytics User',
    email: '<EMAIL>',
    companyId: 'test-analytics-company-id',
  }

  // Create service instance
  const focusAnalyticsService = new FocusAnalyticsService()

  // Setup test data before tests
  beforeEach(async () => {
    // Create test company first
    await prisma.company.upsert({
      where: { id: testUser.companyId },
      update: {},
      create: {
        id: testUser.companyId,
        name: 'Test Analytics Company',
        domains: ['test-analytics.com'],
      },
    })

    // Create test user
    await prisma.user.upsert({
      where: { id: testUser.id },
      update: {},
      create: {
        id: testUser.id,
        name: testUser.name,
        email: testUser.email,
        companyId: testUser.companyId,
        role: 'EMPLOYEE',
      },
    })

    // Create test tasks
    await Promise.all([
      prisma.focusTask.create({
        data: {
          id: 'task-1',
          title: 'Complete project documentation',
          description: 'Write comprehensive docs',
          status: 'COMPLETED',
          priority: 'HIGH',
          userId: testUser.id,
          companyId: testUser.companyId,
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-02'),
        },
      }),
      prisma.focusTask.create({
        data: {
          id: 'task-2',
          title: 'Review code changes',
          description: 'Review PR #123',
          status: 'IN_PROGRESS',
          priority: 'MEDIUM',
          userId: testUser.id,
          companyId: testUser.companyId,
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
      }),
      prisma.focusTask.create({
        data: {
          id: 'task-3',
          title: 'Update dependencies',
          description: 'Update npm packages',
          status: 'TODO',
          priority: 'LOW',
          userId: testUser.id,
          companyId: testUser.companyId,
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
      }),
      prisma.focusTask.create({
        data: {
          id: 'task-4',
          title: 'Fix bug in authentication',
          description: 'Fix login issue',
          status: 'COMPLETED',
          priority: 'HIGH',
          userId: testUser.id,
          companyId: testUser.companyId,
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-02'),
        },
      }),
    ])

    // Create test goals
    await Promise.all([
      prisma.goal.create({
        data: {
          id: 'goal-1',
          title: 'Improve code quality',
          description: 'Increase test coverage to 90%',
          targetDate: new Date('2024-12-31'),
          progress: 60,
          status: 'IN_PROGRESS',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      }),
      prisma.goal.create({
        data: {
          id: 'goal-2',
          title: 'Learn new technology',
          description: 'Master React 18 features',
          targetDate: new Date('2024-06-30'),
          progress: 40,
          status: 'IN_PROGRESS',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      }),
    ])

    // Create test skills
    await Promise.all([
      prisma.skill.create({
        data: {
          id: 'skill-1',
          name: 'TypeScript',
          category: 'TECHNICAL',
          importance: 5,
          currentLevel: 'INTERMEDIATE',
          targetLevel: 'ADVANCED',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      }),
      prisma.skill.create({
        data: {
          id: 'skill-2',
          name: 'React',
          category: 'TECHNICAL',
          importance: 5,
          currentLevel: 'BEGINNER',
          targetLevel: 'INTERMEDIATE',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      }),
    ])
  })

  // Cleanup after tests
  afterEach(async () => {
    // Clean up in reverse order of creation
    await prisma.skill.deleteMany({
      where: { userId: testUser.id },
    })
    await prisma.goal.deleteMany({
      where: { userId: testUser.id },
    })
    await prisma.focusTask.deleteMany({
      where: { userId: testUser.id },
    })
    await prisma.user.deleteMany({
      where: { id: testUser.id },
    })
    await prisma.company.deleteMany({
      where: { id: testUser.companyId },
    })
  })

  it('should generate productivity insights based on real task data', async () => {
    const insights = await focusAnalyticsService.generateInsights(testUser.id, testUser.companyId)

    expect(insights).toBeDefined()
    expect(insights.productivityInsights).toBeDefined()
    expect(insights.productivityInsights.length).toBeGreaterThan(0)

    const productivityInsight = insights.productivityInsights[0]
    expect(productivityInsight.title).toContain('Task Completion')
    expect(productivityInsight.metric).toBe(50) // 2 completed out of 4 total
    expect(productivityInsight.category).toBe('productivity')
  })

  it('should generate goal insights based on real goal data', async () => {
    const insights = await focusAnalyticsService.generateInsights(testUser.id, testUser.companyId)

    expect(insights).toBeDefined()
    expect(insights.goalInsights).toBeDefined()
    expect(insights.goalInsights.length).toBeGreaterThan(0)

    const goalInsight = insights.goalInsights[0]
    expect(goalInsight.title).toContain('Goal Progress')
    expect(goalInsight.progress).toBe(50) // Average of 60% and 40%
    expect(goalInsight.category).toBe('goals')
  })

  it('should generate skill insights based on real skill data', async () => {
    const insights = await focusAnalyticsService.generateInsights(testUser.id, testUser.companyId)

    expect(insights).toBeDefined()
    expect(insights.skillInsights).toBeDefined()
    expect(insights.skillInsights.length).toBeGreaterThan(0)

    const skillInsight = insights.skillInsights[0]
    expect(skillInsight.title).toContain('Skill Development')
    expect(skillInsight.category).toBe('skills')
    expect(skillInsight.gap).toBeGreaterThan(0)
  })

  it('should store user feedback for insights in the database', async () => {
    const feedback = await focusAnalyticsService.storeFeedback(
      testUser.id,
      'helpful',
      'productivity'
    )

    expect(feedback).toBe(true)

    // Verify feedback was stored (we'll need to create a simple feedback table or use existing one)
    // For now, just verify the method returns true
  })

  it('should respect feature flag for context-awareness', async () => {
    const insights = await focusAnalyticsService.generateInsights(testUser.id, testUser.companyId)

    expect(insights).toBeDefined()
    expect(insights.isContextAware).toBeDefined()
    expect(typeof insights.isContextAware).toBe('boolean')
  })
})
