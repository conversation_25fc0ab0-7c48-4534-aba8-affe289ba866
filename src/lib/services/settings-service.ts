import { cache } from 'react'
import { auth } from '@/lib/auth/config'
import { hasPermission, type Permission } from '@/lib/permissions'

export interface SettingsSection {
  id: string
  title: string
  permission?: Permission
  isDisabled?: boolean
  subsections?: SettingsSection[]
}

export interface UserSettings {
  id: string
  userId: string
  section: string
  key: string
  value: unknown
  updatedAt: Date
}

// Cache the settings sections to avoid unnecessary database queries
export const getSettingsSections = cache(async () => {
  const session = await auth()
  const userRole = session?.user?.role

  const sections: SettingsSection[] = [
    {
      id: 'profile',
      title: 'Profile (Personal)',
      subsections: [
        {
          id: 'personal-info',
          title: 'Personal Info',
          permission: 'settings:personal',
        },
        {
          id: 'security',
          title: 'Security & Login',
          permission: 'settings:personal',
        },
        {
          id: 'notifications',
          title: 'Notification Preferences',
          permission: 'settings:personal',
        },
        {
          id: 'connected-accounts',
          title: 'Connected Accounts',
          permission: 'settings:personal',
        },
        {
          id: 'activity',
          title: 'Activity & Devices',
          permission: 'settings:personal',
        },
        {
          id: 'privacy',
          title: 'Data & Privacy',
          permission: 'settings:personal',
        },
      ],
    },
    {
      id: 'platform',
      title: 'Platform (Preferences)',
      subsections: [
        {
          id: 'appearance',
          title: 'Appearance',
          permission: 'settings:personal',
        },
        {
          id: 'ai-models',
          title: 'AI Models',
          permission: 'settings:personal',
        },
        {
          id: 'notifications',
          title: 'Notifications',
          permission: 'settings:personal',
        },
        {
          id: 'dashboard',
          title: 'Dashboard Preferences',
          permission: 'settings:personal',
        },
        {
          id: 'language',
          title: 'Language & Timezone',
          permission: 'settings:personal',
        },
        {
          id: 'accessibility',
          title: 'Accessibility',
          permission: 'settings:personal',
        },
        {
          id: 'export',
          title: 'Export Settings',
          isDisabled: true,
        },
      ],
    },
  ]

  // Add admin sections if user has permission
  if (hasPermission(userRole, 'admin:users')) {
    sections.push(
      {
        id: 'user-management',
        title: 'User & Access Control',
        permission: 'admin:users',
        subsections: [
          {
            id: 'users',
            title: 'User Management',
            permission: 'admin:users',
          },
          {
            id: 'departments',
            title: 'Departments',
            permission: 'admin:users',
          },
          {
            id: 'sub-departments',
            title: 'Functions',
            permission: 'admin:users',
          },
          {
            id: 'audit',
            title: 'Audit Trail',
            permission: 'admin:users',
          },
        ],
      },
      {
        id: 'content-management',
        title: 'Content Management',
        permission: 'admin:users',
        subsections: [
          {
            id: 'resources',
            title: 'Resource Library Settings',
            permission: 'admin:users',
          },
          {
            id: 'career-paths',
            title: 'Career Path Configuration',
            permission: 'admin:users',
          },
        ],
      }
    )
  }

  // Add superadmin sections if user has permission
  if (hasPermission(userRole, 'admin:system')) {
    sections.push(
      {
        id: 'advanced',
        title: 'Advanced',
        permission: 'admin:system',
        subsections: [
          {
            id: 'logs',
            title: 'System Logs & Monitoring',
            permission: 'admin:system',
          },
          {
            id: 'data',
            title: 'Data Management & Backup',
            permission: 'admin:system',
          },
          {
            id: 'api',
            title: 'API Management',
            isDisabled: true,
          },
        ],
      },
      {
        id: 'superadmin',
        title: 'SuperAdmin Panel',
        permission: 'admin:system',
        subsections: [
          {
            id: 'panel',
            title: 'SuperAdmin Dashboard',
            permission: 'admin:system',
          },
        ],
      }
    )
  }

  return sections
})

// Cache user settings to avoid unnecessary database queries
export const getUserSettings = cache(async (userId: string, section?: string) => {
  const { prisma } = await import('@/lib/prisma')

  try {
    const where = section ? { userId, section } : { userId }

    const settings = await prisma.userSetting.findMany({
      where,
      orderBy: { updatedAt: 'desc' },
    })

    return settings
  } catch (error) {
    console.error('Failed to fetch user settings:', error)
    return [] as UserSettings[]
  }
})

// Update user settings
export async function updateUserSettings(
  userId: string,
  section: string,
  key: string,
  value: unknown
) {
  const { prisma } = await import('@/lib/prisma')

  try {
    // Find existing setting or create new one
    const existingSetting = await prisma.userSetting.findFirst({
      where: { userId, section, key },
    })

    let updatedSetting

    if (existingSetting) {
      // Update existing setting
      updatedSetting = await prisma.userSetting.update({
        where: { id: existingSetting.id },
        data: { value, updatedAt: new Date() },
      })
    } else {
      // Create new setting
      updatedSetting = await prisma.userSetting.create({
        data: { userId, section, key, value },
      })
    }

    return updatedSetting
  } catch (error) {
    console.error('Failed to update user setting:', error)
    throw error
  }
}
