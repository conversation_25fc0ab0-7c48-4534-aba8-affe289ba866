// Client-side service for focus task operations
// Calls API routes instead of using Prisma directly

import { FocusTask } from '@prisma/client'

/**
 * Input types for task operations
 */
export interface CreateTaskInput {
  title: string
  description?: string
  priority?: 'low' | 'medium' | 'high'
  dueDate?: Date
  estimatedMinutes?: number
  tags?: string[]
  userId: string
  companyId: string
}

export interface UpdateTaskInput {
  title?: string
  description?: string
  status?: 'pending' | 'in-progress' | 'completed' | 'cancelled'
  priority?: 'low' | 'medium' | 'high'
  dueDate?: Date
  completedAt?: Date
  estimatedMinutes?: number
  actualMinutes?: number
  tags?: string[]
  timeBlocks?: Array<{
    startTime: string
    endTime: string
    date?: string
  }>
  order?: number
}

export interface TaskFilters {
  status?: string
  priority?: string
  dueDateRange?: 'today' | 'week' | 'month' | 'overdue'
  tags?: string[]
}

export interface TaskSortOptions {
  field: 'createdAt' | 'dueDate' | 'priority' | 'order' | 'title'
  direction: 'asc' | 'desc'
}

/**
 * Client-side service for focus task operations
 * Makes HTTP requests to API routes instead of using Prisma directly
 */
export class FocusTaskClientService {
  private baseUrl = '/api/focus/tasks'

  /**
   * Handle API errors consistently
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }
    return response.json()
  }

  /**
   * Get all tasks for a user with optional filtering and sorting
   */
  async getUserTasks(
    userId: string,
    filters?: TaskFilters,
    sort?: TaskSortOptions
  ): Promise<FocusTask[]> {
    try {
      const params = new URLSearchParams()

      if (filters?.status) {
        params.append('status', filters.status)
      }

      if (filters?.priority) {
        params.append('priority', filters.priority)
      }

      if (filters?.dueDateRange) {
        params.append('dueDateRange', filters.dueDateRange)
      }

      if (filters?.tags && filters.tags.length > 0) {
        params.append('tags', filters.tags.join(','))
      }

      if (sort?.field) {
        params.append('sortField', sort.field)
        params.append('sortDirection', sort.direction)
      }

      const url = `${this.baseUrl}?${params.toString()}`
      const response = await fetch(url)

      const data = await this.handleResponse<{ tasks: FocusTask[] }>(response)
      return data.tasks
    } catch (error) {
      console.error('Error getting user tasks:', error)
      throw new Error('Failed to retrieve tasks')
    }
  }

  /**
   * Get a single task by ID
   */
  async getTaskById(taskId: string): Promise<FocusTask | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${taskId}`)

      if (response.status === 404) {
        return null
      }

      const data = await this.handleResponse<{ task: FocusTask }>(response)
      return data.task
    } catch (error) {
      console.error('Error getting task by ID:', error)
      return null
    }
  }

  /**
   * Create a new task
   */
  async createTask(input: CreateTaskInput): Promise<FocusTask> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const data = await this.handleResponse<{ task: FocusTask }>(response)
      return data.task
    } catch (error) {
      console.error('Error creating task:', error)
      throw new Error('Failed to create task')
    }
  }

  /**
   * Update an existing task
   */
  async updateTask(taskId: string, input: UpdateTaskInput): Promise<FocusTask> {
    try {
      const response = await fetch(`${this.baseUrl}/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      const data = await this.handleResponse<{ task: FocusTask }>(response)
      return data.task
    } catch (error) {
      console.error('Error updating task:', error)
      throw new Error('Failed to update task')
    }
  }

  /**
   * Delete a task
   */
  async deleteTask(taskId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/${taskId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        return true
      }

      throw new Error('Delete request failed')
    } catch (error) {
      console.error('Error deleting task:', error)
      throw new Error('Failed to delete task')
    }
  }

  /**
   * Reorder tasks for a user
   */
  async reorderTasks(userId: string, taskIds: string[]): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/reorder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskIds }),
      })

      return response.ok
    } catch (error) {
      console.error('Error reordering tasks:', error)
      throw new Error('Failed to reorder tasks')
    }
  }

  /**
   * Complete a task (shorthand for updating status)
   */
  async completeTask(taskId: string): Promise<FocusTask> {
    return this.updateTask(taskId, {
      status: 'completed',
      completedAt: new Date(),
    })
  }

  /**
   * Get task statistics for a user
   */
  async getTaskStats(userId: string): Promise<{
    total: number
    completed: number
    pending: number
    overdue: number
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/stats`)
      const data = await this.handleResponse<{ stats: any }>(response)
      return data.stats
    } catch (error) {
      console.error('Error getting task stats:', error)
      throw new Error('Failed to retrieve task statistics')
    }
  }

  /**
   * Get today's tasks for a user
   */
  async getTodayTasks(userId: string): Promise<FocusTask[]> {
    return this.getUserTasks(userId, { dueDateRange: 'today' })
  }
}

// Export a singleton instance
export const focusTaskClient = new FocusTaskClientService()
