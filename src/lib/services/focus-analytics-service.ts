/**
 * FocusAnalyticsService
 * Aggregates data for insights from different sources (tasks, goals, skills)
 * Part of Task 5.5: Develo<PERSON> Insights with AI-powered Suggestions
 */

import { hasFeature } from '@/lib/feature-flags'
import { prisma } from '@/lib/prisma'

// Types for analytics data
export interface FocusAnalyticsData {
  productivityInsights: ProductivityInsight[]
  goalInsights: GoalInsight[]
  skillInsights: SkillInsight[]
  isContextAware: boolean
}

export interface ProductivityInsight {
  id: string
  title: string
  description: string
  metric: number
  trend: 'up' | 'down' | 'neutral'
  trendValue: number
  category: string
  priority: 'high' | 'medium' | 'low'
  actionItems?: string[]
}

export interface GoalInsight {
  id: string
  title: string
  description: string
  progress: number
  target: number
  daysRemaining: number
  onTrack: boolean
  category: string
  actionItems?: string[]
}

export interface SkillInsight {
  id: string
  title: string
  description: string
  currentLevel: string
  targetLevel: string
  gap: number
  category: string
  actionItems?: string[]
}

/**
 * Service for generating analytics and insights from Focus data
 */
export class FocusAnalyticsService {
  /**
   * Generate insights for a user based on their data
   */
  async generateInsights(userId: string, companyId: string): Promise<FocusAnalyticsData> {
    const isContextAware = await hasFeature(companyId, 'contextAwareness')

    // Get productivity insights
    const productivityInsights = await this.generateProductivityInsights(userId, companyId)

    // Get goal insights
    const goalInsights = await this.generateGoalInsights(userId, companyId)

    // Get skill insights
    const skillInsights = await this.generateSkillInsights(userId, companyId)

    return {
      productivityInsights,
      goalInsights,
      skillInsights,
      isContextAware,
    }
  }

  /**
   * Generate productivity insights from task data
   */
  private async generateProductivityInsights(
    userId: string,
    companyId: string
  ): Promise<ProductivityInsight[]> {
    // Get task statistics
    const tasks = await prisma.focusTask.findMany({
      where: { userId, companyId },
    })

    if (tasks.length === 0) {
      return []
    }

    const completedTasks = tasks.filter(task => task.status === 'COMPLETED')
    const completionRate = Math.round((completedTasks.length / tasks.length) * 100)

    const insight: ProductivityInsight = {
      id: 'productivity-1',
      title: 'Task Completion Rate',
      description: `You've completed ${completedTasks.length} out of ${tasks.length} tasks.`,
      metric: completionRate,
      trend: completionRate >= 70 ? 'up' : completionRate >= 40 ? 'neutral' : 'down',
      trendValue: 0, // Could calculate based on historical data
      category: 'productivity',
      priority: completionRate < 50 ? 'high' : 'medium',
      actionItems: [
        'Review incomplete tasks',
        'Schedule focus time',
        'Break down large tasks into smaller ones',
      ],
    }

    return [insight]
  }

  /**
   * Generate goal insights from goal data
   */
  private async generateGoalInsights(userId: string, companyId: string): Promise<GoalInsight[]> {
    // Get goal statistics
    const goals = await prisma.goal.findMany({
      where: { userId, companyId, status: 'IN_PROGRESS' },
    })

    if (goals.length === 0) {
      return []
    }

    const averageProgress = Math.round(
      goals.reduce((sum, goal) => sum + (goal.progress || 0), 0) / goals.length
    )

    const insight: GoalInsight = {
      id: 'goal-1',
      title: 'Goal Progress Overview',
      description: `You have ${goals.length} goals in progress with an average completion of ${averageProgress}%.`,
      progress: averageProgress,
      target: 100,
      daysRemaining: 30, // Could calculate based on target dates
      onTrack: averageProgress >= 50,
      category: 'goals',
      actionItems: [
        'Focus on goals with least progress',
        'Set weekly milestones',
        'Review goal priorities',
      ],
    }

    return [insight]
  }

  /**
   * Generate skill insights from skill data
   */
  private async generateSkillInsights(userId: string, companyId: string): Promise<SkillInsight[]> {
    // Get skill statistics
    const skills = await prisma.skill.findMany({
      where: { userId, companyId, importance: { gte: 4 } }, // High importance skills (4-5)
    })

    if (skills.length === 0) {
      return []
    }

    // Find skills that need development (not at target level)
    const skillsNeedingDevelopment = skills.filter(
      skill => skill.currentLevel !== skill.targetLevel
    )

    if (skillsNeedingDevelopment.length === 0) {
      return []
    }

    const firstSkill = skillsNeedingDevelopment[0]

    const insight: SkillInsight = {
      id: 'skill-1',
      title: 'Priority Skill Development',
      description: `You have ${skillsNeedingDevelopment.length} high-importance skills that need development.`,
      currentLevel: firstSkill.currentLevel || 'BEGINNER',
      targetLevel: firstSkill.targetLevel || 'INTERMEDIATE',
      gap: skillsNeedingDevelopment.length,
      category: 'skills',
      actionItems: [
        'Focus on one high-priority skill at a time',
        'Set up learning schedule',
        'Find practice opportunities',
      ],
    }

    return [insight]
  }

  /**
   * Store user feedback for insights
   */
  async storeFeedback(
    userId: string,
    feedback: 'helpful' | 'not_helpful',
    category: string
  ): Promise<boolean> {
    try {
      // For now, just return true as we don't have a feedback table
      // In a real implementation, we would store this in a feedback table
      console.log(`Storing feedback: ${feedback} for category: ${category} from user: ${userId}`)
      return true
    } catch (error) {
      console.error('Error storing feedback:', error)
      return false
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  async getAnalyticsData(userId: string, companyId: string): Promise<FocusAnalyticsData> {
    return this.generateInsights(userId, companyId)
  }
}
