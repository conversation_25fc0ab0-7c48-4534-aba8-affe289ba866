'use client'

import { toast } from 'sonner'

// Define Goal types matching Prisma schema
export type GoalType = 'OBJECTIVE' | 'KEY_RESULT' | 'MILESTONE' | 'HABIT'
export type GoalStatus =
  | 'NOT_STARTED'
  | 'IN_PROGRESS'
  | 'ON_TRACK'
  | 'AT_RISK'
  | 'COMPLETED'
  | 'CANCELLED'
export type GoalCategory = 'PERSONAL' | 'PROFESSIONAL' | 'LEARNING' | 'TEAM' | 'COMPANY'
export type GoalTimeframe = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'
export type GoalPriority = 'LOW' | 'MEDIUM' | 'HIGH'

export interface KeyResult {
  id: string
  title: string
  description?: string
  targetValue: number
  currentValue: number
  unit: string
  status: string
  dueDate?: string
}

export interface Goal {
  id: string
  title: string
  description?: string
  type: string
  status: string
  progress: number
  targetValue?: number
  currentValue?: number
  unit?: string
  category: string
  timeframe: GoalTimeframe
  startDate?: string
  targetDate?: string
  priority: string
  parentId?: string
  userId: string
  keyResults?: KeyResult[]
  children?: Goal[]
}

// Client API for Goals
class GoalsClient {
  private async fetchWithErrorHandling(url: string, options: RequestInit = {}) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      if (!response.ok) {
        if (url.includes('goals')) {
          throw new Error('Failed to fetch goals')
        }
        const errorData = await response.json()
        throw new Error(errorData.error || 'An error occurred')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('API Error:', error)
      toast.error(error instanceof Error ? error.message : 'An unexpected error occurred')
      throw error
    }
  }

  async getGoals(): Promise<Goal[]> {
    return this.fetchWithErrorHandling('/api/goals', {
      method: 'GET',
    })
  }

  async getGoal(goalId: string): Promise<Goal> {
    return this.fetchWithErrorHandling(`/api/goals/${goalId}`, {
      method: 'GET',
    })
  }

  async createGoal(goalData: Partial<Goal>): Promise<Goal> {
    return this.fetchWithErrorHandling('/api/goals', {
      method: 'POST',
      body: JSON.stringify(goalData),
    })
  }

  async updateGoal(goalId: string, goalData: Partial<Goal>): Promise<Goal> {
    return this.fetchWithErrorHandling(`/api/goals/${goalId}`, {
      method: 'PATCH',
      body: JSON.stringify(goalData),
    })
  }

  async deleteGoal(goalId: string): Promise<{ success: boolean }> {
    return this.fetchWithErrorHandling(`/api/goals/${goalId}`, {
      method: 'DELETE',
    })
  }

  async createKeyResult(
    objectiveId: string,
    keyResultData: Partial<KeyResult>
  ): Promise<KeyResult> {
    return this.fetchWithErrorHandling(`/api/goals/${objectiveId}/key-results`, {
      method: 'POST',
      body: JSON.stringify(keyResultData),
    })
  }

  async updateKeyResult(
    keyResultId: string,
    keyResultData: Partial<KeyResult> & { objectiveId?: string }
  ): Promise<KeyResult> {
    return this.fetchWithErrorHandling(`/api/goals/key-results/${keyResultId}`, {
      method: 'PATCH',
      body: JSON.stringify(keyResultData),
    })
  }

  async deleteKeyResult(objectiveId: string, keyResultId: string): Promise<{ success: boolean }> {
    return this.fetchWithErrorHandling(`/api/goals/${objectiveId}/key-results/${keyResultId}`, {
      method: 'DELETE',
    })
  }

  async updateKeyResultProgress(
    keyResultId: string,
    objectiveId: string,
    currentValue: number
  ): Promise<KeyResult> {
    return this.fetchWithErrorHandling(
      `/api/goals/${objectiveId}/key-results/${keyResultId}/progress`,
      {
        method: 'PATCH',
        body: JSON.stringify({ currentValue }),
      }
    )
  }

  async getGoalsByTimeframe(timeframe: GoalTimeframe): Promise<Goal[]> {
    return this.fetchWithErrorHandling(`/api/goals?timeframe=${timeframe}`, {
      method: 'GET',
    })
  }

  async getAnalytics(): Promise<{
    totalGoals: number
    completedGoals: number
    inProgressGoals: number
    atRiskGoals: number
    averageProgress: number
  }> {
    return this.fetchWithErrorHandling('/api/goals/analytics', {
      method: 'GET',
    })
  }

  async getGoalStatistics(): Promise<{
    totalGoals: number
    completedGoals: number
    inProgressGoals: number
    averageProgress: number
    completedThisMonth: number
    categoryBreakdown: Record<string, number>
  }> {
    return this.fetchWithErrorHandling('/api/goals/statistics')
  }

  async reorderGoals(goalIds: string[]): Promise<{ success: boolean }> {
    return this.fetchWithErrorHandling('/api/goals/reorder', {
      method: 'POST',
      body: JSON.stringify({ goalIds }),
    })
  }

  async exportGoals(format: 'csv' | 'json' | 'pdf' = 'csv'): Promise<Blob> {
    const response = await fetch(`/api/goals/export?format=${format}`)

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to export goals')
    }

    return response.blob()
  }
}

export const goalsClient = new GoalsClient()
