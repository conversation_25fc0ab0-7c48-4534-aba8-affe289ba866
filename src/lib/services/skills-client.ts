'use client'

import { toast } from 'sonner'
import type { SkillCategory, SkillLevel } from '@prisma/client'

// Types based on Prisma schema
export interface Skill {
  id: string
  name: string
  description?: string | null
  category: SkillCategory
  currentLevel: SkillLevel
  targetLevel?: SkillLevel | null
  importance: number
  lastAssessed?: Date | null
  notes?: string | null
  tags: string[]
  assessments?: SkillAssessment[]
  createdAt?: Date
  updatedAt?: Date
}

export interface SkillAssessment {
  id: string
  score: number
  level: SkillLevel
  assessmentDate: Date
  assessmentType?: string
  notes?: string | null
  feedback?: any
  nextSteps?: string | null
}

export interface SkillsResponse {
  skills: Skill[]
  totalSkills: number
  skillsByCategory: Record<string, number>
  skillsByLevel: Record<string, number>
}

export interface SkillFilters {
  category?: SkillCategory[]
  level?: SkillLevel[]
  importance?: number[]
  search?: string
}

export interface CreateSkillInput {
  name: string
  description?: string
  category: SkillCategory
  currentLevel: SkillLevel
  targetLevel?: SkillLevel
  importance: number
  tags?: string[]
}

export interface UpdateSkillInput {
  name?: string
  description?: string
  category?: SkillCategory
  currentLevel?: SkillLevel
  targetLevel?: SkillLevel
  importance?: number
  tags?: string[]
  notes?: string
}

export interface CreateAssessmentInput {
  skillId: string
  score: number
  level: SkillLevel
  notes?: string
  assessmentType?: string
  nextSteps?: string
  feedback?: any
}

export interface SkillGap {
  skillId: string
  skillName: string
  currentLevel: SkillLevel
  targetLevel: SkillLevel
  gapScore: number // Difference between current and target
  importance: number
  category: SkillCategory
}

export interface SkillStatistics {
  totalSkills: number
  skillsByCategory: Record<string, number>
  skillsByLevel: Record<string, number>
  averageImportance: number
  topSkills: Skill[]
  skillsNeedingDevelopment: SkillGap[]
  recentAssessments: SkillAssessment[]
  skillGrowthRate: number
}

// Client-side service for skills
export const skillsClient = {
  // Get all skills with optional filtering
  async getUserSkills(filters?: SkillFilters): Promise<SkillsResponse> {
    try {
      const queryParams = new URLSearchParams()

      if (filters?.category?.length) {
        filters.category.forEach(cat => queryParams.append('category', cat))
      }

      if (filters?.level?.length) {
        filters.level.forEach(level => queryParams.append('level', level))
      }

      if (filters?.importance?.length) {
        filters.importance.forEach(imp => queryParams.append('importance', imp.toString()))
      }

      if (filters?.search) {
        queryParams.append('search', filters.search)
      }

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : ''
      const response = await fetch(`/api/skills${queryString}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch skills')
      }

      return await response.json()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch skills'
      toast.error(errorMessage)
      console.error('Error fetching skills:', error)
      return { skills: [], totalSkills: 0, skillsByCategory: {}, skillsByLevel: {} }
    }
  },

  // Get a specific skill by ID
  async getSkillById(skillId: string): Promise<Skill> {
    try {
      const response = await fetch(`/api/skills/${skillId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch skill')
      }

      return await response.json()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch skill'
      toast.error(errorMessage)
      console.error('Error fetching skill:', error)
      throw error
    }
  },

  // Create a new skill
  async createSkill(input: CreateSkillInput): Promise<Skill> {
    try {
      const response = await fetch('/api/skills', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create skill')
      }

      return await response.json()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create skill'
      toast.error(errorMessage)
      console.error('Error creating skill:', error)
      throw error
    }
  },

  // Update an existing skill
  async updateSkill(skillId: string, input: UpdateSkillInput): Promise<Skill> {
    try {
      const response = await fetch(`/api/skills/${skillId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to update skill')
      }

      return await response.json()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update skill'
      toast.error(errorMessage)
      console.error('Error updating skill:', error)
      throw error
    }
  },

  // Delete a skill
  async deleteSkill(skillId: string): Promise<void> {
    try {
      const response = await fetch(`/api/skills/${skillId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to delete skill')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete skill'
      toast.error(errorMessage)
      console.error('Error deleting skill:', error)
      throw error
    }
  },

  // Create a skill assessment
  async createAssessment(input: CreateAssessmentInput): Promise<SkillAssessment> {
    try {
      const response = await fetch(`/api/skills/${input.skillId}/assessments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create assessment')
      }

      return await response.json()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create assessment'
      toast.error(errorMessage)
      console.error('Error creating assessment:', error)
      throw error
    }
  },

  // Get skill statistics
  async getSkillStatistics(): Promise<SkillStatistics> {
    try {
      const response = await fetch('/api/skills/statistics', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch skill statistics')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch skill statistics'
      toast.error(errorMessage)
      console.error('Error fetching skill statistics:', error)
      throw error
    }
  },

  // Get skills needing development (gap between current and target)
  async getSkillsNeedingDevelopment(): Promise<SkillGap[]> {
    try {
      const response = await fetch('/api/skills/development-needs', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch skills needing development')
      }

      return await response.json()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to fetch skills needing development'
      toast.error(errorMessage)
      console.error('Error fetching skills needing development:', error)
      return []
    }
  },
}
