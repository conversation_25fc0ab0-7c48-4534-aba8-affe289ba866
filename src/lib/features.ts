/**
 * Features Module
 *
 * Provides utilities for feature flag management across the application.
 * Features are scoped to companies and cached in Redis for performance.
 */

import { redis } from '@/lib/redis'
import { prisma } from '@/lib/prisma'

// Redis key prefix for feature flags
const FEATURE_FLAG_PREFIX = 'feature_flag:'

// Cache TTL in seconds
const FEATURE_FLAG_CACHE_TTL = 10 * 60 // 10 minutes

/**
 * Known feature flags in the system
 */
export const FEATURES = {
  // Feature flags for new UI components
  UI_COMPONENTS: {
    NEW_DASHBOARD: 'newDashboard',
    ENHANCED_REPORTS: 'enhancedReports',
    BETA_SETTINGS: 'betaSettings',
  },

  // Feature flags for new functionality
  FUNCTIONALITY: {
    BULK_OPERATIONS: 'bulkOperations',
    ADVANCED_SEARCH: 'advancedSearch',
    EXPORT_TOOLS: 'exportTools',
  },

  // Feature flags for experimental features
  EXPERIMENTAL: {
    CONTEXT_AWARENESS: 'contextAwareness',
    GEN_AI_INSIGHTS: 'genAiInsights',
    BEHAVIORAL_ANALYTICS: 'behavioralAnalytics',
  },

  // Feature flags for backend systems
  SYSTEMS: {
    NEW_API_ENDPOINTS: 'newApiEndpoints',
    ENHANCED_CACHING: 'enhancedCaching',
    METRICS_COLLECTION: 'metricsCollection',
  },
}

/**
 * Check if a feature flag is enabled for a company
 *
 * @param companyId The company ID to check the feature for
 * @param featureKey The feature key to check
 * @returns True if the feature is enabled, false otherwise
 */
export async function hasFeature(companyId: string, featureKey: string): Promise<boolean> {
  try {
    if (!companyId || !featureKey) {
      return false
    }

    // Generate cache key
    const cacheKey = `feature-flag:${companyId}:${featureKey}`

    // Try to get from cache first
    const cachedResult = await redis.get(cacheKey)
    if (cachedResult !== null) {
      return cachedResult === 'true'
    }

    // Not in cache, check database
    const featureFlag = await prisma.featureFlag.findFirst({
      where: {
        key: featureKey,
        companyId,
        enabled: true,
      },
      select: {
        id: true,
        enabled: true,
      },
    })

    const isEnabled = !!featureFlag?.enabled

    // Cache the result
    await redis.setex(cacheKey, FEATURE_FLAG_CACHE_TTL, isEnabled.toString())

    return isEnabled
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error(`Error checking feature flag ${featureKey} for company ${companyId}:`, error)
    return false // Default to disabled on error
  }
}

/**
 * Get feature flag value if enabled
 *
 * @param companyId The company ID to check the feature for
 * @param featureKey The feature key to check
 * @returns The feature value if enabled, null otherwise
 */
export async function getFeatureValue<T = any>(
  companyId: string,
  featureKey: string
): Promise<T | null> {
  try {
    if (!companyId || !featureKey) {
      return null
    }

    // Generate cache keys
    const enabledCacheKey = `feature-flag:${companyId}:${featureKey}`
    const valueCacheKey = `feature-flag:${companyId}:${featureKey}:value`

    // Try to get from cache first
    const [cachedEnabled, cachedValue] = await Promise.all([
      redis.get(enabledCacheKey),
      redis.get(valueCacheKey),
    ])

    // If we have both cached values, return the value if enabled
    if (cachedEnabled !== null && cachedValue !== null) {
      return cachedEnabled === 'true' ? (JSON.parse(cachedValue) as T) : null
    }

    // Not in cache or incomplete cache, check database
    const featureFlag = await prisma.featureFlag.findFirst({
      where: {
        key: featureKey,
        companyId,
      },
      select: {
        enabled: true,
        value: true,
      },
    })

    if (!featureFlag) {
      // Cache negative result
      await redis.setex(enabledCacheKey, FEATURE_FLAG_CACHE_TTL, 'false')
      await redis.setex(valueCacheKey, FEATURE_FLAG_CACHE_TTL, 'null')
      return null
    }

    // Cache results
    await redis.setex(
      enabledCacheKey,
      FEATURE_FLAG_CACHE_TTL,
      featureFlag.enabled ? 'true' : 'false'
    )
    await redis.setex(
      valueCacheKey,
      FEATURE_FLAG_CACHE_TTL,
      featureFlag.value ? JSON.stringify(featureFlag.value) : 'null'
    )

    // Return value if enabled
    return featureFlag.enabled ? (featureFlag.value as T) : null
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error(
          `Error getting feature value for ${featureKey} in company ${companyId}:`,
          error
        )
    return null // Default to null on error
  }
}

/**
 * Invalidate feature flag cache for a company
 *
 * @param companyId The company ID to invalidate cache for
 * @param featureKey Optional specific feature key to invalidate
 */
export async function invalidateFeatureFlagCache(
  companyId: string,
  featureKey?: string
): Promise<number> {
  try {
    // Create pattern for matching keys
    const pattern = featureKey
      ? `feature-flag:${companyId}:${featureKey}*`
      : `feature-flag:${companyId}:*`

    // Get all matching keys
    const keys = await redis.keys(pattern)

    if (keys.length === 0) {
      return 0
    }

    // Delete all matching keys
    await redis.del(...keys)
    return keys.length
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error(`Error invalidating feature flag cache for company ${companyId}:`, error)
    return 0
  }
}

/**
 * Set a feature flag value with cache invalidation
 *
 * @param companyId The company ID to set the feature for
 * @param featureKey The feature key to set
 * @param enabled Whether the feature is enabled
 * @param value Optional value for the feature
 */
export async function setFeatureFlag(
  companyId: string,
  featureKey: string,
  enabled: boolean,
  value?: any
): Promise<boolean> {
  try {
    // Check if feature flag exists
    const existing = await prisma.featureFlag.findFirst({
      where: {
        key: featureKey,
        companyId,
      },
      select: {
        id: true,
      },
    })

    // Update or create feature flag
    if (existing) {
      await prisma.featureFlag.update({
        where: {
          id: existing.id,
        },
        data: {
          enabled,
          value: value !== undefined ? value : undefined,
          updatedAt: new Date(),
        },
      })
    } else {
      await prisma.featureFlag.create({
        data: {
          key: featureKey,
          companyId,
          enabled,
          value: value !== undefined ? value : null,
        },
      })
    }

    // Invalidate cache
    await invalidateFeatureFlagCache(companyId, featureKey)

    return true
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error(`Error setting feature flag ${featureKey} for company ${companyId}:`, error)
    return false
  }
}

/**
 * Gets all enabled features for a company
 *
 * @param companyId The company ID to check
 * @returns A record of feature keys and their enabled status
 */
export async function getCompanyFeatures(companyId: string): Promise<Record<string, boolean>> {
  try {
    // Check if we have a cached feature map
    const cacheKey = `${FEATURE_FLAG_PREFIX}${companyId}:all`
    const cachedMap = await redis.get(cacheKey)

    if (cachedMap) {
      return JSON.parse(cachedMap as string)
    }

    // Not in cache, fetch from database
    const featureFlags = await prisma.featureFlag.findMany({
      where: { companyId },
    })

    // Transform into a record
    const featureMap = featureFlags.reduce(
      (acc, flag) => {
        acc[flag.key] = flag.enabled
        return acc
      },
      {} as Record<string, boolean>
    )

    // Cache the result
    await redis.set(cacheKey, JSON.stringify(featureMap), 'EX', FEATURE_FLAG_CACHE_TTL)

    return featureMap
  } catch {
    // console.error(`Error fetching feature flags for company ${companyId}:`, error)
    return {}
  }
}

/**
 * Sets a feature flag for a company
 *
 * @param companyId The company ID
 * @param featureKey The feature flag key
 * @param enabled Whether the feature should be enabled
 * @returns True if the operation was successful
 */
export async function setFeature(
  companyId: string,
  featureKey: string,
  enabled: boolean
): Promise<boolean> {
  try {
    // Update or create the feature flag
    await prisma.featureFlag.upsert({
      where: {
        companyId_key: {
          companyId,
          key: featureKey,
        },
      },
      update: { enabled },
      create: {
        companyId,
        key: featureKey,
        enabled,
      },
    })

    // Invalidate caches
    const cacheKey = `${FEATURE_FLAG_PREFIX}${companyId}:${featureKey}`
    const allCacheKey = `${FEATURE_FLAG_PREFIX}${companyId}:all`

    await redis.del(cacheKey)
    await redis.del(allCacheKey)

    return true
  } catch {
    // console.error(`Error setting feature flag ${featureKey} for company ${companyId}:`, error)
    return false
  }
}
