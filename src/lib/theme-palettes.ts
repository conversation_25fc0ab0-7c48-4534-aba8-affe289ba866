export type ColorPalette = {
  id: string
  name: string
  primary: string
  secondary: string
  accent: string
}

// Consolidated palette list (extend as needed)
export const colorPalettes: Record<string, ColorPalette> = {
  'emynent-dark': {
    id: 'emynent-dark',
    name: 'Emyne<PERSON> (Dark)',
    primary: '#93C5FD',
    secondary: '#9CA3AF',
    accent: '#FCD34D',
  },
  'emynent-light': {
    id: 'emynent-light',
    name: '<PERSON><PERSON><PERSON> (Light)',
    primary: '#1E40AF',
    secondary: '#6B7280',
    accent: '#F59E0B',
  },
  nightowl: {
    id: 'nightowl',
    name: 'Night Owl',
    primary: '#82AAFF',
    secondary: '#C792EA',
    accent: '#FFCB6B',
  },
  draculapro: {
    id: 'draculapro',
    name: 'Dracula Pro',
    primary: '#BD93F9',
    secondary: '#FF79C6',
    accent: '#50FA7B',
  },
  'github-dark': {
    id: 'github-dark',
    name: 'GitHub Dark',
    primary: '#58A6FF',
    secondary: '#79C0FF',
    accent: '#85E89D',
  },
  'blue-dark': {
    id: 'blue-dark',
    name: '<PERSON> Dark',
    primary: '#3B82F6',
    secondary: '#60A5FA',
    accent: '#93C5FD',
  },
  'green-dark': {
    id: 'green-dark',
    name: 'Green Dark',
    primary: '#10B981',
    secondary: '#34D399',
    accent: '#6EE7B7',
  },
  'slate-dark': {
    id: 'slate-dark',
    name: 'Slate Dark',
    primary: '#64748B',
    secondary: '#94A3B8',
    accent: '#CBD5E1',
  },
  twilight: {
    id: 'twilight',
    name: 'Twilight',
    primary: '#8B5CF6',
    secondary: '#A78BFA',
    accent: '#C4B5FD',
  },
  // Light variants can be added similarly
}

export function applyPaletteToDocument(paletteId: string) {
  const palette = colorPalettes[paletteId]
  if (!palette) return

  document.documentElement.style.setProperty('--primary', palette.primary)
  document.documentElement.style.setProperty('--secondary', palette.secondary)
  document.documentElement.style.setProperty('--accent', palette.accent)
}
