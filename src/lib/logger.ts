export interface Logger {
  info(message: string, meta?: unknown): void
  warn(message: string, meta?: unknown): void
  error(message: string, meta?: unknown): void
  debug(message: string, meta?: unknown): void
}

class ConsoleLogger implements Logger {
  info(_message: string, _meta?: unknown): void {
    // console.log(`[INFO] ${message}`, meta ? JSON.stringify(meta) : '');
  }

  warn(_message: string, _meta?: unknown): void {
    // console.warn(`[WARN] ${message}`, meta ? JSON.stringify(meta) : '');
  }

  error(_message: string, _meta?: unknown): void {
    // console.error(`[ERROR] ${message}`, meta ? JSON.stringify(meta) : '');
  }

  debug(_message: string, _meta?: unknown): void {
    if (process.env.NODE_ENV === 'development') {
      // console.debug(`[DEBUG] ${message}`, meta ? JSON.stringify(meta) : '');
    }
  }
}

export const logger: Logger = new ConsoleLogger()
