import { NextRequest, NextResponse } from 'next/server'
import { getTenantContextFromHeaders, TenantContext } from './tenant-context'
import { TenantIsolationManager } from '../prisma'
import { headers } from 'next/headers'

/**
 * Middleware to protect API routes with tenant validation
 */
export async function withTenantProtection(
  handler: (req: NextRequest, tenantContext: TenantContext) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    try {
      // Extract tenant context from headers
      const headersList = headers()
      const tenantContext = getTenantContextFromHeaders(headersList)

      // If no tenant context is found, reject the request
      if (!tenantContext) {
        return new NextResponse(
          JSON.stringify({
            error: 'Tenant context missing',
            message: 'Unable to determine company context',
          }),
          {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        )
      }

      // Call the handler with the tenant context
      return handler(request, tenantContext)
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('🔴 Error in tenant API middleware:', error)

      return new NextResponse(
        JSON.stringify({
          error: 'Tenant middleware error',
          message: error instanceof Error ? error.message : 'Unknown error',
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
    }
  }
}

/**
 * Create a tenant-scoped Prisma client for an API route
 */
export function createTenantClientForAPI(tenantContext: TenantContext) {
  try {
    if (!tenantContext.companyId) {
      throw new Error('Company ID is required for tenant-scoped operations')
    }

    return TenantIsolationManager.createTenantClient(tenantContext.companyId)
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('🔴 Error creating tenant client:', error)
    throw error
  }
}

/**
 * Helper to validate a request has the correct tenant permissions
 */
export async function validateTenantPermissions(
  tenantContext: TenantContext,
  requiredRoles: string[] = []
): Promise<boolean> {
  try {
    // Validate tenant isolation
    const isValidTenant = await TenantIsolationManager.validateTenantIsolation(
      tenantContext.companyId
    )

    if (!isValidTenant) {
      return false
    }

    // If specific roles are required, validate user role
    if (requiredRoles.length > 0 && tenantContext.role) {
      return requiredRoles.includes(tenantContext.role)
    }

    return true
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('🔴 Error validating tenant permissions:', error)
    return false
  }
}
