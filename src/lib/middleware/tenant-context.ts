import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { TenantIsolationManager } from '../prisma'

export interface TenantContext {
  companyId: string
  userId?: string
  role?: string
}

/**
 * Extract tenant context from the request
 * This will be used to scope database operations
 */
export async function extractTenantContext(request: NextRequest): Promise<TenantContext | null> {
  try {
    // Get token from NextAuth
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token) {
      return null
    }

    // Extract company ID from the token
    const companyId = token.companyId as string

    // Validate the tenant context
    if (!companyId) {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('⚠️ Missing companyId in session token')
      return null
    }

    // Create the tenant context
    return {
      companyId,
      userId: token.sub as string,
      role: token.role as string,
    }
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('🔴 Error extracting tenant context:', error)
    return null
  }
}

/**
 * Validate tenant isolation based on the request
 * Ensures the user has access to the requested company/tenant
 */
export async function validateTenantAccess(tenantContext: TenantContext): Promise<boolean> {
  try {
    if (!tenantContext.companyId) {
      return false
    }

    // Validate tenant isolation using the manager
    const isValid = await TenantIsolationManager.validateTenantIsolation(tenantContext.companyId)

    return isValid
  } catch {
    if (process.env.NODE_ENV === 'development')
      if (process.env.NODE_ENV === 'development')
        console.error('🔴 Error validating tenant access:', error)
    return false
  }
}

/**
 * Set tenant context in response headers
 * This allows downstream middleware and API routes to access tenant information
 */
export function setTenantContextHeaders(
  response: NextResponse,
  tenantContext: TenantContext
): NextResponse {
  // Set tenant context in headers for API routes to consume
  response.headers.set('x-tenant-id', tenantContext.companyId)

  if (tenantContext.userId) {
    response.headers.set('x-user-id', tenantContext.userId)
  }

  if (tenantContext.role) {
    response.headers.set('x-user-role', tenantContext.role)
  }

  return response
}

/**
 * Handle tenant validation errors
 */
export function handleTenantValidationError(request: NextRequest): NextResponse {
  if (process.env.NODE_ENV === 'development')
    if (process.env.NODE_ENV === 'development')
      console.error('🔴 Tenant validation failed for:', request.url)

  // For API routes, return a 403 response
  if (request.nextUrl.pathname.startsWith('/api/')) {
    return new NextResponse(
      JSON.stringify({
        error: 'Tenant validation failed',
        message: 'Unable to validate company access',
      }),
      {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }

  // For page routes, redirect to an error page
  return NextResponse.redirect(new URL('/unauthorized?reason=tenant', request.url))
}

/**
 * Get tenant context from headers (for use in API routes)
 */
export function getTenantContextFromHeaders(headers: Headers): TenantContext | null {
  const companyId = headers.get('x-tenant-id')

  if (!companyId) {
    return null
  }

  return {
    companyId,
    userId: headers.get('x-user-id') || undefined,
    role: headers.get('x-user-role') || undefined,
  }
}
