import { PrismaClient } from '@prisma/client'
import { prisma } from './prisma'

export interface TenantScopedPrisma extends PrismaClient {
  companyId: string
}

/**
 * Manages tenant isolation for multi-tenant database operations
 */
export class TenantIsolationManager {
  private static clientCache = new Map<string, TenantScopedPrisma>()

  /**
   * Creates a tenant-scoped Prisma client for a specific company
   */
  static createTenantClient(companyId: string): TenantScopedPrisma {
    // Check cache first
    if (this.clientCache.has(companyId)) {
      return this.clientCache.get(companyId)!
    }

    // Create new tenant-scoped client by extending the base prisma client
    const tenantClient = prisma.$extends({
      query: {
        $allModels: {
          async $allOperations({ model, operation, args, query }) {
            // Only apply tenant isolation for models that have companyId field
            const tenantAwareModels = [
              'User',
              'UserProfile',
              'UserActivity',
              'Company',
              'Role',
              'UserRole',
              'FeatureFlag',
              'UserGoal',
              'Goal',
              'GoalUpdate',
              'Skill',
              'UserSkill',
              'PerformanceReview',
              'Feedback',
              'Team',
              'TeamMember',
              'OKR',
              'Analytics',
            ]

            if (tenantAwareModels.includes(model)) {
              // Add companyId constraint to where clause for read operations
              if (
                ['findFirst', 'findMany', 'findUnique', 'count', 'aggregate'].includes(operation)
              ) {
                args.where = args.where || {}
                args.where.companyId = companyId
              }

              // Add companyId to data for create operations
              if (operation === 'create') {
                args.data = args.data || {}
                args.data.companyId = companyId
              }

              // Add companyId constraint for update/delete operations
              if (['update', 'updateMany', 'delete', 'deleteMany'].includes(operation)) {
                args.where = args.where || {}
                args.where.companyId = companyId
              }
            }

            return query(args)
          },
        },
      },
    }) as TenantScopedPrisma

    // Add companyId property to the client
    ;(tenantClient as any).companyId = companyId

    // Cache the client
    this.clientCache.set(companyId, tenantClient)

    return tenantClient
  }

  /**
   * Validates that a company exists and is active for tenant isolation
   */
  static async validateTenantIsolation(companyId: string): Promise<boolean> {
    try {
      if (!companyId || typeof companyId !== 'string') {
        return false
      }

      // Check if company exists and is active
      const company = await prisma.company.findFirst({
        where: {
          id: companyId,
          isActive: true,
        },
        select: {
          id: true,
          isActive: true,
        },
      })

      return !!company
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.error('Error validating tenant isolation:', error)
      return false
    }
  }

  /**
   * Clears the tenant client cache for a specific company or all companies
   */
  static clearTenantClientCache(companyId?: string): void {
    if (companyId) {
      this.clientCache.delete(companyId)
    } else {
      this.clientCache.clear()
    }
  }

  /**
   * Gets the current cache size for monitoring
   */
  static getCacheSize(): number {
    return this.clientCache.size
  }

  /**
   * Lists all cached company IDs for debugging
   */
  static getCachedCompanyIds(): string[] {
    return Array.from(this.clientCache.keys())
  }
}

export default TenantIsolationManager
