/**
 * Service Provider
 *
 * This module determines whether to use real services or mock implementations
 * based on environment configuration.
 */

import * as mockServices from './mock-service'

// Import real services if they exist
let realServices = null
try {
  // This will fail if services don't exist or can't connect
  realServices = require('./real-services')
} catch {
  // console.log('[SERVICE PROVIDER] Using mock services due to import error');
}

// Check if mocks should be used explicitly
const useMocks = process.env.USE_MOCKS === 'true' || !realServices

// Export the appropriate service implementations
export const hasFeature = useMocks ? mockServices.hasFeature : realServices?.hasFeature

export const getUserSettings = useMocks
  ? mockServices.getUserSettings
  : realServices?.getUserSettings

export const updateUserSettings = useMocks
  ? mockServices.updateUserSettings
  : realServices?.updateUserSettings

export const getCompany = useMocks ? mockServices.getCompany : realServices?.getCompany

export const getUserContext = useMocks ? mockServices.getUserContext : realServices?.getUserContext

// Initialize function - logs which services are being used
export function initializeServices() {
  if (useMocks) {
    // console.log('[SERVICE PROVIDER] Using mock services');
    return {
      usingMocks: true,
      database: false,
      redis: false,
    }
  } else {
    const services = mockServices.areServicesAvailable()
    // console.log(`[SERVICE PROVIDER] Using real services - Database: ${services.database}, Redis: ${services.redis}`);
    return {
      usingMocks: false,
      ...services,
    }
  }
}
