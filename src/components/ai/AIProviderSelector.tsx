import React from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Brain } from 'lucide-react'

interface AIProviderSelectorProps {
  currentProvider: string
  availableProviders: string[]
  onProviderChange: (provider: string) => void
  isLoading: boolean
}

export function AIProviderSelector({
  currentProvider,
  availableProviders,
  onProviderChange,
  isLoading,
}: AIProviderSelectorProps) {
  const providerDisplayNames: Record<string, string> = {
    openai: 'OpenAI',
    claude: 'Claude',
    gemini: 'Gemini',
    deepseek: 'DeepSeek',
  }

  return (
    <div className='space-y-2'>
      <div className='flex items-center gap-2'>
        <Brain className='h-3 w-3 text-primary' />
        <span className='text-xs font-medium text-muted-foreground'>AI Provider</span>
      </div>

      <Select value={currentProvider} onValueChange={onProviderChange} disabled={isLoading}>
        <SelectTrigger className='h-8 text-xs'>
          <SelectValue placeholder='Select AI provider' />
        </SelectTrigger>
        <SelectContent>
          {availableProviders.map(provider => (
            <SelectItem key={provider} value={provider} className='text-xs'>
              {providerDisplayNames[provider] || provider}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
