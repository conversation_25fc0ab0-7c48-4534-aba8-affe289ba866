'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, Too<PERSON>ipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useNavigationContext } from '@/lib/context/navigation-context'
import { useUserContext } from '@/lib/context/user-context'
import { useAIModelProvider } from '@/lib/hooks/useAIModelProvider'
import { hasFeatureSync } from '@/lib/services/feature-flag-service'
import { cn } from '@/lib/utils'
import { Role } from '@prisma/client'
import {
  Activity,
  AlertCircle,
  Award,
  BookOpen,
  Bot,
  Brain,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Compass,
  // Icons for the 9 navigation areas + settings
  LayoutDashboard,
  MessageCircle,
  Settings,
  Shield,
  Sparkles,
  Target,
  User,
  Users,
  Zap,
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useCallback, useEffect, useMemo, useState } from 'react'

// Enhanced navigation item interface
interface NavigationItem {
  id: string
  name: string
  href: string
  icon: React.ElementType
  description: string
  group: string
  features: string[]
  priority: number
  relevanceScore?: number
  isRecommended?: boolean
  notification?: number
  newActivity?: boolean
  aiSuggestion?: string
}

// Navigation zone interface with collapsible state
interface NavigationZone {
  id: string
  name: string
  items: NavigationItem[]
  collapsed?: boolean
  icon?: React.ElementType
}

// Define the navigation groups with updated names
const PERSONAL_ZONE = 'Personal'
const TEAM_ZONE = 'Team'
const ORGANISATION_ZONE = 'Organisation'
const SETTINGS_ZONE = 'Settings'

// Enhanced navigation items with consolidated settings
const baseNavigationItems: NavigationItem[] = [
  // Personal Zone
  {
    id: 'focus',
    name: 'Focus',
    href: '/focus',
    icon: LayoutDashboard,
    description: 'Your daily command center',
    group: PERSONAL_ZONE,
    features: ['daily-planning', 'goal-tracking', 'productivity'],
    priority: 10,
  },
  {
    id: 'grow',
    name: 'Grow',
    href: '/grow',
    icon: BookOpen,
    description: 'Your learning journey',
    group: PERSONAL_ZONE,
    features: ['learning', 'skills', 'development', 'career-path'],
    priority: 7,
  },

  // Team Zone
  {
    id: 'team',
    name: 'Team',
    href: '/team',
    icon: Users,
    description: 'Your squad management',
    group: TEAM_ZONE,
    features: ['team-management', 'leadership', 'delegation'],
    priority: 8,
  },
  {
    id: 'connect',
    name: 'Connect',
    href: '/connect',
    icon: MessageCircle,
    description: 'Communication & feedback',
    group: TEAM_ZONE,
    features: ['feedback', 'communication', 'collaboration'],
    priority: 6,
  },
  {
    id: 'celebrate',
    name: 'Celebrate',
    href: '/celebrate',
    icon: Award,
    description: 'Recognition & community',
    group: TEAM_ZONE,
    features: ['recognition', 'rewards', 'culture'],
    priority: 5,
  },

  // Organisation Zone
  {
    id: 'vision',
    name: 'Vision',
    href: '/vision',
    icon: Target,
    description: 'Company strategy & alignment',
    group: ORGANISATION_ZONE,
    features: ['strategy', 'goals', 'okrs', 'alignment'],
    priority: 7,
  },
  {
    id: 'explore',
    name: 'Explore',
    href: '/explore',
    icon: Compass,
    description: 'Internal opportunities',
    group: ORGANISATION_ZONE,
    features: ['opportunities', 'projects', 'growth'],
    priority: 6,
  },
  {
    id: 'pulse',
    name: 'Pulse',
    href: '/pulse',
    icon: Activity,
    description: 'Organizational insights',
    group: ORGANISATION_ZONE,
    features: ['analytics', 'insights', 'trends', 'reporting'],
    priority: 7,
  },

  // Settings Zone - Consolidated to single entry
  {
    id: 'settings',
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    description: 'System settings and preferences',
    group: SETTINGS_ZONE,
    features: ['settings', 'preferences'],
    priority: 9,
    // Removed individual settings items to eliminate duplication
    // The settings page has its own comprehensive internal navigation
  },
]

// AI Provider Selector Component
function AIProviderSelector({
  currentProvider,
  availableProviders,
  onProviderChange,
  isLoading,
}: {
  currentProvider: string | null
  availableProviders: string[]
  onProviderChange: (provider: string) => void
  isLoading: boolean
}) {
  const [isOpen, setIsOpen] = useState(false)

  const providerNames: Record<string, string> = {
    openai: 'OpenAI',
    claude: 'Claude',
    gemini: 'Gemini',
    deepseek: 'DeepSeek',
  }

  return (
    <div className='relative' data-testid='ai-provider-selector'>
      <Button
        variant='ghost'
        size='sm'
        className='w-full justify-start h-auto p-1 text-xs'
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
      >
        <Zap className='h-3 w-3 mr-1' />
        Switch AI Model
      </Button>

      {isOpen && (
        <div className='absolute bottom-full left-0 w-full mb-1 bg-popover border border-border rounded-md shadow-md z-50'>
          {availableProviders.map(provider => (
            <button
              key={provider}
              className='w-full text-left px-2 py-1 text-xs hover:bg-muted first:rounded-t-md last:rounded-b-md'
              onClick={() => {
                onProviderChange(provider)
                setIsOpen(false)
              }}
            >
              {providerNames[provider] || provider}
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

// Enhanced tooltip component for minimized navigation
function NavigationTooltip({
  children,
  item,
  collapsed,
}: {
  children: React.ReactNode
  item: NavigationItem
  collapsed: boolean
}) {
  if (!collapsed) return <>{children}</>

  return (
    <Tooltip delayDuration={300}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side='right' className='max-w-xs'>
        <div className='space-y-1'>
          <div className='font-medium'>{item.name}</div>
          <div className='text-xs text-muted-foreground'>{item.description}</div>
          {item.aiSuggestion && (
            <div className='text-xs text-primary' data-testid='contextual-hint'>
              {item.aiSuggestion}
            </div>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  )
}

// Zone header component with collapse functionality
function ZoneHeader({
  zone,
  collapsed,
  sidebarCollapsed,
  onToggle,
}: {
  zone: NavigationZone
  collapsed: boolean
  sidebarCollapsed: boolean
  onToggle: () => void
}) {
  if (sidebarCollapsed) return null

  return (
    <div className='flex items-center justify-between px-3 py-2 group'>
      <div className='text-xs font-semibold text-muted-foreground uppercase tracking-wider'>
        {zone.name}
      </div>
      <Button
        variant='ghost'
        size='icon'
        className='h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity'
        onClick={onToggle}
        data-testid={`zone-toggle-${zone.id}`}
        aria-label={`${collapsed ? 'Expand' : 'Collapse'} ${zone.name} section`}
      >
        {collapsed ? <ChevronDown size={12} /> : <ChevronUp size={12} />}
      </Button>
    </div>
  )
}

export function EnhancedAIFirstSidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()
  const [collapsed, setCollapsed] = useState(false)
  const [zoneStates, setZoneStates] = useState<Record<string, boolean>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [aiRecommendations, setAiRecommendations] = useState<string[]>([])
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Context hooks
  const { navigationState, setNavigationState } = useNavigationContext()
  const { userContext, updateUserContext } = useUserContext()
  const { currentProvider, switchProvider, isProviderReady, providerError } = useAIModelProvider()

  const userRole = session?.user?.role
  const companyId = session?.user?.companyId
  const isSuperAdmin = userRole === Role.SUPERADMIN
  const isContextAware = companyId ? hasFeatureSync(companyId, 'contextAwareness') : false

  // Load persisted states from localStorage
  useEffect(() => {
    const cachedCollapsed = localStorage.getItem('sidebar-collapsed')
    if (cachedCollapsed === 'true') {
      setCollapsed(true)
    }

    // Load zone states
    const zones = ['personal', 'team', 'organisation', 'settings']
    const initialZoneStates: Record<string, boolean> = {}
    zones.forEach(zone => {
      const cached = localStorage.getItem(`sidebar-zone-${zone}-collapsed`)
      initialZoneStates[zone] = cached === 'true'
    })
    setZoneStates(initialZoneStates)
  }, [])

  // Generate context-aware navigation items
  const generateContextAwareItems = useCallback((): NavigationItem[] => {
    if (!isContextAware || !userContext) {
      return baseNavigationItems
    }

    return baseNavigationItems.map(item => {
      const frequency = userContext.navigationFrequency?.[item.id] || 0
      const timeSpent = userContext.timeSpent?.[item.id] || 0
      const recentActivity = userContext.recentlyVisited?.includes(item.href) || false

      // Calculate relevance score based on AI analysis
      const relevanceScore = Math.min(
        10,
        frequency * 0.4 + timeSpent * 0.3 + (recentActivity ? 3 : 0)
      )

      return {
        ...item,
        relevanceScore,
        isRecommended: relevanceScore > 7,
        newActivity: recentActivity,
        aiSuggestion:
          relevanceScore > 8
            ? `Based on your recent activity, this area needs attention`
            : undefined,
      }
    })
  }, [isContextAware, userContext])

  // Group navigation items into zones
  const groupNavigationItems = useCallback(
    (items: NavigationItem[]): NavigationZone[] => {
      const zones: NavigationZone[] = [
        {
          id: 'personal',
          name: PERSONAL_ZONE,
          items: items.filter(item => item.group === PERSONAL_ZONE),
          collapsed: zoneStates.personal || false,
          icon: User,
        },
        {
          id: 'team',
          name: TEAM_ZONE,
          items: items.filter(item => item.group === TEAM_ZONE),
          collapsed: zoneStates.team || false,
          icon: Users,
        },
        {
          id: 'organisation',
          name: ORGANISATION_ZONE,
          items: items.filter(item => item.group === ORGANISATION_ZONE),
          collapsed: zoneStates.organisation || false,
          icon: Target,
        },
        {
          id: 'settings',
          name: SETTINGS_ZONE,
          items: items.filter(item => item.group === SETTINGS_ZONE),
          collapsed: zoneStates.settings || false,
          icon: Settings,
        },
      ]

      // Sort items within each zone by relevance score and priority
      zones.forEach(zone => {
        zone.items.sort((a, b) => {
          const scoreA = a.relevanceScore || 0
          const scoreB = b.relevanceScore || 0
          if (scoreA !== scoreB) return scoreB - scoreA
          return b.priority - a.priority
        })
      })

      return zones
    },
    [zoneStates]
  )

  // Handle sidebar collapse toggle
  const handleCollapseToggle = useCallback(() => {
    const newCollapsed = !collapsed
    setCollapsed(newCollapsed)
    localStorage.setItem('sidebar-collapsed', newCollapsed.toString())
  }, [collapsed])

  // Handle zone collapse toggle
  const handleZoneToggle = useCallback(
    (zoneId: string) => {
      const newZoneStates = {
        ...zoneStates,
        [zoneId]: !zoneStates[zoneId],
      }
      setZoneStates(newZoneStates)
      localStorage.setItem(`sidebar-zone-${zoneId}-collapsed`, newZoneStates[zoneId].toString())
    },
    [zoneStates]
  )

  // Handle navigation click with analytics
  const handleNavigationClick = useCallback(
    (item: NavigationItem, event: React.MouseEvent) => {
      // Track navigation for AI learning
      if (isContextAware && userContext) {
        updateUserContext({
          ...userContext,
          navigationFrequency: {
            ...userContext.navigationFrequency,
            [item.id]: (userContext.navigationFrequency?.[item.id] || 0) + 1,
          },
          recentlyVisited: [
            item.href,
            ...(userContext.recentlyVisited || []).filter(href => href !== item.href).slice(0, 9),
          ],
        })
      }

      // Update navigation state
      setNavigationState({
        ...navigationState,
        currentPath: item.href,
        lastNavigationTime: Date.now(),
      })
    },
    [isContextAware, userContext, updateUserContext, navigationState, setNavigationState]
  )

  // Generate final navigation items and zones
  const contextAwareItems = useMemo(() => generateContextAwareItems(), [generateContextAwareItems])
  const navigationZones = useMemo(
    () => groupNavigationItems(contextAwareItems),
    [groupNavigationItems, contextAwareItems]
  )

  return (
    <TooltipProvider>
      <div
        data-testid='sidebar'
        className={cn(
          'h-screen bg-card border-r border-border transition-all duration-300 flex flex-col relative',
          collapsed ? 'w-16' : 'w-64'
        )}
      >
        {/* Enhanced Header with Better Spacing */}
        <header
          className='w-full h-14 flex items-center border-b border-border bg-background/80 backdrop-blur-lg relative'
          role='banner'
        >
          <div
            className={cn('flex items-center flex-1', collapsed ? 'justify-center px-2' : 'px-4')}
          >
            {!collapsed && (
              <Link href='/dashboard' className='flex items-center space-x-2 group'>
                <Bot className='h-7 w-7 text-[var(--primary)] group-hover:text-[var(--primary)]/80 transition-colors' />
                <span className='text-xl font-bold text-foreground group-hover:text-[var(--primary)] transition-colors'>
                  Emynent
                </span>
              </Link>
            )}
            {collapsed && (
              <Link href='/dashboard' className='flex items-center justify-center group'>
                <Bot className='h-7 w-7 text-[var(--primary)] group-hover:text-[var(--primary)]/80 transition-colors' />
              </Link>
            )}
          </div>

          {/* Improved Chevron Positioning */}
          <Button
            variant='ghost'
            size='icon'
            onClick={handleCollapseToggle}
            className={cn(
              'hover:bg-muted/50 transition-colors',
              collapsed ? 'absolute top-3 right-2 h-8 w-8' : 'mr-4 h-8 w-8'
            )}
            data-testid='sidebar-collapse-toggle'
            aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            aria-expanded={!collapsed}
          >
            {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
          </Button>
        </header>

        {/* AI Provider Indicator */}
        {!collapsed && isContextAware && (
          <div className='px-4 py-2 border-b border-border/50'>
            <div className='flex items-center justify-between text-xs text-muted-foreground'>
              <span data-testid='ai-provider-indicator'>AI: {currentProvider || 'OpenAI'}</span>
              {providerError && <AlertCircle className='h-3 w-3 text-destructive' />}
              {isContextAware && (
                <div
                  className='h-2 w-2 bg-green-500 rounded-full'
                  data-testid='ai-recommendation-indicator'
                />
              )}
            </div>
          </div>
        )}

        {/* Navigation Context Indicator */}
        <div data-testid='navigation-context' data-initialized='true' className='hidden' />

        {/* Enhanced Navigation Content */}
        <nav
          className='flex-1 space-y-2 px-2 py-4 overflow-y-auto'
          role='navigation'
          aria-label='Main navigation'
        >
          {navigationZones.map(zone => (
            <div key={zone.id} data-testid={`${zone.id}-zone`} className='space-y-1'>
              {/* Zone Header with Collapse */}
              <ZoneHeader
                zone={zone}
                collapsed={zone.collapsed || false}
                sidebarCollapsed={collapsed}
                onToggle={() => handleZoneToggle(zone.id)}
              />

              {/* Zone Items */}
              {(!zone.collapsed || collapsed) && (
                <div className='space-y-1'>
                  {zone.items.map(item => {
                    const isActive = pathname.startsWith(item.href)
                    const ItemIcon = item.icon

                    return (
                      <NavigationTooltip key={item.id} item={item} collapsed={collapsed}>
                        <Link
                          href={item.href}
                          data-testid={`nav-${item.id}`}
                          onClick={e => handleNavigationClick(item, e)}
                          className={cn(
                            'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors group relative',
                            isActive
                              ? 'bg-primary/10 text-primary'
                              : 'text-foreground/60 hover:text-foreground hover:bg-muted',
                            collapsed ? 'justify-center' : 'justify-start',
                            item.isRecommended && 'ring-1 ring-primary/20'
                          )}
                          role='link'
                          aria-label={collapsed ? `${item.name} - ${item.description}` : undefined}
                          aria-description={item.description}
                        >
                          <ItemIcon
                            className={cn('h-5 w-5 flex-shrink-0', collapsed ? 'mx-0' : 'mr-3')}
                          />

                          {!collapsed && (
                            <>
                              <span className='flex-1'>{item.name}</span>

                              {/* AI Enhancement Indicators */}
                              <div className='flex items-center gap-1'>
                                {item.isRecommended && (
                                  <Sparkles className='h-3 w-3 text-primary' />
                                )}

                                {item.newActivity && (
                                  <div className='h-2 w-2 bg-primary rounded-full' />
                                )}

                                {item.notification && item.notification > 0 && (
                                  <Badge
                                    variant='secondary'
                                    className='h-5 w-5 rounded-full p-0 text-xs'
                                  >
                                    {item.notification > 9 ? '9+' : item.notification}
                                  </Badge>
                                )}
                              </div>
                            </>
                          )}

                          {/* Relevance Score Indicator (for testing) */}
                          {item.relevanceScore && (
                            <div className='absolute -right-1 -top-1 hidden'>
                              {item.relevanceScore}
                            </div>
                          )}
                        </Link>
                      </NavigationTooltip>
                    )
                  })}
                </div>
              )}
            </div>
          ))}

          {/* SuperAdmin Section */}
          {isSuperAdmin && (
            <div className='mt-4 pt-4 border-t border-border'>
              {!collapsed && (
                <div className='px-3 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider'>
                  System
                </div>
              )}

              <NavigationTooltip
                item={{
                  id: 'superadmin',
                  name: 'Super Admin',
                  href: '/superadmin',
                  icon: Shield,
                  description: 'System administration panel',
                  group: 'system',
                  features: ['admin', 'system'],
                  priority: 10,
                }}
                collapsed={collapsed}
              >
                <Link
                  href='/superadmin'
                  className={cn(
                    'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                    pathname.startsWith('/superadmin')
                      ? 'bg-primary/10 text-primary'
                      : 'text-foreground/60 hover:text-foreground hover:bg-muted',
                    collapsed ? 'justify-center' : 'justify-start'
                  )}
                  aria-label={collapsed ? 'Super Admin - System administration panel' : undefined}
                >
                  <Shield className={cn('h-5 w-5', collapsed ? 'mx-0' : 'mr-3')} />
                  {!collapsed && <span>Super Admin</span>}
                </Link>
              </NavigationTooltip>
            </div>
          )}
        </nav>

        {/* AI Recommendations Section */}
        {!collapsed && isContextAware && (
          <div className='border-t border-border p-4' data-testid='ai-recommendations'>
            <div className='flex items-center gap-2 mb-2'>
              <Brain className='h-4 w-4 text-primary' />
              <span className='text-xs font-medium'>AI Insights</span>
            </div>

            {isLoading && (
              <div className='text-xs text-muted-foreground'>Loading recommendations...</div>
            )}

            {errorMessage && <div className='text-xs text-destructive'>{errorMessage}</div>}

            {!isLoading && !errorMessage && providerError && (
              <div className='text-xs text-muted-foreground'>
                AI features temporarily unavailable
              </div>
            )}

            {!isLoading && !errorMessage && !providerError && aiRecommendations.length > 0 && (
              <div className='space-y-1'>
                {aiRecommendations.slice(0, 2).map((recommendation, index) => (
                  <div key={index} className='text-xs text-muted-foreground'>
                    <span className='text-primary'>AI suggests:</span> {recommendation}
                  </div>
                ))}
              </div>
            )}

            {/* AI Provider Selector */}
            <div className='mt-2'>
              <AIProviderSelector
                currentProvider={currentProvider}
                availableProviders={['openai', 'claude', 'gemini', 'deepseek']}
                onProviderChange={switchProvider}
                isLoading={isLoading}
              />
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}
