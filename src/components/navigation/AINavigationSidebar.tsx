'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Role } from '@prisma/client'
import {
  Activity,
  Award,
  BarChart3,
  BookOpen,
  Bot,
  Brain,
  Building,
  Calendar,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Compass,
  ExternalLink,
  Eye,
  FileText,
  Flag,
  FolderOpen,
  Globe,
  Heart,
  LayoutDashboard,
  Library,
  Lightbulb,
  MessageCircle,
  MessageSquare,
  Monitor,
  Search,
  Settings,
  Shield,
  Sparkles,
  Target,
  TrendingUp,
  Trophy,
  User,
  UserCheck,
  Users,
  Zap,
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useCallback, useEffect, useState } from 'react'

// Reusable Quick Actions Component

// Navigation structure matching the original sidebar
interface NavigationSubItem {
  name: string
  href: string
  icon: React.ElementType
}

interface NavigationItem {
  name: string
  href: string
  icon: React.ElementType
  group: string
  subItems?: NavigationSubItem[]
}

const navigationItems: NavigationItem[] = [
  // Personal Zone
  {
    name: 'Focus',
    href: '/focus',
    icon: LayoutDashboard,
    group: 'Personal',
    subItems: [
      { name: 'Today', href: '/focus/today', icon: Calendar },
      { name: 'Goals', href: '/focus/goals', icon: Target },
      { name: 'Skills', href: '/focus/skills', icon: Brain },
      { name: 'Performance', href: '/focus/performance', icon: TrendingUp },
      { name: 'Insights', href: '/focus/insights', icon: BarChart3 },
    ],
  },
  {
    name: 'Grow',
    href: '/grow',
    icon: BookOpen,
    group: 'Personal',
    subItems: [
      { name: 'Learning', href: '/grow/learning', icon: BookOpen },
      { name: 'Mentorship', href: '/grow/mentorship', icon: UserCheck },
      { name: 'Certifications', href: '/grow/certifications', icon: Award },
      { name: 'Career Path', href: '/grow/career-path', icon: TrendingUp },
      { name: 'Resources', href: '/grow/resources', icon: Library },
    ],
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    group: 'Personal',
    subItems: [
      { name: 'Profile', href: '/settings/profile', icon: User },
      { name: 'Platform', href: '/settings/platform', icon: Monitor },
      { name: 'User & Access Control', href: '/settings/user-access-control', icon: Shield },
      { name: 'Content Management', href: '/settings/content-management', icon: FileText },
      { name: 'Advanced', href: '/settings/advanced', icon: Zap },
    ],
  },

  // Team Zone
  {
    name: 'Team',
    href: '/team',
    icon: Users,
    group: 'Team',
    subItems: [
      { name: 'Overview', href: '/team/overview', icon: LayoutDashboard },
      { name: 'Members', href: '/team/members', icon: Users },
      { name: 'Goals', href: '/team/goals', icon: Target },
      { name: 'Development', href: '/team/development', icon: TrendingUp },
      { name: 'Feedback', href: '/team/feedback', icon: MessageSquare },
      { name: 'Analytics', href: '/team/analytics', icon: BarChart3 },
    ],
  },
  {
    name: 'Connect',
    href: '/connect',
    icon: MessageSquare,
    group: 'Team',
    subItems: [
      { name: 'Feedback', href: '/connect/feedback', icon: MessageSquare },
      { name: 'Recognition', href: '/connect/recognition', icon: Award },
      { name: 'Communication', href: '/connect/communication', icon: MessageCircle },
      { name: 'Collaboration', href: '/connect/collaboration', icon: Users },
      { name: 'Network', href: '/connect/network', icon: Globe },
    ],
  },
  {
    name: 'Celebrate',
    href: '/celebrate',
    icon: Trophy,
    group: 'Team',
    subItems: [
      { name: 'Achievements', href: '/celebrate/achievements', icon: Trophy },
      { name: 'Recognition', href: '/celebrate/recognition', icon: Award },
      { name: 'Milestones', href: '/celebrate/milestones', icon: Flag },
      { name: 'Team Wins', href: '/celebrate/team-wins', icon: Users },
      { name: 'Company', href: '/celebrate/company', icon: Building },
    ],
  },

  // Organisation Zone
  {
    name: 'Vision',
    href: '/vision',
    icon: Eye,
    group: 'Organisation',
    subItems: [
      { name: 'Strategy', href: '/vision/strategy', icon: Target },
      { name: 'Planning', href: '/vision/planning', icon: Calendar },
      { name: 'Alignment', href: '/vision/alignment', icon: Compass },
      { name: 'Progress', href: '/vision/progress', icon: TrendingUp },
      { name: 'Intelligence', href: '/vision/intelligence', icon: Brain },
    ],
  },
  {
    name: 'Explore',
    href: '/explore',
    icon: Compass,
    group: 'Organisation',
    subItems: [
      { name: 'Opportunities', href: '/explore/opportunities', icon: Search },
      { name: 'Projects', href: '/explore/projects', icon: FolderOpen },
      { name: 'Networking', href: '/explore/networking', icon: Globe },
      { name: 'Innovation', href: '/explore/innovation', icon: Lightbulb },
      { name: 'External', href: '/explore/external', icon: ExternalLink },
    ],
  },
  {
    name: 'Pulse',
    href: '/pulse',
    icon: Activity,
    group: 'Organisation',
    subItems: [
      { name: 'Dashboard', href: '/pulse/dashboard', icon: LayoutDashboard },
      { name: 'Engagement', href: '/pulse/engagement', icon: Heart },
      { name: 'Performance', href: '/pulse/performance', icon: TrendingUp },
      { name: 'Skills', href: '/pulse/skills', icon: Brain },
      { name: 'Culture', href: '/pulse/culture', icon: Users },
      { name: 'Predictive', href: '/pulse/predictive', icon: Zap },
    ],
  },
]

// Interfaces for behavioral data and AI features
interface BehaviorPattern {
  action: string
  frequency: number
  lastUsed: Date
  context: string
}

interface UserContext {
  recentlyVisited: string[]
  clickFrequency: Record<string, number>
  timeSpent: Record<string, number>
  preferredWorkingHours: number[]
  activeRole: Role
  notifications?: Record<string, number>
}

interface SmartRecommendation {
  id: string
  title: string
  reason: string
  confidence: number
  action: () => void
}

interface PredictedAction {
  id: string
  action: string
  target: string
  confidence: number
  reason: string
}

interface BehaviorEvent {
  action: string
  target: string
  timestamp: Date
  context: Record<string, unknown>
}

interface SystemStatus {
  health: 'healthy' | 'warning' | 'critical'
  uptime: number
  activeUsers: number
}

interface AINavigationSidebarProps {
  userRole: Role
  behaviorData: BehaviorPattern[]
  userContext: UserContext
  recommendations: SmartRecommendation[]
  collapsed: boolean
  onToggle: () => void
  intentPredictions: PredictedAction[]
  onActionPredict: (action: PredictedAction) => void
  onBehaviorTrack: (event: BehaviorEvent) => void
  superAdminAccess?: boolean
  systemHealth?: SystemStatus
  className?: string
}

export function AINavigationSidebar({
  userRole,
  behaviorData,
  userContext,
  recommendations,
  collapsed,
  onToggle,
  intentPredictions,
  onActionPredict,
  onBehaviorTrack,
  superAdminAccess = false,
  systemHealth,
  className,
}: AINavigationSidebarProps) {
  const pathname = usePathname()

  // State for expanded menu items
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  // Auto-expand parent menus when on submenu pages
  useEffect(() => {
    setExpandedItems(prevExpanded => {
      const newExpanded = new Set(prevExpanded) // Preserve manually expanded items

      navigationItems.forEach(item => {
        if (item.subItems) {
          const hasActiveSubItem = item.subItems.some(subItem => pathname.startsWith(subItem.href))
          if (hasActiveSubItem) {
            newExpanded.add(item.name)
          }
        }
      })

      return newExpanded
    })
  }, [pathname])

  const toggleExpanded = useCallback(
    (itemName: string) => {
      const newExpanded = new Set(expandedItems)
      if (newExpanded.has(itemName)) {
        newExpanded.delete(itemName)
      } else {
        newExpanded.add(itemName)
      }
      setExpandedItems(newExpanded)
    },
    [expandedItems]
  )

  const isItemActive = useCallback(
    (item: NavigationItem) => {
      if (pathname === item.href) return true
      if (item.subItems) {
        return item.subItems.some(subItem => pathname.startsWith(subItem.href))
      }
      return pathname.startsWith(item.href)
    },
    [pathname]
  )

  const isSubItemActive = useCallback(
    (subItem: NavigationSubItem) => {
      return pathname.startsWith(subItem.href)
    },
    [pathname]
  )

  // Track navigation behavior
  const handleNavigation = useCallback(
    (href: string, itemName: string) => {
      const event: BehaviorEvent = {
        action: 'navigation_click',
        target: href,
        timestamp: new Date(),
        context: {
          itemName,
          userRole,
          fromPath: pathname,
        },
      }

      try {
        onBehaviorTrack(event)
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Behavior tracking failed:', error)
        }
      }
    },
    [onBehaviorTrack, userRole, pathname]
  )

  return (
    <div
      data-testid='ai-navigation-sidebar'
      className={cn(
        'h-screen flex flex-col bg-card border-r border-border transition-all duration-300',
        collapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* Header */}
      <header className='w-full h-14 flex items-center justify-between px-4 border-b border-border bg-background/80 backdrop-blur-lg'>
        {!collapsed && (
          <Link href='/dashboard' className='flex items-center space-x-2 group'>
            <Bot className='h-7 w-7 text-[var(--primary)] group-hover:text-[var(--primary)]/80 transition-colors' />
            <span className='text-xl font-bold text-foreground group-hover:text-[var(--primary)] transition-colors'>
              Emynent
            </span>
          </Link>
        )}
        {collapsed && (
          <Link href='/dashboard' className='flex items-center justify-center group'>
            <Bot className='h-7 w-7 text-[var(--primary)] group-hover:text-[var(--primary)]/80 transition-colors' />
          </Link>
        )}
        <Button
          variant='ghost'
          size='icon'
          onClick={onToggle}
          className='ml-auto hover:bg-muted/50'
          data-testid='sidebar-collapse-toggle'
        >
          {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </Button>
      </header>

      {/* Navigation Content */}
      <nav className='space-y-1 px-2 py-4 overflow-y-auto' role='navigation'>
        {/* Quick Access Section */}
        {!collapsed && (
          <div className='px-3 py-2'>
            <h2 className='mb-2 px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider'>
              Quick Access
            </h2>
            <div className='space-y-1'>
              {recommendations.slice(0, 2).map(rec => (
                <button
                  key={rec.id}
                  onClick={rec.action}
                  className='w-full text-left px-3 py-2 text-xs rounded-lg bg-muted/50 hover:bg-muted transition-colors'
                  data-testid={`quick-access-${rec.id}`}
                >
                  <div className='flex items-center gap-2'>
                    <Sparkles className='h-3 w-3 text-primary' />
                    <span className='font-medium'>{rec.title}</span>
                  </div>
                  <p className='mt-1 text-muted-foreground line-clamp-1'>{rec.reason}</p>
                  <div className='mt-1 flex items-center gap-1'>
                    <div className='h-1 w-full bg-muted rounded-full'>
                      <div
                        className='h-1 bg-primary rounded-full'
                        style={{ width: `${rec.confidence * 100}%` }}
                      />
                    </div>
                    <span
                      className='text-xs text-muted-foreground'
                      data-testid='notification-badge'
                    >
                      {Math.round(rec.confidence * 100)}%
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Group navigation items by zones */}
        {['Personal', 'Team', 'Organisation'].map(group => {
          const groupItems = navigationItems.filter(item => item.group === group)

          return (
            <div key={group} className='space-y-1'>
              {!collapsed && (
                <div className='px-3 py-2 text-xs font-semibold text-foreground/40 uppercase tracking-wider'>
                  {group}
                </div>
              )}
              {groupItems.map(item => {
                const isActive = isItemActive(item)
                const isExpanded = expandedItems.has(item.name)
                const hasSubItems = item.subItems && item.subItems.length > 0

                return (
                  <div key={item.name}>
                    {/* Main navigation item */}
                    <div
                      className={cn(
                        'flex items-center rounded-md text-sm font-medium transition-colors',
                        isActive
                          ? 'bg-primary/10 text-primary'
                          : 'text-foreground/60 hover:text-foreground hover:bg-muted',
                        collapsed ? 'justify-center' : 'justify-start'
                      )}
                    >
                      <Link
                        href={item.href}
                        data-testid={`nav-${item.name.toLowerCase()}`}
                        className={cn(
                          'flex items-center px-3 py-2 flex-1',
                          collapsed ? 'justify-center' : 'justify-start'
                        )}
                        onClick={() => handleNavigation(item.href, item.name)}
                      >
                        <item.icon className={cn('h-5 w-5', collapsed ? 'mx-0' : 'mr-3')} />
                        {!collapsed && <span>{item.name}</span>}
                      </Link>

                      {/* Expand/collapse button for items with submenus */}
                      {hasSubItems && !collapsed && (
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => toggleExpanded(item.name)}
                          className='h-8 w-8 p-0 mr-1'
                          data-testid={`nav-${item.name.toLowerCase()}-toggle`}
                          role='button'
                          aria-label={`${isExpanded ? 'Collapse' : 'Expand'} ${item.name} submenu`}
                          aria-expanded={isExpanded}
                        >
                          <ChevronDown
                            className={cn(
                              'h-4 w-4 transition-transform',
                              isExpanded ? 'rotate-180' : ''
                            )}
                          />
                        </Button>
                      )}
                    </div>

                    {/* Submenu items */}
                    {hasSubItems && !collapsed && isExpanded && (
                      <div className='ml-6 mt-1 space-y-1'>
                        {item.subItems!.map(subItem => {
                          const isSubActive = isSubItemActive(subItem)
                          return (
                            <Link
                              key={subItem.name}
                              href={subItem.href}
                              data-testid={`nav-${item.name.toLowerCase()}-${subItem.name
                                .toLowerCase()
                                .replace(/[^a-z0-9]+/g, '-')
                                .replace(/^-+|-+$/g, '')}`}
                              className={cn(
                                'flex items-center px-3 py-2 rounded-md text-sm transition-colors',
                                isSubActive
                                  ? 'bg-primary/10 text-primary'
                                  : 'text-foreground/50 hover:text-foreground hover:bg-muted/50'
                              )}
                              onClick={() =>
                                handleNavigation(subItem.href, `${item.name} > ${subItem.name}`)
                              }
                            >
                              <subItem.icon className='h-4 w-4 mr-3' />
                              <span>{subItem.name}</span>
                            </Link>
                          )
                        })}
                      </div>
                    )}
                  </div>
                )
              })}
              {!collapsed && <div className='h-2' />} {/* Spacer between groups */}
            </div>
          )
        })}

        {/* SuperAdmin Link - Only visible to users with SUPERADMIN role */}
        {superAdminAccess && (
          <Link
            href='/superadmin'
            className={cn(
              'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
              pathname.startsWith('/superadmin')
                ? 'bg-primary/10 text-primary'
                : 'text-foreground/60 hover:text-foreground hover:bg-muted',
              collapsed ? 'justify-center' : 'justify-start'
            )}
            onClick={() => handleNavigation('/superadmin', 'SuperAdmin')}
          >
            <Shield className={cn('h-5 w-5', collapsed ? 'mx-0' : 'mr-3')} />
            {!collapsed && <span>SuperAdmin</span>}
          </Link>
        )}

        {/* Intent Predictions */}
        {!collapsed && intentPredictions.length > 0 && (
          <div className='px-3 py-2'>
            <h2 className='mb-2 px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider'>
              Predicted Actions
            </h2>
            <div className='space-y-1'>
              {intentPredictions.slice(0, 2).map(prediction => (
                <button
                  key={prediction.id}
                  onClick={() => onActionPredict(prediction)}
                  className='w-full text-left px-3 py-2 text-xs rounded-lg bg-blue-50 dark:bg-blue-950/20 hover:bg-blue-100 dark:hover:bg-blue-950/30 transition-colors'
                >
                  <div className='flex items-center gap-2'>
                    <TrendingUp className='h-3 w-3 text-blue-600' />
                    <span className='font-medium'>
                      {prediction.target?.split('/').pop() || prediction.intent}
                    </span>
                  </div>
                  <p className='mt-1 text-muted-foreground line-clamp-1'>{prediction.reason}</p>
                  <span className='text-xs text-blue-600'>
                    {Math.round(prediction.confidence * 100)}% confidence
                  </span>
                </button>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Quick Actions Section - Using Reusable Component */}
      {!collapsed && (
        <QuickActions
          orientation='vertical'
          tooltipSide='right'
          onActionTrigger={action => {
            onBehaviorTrack({
              action: `quick_action_${action}`,
              target: 'sidebar',
              timestamp: new Date(),
              context: {
                userRole,
                collapsed,
                sidebarType: 'ai_navigation',
              },
            })
          }}
        />
      )}
    </div>
  )
}
