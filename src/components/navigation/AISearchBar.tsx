'use client'

import { useState, KeyboardEvent } from 'react'
import { Search, Sparkles } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { componentThemes } from '@/lib/design-system/themes'

interface AISearchBarProps {
  onSearch?: (query: string) => void
  placeholder?: string
  className?: string
}

export function AISearchBar({
  onSearch,
  placeholder = 'Search with AI assistance...',
  className,
}: AISearchBarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isFocused, setIsFocused] = useState(false)

  const handleSearch = (query: string) => {
    if (onSearch) {
      onSearch(query)
    }
    // Default search behavior would go here
    if (process.env.NODE_ENV === 'development') console.log('Searching for:', query)
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSearch(searchQuery)
    } else if (e.key === 'Escape') {
      setSearchQuery('')
      setIsFocused(false)
      ;(e.target as HTMLInputElement).blur()
    }
  }

  return (
    <div className={cn('relative w-full', className)} data-testid='ai-search-container'>
      <div className='relative group'>
        <Search
          className={cn(
            'absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-200',
            isFocused ? 'text-foreground/60' : 'text-muted-foreground/60'
          )}
          data-testid='search-icon'
        />

        <Input
          type='text'
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={cn(
            'pl-10 pr-10 h-9 text-sm transition-all duration-200',
            // Follow design system form patterns - using consistent background and border
            'bg-muted/30 border border-border/50 rounded-lg',
            'hover:bg-muted/50 hover:border-border/70',
            // Use design system focus styling - primary border on focus
            'focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary',
            'placeholder:text-muted-foreground/50'
          )}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          aria-label='Search'
          role='textbox'
          data-testid='search-input'
        />

        {/* AI indicator following design system patterns */}
        {(isFocused || searchQuery) && (
          <div className='absolute right-3 top-1/2 transform -translate-y-1/2'>
            <Sparkles
              className={cn(
                'h-4 w-4 transition-all duration-300',
                // Use design system primary color for AI indicator
                'text-primary',
                isFocused ? 'animate-pulse scale-110' : 'animate-pulse'
              )}
              data-testid='ai-powered-indicator'
            />
          </div>
        )}
      </div>
    </div>
  )
}
