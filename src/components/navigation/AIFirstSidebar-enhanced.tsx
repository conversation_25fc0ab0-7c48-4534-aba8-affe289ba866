'use client'

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, Too<PERSON>ipContent, <PERSON>ltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { trackBehavioralEvent } from '@/lib/analytics/behavioral-tracking'
import { useNavigationContext } from '@/lib/context/NavigationContextProvider'
import { hasFeatureSync } from '@/lib/services/feature-flag-service'
import { cn } from '@/lib/utils'
import { Role } from '@prisma/client'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useCallback, useState } from 'react'

// Enhanced icon set
import {
  Bot,
  ChevronLeft,
  ChevronRight,
  FoldVertical,
  HelpCircle,
  LayoutDashboard,
  RefreshCw,
  Sparkles,
  TrendingUp,
  UnfoldVertical,
} from 'lucide-react'

// Navigation items and zones

// Support Modal Component
import { SupportModal } from '@/components/navigation/SupportModal'

// AI-Native interfaces
interface SmartRecommendation {
  id: string
  title: string
  reason: string
  confidence: number
  action: () => void
}

export function AIFirstSidebarEnhanced() {
  const pathname = usePathname()
  const { data: session } = useSession()
  const {
    toggleSidebarMinimize,
    isSidebarFullyCollapsed,
    isSidebarMinimized,
    getSidebarState,
    getSidebarWidth,
    userContext,
    smartRecommendations,
    intentPredictions,
    refreshRecommendations,
  } = useNavigationContext()

  const [zoneStates, setZoneStates] = useState<Record<string, boolean>>({})
  const [itemStates, setItemStates] = useState<Record<string, boolean>>({})
  const [allDropdownsCollapsed, setAllDropdownsCollapsed] = useState(false)
  const [supportModalOpen, setSupportModalOpen] = useState(false)

  const userRole = session?.user?.role
  const companyId = session?.user?.companyId
  const isSuperAdmin = userRole === Role.SUPERADMIN
  const isContextAware = companyId ? hasFeatureSync(companyId, 'contextAwareness') : false

  const sidebarState = getSidebarState()
  const isExpanded = sidebarState === 'expanded'
  const isMinimized = sidebarState === 'minimized'
  const isHidden = sidebarState === 'hidden'

  // Enhanced behavioral tracking
  const trackBehaviorEvent = useCallback(
    (action: string, target: string, context: Record<string, unknown>) => {
      trackBehavioralEvent(action, {
        target,
        timestamp: Date.now(),
        context,
        userRole: userRole,
        isContextAware: isContextAware,
      })
    },
    [userRole, isContextAware]
  )

  // Enhanced handlers
  const handleSidebarToggle = useCallback(() => {
    toggleSidebarMinimize()
    trackBehaviorEvent('sidebar_minimize_toggle', 'sidebar', {
      action: isMinimized ? 'expand_sidebar' : 'minimize_sidebar',
      currentState: sidebarState,
    })
  }, [toggleSidebarMinimize, isMinimized, sidebarState, trackBehaviorEvent])

  const handleRefreshAI = useCallback(() => {
    if (refreshRecommendations) {
      refreshRecommendations()
    }
    trackBehaviorEvent('refresh_ai_suggestions', 'ai_system', { trigger: 'manual_refresh' })
  }, [refreshRecommendations, trackBehaviorEvent])

  const handleRecommendationClick = useCallback(
    (recommendation: SmartRecommendation) => {
      recommendation.action()
      trackBehaviorEvent('recommendation_click', recommendation.id, {
        title: recommendation.title,
        confidence: recommendation.confidence,
      })
    },
    [trackBehaviorEvent]
  )

  // Don't render if hidden
  if (isHidden) {
    return null
  }

  return (
    <TooltipProvider>
      <aside
        className={cn(
          'relative flex flex-col bg-gradient-to-b from-white to-slate-50 dark:from-gray-900 dark:to-gray-950 border-r border-gray-200 dark:border-gray-800',
          getSidebarWidth(),
          'transition-all duration-300 ease-in-out',
          isMinimized && 'w-16'
        )}
      >
        {/* Header with Logo and Collapse Button */}
        <div className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700'>
          {!isMinimized && (
            <div className='flex items-center gap-2'>
              <div className='w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center'>
                <span className='text-white font-bold text-sm'>E</span>
              </div>
              <span className='font-semibold text-gray-900 dark:text-white'>Emynent</span>
            </div>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                onClick={handleSidebarToggle}
                className='h-8 w-8 p-0'
              >
                {isMinimized ? (
                  <ChevronRight className='h-4 w-4' />
                ) : (
                  <ChevronLeft className='h-4 w-4' />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side='right'>
              {isMinimized ? 'Expand sidebar' : 'Minimize sidebar'}
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Quick Access Section (AI Recommendations) */}
        {!isMinimized &&
          isContextAware &&
          smartRecommendations &&
          smartRecommendations.length > 0 && (
            <div className='p-4 border-b border-gray-200 dark:border-gray-700'>
              <div className='flex items-center gap-2 mb-3'>
                <Sparkles className='h-4 w-4 text-blue-600' />
                <span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                  Quick Access
                </span>
              </div>
              <div className='space-y-2'>
                {smartRecommendations.slice(0, 2).map(rec => (
                  <button
                    key={rec.id}
                    onClick={() => handleRecommendationClick(rec)}
                    className='w-full text-left p-2 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors'
                  >
                    <div className='flex items-center gap-2'>
                      <TrendingUp className='h-3 w-3 text-blue-600' />
                      <span className='text-sm font-medium text-blue-700 dark:text-blue-300'>
                        {rec.title}
                      </span>
                      <span className='text-xs text-blue-500 dark:text-blue-400 ml-auto'>
                        {Math.round(rec.confidence * 100)}%
                      </span>
                    </div>
                    <p className='text-xs text-blue-600 dark:text-blue-400 mt-1'>{rec.reason}</p>
                  </button>
                ))}
              </div>
            </div>
          )}

        {/* Main Navigation Content */}
        <div className='flex-1 overflow-y-auto px-4 py-4 space-y-6'>
          {/* Navigation zones rendering would go here - keeping existing logic */}
          <div className='text-center text-gray-500 py-8'>
            <Bot className='h-12 w-12 mx-auto mb-2 opacity-50' />
            <p className='text-sm'>Navigation content preserved from original</p>
            <p className='text-xs text-gray-400 mt-1'>All zones and items will render here</p>
          </div>
        </div>

        {/* Enhanced Quick Actions Section */}
        <div className='border-t border-gray-200 dark:border-gray-700 p-4'>
          {!isMinimized && (
            <div className='flex items-center gap-2 mb-3'>
              <Bot className='h-4 w-4 text-gray-600 dark:text-gray-400' />
              <span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                Quick Actions
              </span>
            </div>
          )}

          <div className={cn('flex gap-2', isMinimized ? 'flex-col' : 'flex-row')}>
            {/* Support Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => setSupportModalOpen(true)}
                  className='h-8 w-8 p-0'
                >
                  <HelpCircle className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent side={isMinimized ? 'right' : 'top'}>Support</TooltipContent>
            </Tooltip>

            {/* Collapse All Dropdowns */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => setAllDropdownsCollapsed(!allDropdownsCollapsed)}
                  className='h-8 w-8 p-0'
                >
                  {allDropdownsCollapsed ? (
                    <UnfoldVertical className='h-4 w-4' />
                  ) : (
                    <FoldVertical className='h-4 w-4' />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side={isMinimized ? 'right' : 'top'}>
                {allDropdownsCollapsed ? 'Expand All' : 'Collapse All'}
              </TooltipContent>
            </Tooltip>

            {/* Refresh AI Suggestions */}
            {isContextAware && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={handleRefreshAI}
                    className='h-8 w-8 p-0'
                  >
                    <RefreshCw className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side={isMinimized ? 'right' : 'top'}>Refresh AI</TooltipContent>
              </Tooltip>
            )}

            {/* Dashboard Quick Access */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant='ghost' size='sm' asChild className='h-8 w-8 p-0'>
                  <Link href='/dashboard'>
                    <LayoutDashboard className='h-4 w-4' />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent side={isMinimized ? 'right' : 'top'}>Dashboard</TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Support Modal */}
        <SupportModal open={supportModalOpen} onClose={() => setSupportModalOpen(false)} />
      </aside>
    </TooltipProvider>
  )
}
