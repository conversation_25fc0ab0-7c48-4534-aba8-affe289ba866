'use client'

import { usePathname } from 'next/navigation'
import SettingsSidebar from '../settings/Sidebar'
import SuperAdminSidebar from '../superadmin/SuperAdminSidebar'
import { AIFirstSidebar } from './AIFirstSidebar'

export function SmartSidebar() {
  const pathname = usePathname()

  // Determine which sidebar to show based on current path
  if (pathname.startsWith('/settings')) {
    return <SettingsSidebar />
  }

  if (pathname.startsWith('/superadmin')) {
    return <SuperAdminSidebar />
  }

  // Default to main navigation sidebar
  return <AIFirstSidebar />
}
