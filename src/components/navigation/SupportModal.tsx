'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import {
  Bell,
  BookOpen,
  Building,
  ExternalLink,
  FileText,
  HelpCircle,
  Mail,
  MessageCircle,
  Palette,
  Phone,
  Settings,
  Shield,
  User,
  Zap,
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'

interface SupportModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface SupportItem {
  id: string
  title: string
  description: string
  href: string
  icon: React.ElementType
  external?: boolean
  category: 'settings' | 'help' | 'contact' | 'resources'
}

const supportItems: SupportItem[] = [
  // Settings Category - Updated to match actual routes
  {
    id: 'account-settings',
    title: 'Account Settings',
    description: 'Manage your profile and account preferences',
    href: '/settings/profile',
    icon: User,
    category: 'settings',
  },
  {
    id: 'company-settings',
    title: 'Company Settings',
    description: 'Configure your organization settings',
    href: '/settings/team',
    icon: Building,
    category: 'settings',
  },
  {
    id: 'platform-settings',
    title: 'Platform Settings',
    description: 'Customize your platform experience',
    href: '/settings/platform/dashboard',
    icon: Settings,
    category: 'settings',
  },
  {
    id: 'appearance-settings',
    title: 'Appearance',
    description: 'Themes, colors, and display preferences',
    href: '/settings/platform/appearance',
    icon: Palette,
    category: 'settings',
  },
  {
    id: 'notification-settings',
    title: 'Notifications',
    description: 'Configure your notification preferences',
    href: '/settings/platform/notifications',
    icon: Bell,
    category: 'settings',
  },
  {
    id: 'privacy-settings',
    title: 'Privacy & Security',
    description: 'Manage your privacy and security settings',
    href: '/settings/access-control',
    icon: Shield,
    category: 'settings',
  },

  // Help & Documentation Category
  {
    id: 'user-guide',
    title: 'User Guide',
    description: 'Complete guide to using Emynent',
    href: '/help/guide',
    icon: BookOpen,
    category: 'help',
  },
  {
    id: 'getting-started',
    title: 'Getting Started',
    description: 'Quick start guide for new users',
    href: '/help/getting-started',
    icon: Zap,
    category: 'help',
  },
  {
    id: 'faq',
    title: 'FAQ',
    description: 'Frequently asked questions and answers',
    href: '/help/faq',
    icon: HelpCircle,
    category: 'help',
  },
  {
    id: 'api-docs',
    title: 'API Documentation',
    description: 'Developer resources and API reference',
    href: '/docs/api',
    icon: FileText,
    category: 'resources',
  },

  // Contact & Support Category
  {
    id: 'contact-support',
    title: 'Contact Support',
    description: 'Get help from our support team',
    href: 'mailto:<EMAIL>',
    icon: Mail,
    category: 'contact',
    external: true,
  },
  {
    id: 'live-chat',
    title: 'Live Chat',
    description: 'Chat with our support team',
    href: '/support/chat',
    icon: MessageCircle,
    category: 'contact',
  },
  {
    id: 'phone-support',
    title: 'Phone Support',
    description: 'Call us for immediate assistance',
    href: 'tel:******-EMYNENT',
    icon: Phone,
    category: 'contact',
    external: true,
  },

  // Resources Category
  {
    id: 'changelog',
    title: "What's New",
    description: 'Latest updates and feature releases',
    href: '/changelog',
    icon: FileText,
    category: 'resources',
  },
]

const categoryLabels = {
  settings: 'Settings & Preferences',
  help: 'Help & Documentation',
  contact: 'Contact & Support',
  resources: 'Resources',
}

const categoryDescriptions = {
  settings: 'Customize your experience and manage your account',
  help: 'Learn how to get the most out of Emynent',
  contact: 'Get personalized help from our support team',
  resources: 'Stay updated with the latest features and changes',
}

export function SupportModal({ open, onOpenChange }: SupportModalProps) {
  const pathname = usePathname()

  // Group items by category
  const itemsByCategory = supportItems.reduce(
    (acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = []
      }
      acc[item.category].push(item)
      return acc
    },
    {} as Record<string, SupportItem[]>
  )

  const handleItemClick = (item: SupportItem) => {
    // Close modal when navigating
    onOpenChange(false)
  }

  const renderSupportItem = (item: SupportItem) => {
    const ItemIcon = item.icon
    const isActive = pathname === item.href
    const isExternal =
      item.external || item.href.startsWith('mailto:') || item.href.startsWith('tel:')

    const itemContent = (
      <div
        className={cn(
          'group flex items-start space-x-3 rounded-xl p-4 transition-all duration-200',
          'border border-border bg-card hover:bg-card/80 dark:hover:bg-card',
          'hover:border-border/80 hover:shadow-lg hover:shadow-primary/5',
          'cursor-pointer',
          isActive && 'border-primary/50 bg-primary/5 ring-1 ring-primary/20'
        )}
        onClick={() => handleItemClick(item)}
      >
        <div
          className={cn(
            'flex h-10 w-10 items-center justify-center rounded-lg transition-colors',
            'bg-muted text-white dark:text-white group-hover:bg-primary/10 group-hover:text-primary',
            isActive && 'bg-primary/15 text-primary'
          )}
        >
          <ItemIcon className='h-5 w-5' />
        </div>
        <div className='flex-1 min-w-0'>
          <div className='flex items-center space-x-2'>
            <h3
              className={cn(
                'font-medium text-white dark:text-white group-hover:text-white',
                isActive && 'text-primary'
              )}
            >
              {item.title}
            </h3>
            {isExternal && (
              <ExternalLink className='h-3 w-3 text-gray-200 dark:text-gray-200 opacity-0 group-hover:opacity-100 transition-opacity' />
            )}
          </div>
          <p className='text-sm text-gray-200 dark:text-gray-200 mt-1 line-clamp-2'>
            {item.description}
          </p>
        </div>
      </div>
    )

    if (isExternal) {
      return (
        <a
          key={item.id}
          href={item.href}
          target={item.href.startsWith('http') ? '_blank' : undefined}
          rel={item.href.startsWith('http') ? 'noopener noreferrer' : undefined}
          className='block'
        >
          {itemContent}
        </a>
      )
    }

    return (
      <Link key={item.id} href={item.href} className='block'>
        {itemContent}
      </Link>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-4xl max-h-[85vh] flex flex-col bg-background border-border shadow-2xl'>
        <DialogHeader className='text-left space-y-3 pb-6 border-b border-border/50 flex-shrink-0'>
          <div className='flex items-center space-x-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary'>
              <HelpCircle className='h-6 w-6' />
            </div>
            <div>
              <DialogTitle className='text-xl font-semibold text-white dark:text-white'>
                Support & Settings
              </DialogTitle>
              <DialogDescription className='text-gray-200 dark:text-gray-200 mt-1'>
                Access settings, documentation, and get help with Emynent
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='flex-1 overflow-y-auto pr-2 -mr-2 space-y-8 min-h-0'>
          {(Object.keys(itemsByCategory) as Array<keyof typeof categoryLabels>).map(category => (
            <div key={category} className='space-y-4'>
              <div className='space-y-2'>
                <h2 className='text-lg font-semibold text-white dark:text-white'>
                  {categoryLabels[category]}
                </h2>
                <p className='text-sm text-gray-200 dark:text-gray-200'>
                  {categoryDescriptions[category]}
                </p>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                {itemsByCategory[category]?.map(renderSupportItem)}
              </div>

              {category !== 'resources' && <Separator className='mt-8 bg-border/50' />}
            </div>
          ))}
        </div>

        <div className='flex items-center justify-between pt-6 border-t border-border/50 mt-6 flex-shrink-0'>
          <p className='text-xs text-gray-200 dark:text-gray-200'>
            Need immediate assistance? Contact us at{' '}
            <a href='mailto:<EMAIL>' className='text-primary hover:underline'>
              <EMAIL>
            </a>
          </p>
          <Button
            variant='outline'
            size='sm'
            onClick={() => onOpenChange(false)}
            className='bg-background hover:bg-muted/50 border-border text-white dark:text-white'
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
