'use client'

import { LogOut, User, <PERSON><PERSON><PERSON>, Shield } from 'lucide-react'
import Link from 'next/link'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/core/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/core/avatar'
import { Button } from '@/components/core/button'
import { useAuthContext } from '@/components/auth/AuthContextProvider'
import { useTheme } from 'next-themes'
import { useRouter } from 'next/navigation'

export function UserProfileSection() {
  const { user, signOut } = useAuthContext()
  const { resolvedTheme } = useTheme()
  const router = useRouter()

  const handleLogout = async () => {
    const result = await signOut()
    if (result.success) {
      router.push('/signin')
    }
  }

  if (!user) {
    return (
      <div className='h-9 w-9 rounded-full bg-muted animate-pulse' aria-label='Loading profile' />
    )
  }

  // Use theme-aware classes instead of hardcoded colors

  const userInitials = user.name
    ? user.name
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
        .slice(0, 2)
    : user.email
      ? user.email[0].toUpperCase()
      : 'U'

  const displayName = user.name || user.email || 'User'
  const userRole = user.role || 'Employee'

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='relative h-9 w-9 rounded-full p-0 focus:ring-1 focus:ring-primary/30 focus:ring-offset-0'
          aria-label='User menu'
        >
          <Avatar className='h-8 w-8 ring-1 ring-primary'>
            <AvatarImage src={user.image || undefined} alt={displayName} />
            <AvatarFallback className='bg-primary/10 text-primary text-xs font-medium'>
              {userInitials}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className='w-64 p-2 bg-background border border-border/50 shadow-lg'
        align='end'
        forceMount
      >
        {/* User Info Header */}
        <DropdownMenuLabel className='p-3 pb-2'>
          <div className='flex flex-col space-y-1'>
            <p className='text-sm font-medium leading-none text-foreground'>{displayName}</p>
            <p className='text-xs text-muted-foreground'>{user.email}</p>
            <div className='flex items-center gap-1 mt-1'>
              <div className='inline-flex items-center rounded-full border border-primary/20 bg-primary/10 px-2 py-0.5'>
                <span className='text-xs font-medium text-primary'>{userRole}</span>
              </div>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator className='bg-border/50' />

        {/* Menu Items with Theme Colors */}
        <DropdownMenuItem asChild>
          <Link
            href='/dashboard'
            className='flex items-center gap-2 px-3 py-2 text-sm cursor-pointer rounded-md transition-colors hover:bg-primary/10 hover:text-primary'
          >
            <User className='h-4 w-4' />
            Profile
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link
            href='/settings'
            className='flex items-center gap-2 px-3 py-2 text-sm cursor-pointer rounded-md transition-colors hover:bg-primary/10 hover:text-primary'
          >
            <Settings className='h-4 w-4' />
            Settings
          </Link>
        </DropdownMenuItem>

        {/* Superadmin Panel - Only show for Superadmin users */}
        {user.role === 'SUPERADMIN' && (
          <DropdownMenuItem asChild>
            <Link
              href='/superadmin'
              className='flex items-center gap-2 px-3 py-2 text-sm cursor-pointer rounded-md transition-colors hover:bg-primary/10 hover:text-primary'
            >
              <Shield className='h-4 w-4' />
              SuperAdmin Panel
            </Link>
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator className='bg-border/50' />

        {/* Sign Out */}
        <DropdownMenuItem
          className='flex items-center gap-2 px-3 py-2 text-sm cursor-pointer rounded-md transition-colors hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive'
          onClick={handleLogout}
        >
          <LogOut className='h-4 w-4' />
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
