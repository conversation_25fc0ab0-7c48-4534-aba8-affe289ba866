'use client'

<<<<<<< HEAD
import React, { useState, useEffect, useCallback } from 'react'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/core/card'
=======
>>>>>>> feature/sidebar-navigation
import { Button } from '@/components/core/button'
import { Label } from '@/components/core/label'
import { RadioGroup, RadioGroupItem } from '@/components/core/radio-group'
import { Slider } from '@/components/core/slider'
import { Switch } from '@/components/core/switch'
<<<<<<< HEAD
import { Badge } from '@/components/core/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core/tabs'
=======
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
>>>>>>> feature/sidebar-navigation
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
<<<<<<< HEAD
} from '@/components/core/select'
import {
  Monitor,
  Moon,
  Sun,
  Palette,
  Settings,
  RotateCw,
  Loader2,
  Check,
  Paintbrush,
  Bell,
  Mail,
  MessageSquare,
  Shield,
  Calendar,
  Clock,
  Accessibility,
  Globe,
  Volume2,
  Eye,
  Keyboard,
} from 'lucide-react'
import { useCustomTheme, ColorScheme } from '@/app/providers/theme-provider'
import { useSettings } from '@/lib/hooks/useSettings'
import { useThemeSync } from '@/hooks/useThemeSync'
import { transitions } from '@/lib/design-system'

// Predefined theme options with production-ready configuration
const PREDEFINED_THEMES = [
  {
    id: 'emynent-light',
    name: 'Emynent Default',
    description: 'Clean and professional light theme',
    primary: '#8957e5',
    secondary: '#6e40c9',
    accent: '#b388ff',
    category: 'light',
    isDefault: true,
  },
  {
    id: 'slate',
    name: 'Slate',
    description: 'Modern gray tones',
    primary: '#334155',
    secondary: '#64748B',
    accent: '#A5B4FC',
    category: 'light',
  },
  {
    id: 'mint',
    name: 'Mint',
    description: 'Fresh green accents',
    primary: '#2da44e',
    secondary: '#3fb950',
    accent: '#63d188',
    category: 'light',
  },
  {
    id: 'emynent-dark',
    name: 'Emynent Dark',
    description: 'Professional dark theme',
    primary: '#9c6dd9',
    secondary: '#b388ff',
    accent: '#d4a7ff',
    category: 'dark',
    isDefault: true,
  },
  {
    id: 'nightowl',
    name: 'Night Owl',
    description: 'Deep blue night theme',
    primary: '#82AAFF',
    secondary: '#C792EA',
    accent: '#FFCB6B',
    category: 'dark',
  },
  {
    id: 'slate-dark',
    name: 'Slate Dark',
    description: 'Elegant dark gray',
    primary: '#6C757D',
    secondary: '#5A6268',
    accent: '#ADB5BD',
    category: 'dark',
  },
] as const

// Notification types configuration
const NOTIFICATION_TYPES = [
  {
    id: 'system',
    title: 'System Notifications',
    description: 'Important alerts about your account, security, and system updates',
    icon: Shield,
  },
  {
    id: 'messages',
    title: 'Message Notifications',
    description: 'Notifications for direct messages and mentions',
    icon: MessageSquare,
  },
  {
    id: 'email',
    title: 'Email Notifications',
    description: 'Get email notifications for important updates',
    icon: Mail,
  },
  {
    id: 'events',
    title: 'Calendar Events',
    description: 'Reminders for upcoming meetings and events',
    icon: Calendar,
  },
] as const

export function PlatformSettings() {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const { colorScheme, setColorScheme } = useCustomTheme()
  const { settings, updateAppearance, isLoading } = useSettings()
  const { saveThemePreferences } = useThemeSync()
=======
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useThemeSync } from '@/hooks/useThemeSync'
import { useSettings } from '@/lib/hooks/useSettings'
import {
  Bell,
  Eye,
  Globe,
  Loader2,
  Mail,
  Monitor,
  Moon,
  Palette,
  RotateCw,
  Smartphone,
  Sparkles,
  Sun,
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

// AI-enhanced theme recommendations
const AI_THEME_RECOMMENDATIONS = [
  {
    id: 'emynent-dark',
    name: 'Emynent Default (Dark)',
    mode: 'dark',
    colorScheme: 'emynent-dark',
    primary: '#8957e5',
    secondary: '#6e40c9',
    accent: '#b388ff',
    aiScore: 95,
    reason: 'Optimized for productivity and reduced eye strain',
  },
  {
    id: 'emynent-light',
    name: 'Emynent Default (Light)',
    mode: 'light',
    colorScheme: 'emynent-light',
    primary: '#8957e5',
    secondary: '#6e40c9',
    accent: '#b388ff',
    aiScore: 90,
    reason: 'Perfect for daytime work and collaboration',
  },
  {
    id: 'twilight',
    name: 'Twilight',
    mode: 'dark',
    colorScheme: 'twilight',
    primary: '#FFD700',
    secondary: '#FFA500',
    accent: '#FFE55C',
    aiScore: 85,
    reason: 'Warm colors reduce fatigue during long sessions',
  },
]

// Notification settings
const NOTIFICATION_TYPES = [
  { id: 'email', label: 'Email Notifications', icon: Mail },
  { id: 'inApp', label: 'In-App Notifications', icon: Bell },
  { id: 'mobile', label: 'Mobile Push', icon: Smartphone },
]

// Accessibility settings
const ACCESSIBILITY_OPTIONS = [
  { id: 'screenReader', label: 'Screen Reader Support' },
  { id: 'highContrast', label: 'High Contrast Mode' },
  { id: 'reducedMotion', label: 'Reduce Motion' },
  { id: 'largeText', label: 'Large Text' },
]

// Language options
const LANGUAGES = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Spanish', flag: '🇪🇸' },
  { code: 'fr', name: 'French', flag: '🇫🇷' },
  { code: 'de', name: 'German', flag: '🇩🇪' },
  { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
  { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
]

export function PlatformSettings() {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const { settings, updateAppearance, isLoading } = useSettings()
  const { saveThemePreferences, loadUserThemePreferences } = useThemeSync()
>>>>>>> feature/sidebar-navigation

  // Theme & Appearance State
  const [currentTheme, setCurrentTheme] = useState<string>('system')
  const [currentColorScheme, setCurrentColorScheme] = useState('emynent-light')
  const [fontSize, setFontSize] = useState(16)
  const [compactMode, setCompactMode] = useState(false)
  const [animationSpeed, setAnimationSpeed] = useState('normal')
<<<<<<< HEAD
  const [selectedPreset, setSelectedPreset] = useState<string>('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [activeTab, setActiveTab] = useState('appearance')

  // Notification settings state
  const [notificationSettings, setNotificationSettings] = useState({
    system: true,
    messages: true,
    email: true,
    events: true,
    push: true,
    sound: true,
    doNotDisturb: false,
    emailDigest: 'daily',
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '07:00',
    },
  })

  // Accessibility settings state
  const [accessibilitySettings, setAccessibilitySettings] = useState({
    highContrast: false,
    reducedMotion: false,
    screenReader: false,
    keyboardNavigation: true,
    textToSpeech: false,
    largeText: false,
  })

  // Track initialization to prevent unnecessary syncing
=======

  // Notification State
  const [notifications, setNotifications] = useState({
    email: true,
    inApp: true,
    mobile: false,
  })

  // Accessibility State
  const [accessibility, setAccessibility] = useState({
    screenReader: false,
    highContrast: false,
    reducedMotion: false,
    largeText: false,
  })

  // Language State
  const [language, setLanguage] = useState('en')

  // AI Recommendations State
  const [aiRecommendations, setAiRecommendations] = useState<any[]>([])
  const [loadingRecommendations, setLoadingRecommendations] = useState(false)

  const [isUpdating, setIsUpdating] = useState(false)
>>>>>>> feature/sidebar-navigation
  const [isInitialized, setIsInitialized] = useState(false)

  // Default settings
  const defaultAppearance = {
    theme: 'system' as const,
    fontSize: 16,
    compactMode: false,
    primaryColor: '#8957e5',
    secondaryColor: '#6e40c9',
    accentColor: '#b388ff',
    borderRadius: 'medium' as const,
    animationSpeed: 'normal' as const,
  }

  const appearance = settings?.appearance || defaultAppearance

<<<<<<< HEAD
  // Initialize component state from settings and theme
  useEffect(() => {
    if (!isInitialized && (appearance || theme)) {
      // Sync with settings
=======
  // Load AI theme recommendations
  const loadAIRecommendations = useCallback(async () => {
    setLoadingRecommendations(true)
    try {
      const response = await fetch('/api/theme/recommendations', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setAiRecommendations(result.data.recommendations || AI_THEME_RECOMMENDATIONS)
        }
      }
    } catch {
      if (process.env.NODE_ENV === 'development')
        if (process.env.NODE_ENV === 'development')
          console.warn('Failed to load AI recommendations:', error)
      setAiRecommendations(AI_THEME_RECOMMENDATIONS)
    } finally {
      setLoadingRecommendations(false)
    }
  }, [])

  // Initialize settings
  useEffect(() => {
    if (!isInitialized && (appearance || theme)) {
      // Sync appearance settings
>>>>>>> feature/sidebar-navigation
      if (appearance) {
        setFontSize(appearance.fontSize || 16)
        setCompactMode(appearance.compactMode || false)
        setAnimationSpeed(appearance.animationSpeed || 'normal')
      }

<<<<<<< HEAD
      // Theme sync - prioritize next-themes for consistency
      if (theme) {
        setCurrentTheme(theme)
      } else {
        setCurrentTheme('system')
      }

      // Set selected preset based on current color scheme
      if (colorScheme) {
        const matchingPreset = PREDEFINED_THEMES.find(p => p.id === colorScheme)
        if (matchingPreset) {
          setSelectedPreset(matchingPreset.id)
        }
      }

      setIsInitialized(true)
    }
  }, [appearance, theme, colorScheme, isInitialized])

  // Theme mode change handler
  const handleThemeChange = useCallback(
    async (value: string) => {
      if (value !== currentTheme) {
        try {
          setIsUpdating(true)
          setCurrentTheme(value)
          setTheme(value)

          // Persist to database - only save the theme mode, keep existing color scheme
          await saveThemePreferences(value as any, colorScheme as any)

          // Track user behavior for future AI learning (placeholder)
          await trackBehavior('theme_change', { from: currentTheme, to: value })

          toast.success(`Theme mode set to ${value}`)
        } catch (error) {
          toast.error('Failed to save theme preferences')
        } finally {
          setIsUpdating(false)
        }
      }
    },
    [currentTheme, colorScheme, setTheme, saveThemePreferences]
  )

  // Preset theme change handler
  const handlePresetChange = useCallback(
    async (themeId: string) => {
      const preset = PREDEFINED_THEMES.find(t => t.id === themeId)
      if (!preset) return

=======
      // Sync theme settings
      if (theme) {
        setCurrentTheme(theme)
      }

      // Load user preferences and AI recommendations
      loadUserThemePreferences()
      loadAIRecommendations()

      setIsInitialized(true)
    }
  }, [appearance, theme, isInitialized, loadUserThemePreferences, loadAIRecommendations])

  // Theme mode change handler with AI integration
  const handleThemeModeChange = useCallback(
    async (value: string) => {
      const previousTheme = currentTheme

      try {
        setIsUpdating(true)
        setCurrentTheme(value)
        setTheme(value)

        // Determine color scheme based on mode
        let newColorScheme = currentColorScheme
        if (value === 'dark' || (value === 'system' && resolvedTheme === 'dark')) {
          newColorScheme = currentColorScheme.includes('light')
            ? currentColorScheme.replace('light', 'dark')
            : 'emynent-dark'
        } else if (value === 'light' || (value === 'system' && resolvedTheme === 'light')) {
          newColorScheme = currentColorScheme.includes('dark')
            ? currentColorScheme.replace('dark', 'light')
            : 'emynent-light'
        }

        setCurrentColorScheme(newColorScheme)

        // Save via AI theme system
        await saveThemePreferences(value as any, newColorScheme as any)

        // Update appearance in database
        await updateAppearance({
          ...appearance,
          theme: value as 'light' | 'dark' | 'system',
        })

        toast.success(`Theme mode set to ${value}`)
      } catch {
        setCurrentTheme(previousTheme)
        toast.error('Failed to update theme mode')
      } finally {
        setIsUpdating(false)
      }
    },
    [
      appearance,
      updateAppearance,
      setTheme,
      currentTheme,
      currentColorScheme,
      saveThemePreferences,
      resolvedTheme,
    ]
  )

  // AI theme recommendation handler
  const handleAIThemeApply = useCallback(
    async (recommendation: any) => {
>>>>>>> feature/sidebar-navigation
      try {
        setIsUpdating(true)
        setSelectedPreset(themeId)
        setColorScheme(themeId as ColorScheme)

<<<<<<< HEAD
        // Persist to database using the correct theme mode and color scheme
        await saveThemePreferences(currentTheme, themeId)

        // Track user behavior for future AI learning (placeholder)
        await trackBehavior('color_scheme_change', { scheme: themeId })

        toast.success(`Applied ${preset.name} theme`)
      } catch (error) {
        console.error('Failed to apply theme:', error)
        toast.error('Failed to apply theme')
=======
        // Apply theme mode and color scheme
        setCurrentTheme(recommendation.mode)
        setCurrentColorScheme(recommendation.colorScheme)
        setTheme(recommendation.mode)

        // Apply CSS variables for immediate feedback
        document.documentElement.style.setProperty('--primary', recommendation.primary)
        document.documentElement.style.setProperty('--secondary', recommendation.secondary)
        document.documentElement.style.setProperty('--accent', recommendation.accent)

        // Save via AI theme system
        await saveThemePreferences(recommendation.mode, recommendation.colorScheme)

        // Update appearance
        await updateAppearance({
          ...appearance,
          theme: recommendation.mode,
          primaryColor: recommendation.primary,
          secondaryColor: recommendation.secondary,
          accentColor: recommendation.accent,
        })

        toast.success(`AI theme "${recommendation.name}" applied successfully!`)
      } catch {
        toast.error('Failed to apply AI theme recommendation')
>>>>>>> feature/sidebar-navigation
      } finally {
        setIsUpdating(false)
      }
    },
<<<<<<< HEAD
    [currentTheme, setColorScheme, saveThemePreferences]
=======
    [appearance, updateAppearance, setTheme, saveThemePreferences]
  )

  // Notification settings handler
  const handleNotificationChange = useCallback(
    async (type: string, enabled: boolean) => {
      try {
        const newNotifications = { ...notifications, [type]: enabled }
        setNotifications(newNotifications)

        // Save to settings
        await updateAppearance({
          ...appearance,
          notifications: newNotifications,
        })

        toast.success(`${type} notifications ${enabled ? 'enabled' : 'disabled'}`)
      } catch {
        toast.error('Failed to update notification settings')
      }
    },
    [notifications, appearance, updateAppearance]
  )

  // Accessibility settings handler
  const handleAccessibilityChange = useCallback(
    async (setting: string, enabled: boolean) => {
      try {
        const newAccessibility = { ...accessibility, [setting]: enabled }
        setAccessibility(newAccessibility)

        // Apply accessibility changes to DOM
        if (setting === 'highContrast') {
          document.documentElement.classList.toggle('high-contrast', enabled)
        }
        if (setting === 'reducedMotion') {
          document.documentElement.classList.toggle('reduce-motion', enabled)
        }
        if (setting === 'largeText') {
          document.documentElement.style.fontSize = enabled ? '18px' : '16px'
        }

        // Save to settings
        await updateAppearance({
          ...appearance,
          accessibility: newAccessibility,
        })

        toast.success(`${setting} ${enabled ? 'enabled' : 'disabled'}`)
      } catch {
        toast.error('Failed to update accessibility settings')
      }
    },
    [accessibility, appearance, updateAppearance]
  )

  // Language change handler
  const handleLanguageChange = useCallback(
    async (languageCode: string) => {
      try {
        setLanguage(languageCode)

        // Save to settings
        await updateAppearance({
          ...appearance,
          language: languageCode,
        })

        const selectedLang = LANGUAGES.find(lang => lang.code === languageCode)
        toast.success(`Language changed to ${selectedLang?.name}`)
      } catch {
        toast.error('Failed to update language settings')
      }
    },
    [appearance, updateAppearance]
>>>>>>> feature/sidebar-navigation
  )

  // Font size change handler
  const handleFontSizeChange = useCallback(
    async (value: number[]) => {
<<<<<<< HEAD
      const newSize = value[0]
      setFontSize(newSize)

      try {
        await updateAppearance({ fontSize: newSize })
        toast.success(`Font size updated to ${newSize}px`)
      } catch (error) {
=======
      const newFontSize = value[0]
      setFontSize(newFontSize)

      try {
        setIsUpdating(true)
        await updateAppearance({
          ...appearance,
          fontSize: newFontSize,
        })
        toast.success('Font size updated')
      } catch {
        setFontSize(appearance.fontSize || 16)
>>>>>>> feature/sidebar-navigation
        toast.error('Failed to update font size')
      }
    },
    [updateAppearance]
  )

<<<<<<< HEAD
  // Compact mode change handler
  const handleCompactModeChange = useCallback(
    async (checked: boolean) => {
      setCompactMode(checked)

      try {
        await updateAppearance({ compactMode: checked })
        toast.success(`Compact mode ${checked ? 'enabled' : 'disabled'}`)
      } catch (error) {
        toast.error('Failed to update compact mode')
      }
    },
    [updateAppearance]
  )

  // Animation speed change handler
  const handleAnimationSpeedChange = useCallback(
    async (value: string) => {
      setAnimationSpeed(value)

      try {
        await updateAppearance({ animationSpeed: value })
        toast.success(`Animation speed set to ${value}`)
      } catch (error) {
        toast.error('Failed to update animation speed')
      }
    },
    [updateAppearance]
  )

  // Notification setting change handler
  const handleNotificationChange = useCallback(async (key: string, value: boolean | string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: value,
    }))

    try {
      const response = await fetch('/api/settings/update', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          notifications: {
            [key]: value,
          },
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save notification preferences')
      }

      toast.success('Notification preferences updated')
    } catch (error) {
      toast.error('Failed to save notification preferences')
    }
  }, [])

  // Accessibility setting change handler
  const handleAccessibilityChange = useCallback(async (key: string, value: boolean) => {
    setAccessibilitySettings(prev => ({
      ...prev,
      [key]: value,
    }))

    try {
      const response = await fetch('/api/settings/update', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          accessibility: {
            [key]: value,
          },
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save accessibility preferences')
      }

      toast.success('Accessibility preferences updated')
    } catch (error) {
      toast.error('Failed to save accessibility preferences')
    }
  }, [])

=======
>>>>>>> feature/sidebar-navigation
  // Reset to defaults handler
  const handleResetToDefaults = useCallback(async () => {
    try {
      setIsUpdating(true)

<<<<<<< HEAD
      // Reset appearance settings
=======
      // Reset all states
>>>>>>> feature/sidebar-navigation
      setCurrentTheme('system')
      setCurrentColorScheme('emynent-light')
      setFontSize(16)
      setCompactMode(false)
      setAnimationSpeed('normal')
<<<<<<< HEAD
      setSelectedPreset('emynent-light')

      // Reset theme
      setTheme('system')
      setColorScheme('emynent-light')

      // Reset notifications
      setNotificationSettings({
        system: true,
        messages: true,
        email: true,
        events: true,
        push: true,
        sound: true,
        doNotDisturb: false,
        emailDigest: 'daily',
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '07:00',
        },
      })

      // Reset accessibility
      setAccessibilitySettings({
        highContrast: false,
        reducedMotion: false,
        screenReader: false,
        keyboardNavigation: true,
        textToSpeech: false,
        largeText: false,
      })

      // Persist to database
      await updateAppearance(defaultAppearance)
      await saveThemePreferences('system', 'emynent-light')

      toast.success('All settings reset to defaults')
    } catch (error) {
=======
      setNotifications({ email: true, inApp: true, mobile: false })
      setAccessibility({
        screenReader: false,
        highContrast: false,
        reducedMotion: false,
        largeText: false,
      })
      setLanguage('en')

      // Reset theme
      setTheme('system')
      await saveThemePreferences('system', 'emynent-light')
      await updateAppearance(defaultAppearance)

      toast.success('All settings reset to defaults')
    } catch {
>>>>>>> feature/sidebar-navigation
      toast.error('Failed to reset settings')
    } finally {
      setIsUpdating(false)
    }
<<<<<<< HEAD
  }, [setTheme, setColorScheme, updateAppearance, saveThemePreferences])

  // Helper function to get filtered themes based on current mode
  const getFilteredThemes = () => {
    const targetCategory = resolvedTheme === 'dark' ? 'dark' : 'light'
    return PREDEFINED_THEMES.filter(theme => theme.category === targetCategory)
  }

  // Track user behavior for future AI learning (placeholder)
  const trackBehavior = async (action: string, properties: any) => {
    try {
      // Handle base URL for different environments (test vs production)
      const baseUrl =
        typeof window !== 'undefined' && window.location.origin
          ? window.location.origin
          : process.env.NODE_ENV === 'test'
            ? 'http://localhost:3000'
            : ''

      const url = `${baseUrl}/api/analytics/track`

      await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: action,
          properties: {
            ...properties,
            timestamp: Date.now(),
          },
        }),
      })
    } catch (error) {
      // Analytics failure shouldn't break UX
      console.warn('Analytics tracking failed:', error)
    }
  }
=======
  }, [setTheme, saveThemePreferences, updateAppearance, defaultAppearance])
>>>>>>> feature/sidebar-navigation

  if (isLoading) {
    return (
      <div className='flex items-center justify-center p-8'>
        <Loader2 className='h-6 w-6 animate-spin' />
        <span className='ml-2'>Loading platform settings...</span>
      </div>
    )
  }

  return (
    <div className='space-y-6'>
<<<<<<< HEAD
      <div>
        <h1 className='text-2xl font-bold tracking-tight'>Platform Settings</h1>
        <p className='text-muted-foreground'>
          Customize your platform experience including appearance, notifications, and accessibility
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='appearance'>Appearance</TabsTrigger>
          <TabsTrigger value='notifications'>Notifications</TabsTrigger>
          <TabsTrigger value='accessibility'>Accessibility</TabsTrigger>
          <TabsTrigger value='advanced'>Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value='appearance' className='space-y-6'>
          {/* Theme Mode Selection */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Monitor className='h-5 w-5' />
                Theme Mode
              </CardTitle>
              <CardDescription>
                Choose your preferred theme mode. System mode automatically adapts to your device
                settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={currentTheme}
                onValueChange={handleThemeChange}
                className='grid grid-cols-3 gap-4'
                aria-label='Theme Mode'
              >
                {[
                  {
                    value: 'light',
                    label: 'Light',
                    icon: Sun,
                    description: 'Clean, bright interface',
                  },
                  { value: 'dark', label: 'Dark', icon: Moon, description: 'Easy on the eyes' },
                  {
                    value: 'system',
                    label: 'System',
                    icon: Monitor,
                    description: 'Follows device settings',
                  },
                ].map(({ value, label, icon: Icon, description }) => (
                  <div key={value} className='flex items-center space-x-2'>
                    <RadioGroupItem value={value} id={value} />
                    <Label
                      htmlFor={value}
                      className='flex items-center gap-3 cursor-pointer flex-1'
                    >
                      <Icon className='h-4 w-4' />
                      <div>
                        <div className='font-medium'>{label}</div>
                        <div className='text-xs text-muted-foreground'>{description}</div>
                      </div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </CardContent>
          </Card>

          {/* Predefined Themes */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Palette className='h-5 w-5' />
                Color Scheme
              </CardTitle>
              <CardDescription>
                Choose from carefully crafted themes that match your {resolvedTheme} mode preference
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {getFilteredThemes().map(preset => (
                  <motion.div
                    key={preset.id}
                    className={cn(
                      'relative p-4 rounded-lg border-2 cursor-pointer transition-all',
                      selectedPreset === preset.id
                        ? 'border-primary ring-2 ring-primary/20'
                        : 'border-border hover:border-primary/50'
                    )}
                    onClick={() => handlePresetChange(preset.id)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    style={{ transition: transitions.smooth }}
                  >
                    {selectedPreset === preset.id && (
                      <div className='absolute top-2 right-2'>
                        <Check className='h-4 w-4 text-primary' />
                      </div>
                    )}

                    <div className='space-y-3'>
                      <div className='flex items-center gap-2'>
                        <h3 className='font-medium'>{preset.name}</h3>
                        {preset.isDefault && (
                          <Badge variant='secondary' className='text-xs'>
                            Default
                          </Badge>
                        )}
                      </div>

                      <p className='text-sm text-muted-foreground'>{preset.description}</p>

                      <div className='flex gap-2'>
                        <div
                          className='w-4 h-4 rounded-full border'
                          style={{ backgroundColor: preset.primary }}
                          title='Primary'
                        />
                        <div
                          className='w-4 h-4 rounded-full border'
                          style={{ backgroundColor: preset.secondary }}
                          title='Secondary'
                        />
                        <div
                          className='w-4 h-4 rounded-full border'
                          style={{ backgroundColor: preset.accent }}
                          title='Accent'
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Customization Options */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Settings className='h-5 w-5' />
                Customization
              </CardTitle>
              <CardDescription>Fine-tune your interface preferences</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Font Size */}
              <div className='space-y-3'>
                <Label htmlFor='font-size'>
                  Font Size
                  <span className='ml-2 text-sm text-muted-foreground'>({fontSize}px)</span>
                </Label>
                <Slider
                  id='font-size'
                  min={12}
                  max={24}
                  step={1}
                  value={[fontSize]}
                  onValueChange={handleFontSizeChange}
                  className='w-full'
                  aria-label='Font size'
                />
                <div className='flex justify-between text-xs text-muted-foreground'>
                  <span>12px</span>
                  <span>24px</span>
                </div>
              </div>

              {/* Compact Mode */}
              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label htmlFor='compact-mode'>Compact Mode</Label>
                  <div className='text-sm text-muted-foreground'>
                    Reduce spacing for a denser interface
                  </div>
                </div>
                <Switch
                  id='compact-mode'
                  checked={compactMode}
                  onCheckedChange={handleCompactModeChange}
                  aria-label='Compact mode toggle'
                />
              </div>

              {/* Animation Speed */}
              <div className='space-y-3'>
                <Label>Animation Speed</Label>
                <RadioGroup
                  value={animationSpeed}
                  onValueChange={handleAnimationSpeedChange}
                  className='flex gap-4'
                >
                  {[
                    { value: 'none', label: 'None' },
                    { value: 'slow', label: 'Slow' },
                    { value: 'normal', label: 'Normal' },
                    { value: 'fast', label: 'Fast' },
                  ].map(({ value, label }) => (
                    <div key={value} className='flex items-center space-x-2'>
                      <RadioGroupItem value={value} id={`animation-${value}`} />
                      <Label htmlFor={`animation-${value}`}>{label}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='notifications' className='space-y-6'>
          {/* Notification Types */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Bell className='h-5 w-5' />
                Notification Settings
              </CardTitle>
              <CardDescription>
                Control which notifications you receive and how they are delivered
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {NOTIFICATION_TYPES.map(type => (
                <div
                  key={type.id}
                  className='flex items-center justify-between p-4 rounded-md bg-muted border border-border'
                >
                  <div className='flex items-start space-x-3'>
                    <type.icon className='h-5 w-5 mt-0.5 text-[var(--primary)]' />
                    <div className='space-y-0.5'>
                      <Label htmlFor={`switch-${type.id}`}>{type.title}</Label>
                      <p className='text-xs text-muted-foreground'>{type.description}</p>
                    </div>
                  </div>
                  <Switch
                    id={`switch-${type.id}`}
                    checked={
                      notificationSettings[type.id as keyof typeof notificationSettings] as boolean
                    }
                    onCheckedChange={checked => handleNotificationChange(type.id, checked)}
                  />
                </div>
              ))}

              <div className='space-y-4 pt-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='switch-push'>Push Notifications</Label>
                    <p className='text-xs text-muted-foreground'>
                      Receive push notifications on this device
                    </p>
                  </div>
                  <Switch
                    id='switch-push'
                    checked={notificationSettings.push}
                    onCheckedChange={checked => handleNotificationChange('push', checked)}
                  />
                </div>

                <div className='flex items-center justify-between pt-2'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='switch-sound'>Sound Alerts</Label>
                    <p className='text-xs text-muted-foreground'>
                      Play sound when notifications arrive
                    </p>
                  </div>
                  <Switch
                    id='switch-sound'
                    checked={notificationSettings.sound}
                    onCheckedChange={checked => handleNotificationChange('sound', checked)}
                  />
                </div>

                <div className='flex items-center justify-between pt-2'>
                  <div className='space-y-0.5'>
                    <Label htmlFor='switch-dnd'>Do Not Disturb</Label>
                    <p className='text-xs text-muted-foreground'>
                      Temporarily mute all notifications
                    </p>
                  </div>
                  <Switch
                    id='switch-dnd'
                    checked={notificationSettings.doNotDisturb}
                    onCheckedChange={checked => handleNotificationChange('doNotDisturb', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Clock className='h-5 w-5' />
                Delivery Schedule
              </CardTitle>
              <CardDescription>Set when you want to receive notifications</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='delivery-frequency'>Email Digest Frequency</Label>
                  <Select
                    value={notificationSettings.emailDigest}
                    onValueChange={value => handleNotificationChange('emailDigest', value)}
                  >
                    <SelectTrigger id='delivery-frequency'>
                      <SelectValue placeholder='Select digest frequency' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='immediate'>Immediately</SelectItem>
                      <SelectItem value='hourly'>Hourly Summary</SelectItem>
                      <SelectItem value='daily'>Daily Digest</SelectItem>
                      <SelectItem value='weekly'>Weekly Digest</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className='text-xs text-muted-foreground mt-1'>
                    How often you want to receive email notification digests
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='accessibility' className='space-y-6'>
          {/* Accessibility Features */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Accessibility className='h-5 w-5' />
                Accessibility Features
              </CardTitle>
              <CardDescription>
                Configure accessibility features to improve your experience
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label htmlFor='high-contrast'>High Contrast Mode</Label>
                  <div className='text-sm text-muted-foreground'>
                    Increase contrast for better visibility
                  </div>
                </div>
                <Switch
                  id='high-contrast'
                  checked={accessibilitySettings.highContrast}
                  onCheckedChange={checked => handleAccessibilityChange('highContrast', checked)}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label htmlFor='reduced-motion'>Reduced Motion</Label>
                  <div className='text-sm text-muted-foreground'>
                    Minimize animations and transitions
                  </div>
                </div>
                <Switch
                  id='reduced-motion'
                  checked={accessibilitySettings.reducedMotion}
                  onCheckedChange={checked => handleAccessibilityChange('reducedMotion', checked)}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label htmlFor='screen-reader'>Screen Reader Support</Label>
                  <div className='text-sm text-muted-foreground'>
                    Enhanced support for screen readers
                  </div>
                </div>
                <Switch
                  id='screen-reader'
                  checked={accessibilitySettings.screenReader}
                  onCheckedChange={checked => handleAccessibilityChange('screenReader', checked)}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label htmlFor='keyboard-navigation'>Keyboard Navigation</Label>
                  <div className='text-sm text-muted-foreground'>
                    Enhanced keyboard navigation support
                  </div>
                </div>
                <Switch
                  id='keyboard-navigation'
                  checked={accessibilitySettings.keyboardNavigation}
                  onCheckedChange={checked =>
                    handleAccessibilityChange('keyboardNavigation', checked)
                  }
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label htmlFor='large-text'>Large Text</Label>
                  <div className='text-sm text-muted-foreground'>
                    Use larger text throughout the interface
                  </div>
                </div>
                <Switch
                  id='large-text'
                  checked={accessibilitySettings.largeText}
                  onCheckedChange={checked => handleAccessibilityChange('largeText', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='advanced' className='space-y-6'>
          {/* AI Suggestions Placeholder */}
          <div data-testid='ai-suggestions-placeholder' className='hidden'>
            {/* Placeholder for future AI-driven theme suggestions */}
          </div>

          {/* Reset Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Reset Settings</CardTitle>
              <CardDescription>
                Restore all platform settings to their default values
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant='outline'
                onClick={handleResetToDefaults}
                disabled={isUpdating}
                className='w-full sm:w-auto'
              >
                <RotateCw className='h-4 w-4 mr-2' />
                {isUpdating ? 'Resetting...' : 'Reset to Defaults'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
=======
      {/* AI Theme Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Sparkles className='h-5 w-5 text-purple-500' />
            AI Theme Recommendations
          </CardTitle>
          <CardDescription>
            Personalized theme suggestions based on your usage patterns and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingRecommendations ? (
            <div className='flex items-center justify-center py-8'>
              <Loader2 className='h-6 w-6 animate-spin' />
              <span className='ml-2'>Loading AI recommendations...</span>
            </div>
          ) : (
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
              {aiRecommendations.map(rec => (
                <div
                  key={rec.id}
                  className='border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer'
                  onClick={() => handleAIThemeApply(rec)}
                >
                  <div className='flex items-center justify-between mb-2'>
                    <h4 className='font-medium'>{rec.name}</h4>
                    <Badge variant='secondary' className='text-xs'>
                      {rec.aiScore}% match
                    </Badge>
                  </div>
                  <div className='flex gap-2 mb-2'>
                    <div
                      className='w-4 h-4 rounded-full border'
                      style={{ backgroundColor: rec.primary }}
                    />
                    <div
                      className='w-4 h-4 rounded-full border'
                      style={{ backgroundColor: rec.secondary }}
                    />
                    <div
                      className='w-4 h-4 rounded-full border'
                      style={{ backgroundColor: rec.accent }}
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>{rec.reason}</p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Appearance & Theming */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Palette className='h-5 w-5' />
            Appearance & Theming
          </CardTitle>
          <CardDescription>Customize the visual appearance of your interface</CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          {/* Theme Mode */}
          <div className='space-y-4'>
            <h3 className='text-sm font-medium'>Theme Mode</h3>
            <RadioGroup
              value={currentTheme}
              onValueChange={handleThemeModeChange}
              className='grid grid-cols-3 gap-4'
            >
              <div>
                <RadioGroupItem value='light' id='light' className='peer sr-only' />
                <Label
                  htmlFor='light'
                  className='flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer'
                >
                  <Sun className='mb-2 h-6 w-6' />
                  <span className='text-sm font-medium'>Light</span>
                </Label>
              </div>
              <div>
                <RadioGroupItem value='dark' id='dark' className='peer sr-only' />
                <Label
                  htmlFor='dark'
                  className='flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer'
                >
                  <Moon className='mb-2 h-6 w-6' />
                  <span className='text-sm font-medium'>Dark</span>
                </Label>
              </div>
              <div>
                <RadioGroupItem value='system' id='system' className='peer sr-only' />
                <Label
                  htmlFor='system'
                  className='flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer'
                >
                  <Monitor className='mb-2 h-6 w-6' />
                  <span className='text-sm font-medium'>System</span>
                </Label>
              </div>
            </RadioGroup>
            <div className='flex items-center gap-2 text-sm text-muted-foreground'>
              <span>Currently in:</span>
              <Badge variant='outline'>
                {resolvedTheme === 'dark' ? 'Dark Mode' : 'Light Mode'}
              </Badge>
            </div>
          </div>

          <Separator />

          {/* Font Size */}
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-sm font-medium'>Font Size</h3>
              <span className='text-sm text-muted-foreground'>{fontSize}px</span>
            </div>
            <Slider
              value={[fontSize]}
              onValueChange={handleFontSizeChange}
              max={24}
              min={12}
              step={1}
              className='w-full'
            />
          </div>

          <Separator />

          {/* Compact Mode */}
          <div className='flex items-center justify-between'>
            <div className='space-y-0.5'>
              <h3 className='text-sm font-medium'>Compact Mode</h3>
              <p className='text-sm text-muted-foreground'>
                Reduce spacing and padding for a more dense interface
              </p>
            </div>
            <Switch checked={compactMode} onCheckedChange={checked => setCompactMode(checked)} />
          </div>
        </CardContent>
      </Card>

      {/* Notification Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Bell className='h-5 w-5' />
            Notification Preferences
          </CardTitle>
          <CardDescription>Configure how and when you receive notifications</CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {NOTIFICATION_TYPES.map(type => {
            const Icon = type.icon
            return (
              <div key={type.id} className='flex items-center justify-between'>
                <div className='flex items-center space-x-3'>
                  <Icon className='h-5 w-5 text-muted-foreground' />
                  <div>
                    <h4 className='text-sm font-medium'>{type.label}</h4>
                  </div>
                </div>
                <Switch
                  checked={notifications[type.id as keyof typeof notifications]}
                  onCheckedChange={checked => handleNotificationChange(type.id, checked)}
                />
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Accessibility Settings */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Eye className='h-5 w-5' />
            Accessibility
          </CardTitle>
          <CardDescription>
            Customize the interface for better accessibility and usability
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {ACCESSIBILITY_OPTIONS.map(option => (
            <div key={option.id} className='flex items-center justify-between'>
              <div>
                <h4 className='text-sm font-medium'>{option.label}</h4>
              </div>
              <Switch
                checked={accessibility[option.id as keyof typeof accessibility]}
                onCheckedChange={checked => handleAccessibilityChange(option.id, checked)}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Language & Localization */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Globe className='h-5 w-5' />
            Language & Localization
          </CardTitle>
          <CardDescription>Select your preferred language and regional settings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div>
              <h3 className='text-sm font-medium mb-2'>Interface Language</h3>
              <Select value={language} onValueChange={handleLanguageChange}>
                <SelectTrigger className='w-full'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {LANGUAGES.map(lang => (
                    <SelectItem key={lang.code} value={lang.code}>
                      <div className='flex items-center gap-2'>
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reset to Defaults */}
      <Card>
        <CardHeader>
          <CardTitle>Reset Settings</CardTitle>
          <CardDescription>Restore all platform settings to their default values</CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant='outline'
            onClick={handleResetToDefaults}
            disabled={isUpdating}
            className='w-full'
          >
            {isUpdating ? (
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
            ) : (
              <RotateCw className='mr-2 h-4 w-4' />
            )}
            Reset to Defaults
          </Button>
        </CardContent>
      </Card>
>>>>>>> feature/sidebar-navigation
    </div>
  )
}
