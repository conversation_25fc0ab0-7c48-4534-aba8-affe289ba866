'use client'

import React, { useMemo } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  Target,
  CheckCircle,
  AlertCircle,
  Clock,
  Calendar,
  TrendingUp,
  ChevronRight,
  GitBranch,
  Trophy,
} from 'lucide-react'

// Define types for OKR structure
interface KeyResult {
  id: string
  title: string
  description?: string
  targetValue: number
  currentValue: number
  unit: string
  status: string
  dueDate?: string
}

interface Objective {
  id: string
  title: string
  description?: string
  status: string
  progress: number
  category?: string
  timeframe: string
  priority: string
  targetDate?: string
  keyResults: KeyResult[]
}

interface OKRVisualizationProps {
  objectives: Objective[]
  timeframe: string
}

export function OKRVisualization({ objectives, timeframe }: OKRVisualizationProps) {
  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No date set'

    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    } catch (error) {
      return 'Invalid date'
    }
  }

  // Calculate key result progress
  const calculateKeyResultProgress = (kr: KeyResult) => {
    if (kr.targetValue === 0) return 0
    return Math.min(100, Math.round((kr.currentValue / kr.targetValue) * 100))
  }

  // Get status badge component
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return (
          <Badge
            variant='outline'
            className='text-green-600 border-green-600 flex items-center gap-1'
          >
            <CheckCircle className='h-3 w-3' />
            <span>Completed</span>
          </Badge>
        )
      case 'IN_PROGRESS':
        return (
          <Badge
            variant='outline'
            className='text-blue-600 border-blue-600 flex items-center gap-1'
          >
            <TrendingUp className='h-3 w-3' />
            <span>In Progress</span>
          </Badge>
        )
      case 'AT_RISK':
        return (
          <Badge
            variant='outline'
            className='text-amber-600 border-amber-600 flex items-center gap-1'
          >
            <AlertCircle className='h-3 w-3' />
            <span>At Risk</span>
          </Badge>
        )
      case 'NOT_STARTED':
        return (
          <Badge
            variant='outline'
            className='text-gray-600 border-gray-600 flex items-center gap-1'
          >
            <Clock className='h-3 w-3' />
            <span>Not Started</span>
          </Badge>
        )
      default:
        return (
          <Badge variant='outline' className='flex items-center gap-1'>
            <span>{status}</span>
          </Badge>
        )
    }
  }

  // Format timeframe for display
  const formatTimeframe = (timeframe: string) => {
    switch (timeframe) {
      case 'DAILY':
        return 'Daily'
      case 'WEEKLY':
        return 'Weekly'
      case 'MONTHLY':
        return 'Monthly'
      case 'QUARTERLY':
        return 'Quarterly'
      case 'YEARLY':
        return 'Yearly'
      case 'CUSTOM':
        return 'Custom'
      default:
        return timeframe
    }
  }

  return (
    <div className='space-y-6'>
      <h3 className='text-xl font-semibold'>
        {formatTimeframe(timeframe)} Objectives & Key Results
      </h3>

      {objectives.length === 0 ? (
        <div className='text-center py-8 border border-dashed rounded-lg'>
          <Target className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
          <p className='text-muted-foreground'>No objectives for this timeframe</p>
          <p className='text-sm text-muted-foreground mt-2'>
            Create your first objective to get started
          </p>
        </div>
      ) : (
        <div className='space-y-8'>
          {objectives.map(objective => (
            <div key={objective.id} className='space-y-4'>
              <Card>
                <CardHeader className='pb-2'>
                  <div className='flex items-start justify-between'>
                    <div className='space-y-1'>
                      <CardTitle className='flex items-center gap-2'>
                        <Target className='h-5 w-5 text-primary' />
                        {objective.title}
                        {objective.keyResults && objective.keyResults.length > 0 && (
                          <GitBranch className='h-4 w-4 text-muted-foreground' />
                        )}
                      </CardTitle>
                      {objective.description && (
                        <p className='text-sm text-muted-foreground'>{objective.description}</p>
                      )}
                    </div>
                    {getStatusBadge(objective.status)}
                  </div>
                </CardHeader>

                <CardContent className='space-y-4'>
                  {/* Progress with Chart */}
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between text-sm'>
                      <span>Progress</span>
                      <span>{objective.progress}%</span>
                    </div>
                    <Progress
                      value={objective.progress}
                      className='h-2'
                      aria-label={`${objective.progress}% complete`}
                    />
                  </div>

                  {/* Metadata */}
                  <div className='flex flex-wrap items-center gap-4 text-sm text-muted-foreground'>
                    {objective.category && (
                      <div className='flex items-center gap-1'>
                        <Target className='h-4 w-4' />
                        {objective.category}
                      </div>
                    )}
                    <div className='flex items-center gap-1'>
                      <Calendar className='h-4 w-4' />
                      {formatTimeframe(objective.timeframe)}
                    </div>
                    <div className='flex items-center gap-1'>
                      <TrendingUp className='h-4 w-4' />
                      {objective.priority} Priority
                    </div>
                    {objective.targetDate && (
                      <div className='flex items-center gap-1'>
                        <Clock className='h-4 w-4' />
                        Due: {formatDate(objective.targetDate)}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Key Results - indented under objective */}
              {objective.keyResults && objective.keyResults.length > 0 && (
                <div className='pl-4 space-y-3 border-l-2 border-primary ml-4'>
                  <div className='flex items-center text-sm font-semibold text-muted-foreground'>
                    <ChevronRight className='h-4 w-4 mr-1' />
                    Key Results ({objective.keyResults.length})
                  </div>

                  {objective.keyResults.map(kr => (
                    <Card key={kr.id} className='border-l-2 border-l-primary'>
                      <CardHeader className='py-3'>
                        <div className='flex justify-between items-start'>
                          <CardTitle className='text-base'>{kr.title}</CardTitle>
                        </div>
                        {kr.description && (
                          <p className='text-sm text-muted-foreground mt-1'>{kr.description}</p>
                        )}
                      </CardHeader>

                      <CardContent className='py-3'>
                        <div className='space-y-3'>
                          <div className='flex items-center justify-between text-sm'>
                            <span>
                              {kr.currentValue} / {kr.targetValue} {kr.unit}
                            </span>
                            <span>{calculateKeyResultProgress(kr)}%</span>
                          </div>
                          <Progress
                            value={calculateKeyResultProgress(kr)}
                            className='h-2'
                            aria-label={`${calculateKeyResultProgress(kr)}% complete`}
                          />

                          <div className='flex items-center justify-between text-sm pt-1'>
                            <div className='flex items-center gap-2'>
                              {getStatusBadge(kr.status)}
                              {kr.dueDate && (
                                <span className='text-muted-foreground flex items-center gap-1'>
                                  <Clock className='h-3 w-3' />
                                  {formatDate(kr.dueDate)}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
