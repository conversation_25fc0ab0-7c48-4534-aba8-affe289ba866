'use client'

import { useState } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Calendar, Target, Award, TrendingUp, Brain, Heart, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { TodaySection } from './TodaySection'
import { GoalsSection } from './GoalsSection'
import { SkillsSection } from './SkillsSection'
import { PerformanceSection } from './PerformanceSection'
import { FocusInsightsSection } from './FocusInsightsSection'
import PersonalReflectionSection from './PersonalReflectionSection'

type FocusTab = 'today' | 'goals' | 'skills' | 'performance' | 'reflection' | 'insights'

const focusTabs = [
  {
    id: 'today' as const,
    label: 'Today',
    icon: Calendar,
    description: 'Current tasks and schedule',
    color: 'text-blue-600',
  },
  {
    id: 'goals' as const,
    label: 'Goals',
    icon: Target,
    description: 'OKRs and objectives',
    color: 'text-green-600',
  },
  {
    id: 'skills' as const,
    label: 'Skills',
    icon: Award,
    description: 'Professional development',
    color: 'text-purple-600',
  },
  {
    id: 'performance' as const,
    label: 'Performance',
    icon: TrendingUp,
    description: 'Metrics and analytics',
    color: 'text-orange-600',
  },
  {
    id: 'reflection' as const,
    label: 'Reflection',
    icon: Heart,
    description: 'Personal growth insights',
    color: 'text-rose-600',
  },
  {
    id: 'insights' as const,
    label: 'Insights',
    icon: Brain,
    description: 'AI recommendations',
    color: 'text-pink-600',
  },
]

export function FocusTabNavigation() {
  const [activeTab, setActiveTab] = useState<FocusTab>('today')

  return (
    <div className='space-y-6'>
      {/* Header and Breadcrumbs */}
      <div className='space-y-2'>
        {/* Breadcrumb Navigation */}
        <nav
          className='flex items-center space-x-1 text-sm text-muted-foreground'
          aria-label='Breadcrumb'
        >
          <Link
            href='/dashboard'
            className='hover:text-foreground transition-colors'
            aria-label='Go to Dashboard'
          >
            Dashboard
          </Link>
          <ChevronRight className='h-4 w-4' />
          <span className='text-foreground font-medium'>Focus</span>
        </nav>

        {/* Section Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>Focus</h1>
            <p className='text-muted-foreground'>
              Your daily command center for productivity and goal tracking
            </p>
            <span className='sr-only'>Personal productivity dashboard</span>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <Tabs
        value={activeTab}
        onValueChange={value => setActiveTab(value as FocusTab)}
        className='w-full'
      >
        <TabsList
          className={cn(
            'grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 h-auto p-1',
            'bg-muted rounded-lg'
          )}
        >
          {focusTabs.map(tab => {
            const Icon = tab.icon
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  'flex flex-col items-center gap-1 p-3 h-auto',
                  'data-[state=active]:bg-background data-[state=active]:shadow-sm',
                  'transition-all duration-200 hover:bg-muted-foreground/10'
                )}
                aria-label={`${tab.label} - ${tab.description}`}
              >
                <Icon className='h-4 w-4' aria-hidden='true' />
                <span className='text-xs font-medium'>{tab.label}</span>
              </TabsTrigger>
            )
          })}
        </TabsList>

        {/* Tab Content */}
        <div className='mt-6'>
          <TabsContent
            value='today'
            className='space-y-6 transition-all duration-200'
            role='tabpanel'
            aria-labelledby='today-tab'
          >
            <TodaySection />
          </TabsContent>

          <TabsContent
            value='goals'
            className='space-y-6 transition-all duration-200'
            role='tabpanel'
            aria-labelledby='goals-tab'
          >
            <GoalsSection />
          </TabsContent>

          <TabsContent
            value='skills'
            className='space-y-6 transition-all duration-200'
            role='tabpanel'
            aria-labelledby='skills-tab'
          >
            <SkillsSection />
          </TabsContent>

          <TabsContent
            value='performance'
            className='space-y-6 transition-all duration-200'
            role='tabpanel'
            aria-labelledby='performance-tab'
          >
            <PerformanceSection />
          </TabsContent>

          <TabsContent
            value='reflection'
            className='space-y-6 transition-all duration-200'
            role='tabpanel'
            aria-labelledby='reflection-tab'
          >
            <PersonalReflectionSection />
          </TabsContent>

          <TabsContent
            value='insights'
            className='space-y-6 transition-all duration-200'
            role='tabpanel'
            aria-labelledby='insights-tab'
          >
            <FocusInsightsSection />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}
