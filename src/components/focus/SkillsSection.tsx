'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/lib/auth/hooks'
import { skillsClient, type Skill, type SkillGap } from '@/lib/services/skills-client'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  TrendingUp,
  Target,
  BookOpen,
  Award,
  Search,
  Filter,
  ArrowUpDown,
  Calendar,
} from 'lucide-react'
import { toast } from 'sonner'
import { SkillAssessmentForm } from './SkillAssessmentForm'

// Import types from skill-client service
export type SkillCategory =
  | 'TECHNICAL'
  | 'SOFT_SKILLS'
  | 'LEADERSHIP'
  | 'COMMUNICATION'
  | 'ANALYTICAL'
  | 'CREATIVE'
  | 'DOMAIN_SPECIFIC'

export type SkillLevel = 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'

interface SkillFilterState {
  search: string
  category: SkillCategory[] | []
  level: SkillLevel[] | []
  importance: number[] | []
}

export function SkillsSection() {
  const { user } = useAuth()
  const [skills, setSkills] = useState<Skill[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isAssessmentDialogOpen, setIsAssessmentDialogOpen] = useState(false)
  const [selectedSkillForAssessment, setSelectedSkillForAssessment] = useState<Skill | null>(null)
  const [skillGaps, setSkillGaps] = useState<SkillGap[]>([])
  const [activeTab, setActiveTab] = useState('all')
  const [filters, setFilters] = useState<SkillFilterState>({
    search: '',
    category: [],
    level: [],
    importance: [],
  })
  const [createSkillData, setCreateSkillData] = useState({
    name: '',
    description: '',
    category: 'TECHNICAL' as SkillCategory,
    currentLevel: 'BEGINNER' as SkillLevel,
    targetLevel: 'INTERMEDIATE' as SkillLevel,
    importance: 3,
    tags: [] as string[],
  })
  const [newTag, setNewTag] = useState('')

  // Load skills using client service
  const loadSkills = useCallback(async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      const response = await skillsClient.getUserSkills({
        search: filters.search || undefined,
        category: filters.category.length ? filters.category : undefined,
        level: filters.level.length ? filters.level : undefined,
        importance: filters.importance.length ? filters.importance : undefined,
      })

      setSkills(response.skills)

      // Also load skill gaps for development tab
      const gaps = await skillsClient.getSkillsNeedingDevelopment()
      setSkillGaps(gaps)
    } catch (error) {
      console.error('Error fetching skills:', error)
      toast.error('Failed to load skills')
      setSkills([])
    } finally {
      setLoading(false)
    }
  }, [user?.id, filters])

  // Load skills on component mount and when filters change
  useEffect(() => {
    loadSkills()
  }, [loadSkills])

  // Handler for creating a new skill
  const handleCreateSkill = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!createSkillData.name.trim()) {
      toast.error('Skill name is required')
      return
    }

    try {
      const newSkill = await skillsClient.createSkill({
        name: createSkillData.name.trim(),
        description: createSkillData.description.trim() || undefined,
        category: createSkillData.category,
        currentLevel: createSkillData.currentLevel,
        targetLevel: createSkillData.targetLevel,
        importance: createSkillData.importance,
        tags: createSkillData.tags,
      })

      setSkills(prev => [newSkill, ...prev])
      setIsCreateDialogOpen(false)
      setCreateSkillData({
        name: '',
        description: '',
        category: 'TECHNICAL',
        currentLevel: 'BEGINNER',
        targetLevel: 'INTERMEDIATE',
        importance: 3,
        tags: [],
      })
      toast.success('Skill added successfully')
    } catch (error) {
      console.error('Error creating skill:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create skill'
      toast.error(errorMessage)
    }
  }

  // Add a tag to the skill being created
  const handleAddTag = () => {
    if (newTag.trim() && !createSkillData.tags.includes(newTag.trim())) {
      setCreateSkillData({
        ...createSkillData,
        tags: [...createSkillData.tags, newTag.trim()],
      })
      setNewTag('')
    }
  }

  // Remove a tag from the skill being created
  const handleRemoveTag = (tagToRemove: string) => {
    setCreateSkillData({
      ...createSkillData,
      tags: createSkillData.tags.filter(tag => tag !== tagToRemove),
    })
  }

  // Open assessment dialog for a skill
  const handleAssessSkill = (skill: Skill) => {
    setSelectedSkillForAssessment(skill)
    setIsAssessmentDialogOpen(true)
  }

  // Get skill level color
  const getSkillLevelColor = (level: SkillLevel) => {
    switch (level) {
      case 'BEGINNER':
        return 'bg-red-100 text-red-800'
      case 'INTERMEDIATE':
        return 'bg-yellow-100 text-yellow-800'
      case 'ADVANCED':
        return 'bg-blue-100 text-blue-800'
      case 'EXPERT':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Get skill level progress percentage
  const getSkillLevelProgress = (level: SkillLevel) => {
    switch (level) {
      case 'BEGINNER':
        return 25
      case 'INTERMEDIATE':
        return 50
      case 'ADVANCED':
        return 75
      case 'EXPERT':
        return 100
      default:
        return 0
    }
  }

  // Format skill level for display
  const formatSkillLevel = (level: SkillLevel) => {
    return level.charAt(0) + level.slice(1).toLowerCase()
  }

  // Get category icon
  const getCategoryIcon = (category: SkillCategory) => {
    switch (category) {
      case 'TECHNICAL':
        return '💻'
      case 'SOFT_SKILLS':
        return '🤝'
      case 'LEADERSHIP':
        return '👑'
      case 'COMMUNICATION':
        return '💬'
      case 'ANALYTICAL':
        return '📊'
      case 'CREATIVE':
        return '🎨'
      case 'DOMAIN_SPECIFIC':
        return '🎯'
      default:
        return '📚'
    }
  }

  // Calculate skill gap score (difference between current and target)
  const calculateGapScore = (current: SkillLevel, target: SkillLevel) => {
    const currentScore = getSkillLevelProgress(current)
    const targetScore = getSkillLevelProgress(target)
    return targetScore - currentScore
  }

  // Filter skills based on search query
  const filterSkills = () => {
    if (activeTab === 'development') {
      return skillGaps
    }

    return skills
  }

  // Update filters
  const updateFilters = (key: keyof SkillFilterState, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }))
  }

  // Reset filters
  const resetFilters = () => {
    setFilters({
      search: '',
      category: [],
      level: [],
      importance: [],
    })
  }

  // Render empty state
  const renderEmptyState = () => (
    <div className='text-center p-8 border rounded-lg'>
      <div className='flex justify-center mb-4'>
        <Award className='h-12 w-12 text-muted-foreground' />
      </div>
      <h3 className='text-lg font-semibold mb-2'>No skills recorded yet</h3>
      <p className='text-muted-foreground mb-4'>
        Start tracking your professional skills and competencies
      </p>
      <Button onClick={() => setIsCreateDialogOpen(true)}>
        <Plus className='h-4 w-4 mr-2' />
        Add your first skill
      </Button>
    </div>
  )

  // Render skill card
  const renderSkillCard = (skill: Skill) => (
    <Card key={skill.id} className='overflow-hidden'>
      <CardHeader className='pb-2'>
        <div className='flex justify-between items-start'>
          <div>
            <CardTitle className='text-base flex items-center'>
              <span className='mr-2'>{getCategoryIcon(skill.category)}</span>
              {skill.name}
            </CardTitle>
            <CardDescription>{skill.description}</CardDescription>
          </div>
          <Badge variant='outline' className={getSkillLevelColor(skill.currentLevel)}>
            {formatSkillLevel(skill.currentLevel)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className='pb-2'>
        <div className='space-y-4'>
          <div>
            <div className='flex justify-between text-sm mb-1'>
              <span>Proficiency</span>
              <span>{getSkillLevelProgress(skill.currentLevel)}%</span>
            </div>
            <Progress value={getSkillLevelProgress(skill.currentLevel)} className='h-2' />
          </div>

          {skill.targetLevel && (
            <div>
              <div className='flex justify-between text-sm mb-1'>
                <span>Target: {formatSkillLevel(skill.targetLevel)}</span>
                <span>{calculateGapScore(skill.currentLevel, skill.targetLevel)}% gap</span>
              </div>
              <div className='relative h-2 rounded-full bg-muted overflow-hidden'>
                <div
                  className='absolute left-0 top-0 h-full bg-blue-200'
                  style={{ width: `${getSkillLevelProgress(skill.currentLevel)}%` }}
                />
                <div
                  className='absolute right-0 top-0 h-full bg-blue-500 opacity-30'
                  style={{
                    width: `${calculateGapScore(skill.currentLevel, skill.targetLevel)}%`,
                    left: `${getSkillLevelProgress(skill.currentLevel)}%`,
                  }}
                />
              </div>
            </div>
          )}

          {skill.tags && skill.tags.length > 0 && (
            <div className='flex flex-wrap gap-1 mt-2'>
              {skill.tags.map((tag, i) => (
                <Badge key={i} variant='secondary' className='text-xs'>
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className='pt-2'>
        <Button
          variant='outline'
          size='sm'
          className='w-full'
          onClick={() => handleAssessSkill(skill)}
        >
          <TrendingUp className='h-4 w-4 mr-2' />
          Assess Skill
        </Button>
      </CardFooter>
    </Card>
  )

  // Render skill gap card
  const renderSkillGapCard = (gap: SkillGap) => (
    <Card key={gap.skillId} className='overflow-hidden'>
      <CardHeader className='pb-2'>
        <div className='flex justify-between items-start'>
          <div>
            <CardTitle className='text-base flex items-center'>
              <span className='mr-2'>{getCategoryIcon(gap.category)}</span>
              {gap.skillName}
            </CardTitle>
          </div>
          <Badge variant='outline' className='bg-blue-100 text-blue-800'>
            {gap.gapScore}% gap
          </Badge>
        </div>
      </CardHeader>
      <CardContent className='pb-2'>
        <div className='space-y-4'>
          <div>
            <div className='flex justify-between text-sm mb-1'>
              <span>Current: {formatSkillLevel(gap.currentLevel)}</span>
              <span>Target: {formatSkillLevel(gap.targetLevel)}</span>
            </div>
            <div className='relative h-2 rounded-full bg-muted overflow-hidden'>
              <div
                className='absolute left-0 top-0 h-full bg-blue-200'
                style={{ width: `${getSkillLevelProgress(gap.currentLevel)}%` }}
              />
              <div
                className='absolute right-0 top-0 h-full bg-blue-500 opacity-30'
                style={{
                  width: `${gap.gapScore}%`,
                  left: `${getSkillLevelProgress(gap.currentLevel)}%`,
                }}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Target className='h-4 w-4 mr-2 text-muted-foreground' />
            <span className='text-sm text-muted-foreground'>Importance: {gap.importance}/5</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className='pt-2'>
        <Button
          variant='outline'
          size='sm'
          className='w-full'
          onClick={() => {
            const skill = skills.find(s => s.id === gap.skillId)
            if (skill) handleAssessSkill(skill)
          }}
        >
          <TrendingUp className='h-4 w-4 mr-2' />
          Assess Skill
        </Button>
      </CardFooter>
    </Card>
  )

  if (loading) {
    return (
      <div className='space-y-4' data-testid='skills-loading'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className='h-48 bg-gray-200 rounded'></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-6'>
      {/* Header with actions */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0'>
        <div>
          <h2 className='text-2xl font-semibold tracking-tight'>Skills Assessment</h2>
          <p className='text-sm text-muted-foreground'>
            Track and develop your professional competencies
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className='h-4 w-4 mr-2' />
          Add Skill
        </Button>
      </div>

      {/* Tabs and filters */}
      <div className='space-y-4'>
        <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0'>
            <TabsList>
              <TabsTrigger value='all'>All Skills</TabsTrigger>
              <TabsTrigger value='development'>Development Needs</TabsTrigger>
            </TabsList>

            <div className='flex items-center space-x-2'>
              <div className='relative'>
                <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search skills...'
                  className='pl-8 w-full sm:w-[200px]'
                  value={filters.search}
                  onChange={e => updateFilters('search', e.target.value)}
                />
              </div>

              <Select
                value={filters.category.length ? filters.category[0] : ''}
                onValueChange={value =>
                  updateFilters('category', value ? [value as SkillCategory] : [])
                }
              >
                <SelectTrigger className='w-[180px]'>
                  <Filter className='h-4 w-4 mr-2' />
                  <SelectValue placeholder='Category' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='ALL'>All Categories</SelectItem>
                  <SelectItem value='TECHNICAL'>Technical</SelectItem>
                  <SelectItem value='SOFT_SKILLS'>Soft Skills</SelectItem>
                  <SelectItem value='LEADERSHIP'>Leadership</SelectItem>
                  <SelectItem value='COMMUNICATION'>Communication</SelectItem>
                  <SelectItem value='ANALYTICAL'>Analytical</SelectItem>
                  <SelectItem value='CREATIVE'>Creative</SelectItem>
                  <SelectItem value='DOMAIN_SPECIFIC'>Domain Specific</SelectItem>
                </SelectContent>
              </Select>

              {(filters.search ||
                filters.category.length > 0 ||
                filters.level.length > 0 ||
                filters.importance.length > 0) && (
                <Button variant='ghost' size='sm' onClick={resetFilters}>
                  Reset
                </Button>
              )}
            </div>
          </div>

          <TabsContent value='all' className='mt-6'>
            {loading ? (
              <div className='text-center p-8'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto'></div>
                <p className='mt-2 text-muted-foreground'>Loading skills...</p>
              </div>
            ) : (skills?.length || 0) === 0 ? (
              renderEmptyState()
            ) : (
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {skills?.map(renderSkillCard) || []}
              </div>
            )}
          </TabsContent>

          <TabsContent value='development' className='mt-6'>
            {loading ? (
              <div className='text-center p-8'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto'></div>
                <p className='mt-2 text-muted-foreground'>Loading skill gaps...</p>
              </div>
            ) : (skillGaps?.length || 0) === 0 ? (
              <div className='text-center p-8 border rounded-lg'>
                <div className='flex justify-center mb-4'>
                  <Target className='h-12 w-12 text-muted-foreground' />
                </div>
                <h3 className='text-lg font-semibold mb-2'>No skill gaps found</h3>
                <p className='text-muted-foreground mb-4'>
                  You're either at your target level for all skills, or haven't set target levels
                  yet.
                </p>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className='h-4 w-4 mr-2' />
                  Add skill with target level
                </Button>
              </div>
            ) : (
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {skillGaps?.map(renderSkillGapCard) || []}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Skill Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className='sm:max-w-[500px]'>
          <DialogHeader>
            <DialogTitle>Add New Skill</DialogTitle>
            <DialogDescription>Track a new professional skill or competency</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleCreateSkill} className='space-y-4'>
            <div className='space-y-4'>
              <div>
                <Label htmlFor='skill-name'>Skill Name</Label>
                <Input
                  id='skill-name'
                  value={createSkillData.name}
                  onChange={e => setCreateSkillData({ ...createSkillData, name: e.target.value })}
                  placeholder='e.g., React Development, Project Management'
                  required
                />
              </div>

              <div>
                <Label htmlFor='skill-description'>Description</Label>
                <Textarea
                  id='skill-description'
                  value={createSkillData.description}
                  onChange={e =>
                    setCreateSkillData({ ...createSkillData, description: e.target.value })
                  }
                  placeholder='Brief description of the skill and its relevance'
                  rows={3}
                />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <Label htmlFor='skill-category'>Category</Label>
                  <Select
                    value={createSkillData.category}
                    onValueChange={value =>
                      setCreateSkillData({ ...createSkillData, category: value as SkillCategory })
                    }
                  >
                    <SelectTrigger id='skill-category'>
                      <SelectValue placeholder='Select category' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='TECHNICAL'>Technical</SelectItem>
                      <SelectItem value='SOFT_SKILLS'>Soft Skills</SelectItem>
                      <SelectItem value='LEADERSHIP'>Leadership</SelectItem>
                      <SelectItem value='COMMUNICATION'>Communication</SelectItem>
                      <SelectItem value='ANALYTICAL'>Analytical</SelectItem>
                      <SelectItem value='CREATIVE'>Creative</SelectItem>
                      <SelectItem value='DOMAIN_SPECIFIC'>Domain Specific</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor='skill-importance'>Importance (1-5)</Label>
                  <Select
                    value={createSkillData.importance.toString()}
                    onValueChange={value =>
                      setCreateSkillData({ ...createSkillData, importance: parseInt(value) })
                    }
                  >
                    <SelectTrigger id='skill-importance'>
                      <SelectValue placeholder='Select importance' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='1'>1 - Low</SelectItem>
                      <SelectItem value='2'>2 - Moderate</SelectItem>
                      <SelectItem value='3'>3 - Medium</SelectItem>
                      <SelectItem value='4'>4 - High</SelectItem>
                      <SelectItem value='5'>5 - Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <Label htmlFor='current-level'>Current Level</Label>
                  <Select
                    value={createSkillData.currentLevel}
                    onValueChange={value =>
                      setCreateSkillData({ ...createSkillData, currentLevel: value as SkillLevel })
                    }
                  >
                    <SelectTrigger id='current-level'>
                      <SelectValue placeholder='Select level' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='BEGINNER'>Beginner</SelectItem>
                      <SelectItem value='INTERMEDIATE'>Intermediate</SelectItem>
                      <SelectItem value='ADVANCED'>Advanced</SelectItem>
                      <SelectItem value='EXPERT'>Expert</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor='target-level'>Target Level</Label>
                  <Select
                    value={createSkillData.targetLevel}
                    onValueChange={value =>
                      setCreateSkillData({ ...createSkillData, targetLevel: value as SkillLevel })
                    }
                  >
                    <SelectTrigger id='target-level'>
                      <SelectValue placeholder='Select target' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='BEGINNER'>Beginner</SelectItem>
                      <SelectItem value='INTERMEDIATE'>Intermediate</SelectItem>
                      <SelectItem value='ADVANCED'>Advanced</SelectItem>
                      <SelectItem value='EXPERT'>Expert</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Tags</Label>
                <div className='flex gap-2 mb-2'>
                  <Input
                    placeholder='Add tags'
                    value={newTag}
                    onChange={e => setNewTag(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        handleAddTag()
                      }
                    }}
                  />
                  <Button type='button' variant='outline' onClick={handleAddTag}>
                    Add
                  </Button>
                </div>

                {createSkillData.tags.length > 0 && (
                  <div className='flex flex-wrap gap-1 mt-2'>
                    {createSkillData.tags.map((tag, i) => (
                      <Badge key={i} variant='secondary' className='flex items-center gap-1'>
                        {tag}
                        <button
                          type='button'
                          className='ml-1 h-4 w-4 rounded-full'
                          onClick={() => handleRemoveTag(tag)}
                        >
                          <X className='h-3 w-3' />
                          <span className='sr-only'>Remove tag</span>
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button type='button' variant='outline' onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button type='submit'>Add Skill</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Skill Assessment Dialog */}
      <Dialog open={isAssessmentDialogOpen} onOpenChange={setIsAssessmentDialogOpen}>
        <DialogContent className='sm:max-w-[600px]'>
          <DialogHeader>
            <DialogTitle>Skill Assessment</DialogTitle>
            <DialogDescription>Evaluate your proficiency and track your progress</DialogDescription>
          </DialogHeader>

          {selectedSkillForAssessment && (
            <SkillAssessmentForm
              skill={selectedSkillForAssessment}
              onSuccess={() => {
                setIsAssessmentDialogOpen(false)
                loadSkills()
              }}
              onCancel={() => setIsAssessmentDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
