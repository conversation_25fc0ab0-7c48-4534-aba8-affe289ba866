/**
 * Time & Energy Management Section
 * Task 5.13: AI-first time blocking and energy tracking system
 * Features: Smart time blocks, energy tracking, productivity insights, work-life balance
 */

'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { useAuth } from '@/lib/auth/hooks'
import {
  TimeEnergyService,
  type TimeBlock,
  type EnergyLevel,
  type ProductivityInsights,
  type WorkLifeBalanceMetrics,
  type OptimalTimeBlockSuggestion,
} from '@/lib/services/time-energy-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar } from '@/components/ui/calendar'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Clock,
  Calendar as CalendarIcon,
  Plus,
  Zap,
  Brain,
  BarChart3,
  TrendingUp,
  Coffee,
  Moon,
  Sun,
  Activity,
  Timer,
  Target,
  Lightbulb,
  Heart,
  AlertCircle,
  CheckCircle,
  Star,
  Sparkles,
  Play,
  Pause,
  MoreVertical,
  Edit,
  Trash2,
} from 'lucide-react'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { format, formatDistanceToNow, isToday, parseISO } from 'date-fns'

// Types
interface TimeBlockFormData {
  title: string
  description: string
  startTime: string
  endTime: string
  type: TimeBlock['type']
  energyLevel: TimeBlock['energyLevel']
  tags: string[]
}

interface EnergyFormData {
  level: number
  mood: string
  context: string
  factors: string[]
}

// Constants
const TIME_BLOCK_TYPES = [
  { value: 'focus', label: 'Deep Work', icon: Target, color: 'text-blue-600' },
  { value: 'meeting', label: 'Meeting', icon: CalendarIcon, color: 'text-green-600' },
  { value: 'break', label: 'Break', icon: Coffee, color: 'text-orange-600' },
  { value: 'administrative', label: 'Admin', icon: BarChart3, color: 'text-purple-600' },
  { value: 'creative', label: 'Creative', icon: Lightbulb, color: 'text-pink-600' },
  { value: 'learning', label: 'Learning', icon: Brain, color: 'text-indigo-600' },
] as const

const ENERGY_LEVELS = [
  { value: 1, label: 'Very Low', icon: Moon, color: 'text-gray-500' },
  { value: 2, label: 'Low', icon: Activity, color: 'text-red-500' },
  { value: 3, label: 'Medium', icon: Sun, color: 'text-yellow-500' },
  { value: 4, label: 'High', icon: Zap, color: 'text-green-500' },
  { value: 5, label: 'Very High', icon: Star, color: 'text-blue-500' },
] as const

const MOODS = [
  'focused',
  'energetic',
  'calm',
  'motivated',
  'stressed',
  'tired',
  'anxious',
  'confident',
  'creative',
  'overwhelmed',
] as const

const ENERGY_FACTORS = [
  'sleep',
  'caffeine',
  'exercise',
  'nutrition',
  'weather',
  'workload',
  'meetings',
  'social_interaction',
] as const

export function TimeEnergyManagementSection() {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<
    'overview' | 'blocks' | 'energy' | 'insights' | 'balance'
  >('overview')

  // Data states
  const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>([])
  const [energyLevels, setEnergyLevels] = useState<EnergyLevel[]>([])
  const [productivityInsights, setProductivityInsights] = useState<ProductivityInsights | null>(
    null
  )
  const [workLifeBalance, setWorkLifeBalance] = useState<WorkLifeBalanceMetrics | null>(null)
  const [aiSuggestions, setAiSuggestions] = useState<OptimalTimeBlockSuggestion[]>([])

  // Modal states
  const [isTimeBlockModalOpen, setIsTimeBlockModalOpen] = useState(false)
  const [isEnergyModalOpen, setIsEnergyModalOpen] = useState(false)
  const [selectedTimeBlock, setSelectedTimeBlock] = useState<TimeBlock | null>(null)

  // Form states
  const [timeBlockForm, setTimeBlockForm] = useState<TimeBlockFormData>({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    type: 'focus',
    energyLevel: 'medium',
    tags: [],
  })

  const [energyForm, setEnergyForm] = useState<EnergyFormData>({
    level: 3,
    mood: 'focused',
    context: '',
    factors: [],
  })

  // Quick energy tracking state
  const [quickEnergyLevel, setQuickEnergyLevel] = useState<number | null>(null)

  // Load data on component mount
  useEffect(() => {
    if (user?.id) {
      loadAllData()
    }
  }, [user?.id])

  const loadAllData = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)

      const today = new Date()
      const startOfDay = new Date(today.setHours(0, 0, 0, 0))
      const endOfDay = new Date(today.setHours(23, 59, 59, 999))

      // Load today's data in parallel
      const [blocksData, energyData, insightsData, balanceData, suggestionsData] =
        await Promise.all([
          TimeEnergyService.getTimeBlocks(user.id, {
            startDate: startOfDay,
            endDate: endOfDay,
          }),
          TimeEnergyService.getEnergyLevels(user.id, {
            startDate: startOfDay,
            endDate: endOfDay,
          }),
          TimeEnergyService.getProductivityInsights(user.id),
          TimeEnergyService.getWorkLifeBalanceMetrics(user.id),
          TimeEnergyService.predictOptimalTimeBlocks(user.id),
        ])

      setTimeBlocks(blocksData)
      setEnergyLevels(energyData)
      setProductivityInsights(insightsData)
      setWorkLifeBalance(balanceData)
      setAiSuggestions(suggestionsData)
    } catch (error) {
      console.error('Error loading time & energy data:', error)
      toast.error('Failed to load time & energy data')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle quick energy level recording
  const handleQuickEnergyRecord = async (level: number) => {
    if (!user?.id) return

    try {
      setQuickEnergyLevel(level)

      await TimeEnergyService.recordEnergyLevel({
        level,
        mood: 'neutral',
        userId: user.id,
      })

      // Refresh energy data
      const today = new Date()
      const startOfDay = new Date(today.setHours(0, 0, 0, 0))
      const endOfDay = new Date(today.setHours(23, 59, 59, 999))

      const updatedEnergyData = await TimeEnergyService.getEnergyLevels(user.id, {
        startDate: startOfDay,
        endDate: endOfDay,
      })

      setEnergyLevels(updatedEnergyData)

      toast.success('Energy level recorded')

      // Clear quick selection after 2 seconds
      setTimeout(() => setQuickEnergyLevel(null), 2000)
    } catch (error) {
      console.error('Error recording energy level:', error)
      toast.error('Failed to record energy level')
      setQuickEnergyLevel(null)
    }
  }

  // Handle time block creation
  const handleCreateTimeBlock = async () => {
    if (!user?.id || !timeBlockForm.title || !timeBlockForm.startTime || !timeBlockForm.endTime) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const today = new Date()
      const startTime = new Date(
        `${today.toISOString().split('T')[0]}T${timeBlockForm.startTime}:00`
      )
      const endTime = new Date(`${today.toISOString().split('T')[0]}T${timeBlockForm.endTime}:00`)

      if (startTime >= endTime) {
        toast.error('End time must be after start time')
        return
      }

      const newTimeBlock = await TimeEnergyService.createTimeBlock({
        title: timeBlockForm.title,
        description: timeBlockForm.description,
        startTime,
        endTime,
        type: timeBlockForm.type,
        energyLevel: timeBlockForm.energyLevel,
        tags: timeBlockForm.tags,
        userId: user.id,
      })

      setTimeBlocks(prev => [...prev, newTimeBlock])
      setIsTimeBlockModalOpen(false)
      setTimeBlockForm({
        title: '',
        description: '',
        startTime: '',
        endTime: '',
        type: 'focus',
        energyLevel: 'medium',
        tags: [],
      })

      toast.success('Time block created')
    } catch (error) {
      console.error('Error creating time block:', error)
      toast.error('Failed to create time block')
    }
  }

  // Handle detailed energy recording
  const handleRecordEnergy = async () => {
    if (!user?.id) return

    try {
      await TimeEnergyService.recordEnergyLevel({
        level: energyForm.level,
        mood: energyForm.mood,
        context: energyForm.context,
        factors: energyForm.factors,
        userId: user.id,
      })

      // Refresh energy data
      const today = new Date()
      const startOfDay = new Date(today.setHours(0, 0, 0, 0))
      const endOfDay = new Date(today.setHours(23, 59, 59, 999))

      const updatedEnergyData = await TimeEnergyService.getEnergyLevels(user.id, {
        startDate: startOfDay,
        endDate: endOfDay,
      })

      setEnergyLevels(updatedEnergyData)
      setIsEnergyModalOpen(false)
      setEnergyForm({
        level: 3,
        mood: 'focused',
        context: '',
        factors: [],
      })

      toast.success('Energy level recorded')
    } catch (error) {
      console.error('Error recording energy level:', error)
      toast.error('Failed to record energy level')
    }
  }

  // Calculate current energy statistics
  const currentEnergyStats = useMemo(() => {
    if (energyLevels.length === 0) return null

    const latest = energyLevels[0]
    const average = energyLevels.reduce((sum, level) => sum + level.level, 0) / energyLevels.length
    const trend = energyLevels.length > 1 ? energyLevels[0].level - energyLevels[1].level : 0

    return {
      current: latest.level,
      average: Math.round(average * 10) / 10,
      trend,
      mood: latest.mood,
    }
  }, [energyLevels])

  // Loading state
  if (isLoading) {
    return (
      <div className='space-y-6' role='main' aria-label='Time & Energy Management'>
        <div className='flex items-center justify-between'>
          <div>
            <Skeleton className='h-8 w-64 mb-2' />
            <Skeleton className='h-4 w-96' />
          </div>
          <Skeleton className='h-10 w-32' />
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className='h-6 w-32' />
              </CardHeader>
              <CardContent>
                <Skeleton className='h-8 w-16 mb-2' />
                <Skeleton className='h-4 w-24' />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-6' role='main' aria-label='Time & Energy Management'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Time & Energy Management</h1>
          <p className='text-muted-foreground'>
            Optimize your productivity with AI-powered time blocking and energy tracking
          </p>
        </div>
        <div className='flex items-center space-x-2'>
          <Button onClick={() => setIsTimeBlockModalOpen(true)} aria-label='Create new time block'>
            <Plus className='h-4 w-4 mr-2' />
            Create Time Block
          </Button>
        </div>
      </div>

      {/* Quick Energy Tracking */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Zap className='h-5 w-5' />
            Quick Energy Check-in
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <span className='text-sm font-medium'>How's your energy right now?</span>
              <div className='flex space-x-2'>
                {ENERGY_LEVELS.map(({ value, label, icon: Icon, color }) => (
                  <Button
                    key={value}
                    variant={quickEnergyLevel === value ? 'default' : 'outline'}
                    size='sm'
                    onClick={() => handleQuickEnergyRecord(value)}
                    aria-label={`${label} energy`}
                    className={cn(
                      'flex items-center gap-1',
                      quickEnergyLevel === value && 'bg-primary'
                    )}
                  >
                    <Icon className={cn('h-4 w-4', color)} />
                    {value}
                  </Button>
                ))}
              </div>
            </div>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setIsEnergyModalOpen(true)}
              aria-label='Record detailed energy level'
            >
              Detailed Entry
            </Button>
          </div>

          {currentEnergyStats && (
            <div className='mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4'>
              <div className='text-center p-3 bg-muted rounded-lg'>
                <div className='text-2xl font-bold'>{currentEnergyStats.current}/5</div>
                <div className='text-sm text-muted-foreground'>Current</div>
              </div>
              <div className='text-center p-3 bg-muted rounded-lg'>
                <div className='text-2xl font-bold'>{currentEnergyStats.average}/5</div>
                <div className='text-sm text-muted-foreground'>Today's Average</div>
              </div>
              <div className='text-center p-3 bg-muted rounded-lg'>
                <div
                  className={cn(
                    'text-2xl font-bold',
                    currentEnergyStats.trend > 0
                      ? 'text-green-600'
                      : currentEnergyStats.trend < 0
                        ? 'text-red-600'
                        : 'text-gray-600'
                  )}
                >
                  {currentEnergyStats.trend > 0 ? '+' : ''}
                  {currentEnergyStats.trend}
                </div>
                <div className='text-sm text-muted-foreground'>Trend</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* AI Suggestions */}
      {aiSuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Sparkles className='h-5 w-5' />
              AI Suggested Time Blocks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {aiSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-3 bg-muted rounded-lg'
                >
                  <div className='flex items-center space-x-3'>
                    <Badge variant='secondary'>{suggestion.suggestedTime}</Badge>
                    <div>
                      <div className='font-medium capitalize'>
                        {suggestion.type.replace('_', ' ')}
                      </div>
                      <div className='text-sm text-muted-foreground'>{suggestion.reason}</div>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Badge variant='outline'>
                      {Math.round(suggestion.confidence * 100)}% confidence
                    </Badge>
                    <Button size='sm' variant='outline'>
                      Use Suggestion
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={(value: any) => setActiveTab(value)}
        className='space-y-6'
      >
        <TabsList className='grid w-full grid-cols-5'>
          <TabsTrigger value='overview'>Overview</TabsTrigger>
          <TabsTrigger value='blocks'>Smart Time Blocks</TabsTrigger>
          <TabsTrigger value='energy'>Energy Tracking</TabsTrigger>
          <TabsTrigger value='insights'>Productivity Insights</TabsTrigger>
          <TabsTrigger value='balance'>Work-Life Balance</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value='overview' className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Today's Blocks</CardTitle>
                <Timer className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{timeBlocks.length}</div>
                <p className='text-xs text-muted-foreground'>
                  {timeBlocks.filter(block => block.completed).length} completed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Energy Level</CardTitle>
                <Zap className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{currentEnergyStats?.current || '--'}/5</div>
                <p className='text-xs text-muted-foreground'>
                  {currentEnergyStats?.mood || 'No data'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Productivity Score</CardTitle>
                <TrendingUp className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {productivityInsights?.productivityScore || '--'}%
                </div>
                <p className='text-xs text-muted-foreground'>Based on recent patterns</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Balance Score</CardTitle>
                <Heart className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {workLifeBalance?.workLifeBalanceScore || '--'}%
                </div>
                <p className='text-xs text-muted-foreground'>Work-life balance</p>
              </CardContent>
            </Card>
          </div>

          {/* Today's Schedule */}
          <Card role='region' aria-label="Today's time blocks">
            <CardHeader>
              <CardTitle>Today's Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              {timeBlocks.length === 0 ? (
                <div className='text-center py-8'>
                  <Clock className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
                  <h3 className='text-lg font-medium mb-2'>No time blocks scheduled</h3>
                  <p className='text-muted-foreground mb-4'>
                    Create your first time block to get started.
                  </p>
                  <Button onClick={() => setIsTimeBlockModalOpen(true)}>
                    <Plus className='h-4 w-4 mr-2' />
                    Create Time Block
                  </Button>
                </div>
              ) : (
                <div className='space-y-3'>
                  {timeBlocks.map(block => {
                    const blockType = TIME_BLOCK_TYPES.find(t => t.value === block.type)
                    const Icon = blockType?.icon || Clock

                    return (
                      <div
                        key={block.id}
                        className='flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors'
                      >
                        <div className='flex items-center space-x-3'>
                          <Icon className={cn('h-5 w-5', blockType?.color)} />
                          <div>
                            <div className='font-medium'>{block.title}</div>
                            <div className='text-sm text-muted-foreground'>
                              {format(new Date(block.startTime), 'HH:mm')} -{' '}
                              {format(new Date(block.endTime), 'HH:mm')}
                              {block.energyLevel && (
                                <Badge variant='outline' className='ml-2'>
                                  {block.energyLevel} energy
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className='flex items-center space-x-2'>
                          {block.completed ? (
                            <CheckCircle className='h-5 w-5 text-green-600' />
                          ) : (
                            <Button size='sm' variant='outline'>
                              <Play className='h-4 w-4' />
                            </Button>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Smart Time Blocks Tab */}
        <TabsContent value='blocks' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Smart Time Blocks</CardTitle>
              <p className='text-muted-foreground'>
                AI-optimized time blocking based on your energy patterns and productivity data
              </p>
            </CardHeader>
            <CardContent>
              {/* Time blocks content will be implemented here */}
              <div className='text-center py-8'>
                <Timer className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
                <h3 className='text-lg font-medium mb-2'>Time Blocks View</h3>
                <p className='text-muted-foreground'>Detailed time block management coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Energy Tracking Tab */}
        <TabsContent
          value='energy'
          className='space-y-6'
          role='region'
          aria-label='Energy tracking'
        >
          <Card>
            <CardHeader>
              <CardTitle>Energy Trends</CardTitle>
            </CardHeader>
            <CardContent>
              {energyLevels.length === 0 ? (
                <div className='text-center py-8'>
                  <Activity className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
                  <h3 className='text-lg font-medium mb-2'>No energy data yet</h3>
                  <p className='text-muted-foreground mb-4'>
                    Start tracking your energy levels to see insights.
                  </p>
                  <Button onClick={() => setIsEnergyModalOpen(true)}>
                    <Plus className='h-4 w-4 mr-2' />
                    Record Energy Level
                  </Button>
                </div>
              ) : (
                <div className='space-y-6'>
                  {/* Energy History */}
                  <div className='space-y-3'>
                    <h4 className='font-medium'>Recent Energy Levels</h4>
                    {energyLevels.slice(0, 5).map(level => {
                      const energyConfig = ENERGY_LEVELS.find(e => e.value === level.level)
                      const Icon = energyConfig?.icon || Activity

                      return (
                        <div
                          key={level.id}
                          className='flex items-center justify-between p-3 border rounded-lg'
                        >
                          <div className='flex items-center space-x-3'>
                            <Icon className={cn('h-5 w-5', energyConfig?.color)} />
                            <div>
                              <div className='font-medium'>
                                {energyConfig?.label} ({level.level}/5)
                              </div>
                              <div className='text-sm text-muted-foreground'>
                                {format(new Date(level.timestamp), 'HH:mm')} • {level.mood}
                                {level.context && ` • ${level.context}`}
                              </div>
                            </div>
                          </div>
                          <div className='text-sm text-muted-foreground'>
                            {formatDistanceToNow(new Date(level.timestamp), { addSuffix: true })}
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  {/* Energy Patterns */}
                  {productivityInsights?.energyPatterns && (
                    <div>
                      <h4 className='font-medium mb-3'>Energy Patterns</h4>
                      <div className='grid grid-cols-3 gap-4'>
                        <div className='text-center p-3 bg-muted rounded-lg'>
                          <div className='text-xl font-bold'>
                            {productivityInsights.energyPatterns.morning.toFixed(1)}
                          </div>
                          <div className='text-sm text-muted-foreground'>Morning</div>
                          <div className='text-xs text-muted-foreground'>6AM - 12PM</div>
                        </div>
                        <div className='text-center p-3 bg-muted rounded-lg'>
                          <div className='text-xl font-bold'>
                            {productivityInsights.energyPatterns.afternoon.toFixed(1)}
                          </div>
                          <div className='text-sm text-muted-foreground'>Afternoon</div>
                          <div className='text-xs text-muted-foreground'>12PM - 6PM</div>
                        </div>
                        <div className='text-center p-3 bg-muted rounded-lg'>
                          <div className='text-xl font-bold'>
                            {productivityInsights.energyPatterns.evening.toFixed(1)}
                          </div>
                          <div className='text-sm text-muted-foreground'>Evening</div>
                          <div className='text-xs text-muted-foreground'>6PM - 12AM</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Productivity Insights Tab */}
        <TabsContent value='insights' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Productivity Insights</CardTitle>
            </CardHeader>
            <CardContent>
              {productivityInsights ? (
                <div className='space-y-6'>
                  {/* Key Metrics */}
                  <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                    <div className='text-center p-4 bg-muted rounded-lg'>
                      <div className='text-2xl font-bold'>
                        {productivityInsights.productivityScore}%
                      </div>
                      <div className='text-sm text-muted-foreground'>Productivity Score</div>
                    </div>
                    <div className='text-center p-4 bg-muted rounded-lg'>
                      <div className='text-2xl font-bold'>
                        {productivityInsights.averageEnergyLevel.toFixed(1)}/5
                      </div>
                      <div className='text-sm text-muted-foreground'>Average Energy</div>
                    </div>
                    <div className='text-center p-4 bg-muted rounded-lg'>
                      <div className='text-2xl font-bold'>
                        {productivityInsights.peakProductivityHours.length}
                      </div>
                      <div className='text-sm text-muted-foreground'>Peak Hours</div>
                    </div>
                  </div>

                  {/* Peak Hours */}
                  {productivityInsights.peakProductivityHours.length > 0 && (
                    <div>
                      <h4 className='font-medium mb-3'>
                        Peak Hours: {productivityInsights.peakProductivityHours.join(', ')}
                      </h4>
                    </div>
                  )}

                  {/* AI Recommendations */}
                  {productivityInsights.recommendations.length > 0 && (
                    <div>
                      <h4 className='font-medium mb-3'>AI Recommendations</h4>
                      <div className='space-y-2'>
                        {productivityInsights.recommendations.map((rec, index) => (
                          <div
                            key={index}
                            className='flex items-start space-x-2 p-3 bg-blue-50 rounded-lg'
                          >
                            <Lightbulb className='h-5 w-5 text-blue-600 mt-0.5' />
                            <div className='text-sm'>{rec}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className='text-center py-8'>
                  <Brain className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
                  <h3 className='text-lg font-medium mb-2'>No insights available</h3>
                  <p className='text-muted-foreground'>
                    Track your time and energy for AI-powered insights.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Work-Life Balance Tab */}
        <TabsContent value='balance' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Work-Life Balance</CardTitle>
            </CardHeader>
            <CardContent>
              {workLifeBalance ? (
                <div className='space-y-6'>
                  {/* Balance Score */}
                  <div className='text-center'>
                    <div className='text-4xl font-bold mb-2'>
                      {workLifeBalance.workLifeBalanceScore}%
                    </div>
                    <div className='text-muted-foreground mb-4'>Overall Balance Score</div>
                    <Progress
                      value={workLifeBalance.workLifeBalanceScore}
                      className='w-full max-w-md mx-auto'
                    />
                  </div>

                  {/* Weekly Balance */}
                  <div>
                    <h4 className='font-medium mb-3'>Weekly Time Distribution</h4>
                    <div className='grid grid-cols-3 gap-4'>
                      <div className='text-center p-4 bg-blue-50 rounded-lg'>
                        <div className='text-xl font-bold text-blue-600'>
                          {workLifeBalance.weeklyBalance.workTime.toFixed(1)}h
                        </div>
                        <div className='text-sm text-muted-foreground'>Work</div>
                      </div>
                      <div className='text-center p-4 bg-green-50 rounded-lg'>
                        <div className='text-xl font-bold text-green-600'>
                          {workLifeBalance.weeklyBalance.personalTime.toFixed(1)}h
                        </div>
                        <div className='text-sm text-muted-foreground'>Personal</div>
                      </div>
                      <div className='text-center p-4 bg-purple-50 rounded-lg'>
                        <div className='text-xl font-bold text-purple-600'>
                          {workLifeBalance.weeklyBalance.restTime.toFixed(1)}h
                        </div>
                        <div className='text-sm text-muted-foreground'>Rest</div>
                      </div>
                    </div>
                  </div>

                  {/* Burnout Risk */}
                  <div className='flex items-center justify-between p-4 border rounded-lg'>
                    <div className='flex items-center space-x-3'>
                      <AlertCircle
                        className={cn(
                          'h-5 w-5',
                          workLifeBalance.burnoutRisk === 'high'
                            ? 'text-red-600'
                            : workLifeBalance.burnoutRisk === 'medium'
                              ? 'text-yellow-600'
                              : 'text-green-600'
                        )}
                      />
                      <div>
                        <div className='font-medium'>Burnout Risk</div>
                        <div className='text-sm text-muted-foreground capitalize'>
                          {workLifeBalance.burnoutRisk} risk level
                        </div>
                      </div>
                    </div>
                    <Badge
                      variant={
                        workLifeBalance.burnoutRisk === 'high'
                          ? 'destructive'
                          : workLifeBalance.burnoutRisk === 'medium'
                            ? 'default'
                            : 'secondary'
                      }
                    >
                      {workLifeBalance.burnoutRisk}
                    </Badge>
                  </div>

                  {/* Recommendations */}
                  {workLifeBalance.recommendations.length > 0 && (
                    <div>
                      <h4 className='font-medium mb-3'>Balance Recommendations</h4>
                      <div className='space-y-2'>
                        {workLifeBalance.recommendations.map((rec, index) => (
                          <div
                            key={index}
                            className='flex items-start space-x-2 p-3 bg-green-50 rounded-lg'
                          >
                            <Heart className='h-5 w-5 text-green-600 mt-0.5' />
                            <div className='text-sm'>{rec}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className='text-center py-8'>
                  <Heart className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
                  <h3 className='text-lg font-medium mb-2'>No balance data</h3>
                  <p className='text-muted-foreground'>
                    Track your time blocks to see work-life balance insights.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Time Block Creation Modal */}
      <Dialog open={isTimeBlockModalOpen} onOpenChange={setIsTimeBlockModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Time Block</DialogTitle>
            <DialogDescription>
              Schedule a focused time block for optimal productivity
            </DialogDescription>
          </DialogHeader>

          <div className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='title'>Title</Label>
              <Input
                id='title'
                placeholder='e.g., Deep work on project X'
                value={timeBlockForm.title}
                onChange={e => setTimeBlockForm(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description (optional)</Label>
              <Textarea
                id='description'
                placeholder='Additional details...'
                value={timeBlockForm.description}
                onChange={e => setTimeBlockForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='start-time'>Start Time</Label>
                <Input
                  id='start-time'
                  type='time'
                  value={timeBlockForm.startTime}
                  onChange={e => setTimeBlockForm(prev => ({ ...prev, startTime: e.target.value }))}
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='end-time'>End Time</Label>
                <Input
                  id='end-time'
                  type='time'
                  value={timeBlockForm.endTime}
                  onChange={e => setTimeBlockForm(prev => ({ ...prev, endTime: e.target.value }))}
                />
              </div>
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label>Block Type</Label>
                <Select
                  value={timeBlockForm.type}
                  onValueChange={(value: any) =>
                    setTimeBlockForm(prev => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_BLOCK_TYPES.map(({ value, label }) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label>Expected Energy Level</Label>
                <Select
                  value={timeBlockForm.energyLevel || 'medium'}
                  onValueChange={(value: any) =>
                    setTimeBlockForm(prev => ({ ...prev, energyLevel: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='low'>Low</SelectItem>
                    <SelectItem value='medium'>Medium</SelectItem>
                    <SelectItem value='high'>High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant='outline' onClick={() => setIsTimeBlockModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTimeBlock} aria-label='Create time block'>
              Create Block
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Energy Recording Modal */}
      <Dialog open={isEnergyModalOpen} onOpenChange={setIsEnergyModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Record Energy Level</DialogTitle>
            <DialogDescription>
              Track your current energy and mood for AI insights
            </DialogDescription>
          </DialogHeader>

          <div className='space-y-4'>
            <div className='space-y-2'>
              <Label>Energy Level</Label>
              <div className='flex space-x-2'>
                {ENERGY_LEVELS.map(({ value, label, icon: Icon, color }) => (
                  <Button
                    key={value}
                    variant={energyForm.level === value ? 'default' : 'outline'}
                    size='sm'
                    onClick={() => setEnergyForm(prev => ({ ...prev, level: value }))}
                    className='flex-1'
                  >
                    <Icon className={cn('h-4 w-4 mr-1', color)} />
                    {value}
                  </Button>
                ))}
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='mood'>Mood</Label>
              <Select
                value={energyForm.mood}
                onValueChange={value => setEnergyForm(prev => ({ ...prev, mood: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MOODS.map(mood => (
                    <SelectItem key={mood} value={mood}>
                      {mood.charAt(0).toUpperCase() + mood.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='context'>Context (optional)</Label>
              <Textarea
                id='context'
                placeholder="What's affecting your energy right now?"
                value={energyForm.context}
                onChange={e => setEnergyForm(prev => ({ ...prev, context: e.target.value }))}
              />
            </div>

            <div className='space-y-2'>
              <Label>Contributing Factors</Label>
              <div className='grid grid-cols-2 gap-2'>
                {ENERGY_FACTORS.map(factor => (
                  <Button
                    key={factor}
                    variant={energyForm.factors.includes(factor) ? 'default' : 'outline'}
                    size='sm'
                    onClick={() => {
                      setEnergyForm(prev => ({
                        ...prev,
                        factors: prev.factors.includes(factor)
                          ? prev.factors.filter(f => f !== factor)
                          : [...prev.factors, factor],
                      }))
                    }}
                    className='justify-start'
                  >
                    {factor.charAt(0).toUpperCase() + factor.slice(1).replace('_', ' ')}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant='outline' onClick={() => setIsEnergyModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleRecordEnergy} aria-label='Record energy level'>
              Record Energy
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
