'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/lib/auth/hooks'
import {
  performanceClient,
  type PerformanceMetric,
  type PerformanceInsight,
  type PerformanceEvaluation,
  type PerformanceStats,
} from '@/lib/services/performance-client'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ArrowUpDown,
  FileText,
  BarChart2,
  Target,
  Award,
  Clock,
  Calendar,
  Lightbulb,
  Users,
  Plus,
  ChevronRight,
  TrendingUp,
  TrendingDown,
  Zap,
  AlertCircle,
  CheckCircle,
  Plus as PlusIcon,
} from 'lucide-react'
import { toast } from 'sonner'
import { PerformanceEvaluationForm } from './PerformanceEvaluationForm'

export function PerformanceSection() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState('30d')
  const [activeTab, setActiveTab] = useState('overview')
  const [performanceData, setPerformanceData] = useState<{
    stats: PerformanceStats
    metrics: PerformanceMetric[]
    insights: PerformanceInsight[]
    recentEvaluations?: PerformanceEvaluation[]
  }>({
    stats: {
      totalTasks: 0,
      completedTasks: 0,
      completionRate: 0,
      averageTaskTime: 0,
      goalsAchieved: 0,
      skillsImproved: 0,
      productivityScore: 0,
      engagementScore: 0,
    },
    metrics: [],
    insights: [],
    recentEvaluations: [],
  })
  const [isEvaluationFormOpen, setIsEvaluationFormOpen] = useState(false)

  // Load performance data
  const loadPerformanceData = useCallback(async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      const data = await performanceClient.getPerformanceData(timeframe)
      setPerformanceData(data)
    } catch (error) {
      console.error('Error fetching performance data:', error)
      toast.error('Failed to load performance data')
    } finally {
      setLoading(false)
    }
  }, [user?.id, timeframe])

  // Load data on component mount and when timeframe changes
  useEffect(() => {
    loadPerformanceData()
  }, [loadPerformanceData])

  if (loading) {
    return (
      <div className='space-y-4' data-testid='performance-loading'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            {[1, 2, 3, 4].map(i => (
              <div key={i} className='h-48 bg-gray-200 rounded'></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Helper to get trend icon
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className='h-4 w-4 text-green-500' />
      case 'down':
        return <TrendingDown className='h-4 w-4 text-red-500' />
      default:
        return <ArrowUpDown className='h-4 w-4 text-gray-500' />
    }
  }

  // Helper to get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'productivity':
        return <Clock className='h-4 w-4' />
      case 'skills':
        return <Award className='h-4 w-4' />
      case 'goals':
        return <Target className='h-4 w-4' />
      case 'engagement':
        return <Users className='h-4 w-4' />
      default:
        return <BarChart2 className='h-4 w-4' />
    }
  }

  // Helper to get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'productivity':
        return 'bg-blue-100 text-blue-800'
      case 'skills':
        return 'bg-purple-100 text-purple-800'
      case 'goals':
        return 'bg-green-100 text-green-800'
      case 'engagement':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Get insight icon
  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'strength':
        return <CheckCircle className='h-5 w-5 text-green-500' />
      case 'improvement':
        return <AlertCircle className='h-5 w-5 text-amber-500' />
      case 'suggestion':
        return <Lightbulb className='h-5 w-5 text-blue-500' />
      default:
        return <Lightbulb className='h-5 w-5 text-gray-500' />
    }
  }

  return (
    <div className='space-y-6'>
      {/* Header with actions */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0'>
        <div>
          <h2 className='text-2xl font-semibold tracking-tight'>Performance Evaluation</h2>
          <p className='text-sm text-muted-foreground'>Track your performance metrics and growth</p>
        </div>
        <div className='flex items-center space-x-2'>
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className='w-[150px]'>
              <Calendar className='h-4 w-4 mr-2' />
              <SelectValue placeholder='Select timeframe' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='7d'>Last 7 days</SelectItem>
              <SelectItem value='30d'>Last 30 days</SelectItem>
              <SelectItem value='90d'>Last 90 days</SelectItem>
              <SelectItem value='1y'>Last year</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={() => setIsEvaluationFormOpen(true)}>
            <Plus className='h-4 w-4 mr-2' />
            New Evaluation
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList>
          <TabsTrigger value='overview'>Overview</TabsTrigger>
          <TabsTrigger value='metrics'>Metrics</TabsTrigger>
          <TabsTrigger value='insights'>Insights</TabsTrigger>
          <TabsTrigger value='evaluations'>Evaluations</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value='overview' className='mt-6 space-y-6'>
          {/* Stats */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            <Card>
              <CardHeader className='pb-2'>
                <CardTitle className='text-sm font-medium'>Productivity Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{performanceData.stats.productivityScore}%</div>
                <Progress value={performanceData.stats.productivityScore} className='h-2 mt-2' />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='pb-2'>
                <CardTitle className='text-sm font-medium'>Task Completion</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{performanceData.stats.completionRate}%</div>
                <div className='text-xs text-muted-foreground mt-1'>
                  {performanceData.stats.completedTasks} of {performanceData.stats.totalTasks} tasks
                  completed
                </div>
                <Progress value={performanceData.stats.completionRate} className='h-2 mt-2' />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='pb-2'>
                <CardTitle className='text-sm font-medium'>Goals Achieved</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{performanceData.stats.goalsAchieved}</div>
                <div className='text-xs text-muted-foreground mt-1'>During selected timeframe</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='pb-2'>
                <CardTitle className='text-sm font-medium'>Skills Improved</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{performanceData.stats.skillsImproved}</div>
                <div className='text-xs text-muted-foreground mt-1'>During selected timeframe</div>
              </CardContent>
            </Card>
          </div>

          {/* Key metrics */}
          <div>
            <h3 className='text-lg font-medium mb-4'>Key Performance Metrics</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {performanceData.metrics.slice(0, 4).map(metric => (
                <Card key={metric.id}>
                  <CardHeader className='pb-2'>
                    <div className='flex justify-between'>
                      <CardTitle className='text-sm font-medium flex items-center'>
                        {getCategoryIcon(metric.category)}
                        <span className='ml-2'>{metric.name}</span>
                      </CardTitle>
                      <Badge variant='outline' className={getCategoryColor(metric.category)}>
                        {metric.category}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='flex justify-between items-baseline'>
                      <div className='text-2xl font-bold'>
                        {metric.value}
                        {metric.unit}
                      </div>
                      <div className='flex items-center text-sm'>
                        {getTrendIcon(metric.trend)}
                        <span
                          className={
                            metric.trend === 'up'
                              ? 'text-green-600'
                              : metric.trend === 'down'
                                ? 'text-red-600'
                                : 'text-gray-600'
                          }
                        >
                          {metric.trendValue > 0 ? '+' : ''}
                          {metric.trendValue}
                          {metric.unit}
                        </span>
                      </div>
                    </div>
                    {metric.description && (
                      <div className='text-xs text-muted-foreground mt-2'>{metric.description}</div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
            {performanceData.metrics.length > 4 && (
              <div className='mt-4 text-center'>
                <Button variant='outline' onClick={() => setActiveTab('metrics')}>
                  View All Metrics
                  <ChevronRight className='h-4 w-4 ml-2' />
                </Button>
              </div>
            )}
          </div>

          {/* Key insights */}
          <div>
            <h3 className='text-lg font-medium mb-4'>Performance Insights</h3>
            <div className='space-y-4'>
              {performanceData.insights.slice(0, 3).map(insight => (
                <Card key={insight.id}>
                  <div className='flex p-4'>
                    <div className='mr-4 mt-0.5'>{getInsightIcon(insight.category)}</div>
                    <div>
                      <h4 className='font-medium mb-1'>{insight.title}</h4>
                      <p className='text-sm text-muted-foreground'>{insight.description}</p>

                      {insight.actionItems && insight.actionItems.length > 0 && (
                        <div className='mt-2'>
                          <h5 className='text-xs font-medium text-muted-foreground mb-1'>
                            Suggested actions:
                          </h5>
                          <ul className='text-sm space-y-1'>
                            {insight.actionItems.map((action, i) => (
                              <li key={i} className='flex items-start'>
                                <ArrowRight className='h-3 w-3 mr-2 mt-1 flex-shrink-0' />
                                <span>{action}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
            {performanceData.insights.length > 3 && (
              <div className='mt-4 text-center'>
                <Button variant='outline' onClick={() => setActiveTab('insights')}>
                  View All Insights
                  <ChevronRight className='h-4 w-4 ml-2' />
                </Button>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Metrics Tab */}
        <TabsContent value='metrics' className='mt-6 space-y-6'>
          <h3 className='text-lg font-medium mb-4'>All Performance Metrics</h3>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            {performanceData.metrics.map(metric => (
              <Card key={metric.id}>
                <CardHeader className='pb-2'>
                  <div className='flex justify-between'>
                    <CardTitle className='text-sm font-medium flex items-center'>
                      {getCategoryIcon(metric.category)}
                      <span className='ml-2'>{metric.name}</span>
                    </CardTitle>
                    <Badge variant='outline' className={getCategoryColor(metric.category)}>
                      {metric.category}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='flex justify-between items-baseline'>
                    <div className='text-2xl font-bold'>
                      {metric.value}
                      {metric.unit}
                    </div>
                    <div className='flex items-center text-sm'>
                      {getTrendIcon(metric.trend)}
                      <span
                        className={
                          metric.trend === 'up'
                            ? 'text-green-600'
                            : metric.trend === 'down'
                              ? 'text-red-600'
                              : 'text-gray-600'
                        }
                      >
                        {metric.trendValue > 0 ? '+' : ''}
                        {metric.trendValue}
                        {metric.unit}
                      </span>
                    </div>
                  </div>
                  {metric.description && (
                    <div className='text-xs text-muted-foreground mt-2'>{metric.description}</div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value='insights' className='mt-6 space-y-6'>
          <h3 className='text-lg font-medium mb-4'>All Performance Insights</h3>
          <div className='space-y-4'>
            {performanceData.insights.map(insight => (
              <Card key={insight.id}>
                <div className='flex p-4'>
                  <div className='mr-4 mt-0.5'>{getInsightIcon(insight.category)}</div>
                  <div>
                    <h4 className='font-medium mb-1'>{insight.title}</h4>
                    <p className='text-sm text-muted-foreground'>{insight.description}</p>

                    {insight.actionItems && insight.actionItems.length > 0 && (
                      <div className='mt-2'>
                        <h5 className='text-xs font-medium text-muted-foreground mb-1'>
                          Suggested actions:
                        </h5>
                        <ul className='text-sm space-y-1'>
                          {insight.actionItems.map((action, i) => (
                            <li key={i} className='flex items-start'>
                              <ArrowRight className='h-3 w-3 mr-2 mt-1 flex-shrink-0' />
                              <span>{action}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Evaluations Tab */}
        <TabsContent value='evaluations' className='mt-6 space-y-6'>
          <div className='flex justify-between items-center mb-4'>
            <h3 className='text-lg font-medium'>Performance Evaluations</h3>
            <Button onClick={() => setIsEvaluationFormOpen(true)}>
              <Plus className='h-4 w-4 mr-2' />
              New Evaluation
            </Button>
          </div>

          {!performanceData.recentEvaluations || performanceData.recentEvaluations.length === 0 ? (
            <div className='text-center p-8 border rounded-lg'>
              <div className='flex justify-center mb-4'>
                <FileText className='h-12 w-12 text-muted-foreground' />
              </div>
              <h3 className='text-lg font-semibold mb-2'>No evaluations yet</h3>
              <p className='text-muted-foreground mb-4'>
                Start tracking your performance with regular evaluations
              </p>
              <Button onClick={() => setIsEvaluationFormOpen(true)}>
                <Plus className='h-4 w-4 mr-2' />
                Create your first evaluation
              </Button>
            </div>
          ) : (
            <div className='space-y-4'>
              {performanceData.recentEvaluations.map(evaluation => (
                <Card key={evaluation.id}>
                  <CardHeader>
                    <div className='flex justify-between items-start'>
                      <div>
                        <CardTitle>{evaluation.title}</CardTitle>
                        <CardDescription>
                          {new Date(evaluation.date).toLocaleDateString()} •{' '}
                          {evaluation.evaluationType} evaluation
                        </CardDescription>
                      </div>
                      <Badge
                        variant={
                          evaluation.rating >= 4
                            ? 'success'
                            : evaluation.rating >= 3
                              ? 'default'
                              : 'outline'
                        }
                      >
                        Rating: {evaluation.rating}/5
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-4'>
                      {/* Strengths */}
                      {evaluation.strengths && evaluation.strengths.length > 0 && (
                        <div>
                          <h4 className='text-sm font-medium mb-2 flex items-center'>
                            <CheckCircle className='h-4 w-4 mr-2 text-green-500' />
                            Key Strengths
                          </h4>
                          <div className='flex flex-wrap gap-1'>
                            {evaluation.strengths.map((strength, i) => (
                              <Badge key={i} variant='secondary'>
                                {strength}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Areas for Improvement */}
                      {evaluation.areasForImprovement &&
                        evaluation.areasForImprovement.length > 0 && (
                          <div>
                            <h4 className='text-sm font-medium mb-2 flex items-center'>
                              <AlertCircle className='h-4 w-4 mr-2 text-amber-500' />
                              Areas for Improvement
                            </h4>
                            <div className='flex flex-wrap gap-1'>
                              {evaluation.areasForImprovement.map((area, i) => (
                                <Badge key={i} variant='outline'>
                                  {area}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                      {/* Feedback preview */}
                      {evaluation.feedback && (
                        <div>
                          <h4 className='text-sm font-medium mb-1'>Feedback</h4>
                          <p className='text-sm text-muted-foreground line-clamp-2'>
                            {evaluation.feedback}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant='outline' size='sm' className='w-full'>
                      View Details
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Performance Evaluation Form Dialog */}
      <Dialog open={isEvaluationFormOpen} onOpenChange={setIsEvaluationFormOpen}>
        <DialogContent className='max-w-3xl max-h-[90vh] overflow-y-auto'>
          <DialogHeader>
            <DialogTitle>New Performance Evaluation</DialogTitle>
            <DialogDescription>
              Assess your performance and identify areas for growth
            </DialogDescription>
          </DialogHeader>

          <PerformanceEvaluationForm
            onSuccess={() => {
              setIsEvaluationFormOpen(false)
              loadPerformanceData()
              toast.success('Performance evaluation saved successfully')
            }}
            onCancel={() => setIsEvaluationFormOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
