import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TimeframeOKRView } from '../TimeframeOKRView'
import { vi } from 'vitest'

// Mock the OKRVisualization component
vi.mock('../OKRVisualization', () => ({
  OKRVisualization: ({ objectives, timeframe }: { objectives: any[]; timeframe: string }) => (
    <div data-testid={`okr-visualization-${timeframe}`}>
      {objectives.length} objectives for {timeframe}
    </div>
  ),
}))

describe('TimeframeOKRView', () => {
  const mockGoals = [
    {
      id: '1',
      title: 'Quarterly Objective 1',
      description: 'First quarterly objective',
      type: 'OBJECTIVE',
      status: 'IN_PROGRESS',
      progress: 40,
      category: 'PROFESSIONAL',
      timeframe: 'QUARTERLY',
      targetDate: '2025-06-30',
      priority: 'high',
      keyResults: [
        {
          id: 'kr1',
          title: 'Key Result 1',
          description: 'First key result',
          targetValue: 100,
          currentValue: 40,
          unit: '%',
          status: 'in_progress',
          dueDate: '2025-06-30',
        },
      ],
    },
    {
      id: '2',
      title: 'Monthly Objective 1',
      description: 'First monthly objective',
      type: 'OBJECTIVE',
      status: 'IN_PROGRESS',
      progress: 60,
      category: 'PERSONAL',
      timeframe: 'MONTHLY',
      targetDate: '2025-02-28',
      priority: 'medium',
      keyResults: [],
    },
    {
      id: '3',
      title: 'Monthly Objective 2',
      description: 'Second monthly objective',
      type: 'OBJECTIVE',
      status: 'ON_TRACK',
      progress: 80,
      category: 'LEARNING',
      timeframe: 'MONTHLY',
      targetDate: '2025-02-28',
      priority: 'high',
      keyResults: [],
    },
    {
      id: '4',
      title: 'Yearly Objective 1',
      description: 'First yearly objective',
      type: 'OBJECTIVE',
      status: 'NOT_STARTED',
      progress: 0,
      category: 'PROFESSIONAL',
      timeframe: 'YEARLY',
      targetDate: '2025-12-31',
      priority: 'high',
      keyResults: [],
    },
  ]

  it('shows loading state when isLoading is true', () => {
    render(<TimeframeOKRView goals={[]} isLoading={true} />)

    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument()
    expect(screen.queryByRole('tablist')).not.toBeInTheDocument()
  })

  it('defaults to QUARTERLY tab when rendered', () => {
    render(<TimeframeOKRView goals={mockGoals} isLoading={false} />)

    const quarterlyTab = screen.getByRole('tab', { name: /quarterly/i })
    expect(quarterlyTab).toHaveAttribute('data-state', 'active')

    expect(screen.getByTestId('okr-visualization-QUARTERLY')).toBeInTheDocument()
    expect(screen.getByText('1 objectives for QUARTERLY')).toBeInTheDocument()
  })

  it('shows count badges for timeframes with objectives', () => {
    render(<TimeframeOKRView goals={mockGoals} isLoading={false} />)

    // Quarterly has 1 objective
    const quarterlyTab = screen.getByRole('tab', { name: /quarterly/i })
    expect(quarterlyTab).toHaveTextContent('1')

    // Monthly has 2 objectives
    const monthlyTab = screen.getByRole('tab', { name: /monthly/i })
    expect(monthlyTab).toHaveTextContent('2')

    // Yearly has 1 objective
    const yearlyTab = screen.getByRole('tab', { name: /yearly/i })
    expect(yearlyTab).toHaveTextContent('1')

    // Daily has 0 objectives, should not have a badge
    const dailyTab = screen.getByRole('tab', { name: /daily/i })
    expect(dailyTab).not.toHaveTextContent(/\d+/)
  })

  it('disables tabs for timeframes with no objectives', () => {
    render(<TimeframeOKRView goals={mockGoals} isLoading={false} />)

    // Daily has no objectives
    const dailyTab = screen.getByRole('tab', { name: /daily/i })
    expect(dailyTab).toBeDisabled()

    // Weekly has no objectives
    const weeklyTab = screen.getByRole('tab', { name: /weekly/i })
    expect(weeklyTab).toBeDisabled()

    // Monthly has objectives
    const monthlyTab = screen.getByRole('tab', { name: /monthly/i })
    expect(monthlyTab).not.toBeDisabled()
  })

  it('switches to another timeframe when tab is clicked', async () => {
    const user = userEvent.setup()
    render(<TimeframeOKRView goals={mockGoals} isLoading={false} />)

    // Initially on Quarterly tab
    expect(screen.getByTestId('okr-visualization-QUARTERLY')).toBeInTheDocument()

    // Click on Monthly tab
    const monthlyTab = screen.getByRole('tab', { name: /monthly/i })
    await user.click(monthlyTab)

    // Should show Monthly content
    await waitFor(() => {
      expect(screen.getByTestId('okr-visualization-MONTHLY')).toBeInTheDocument()
      expect(screen.getByText('2 objectives for MONTHLY')).toBeInTheDocument()
    })

    // Quarterly content should be hidden
    expect(screen.queryByTestId('okr-visualization-QUARTERLY')).not.toBeInTheDocument()
  })

  it('defaults to first available timeframe if QUARTERLY has no objectives', () => {
    // Create mock goals with no quarterly objectives
    const goalsWithoutQuarterly = mockGoals.filter(g => g.timeframe !== 'QUARTERLY')

    render(<TimeframeOKRView goals={goalsWithoutQuarterly} isLoading={false} />)

    // Should default to Monthly as it's the first timeframe with objectives
    const monthlyTab = screen.getByRole('tab', { name: /monthly/i })
    expect(monthlyTab).toHaveAttribute('data-state', 'active')

    expect(screen.getByTestId('okr-visualization-MONTHLY')).toBeInTheDocument()
  })

  it('groups goals correctly by timeframe', () => {
    render(<TimeframeOKRView goals={mockGoals} isLoading={false} />)

    // Switch to Monthly tab
    const user = userEvent.setup()
    const monthlyTab = screen.getByRole('tab', { name: /monthly/i })
    user.click(monthlyTab)

    // Verify Monthly tab shows 2 objectives
    waitFor(() => {
      expect(screen.getByText('2 objectives for MONTHLY')).toBeInTheDocument()
    })

    // Switch to Yearly tab
    const yearlyTab = screen.getByRole('tab', { name: /yearly/i })
    user.click(yearlyTab)

    // Verify Yearly tab shows 1 objective
    waitFor(() => {
      expect(screen.getByText('1 objectives for YEARLY')).toBeInTheDocument()
    })
  })
})
