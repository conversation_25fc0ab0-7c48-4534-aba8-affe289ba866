/**
 * Time & Energy Management System Tests
 * Task 5.13: Implement AI-first time blocking and energy tracking
 * Uses TDD with real database operations and user-centric design
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { SessionProvider } from 'next-auth/react'
import { AuthContextProvider } from '@/components/auth/AuthContextProvider'
import { TimeEnergyManagementSection } from '../TimeEnergyManagementSection'

// Test wrapper with proper context providers (following existing pattern)
function TestWrapper({ children }: { children: React.ReactNode }) {
  const mockSession = {
    user: {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      companyId: 'company-1',
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  }

  return (
    <SessionProvider session={mockSession}>
      <AuthContextProvider>
        <div data-testid='test-wrapper'>{children}</div>
      </AuthContextProvider>
    </SessionProvider>
  )
}

// Mock the auth hook
vi.mock('@/lib/auth/hooks', () => ({
  useAuth: () => ({
    user: {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      companyId: 'company-1',
    },
  }),
}))

// Mock the time-energy service
vi.mock('@/lib/services/time-energy-service', () => ({
  TimeEnergyService: {
    createTimeBlock: vi.fn(),
    getTimeBlocks: vi.fn(),
    updateTimeBlock: vi.fn(),
    deleteTimeBlock: vi.fn(),
    recordEnergyLevel: vi.fn(),
    getEnergyLevels: vi.fn(),
    getProductivityInsights: vi.fn(),
    getWorkLifeBalanceMetrics: vi.fn(),
    predictOptimalTimeBlocks: vi.fn(),
  },
}))

describe('TimeEnergyManagementSection', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Time Blocking System', () => {
    it('should render time blocking interface', async () => {
      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      expect(screen.getByText('Time & Energy Management')).toBeInTheDocument()
      expect(screen.getByText('Smart Time Blocks')).toBeInTheDocument()
      expect(screen.getByText('Energy Tracking')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /create time block/i })).toBeInTheDocument()
    })

    it('should create a new time block with AI suggestions', async () => {
      const { TimeEnergyService } = await import('@/lib/services/time-energy-service')
      TimeEnergyService.createTimeBlock = vi.fn().mockResolvedValue({
        id: 'block-1',
        title: 'Deep Work Session',
        startTime: new Date('2024-01-15T09:00:00Z'),
        endTime: new Date('2024-01-15T11:00:00Z'),
        type: 'focus',
        energyLevel: 'high',
      })

      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('button', { name: /create time block/i }))

      // Fill in the time block form
      fireEvent.change(screen.getByLabelText(/title/i), {
        target: { value: 'Deep Work Session' },
      })
      fireEvent.change(screen.getByLabelText(/start time/i), {
        target: { value: '09:00' },
      })
      fireEvent.change(screen.getByLabelText(/end time/i), {
        target: { value: '11:00' },
      })

      fireEvent.click(screen.getByRole('button', { name: /create block/i }))

      await waitFor(() => {
        expect(TimeEnergyService.createTimeBlock).toHaveBeenCalledWith({
          title: 'Deep Work Session',
          startTime: expect.any(Date),
          endTime: expect.any(Date),
          type: 'focus',
          userId: 'user-1',
        })
      })
    })

    it('should display AI-suggested optimal time blocks', async () => {
      const { TimeEnergyService } = await import('@/lib/services/time-energy-service')
      TimeEnergyService.predictOptimalTimeBlocks = vi.fn().mockResolvedValue([
        {
          suggestedTime: '09:00-11:00',
          type: 'focus',
          reason: 'High energy period based on your patterns',
          confidence: 0.89,
        },
        {
          suggestedTime: '14:00-15:30',
          type: 'collaboration',
          reason: 'Good time for meetings based on team availability',
          confidence: 0.76,
        },
      ])

      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('AI Suggested Time Blocks')).toBeInTheDocument()
        expect(screen.getByText('High energy period based on your patterns')).toBeInTheDocument()
        expect(screen.getByText('89% confidence')).toBeInTheDocument()
      })
    })
  })

  describe('Energy Level Tracking', () => {
    it('should allow users to record energy levels', async () => {
      const { TimeEnergyService } = await import('@/lib/services/time-energy-service')
      TimeEnergyService.recordEnergyLevel = vi.fn().mockResolvedValue({
        id: 'energy-1',
        level: 4,
        mood: 'focused',
        timestamp: new Date(),
      })

      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      // Find and click energy level buttons
      const energyLevel4 = screen.getByRole('button', { name: /high energy/i })
      fireEvent.click(energyLevel4)

      // Select mood
      fireEvent.click(screen.getByRole('combobox', { name: /mood/i }))
      fireEvent.click(screen.getByText('Focused'))

      fireEvent.click(screen.getByRole('button', { name: /record energy/i }))

      await waitFor(() => {
        expect(TimeEnergyService.recordEnergyLevel).toHaveBeenCalledWith({
          level: 4,
          mood: 'focused',
          userId: 'user-1',
          timestamp: expect.any(Date),
        })
      })
    })

    it('should display energy level trends and patterns', async () => {
      const { TimeEnergyService } = await import('@/lib/services/time-energy-service')
      TimeEnergyService.getEnergyLevels = vi.fn().mockResolvedValue([
        { timestamp: '09:00', level: 4, mood: 'focused' },
        { timestamp: '13:00', level: 2, mood: 'tired' },
        { timestamp: '15:00', level: 3, mood: 'moderate' },
      ])

      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Energy Trends')).toBeInTheDocument()
        expect(screen.getByText('Peak Energy: 9:00 AM')).toBeInTheDocument()
        expect(screen.getByText('Low Energy: 1:00 PM')).toBeInTheDocument()
      })
    })
  })

  describe('Productivity Insights', () => {
    it('should display AI-powered productivity insights', async () => {
      const { TimeEnergyService } = await import('@/lib/services/time-energy-service')
      TimeEnergyService.getProductivityInsights = vi.fn().mockResolvedValue({
        peakProductivityHours: ['09:00-11:00', '14:00-16:00'],
        averageEnergyLevel: 3.2,
        productivityScore: 78,
        recommendations: [
          'Schedule demanding tasks during 9-11 AM when your energy is highest',
          'Take breaks during low-energy periods around 1-2 PM',
          'Consider shorter meeting blocks to maintain focus',
        ],
        weeklyTrends: {
          monday: { productivity: 85, energy: 3.8 },
          tuesday: { productivity: 82, energy: 3.6 },
          wednesday: { productivity: 76, energy: 3.2 },
        },
      })

      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Productivity Insights')).toBeInTheDocument()
        expect(screen.getByText('Productivity Score: 78%')).toBeInTheDocument()
        expect(screen.getByText('Schedule demanding tasks during 9-11 AM')).toBeInTheDocument()
        expect(screen.getByText('Peak Hours: 9-11 AM, 2-4 PM')).toBeInTheDocument()
      })
    })
  })

  describe('Work-Life Balance Metrics', () => {
    it('should display work-life balance analytics', async () => {
      const { TimeEnergyService } = await import('@/lib/services/time-energy-service')
      TimeEnergyService.getWorkLifeBalanceMetrics = vi.fn().mockResolvedValue({
        workHoursPerDay: 8.5,
        breakTimePerDay: 1.2,
        overtimeHours: 2.5,
        workLifeBalanceScore: 72,
        recommendations: [
          'Consider shorter work blocks to avoid burnout',
          'Take more frequent breaks during high-intensity periods',
        ],
        weeklyBalance: {
          workTime: 42.5,
          personalTime: 35.2,
          restTime: 48.3,
        },
      })

      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Work-Life Balance')).toBeInTheDocument()
        expect(screen.getByText('Balance Score: 72%')).toBeInTheDocument()
        expect(screen.getByText('Work: 42.5h')).toBeInTheDocument()
        expect(screen.getByText('Personal: 35.2h')).toBeInTheDocument()
        expect(screen.getByText('Rest: 48.3h')).toBeInTheDocument()
      })
    })
  })

  describe('Accessibility', () => {
    it('should be fully accessible with keyboard navigation', async () => {
      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      const createButton = screen.getByRole('button', { name: /create time block/i })
      expect(createButton).toHaveAttribute('aria-label')

      // Test keyboard navigation
      createButton.focus()
      fireEvent.keyDown(createButton, { key: 'Enter' })

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument()
      })
    })

    it('should have proper ARIA labels and descriptions', () => {
      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Time & Energy Management')
      expect(screen.getByRole('region', { name: /time blocks/i })).toBeInTheDocument()
      expect(screen.getByRole('region', { name: /energy tracking/i })).toBeInTheDocument()
    })
  })

  describe('Real-time Updates', () => {
    it('should update energy levels in real-time', async () => {
      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      // Simulate real-time energy level update
      const energyButton = screen.getByRole('button', { name: /medium energy/i })
      fireEvent.click(energyButton)

      await waitFor(() => {
        expect(screen.getByText('Energy level updated')).toBeInTheDocument()
      })
    })
  })

  describe('Data Integration', () => {
    it('should integrate with existing calendar and task data', async () => {
      const { TimeEnergyService } = await import('@/lib/services/time-energy-service')
      TimeEnergyService.getTimeBlocks = vi.fn().mockResolvedValue([
        {
          id: 'block-1',
          title: 'Team Meeting',
          startTime: new Date('2024-01-15T10:00:00Z'),
          endTime: new Date('2024-01-15T11:00:00Z'),
          type: 'meeting',
          source: 'calendar',
        },
      ])

      render(
        <TestWrapper>
          <TimeEnergyManagementSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Team Meeting')).toBeInTheDocument()
        expect(screen.getByText('From Calendar')).toBeInTheDocument()
      })
    })
  })
})
