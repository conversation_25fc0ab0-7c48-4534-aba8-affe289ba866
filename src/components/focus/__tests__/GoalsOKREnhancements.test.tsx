import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { prisma } from '@/lib/prisma'
import { GoalsSection } from '../GoalsSection'
import { toast } from 'sonner'

// Test wrapper with required providers
function TestWrapper({ children }: { children: React.ReactNode }) {
  return <div data-testid='test-wrapper'>{children}</div>
}

import { vi, beforeAll, beforeEach, afterAll } from 'vitest'

// Test data setup - following TDD principles with real data
let testUser: { id: string; companyId: string }
let testCompany: { id: string }

// Mock DOM methods for test environment to fix Radix UI errors
beforeAll(() => {
  // Mock pointer capture
  Object.defineProperty(Element.prototype, 'hasPointerCapture', {
    value: vi.fn(() => false),
    writable: true,
  })

  Object.defineProperty(Element.prototype, 'setPointerCapture', {
    value: vi.fn(),
    writable: true,
  })

  Object.defineProperty(Element.prototype, 'releasePointerCapture', {
    value: vi.fn(),
    writable: true,
  })

  // Mock scrollIntoView
  Object.defineProperty(Element.prototype, 'scrollIntoView', {
    value: vi.fn(),
    writable: true,
  })

  // Mock createObjectURL for export functionality
  Object.defineProperty(window.URL, 'createObjectURL', {
    value: vi.fn(() => 'mock-url'),
    writable: true,
  })

  Object.defineProperty(window.URL, 'revokeObjectURL', {
    value: vi.fn(),
    writable: true,
  })
})

// Mock auth hook with real test user data
vi.mock('@/lib/auth/hooks', () => ({
  useAuth: () => ({
    user: {
      id: testUser?.id || 'test-user-123',
      companyId: testUser?.companyId || 'test-company-123',
      role: 'EMPLOYEE',
    },
  }),
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    refresh: vi.fn(),
  }),
}))

// Mock sonner for toast notifications in test environment
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

describe('GoalsSection - Enhanced OKR Features', () => {
  beforeEach(async () => {
    // Clean up test data following proper cascade order
    await prisma.goalUpdate.deleteMany()
    await prisma.goalCollaborator.deleteMany()
    await prisma.keyResult.deleteMany()
    await prisma.goal.deleteMany()
    await prisma.userProfile.deleteMany()
    await prisma.user.deleteMany()
    await prisma.company.deleteMany()

    // Create test company first (required for foreign key constraint)
    testCompany = await prisma.company.create({
      data: {
        id: 'test-company-123',
        name: 'Test Company',
        domains: ['test.com'],
        subscriptionStatus: 'TRIAL',
        isActive: true,
        maxUsers: 10,
      },
    })

    // Create test user (required for foreign key constraint)
    testUser = await prisma.user.create({
      data: {
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'EMPLOYEE',
        companyId: testCompany.id,
        emailVerified: new Date(),
        onboardingCompleted: true,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data in proper order
    await prisma.goalUpdate.deleteMany()
    await prisma.goalCollaborator.deleteMany()
    await prisma.keyResult.deleteMany()
    await prisma.goal.deleteMany()
    await prisma.userProfile.deleteMany()
    await prisma.user.deleteMany()
    await prisma.company.deleteMany()
    await prisma.$disconnect()
  })

  describe('Advanced Goal Visualization', () => {
    it('should display hierarchical goal relationships', async () => {
      // Create parent and child goals with proper foreign key relationships
      const parentGoal = await prisma.goal.create({
        data: {
          title: 'Increase Company Revenue',
          type: 'OBJECTIVE',
          category: 'COMPANY',
          timeframe: 'YEARLY',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      })

      const childGoal = await prisma.goal.create({
        data: {
          title: 'Launch New Product Line',
          type: 'KEY_RESULT',
          category: 'COMPANY',
          timeframe: 'QUARTERLY',
          parentGoalId: parentGoal.id,
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      // Wait for goals to load
      await waitFor(() => {
        expect(screen.getByText('Increase Company Revenue')).toBeInTheDocument()
      })

      // Should show parent goal
      expect(screen.getByText('Increase Company Revenue')).toBeInTheDocument()

      // Should show child goal indented under parent
      expect(screen.getByText('Launch New Product Line')).toBeInTheDocument()

      // Should indicate goal hierarchy with visual indicators
      expect(screen.getByTestId('goal-hierarchy-indicator')).toBeInTheDocument()
    })

    it('should display progress visualization with charts', async () => {
      const goal = await prisma.goal.create({
        data: {
          title: 'Complete React Certification',
          progress: 65,
          category: 'LEARNING',
          timeframe: 'MONTHLY',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Complete React Certification')).toBeInTheDocument()
      })

      // Should show progress visualization
      expect(screen.getByTestId('goal-progress-chart')).toBeInTheDocument()
      expect(screen.getByText('65%')).toBeInTheDocument()

      // Should show progress bar
      const progressBar = screen.getByRole('progressbar')
      expect(progressBar).toHaveAttribute('aria-valuenow', '65')
    })

    it('should display key results with progress indicators', async () => {
      const goal = await prisma.goal.create({
        data: {
          title: 'Improve Team Performance',
          category: 'TEAM',
          timeframe: 'QUARTERLY',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      })

      const keyResult = await prisma.keyResult.create({
        data: {
          title: 'Reduce bug count by 50%',
          targetValue: 50,
          currentValue: 30,
          unit: 'bugs',
          goalId: goal.id,
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Improve Team Performance')).toBeInTheDocument()
      })

      // Should show key results section
      expect(screen.getByText('Key Results')).toBeInTheDocument()
      expect(screen.getByText('Reduce bug count by 50%')).toBeInTheDocument()

      // Should show progress towards key result
      expect(screen.getByText('30/50 bugs')).toBeInTheDocument()

      // Should show progress percentage
      expect(screen.getByText('60%')).toBeInTheDocument() // 30/50 = 60%
    })
  })

  describe('Milestone Tracking', () => {
    it('should display milestone goals with celebration indicators', async () => {
      const milestoneGoal = await prisma.goal.create({
        data: {
          title: 'Complete Project Phase 1',
          type: 'MILESTONE',
          status: 'COMPLETED',
          category: 'PROFESSIONAL',
          timeframe: 'MONTHLY',
          completedAt: new Date(),
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Complete Project Phase 1')).toBeInTheDocument()
      })

      // Should show milestone indicator
      expect(screen.getByTestId('milestone-indicator')).toBeInTheDocument()

      // Should show celebration for completed milestone
      expect(screen.getByTestId('celebration-indicator')).toBeInTheDocument()
      expect(screen.getByText('🎉')).toBeInTheDocument()
    })

    it('should allow creating milestone goals', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      // Wait for component to load (not showing loading state)
      await waitFor(() => {
        expect(screen.queryByTestId('goals-loading')).not.toBeInTheDocument()
      })

      // Click create goal button
      const createButton = screen.getByRole('button', { name: /create goal/i })
      await act(async () => {
        await user.click(createButton)
      })

      // Should show milestone option in goal type
      await waitFor(() => {
        expect(screen.getByLabelText(/title/i)).toBeInTheDocument()
      })

      // Fill in milestone details
      await user.type(screen.getByLabelText(/title/i), 'Launch MVP')

      // Select milestone type (first combobox without aria-label)
      const comboboxes = screen.getAllByRole('combobox')
      const typeSelect = comboboxes[0] // First combobox is type
      await user.click(typeSelect)

      // Wait for dropdown to open and select milestone from the options
      await waitFor(() => {
        expect(screen.getByRole('option', { name: 'Milestone' })).toBeInTheDocument()
      })
      await user.click(screen.getByRole('option', { name: 'Milestone' }))

      // Fill in required fields
      const categorySelect = comboboxes[1] // Second combobox is category
      await user.click(categorySelect)
      await waitFor(() => {
        expect(screen.getByRole('option', { name: 'Professional' })).toBeInTheDocument()
      })
      await user.click(screen.getByRole('option', { name: 'Professional' }))

      const timeframeSelect = comboboxes[2] // Third combobox is timeframe
      await user.click(timeframeSelect)
      await waitFor(() => {
        expect(screen.getByRole('option', { name: 'Monthly' })).toBeInTheDocument()
      })
      await user.click(screen.getByRole('option', { name: 'Monthly' }))

      const prioritySelect = comboboxes[3] // Fourth combobox is priority
      await user.click(prioritySelect)
      await waitFor(() => {
        expect(screen.getByRole('option', { name: 'High' })).toBeInTheDocument()
      })
      await user.click(screen.getByRole('option', { name: 'High' }))

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create/i })
      await act(async () => {
        await user.click(submitButton)
      })

      // Should create milestone goal
      await waitFor(
        () => {
          expect(screen.getByText('Launch MVP')).toBeInTheDocument()
        },
        { timeout: 10000 }
      )
    })
  })

  describe('Reminder System', () => {
    it('should show check-in reminders for overdue goals', async () => {
      // Create goal that needs check-in (no recent updates)
      const goal = await prisma.goal.create({
        data: {
          title: 'Weekly Fitness Goal',
          timeframe: 'WEEKLY',
          category: 'HEALTH',
          status: 'IN_PROGRESS',
          userId: testUser.id,
          companyId: testUser.companyId,
          updatedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 days ago
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Weekly Fitness Goal')).toBeInTheDocument()
      })

      // Should show reminder indicator
      expect(screen.getByTestId('reminder-indicator')).toBeInTheDocument()
      expect(screen.getByText('Check-in overdue')).toBeInTheDocument()
    })

    it('should allow quick check-in updates', async () => {
      const user = userEvent.setup()

      const goal = await prisma.goal.create({
        data: {
          title: 'Daily Reading',
          timeframe: 'DAILY',
          category: 'PERSONAL',
          status: 'IN_PROGRESS',
          progress: 50,
          userId: testUser.id,
          companyId: testUser.companyId,
          updatedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 days ago
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Daily Reading')).toBeInTheDocument()
      })

      // Should show quick check-in button
      const checkInButton = screen.getByRole('button', { name: /quick check-in/i })
      await user.click(checkInButton)

      // Should show check-in modal
      await waitFor(() => {
        expect(screen.getByRole('heading', { name: 'Quick Check-in' })).toBeInTheDocument()
      })

      // Fill in check-in details
      const progressInput = screen.getByLabelText(/progress/i)
      await user.clear(progressInput)
      await user.type(progressInput, '75')

      const notesTextarea = screen.getByLabelText(/notes/i)
      await user.type(notesTextarea, 'Made good progress today')

      // Submit check-in
      const submitButton = screen.getByRole('button', { name: /update/i })
      await act(async () => {
        await user.click(submitButton)
      })

      // Should update goal progress
      await waitFor(() => {
        expect(screen.getByText('75%')).toBeInTheDocument()
      })
    })
  })

  describe('Enhanced Reporting', () => {
    it('should display goal completion trends', async () => {
      // Create multiple goals with different completion dates
      await Promise.all([
        prisma.goal.create({
          data: {
            title: 'Goal 1',
            status: 'COMPLETED',
            completedAt: new Date('2024-01-15'),
            userId: testUser.id,
            companyId: testUser.companyId,
          },
        }),
        prisma.goal.create({
          data: {
            title: 'Goal 2',
            status: 'COMPLETED',
            completedAt: new Date('2024-01-30'),
            userId: testUser.id,
            companyId: testUser.companyId,
          },
        }),
      ])

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      // Should show analytics section
      await waitFor(() => {
        expect(screen.getByTestId('goals-analytics')).toBeInTheDocument()
      })

      // Should show completion trend chart
      expect(screen.getByTestId('completion-trend-chart')).toBeInTheDocument()

      // Should show monthly completion stats
      expect(screen.getByText('Goals completed this month: 2')).toBeInTheDocument()
    })

    it('should display category breakdown', async () => {
      // Create goals in different categories
      await Promise.all([
        prisma.goal.create({
          data: {
            title: 'Personal Goal',
            category: 'PERSONAL',
            userId: testUser.id,
            companyId: testUser.companyId,
          },
        }),
        prisma.goal.create({
          data: {
            title: 'Learning Goal',
            category: 'LEARNING',
            userId: testUser.id,
            companyId: testUser.companyId,
          },
        }),
      ])

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      // Should show category breakdown
      await waitFor(() => {
        expect(screen.getByTestId('category-breakdown')).toBeInTheDocument()
      })

      expect(screen.getByText('Personal: 1')).toBeInTheDocument()
      expect(screen.getByText('Learning: 1')).toBeInTheDocument()
    })

    it('should export goals data', async () => {
      const user = userEvent.setup()

      const goal = await prisma.goal.create({
        data: {
          title: 'Test Goal',
          category: 'PROFESSIONAL',
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Test Goal')).toBeInTheDocument()
      })

      // Should show export button
      const exportButton = screen.getByRole('button', { name: /export goals/i })
      await user.click(exportButton)

      // Should trigger export functionality and show success toast
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Export completed')
      })
    })
  })

  describe('AI-Enhanced Features (Placeholders)', () => {
    it('should show AI goal suggestions placeholder', async () => {
      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      // Should show AI suggestions section (placeholder)
      await waitFor(() => {
        expect(screen.getByTestId('ai-suggestions-placeholder')).toBeInTheDocument()
      })

      expect(screen.getByText('AI-powered goal suggestions coming soon')).toBeInTheDocument()
    })

    it('should show smart progress predictions placeholder', async () => {
      const goal = await prisma.goal.create({
        data: {
          title: 'Test Goal',
          progress: 40,
          userId: testUser.id,
          companyId: testUser.companyId,
        },
      })

      render(
        <TestWrapper>
          <GoalsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Test Goal')).toBeInTheDocument()
      })

      // Should show AI progress prediction placeholder
      expect(screen.getByTestId('ai-progress-prediction-placeholder')).toBeInTheDocument()
      expect(screen.getByText('AI progress predictions coming soon')).toBeInTheDocument()
    })
  })
})
