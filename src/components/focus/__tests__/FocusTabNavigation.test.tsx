'use client'

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { FocusTabNavigation } from '../FocusTabNavigation'

// Mock the user context provider
vi.mock('@/lib/context/UserContextProvider', () => ({
  useUserContext: () => ({
    user: {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'EMPLOYEE',
      companyId: 'test-company-id',
    },
    contextData: {
      preferences: { focusDefaultTab: 'today' },
      recentActions: [],
      historicalData: {},
    },
  }),
}))

describe('FocusTabNavigation', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Initial Rendering', () => {
    it('renders all five tab triggers correctly', () => {
      render(<FocusTabNavigation />)

      // Verify all tabs are present
      expect(screen.getByRole('tab', { name: /Today/ })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /Goals/ })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /Skills/ })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /Performance/ })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /Insights/ })).toBeInTheDocument()
    })

    it('renders Today tab as active by default', () => {
      render(<FocusTabNavigation />)

      const todayTab = screen.getByRole('tab', { name: /Today/ })
      expect(todayTab).toHaveAttribute('data-state', 'active')
    })

    it('renders the section header correctly', () => {
      render(<FocusTabNavigation />)

      expect(screen.getByRole('heading', { name: 'Focus', level: 1 })).toBeInTheDocument()
      expect(
        screen.getByText('Your daily command center for productivity and goal tracking')
      ).toBeInTheDocument()
    })

    it('renders breadcrumb navigation', () => {
      render(<FocusTabNavigation />)

      expect(screen.getByLabelText('Breadcrumb')).toBeInTheDocument()
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      // Use the breadcrumb specific Focus element
      const breadcrumbNav = screen.getByLabelText('Breadcrumb')
      expect(breadcrumbNav).toHaveTextContent('Focus')
    })
  })

  describe('Tab Navigation', () => {
    it('switches tabs when clicked', async () => {
      render(<FocusTabNavigation />)

      const goalsTab = screen.getByRole('tab', { name: /Goals/ })
      await user.click(goalsTab)

      expect(goalsTab).toHaveAttribute('data-state', 'active')
      expect(screen.getByRole('tab', { name: /Today/ })).toHaveAttribute('data-state', 'inactive')
    })

    it('shows correct content for each tab', async () => {
      render(<FocusTabNavigation />)

      // Test Today tab content (check for tab content, not tab button)
      const todayElements = screen.getAllByText('Today')
      expect(todayElements.length).toBeGreaterThan(0)

      // Switch to Goals tab
      await user.click(screen.getByRole('tab', { name: /Goals/ }))
      await waitFor(() => {
        expect(screen.getByText('Goals')).toBeInTheDocument()
      })

      // Switch to Skills tab
      await user.click(screen.getByRole('tab', { name: /Skills/ }))
      await waitFor(() => {
        // Check for Skills tab being active instead of specific content loading
        const skillsTab = screen.getByRole('tab', { name: /Skills/ })
        expect(skillsTab).toHaveAttribute('aria-selected', 'true')
      })

      // Switch to Performance tab
      await user.click(screen.getByRole('tab', { name: /Performance/ }))
      await waitFor(() => {
        // Check for Performance tab being active instead of specific content
        const performanceTab = screen.getByRole('tab', { name: /Performance/ })
        expect(performanceTab).toHaveAttribute('aria-selected', 'true')
      })

      // Switch to Insights tab
      await user.click(screen.getByRole('tab', { name: /Insights/ }))
      await waitFor(() => {
        // Check for Insights tab being active instead of specific content
        const insightsTab = screen.getByRole('tab', { name: /Insights/ })
        expect(insightsTab).toHaveAttribute('aria-selected', 'true')
      })
    })

    it('maintains tab state during navigation', async () => {
      render(<FocusTabNavigation />)

      // Switch to Skills tab
      await user.click(screen.getByRole('tab', { name: /Skills/ }))
      expect(screen.getByRole('tab', { name: /Skills/ })).toHaveAttribute('data-state', 'active')

      // Switch to another tab and back
      await user.click(screen.getByRole('tab', { name: /Goals/ }))
      await user.click(screen.getByRole('tab', { name: /Skills/ }))

      expect(screen.getByRole('tab', { name: /Skills/ })).toHaveAttribute('data-state', 'active')
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<FocusTabNavigation />)

      const tabList = screen.getByRole('tablist')
      expect(tabList).toBeInTheDocument()

      const tabs = screen.getAllByRole('tab')
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected')
      })
    })

    it('has proper heading hierarchy', () => {
      render(<FocusTabNavigation />)

      // Get all headings and check we have both Focus and tab content headings
      const allHeadings = screen.getAllByRole('heading')
      expect(allHeadings.length).toBeGreaterThan(0)

      // Find the main Focus heading
      const focusHeading = allHeadings.find(heading => heading.textContent === 'Focus')
      expect(focusHeading).toBeInTheDocument()
      expect(focusHeading).toHaveTextContent('Focus')
    })

    it('provides screen reader friendly text', () => {
      render(<FocusTabNavigation />)

      expect(screen.getByText('Personal productivity dashboard')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('renders fallback content when tab content fails', () => {
      // Mock console.error to avoid test noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      render(<FocusTabNavigation />)

      // Should still render basic structure even if content fails
      expect(screen.getByRole('tablist')).toBeInTheDocument()
      expect(screen.getAllByRole('tab')).toHaveLength(5)

      consoleSpy.mockRestore()
    })
  })
})
