import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { KeyResultForm } from '../KeyResultForm'
import { toast } from 'sonner'

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock('@/lib/services/goals-client', () => ({
  goalsClient: {
    createKeyResult: vi.fn().mockResolvedValue({ id: 'new-kr-id', title: 'New Key Result' }),
    updateKeyResult: vi.fn().mockResolvedValue({ id: 'kr-1', title: 'Updated Key Result' }),
  },
}))

describe('KeyResultForm', () => {
  const mockOnSuccess = vi.fn()
  const mockOnCancel = vi.fn()
  const objectiveId = 'obj-1'

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the form with default values for new key result', () => {
    render(
      <KeyResultForm objectiveId={objectiveId} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Check form elements are present
    expect(screen.getByTestId('kr-title-input')).toBeInTheDocument()
    expect(screen.getByTestId('kr-description-input')).toBeInTheDocument()
    expect(screen.getByTestId('kr-target-input')).toBeInTheDocument()
    expect(screen.getByTestId('kr-current-input')).toBeInTheDocument()
    expect(screen.getByTestId('kr-unit-select')).toBeInTheDocument()
    expect(screen.getByTestId('kr-due-date-input')).toBeInTheDocument()
    expect(screen.getByTestId('kr-progress')).toBeInTheDocument()

    // Check default values
    expect(screen.getByTestId('kr-title-input')).toHaveValue('')
    expect(screen.getByTestId('kr-target-input')).toHaveValue(100)
    expect(screen.getByTestId('kr-current-input')).toHaveValue(0)

    // Check button text
    expect(screen.getByTestId('kr-submit-button')).toHaveTextContent('Add Key Result')
  })

  it('renders with existing key result data when editing', () => {
    const keyResult = {
      id: 'kr-1',
      title: 'Existing Key Result',
      description: 'This is a test key result',
      targetValue: 200,
      currentValue: 100,
      unit: 'count',
      dueDate: '2025-06-30',
    }

    render(
      <KeyResultForm
        objectiveId={objectiveId}
        keyResult={keyResult}
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    )

    // Check values are populated
    expect(screen.getByTestId('kr-title-input')).toHaveValue('Existing Key Result')
    expect(screen.getByTestId('kr-description-input')).toHaveValue('This is a test key result')
    expect(screen.getByTestId('kr-target-input')).toHaveValue(200)
    expect(screen.getByTestId('kr-current-input')).toHaveValue(100)
    expect(screen.getByTestId('kr-due-date-input')).toHaveValue('2025-06-30')

    // Check progress
    expect(screen.getByTestId('kr-progress')).toHaveAttribute('value', '50')

    // Check button text
    expect(screen.getByTestId('kr-submit-button')).toHaveTextContent('Update Key Result')
  })

  it('shows validation errors when form is submitted with invalid data', async () => {
    const user = userEvent.setup()
    render(
      <KeyResultForm objectiveId={objectiveId} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Clear the title field (which is required)
    const titleInput = screen.getByTestId('kr-title-input')
    await user.clear(titleInput)

    // Submit the form
    const submitButton = screen.getByTestId('kr-submit-button')
    await user.click(submitButton)

    // Check that validation error appears
    expect(screen.getByText('Title is required')).toBeInTheDocument()

    // Verify that the create API was not called
    expect(mockOnSuccess).not.toHaveBeenCalled()
  })

  it('creates a new key result successfully', async () => {
    const user = userEvent.setup()
    const { goalsClient } = await import('@/lib/services/goals-client')

    render(
      <KeyResultForm objectiveId={objectiveId} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Fill out the form
    await user.type(screen.getByTestId('kr-title-input'), 'My New Key Result')
    await user.type(screen.getByTestId('kr-description-input'), 'This is a description')
    await user.clear(screen.getByTestId('kr-target-input'))
    await user.type(screen.getByTestId('kr-target-input'), '500')

    // Submit the form
    await user.click(screen.getByTestId('kr-submit-button'))

    // Verify that the create API was called with correct data
    await waitFor(() => {
      expect(goalsClient.createKeyResult).toHaveBeenCalledWith({
        title: 'My New Key Result',
        description: 'This is a description',
        targetValue: 500,
        currentValue: 0,
        unit: '%',
        dueDate: '',
        objectiveId,
      })
    })

    // Verify success callback was called and toast shown
    expect(mockOnSuccess).toHaveBeenCalledTimes(1)
    expect(toast.success).toHaveBeenCalledWith('Key result created successfully')
  })

  it('updates an existing key result successfully', async () => {
    const user = userEvent.setup()
    const { goalsClient } = await import('@/lib/services/goals-client')

    const keyResult = {
      id: 'kr-1',
      title: 'Existing Key Result',
      description: 'This is a test key result',
      targetValue: 200,
      currentValue: 100,
      unit: 'count',
      dueDate: '2025-06-30',
    }

    render(
      <KeyResultForm
        objectiveId={objectiveId}
        keyResult={keyResult}
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    )

    // Update the title
    await user.clear(screen.getByTestId('kr-title-input'))
    await user.type(screen.getByTestId('kr-title-input'), 'Updated Key Result Title')

    // Submit the form
    await user.click(screen.getByTestId('kr-submit-button'))

    // Verify that the update API was called with correct data
    await waitFor(() => {
      expect(goalsClient.updateKeyResult).toHaveBeenCalledWith('kr-1', {
        title: 'Updated Key Result Title',
        description: 'This is a test key result',
        targetValue: 200,
        currentValue: 100,
        unit: 'count',
        dueDate: '2025-06-30',
        objectiveId,
      })
    })

    // Verify success callback was called and toast shown
    expect(mockOnSuccess).toHaveBeenCalledTimes(1)
    expect(toast.success).toHaveBeenCalledWith('Key result updated successfully')
  })

  it('calls the cancel handler when cancel button is clicked', async () => {
    const user = userEvent.setup()

    render(
      <KeyResultForm objectiveId={objectiveId} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Click the cancel button
    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)

    // Verify that the cancel callback was called
    expect(mockOnCancel).toHaveBeenCalledTimes(1)
  })

  it('handles API errors gracefully', async () => {
    const user = userEvent.setup()
    const { goalsClient } = await import('@/lib/services/goals-client')

    // Mock API failure
    goalsClient.createKeyResult.mockRejectedValueOnce(new Error('API Error'))

    render(
      <KeyResultForm objectiveId={objectiveId} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Fill out the form minimally
    await user.type(screen.getByTestId('kr-title-input'), 'My New Key Result')

    // Submit the form
    await user.click(screen.getByTestId('kr-submit-button'))

    // Verify error handling
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to save key result')
    })

    // Verify success callback was not called
    expect(mockOnSuccess).not.toHaveBeenCalled()
  })
})
