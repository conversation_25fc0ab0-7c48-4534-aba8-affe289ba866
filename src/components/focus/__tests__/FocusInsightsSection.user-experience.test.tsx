'use client'

/**
 * Real User Experience Testing for FocusInsightsSection
 * Task 5.10: Conduct User Testing and Refinement of Insights
 *
 * This test suite validates real user experience scenarios using actual data,
 * focusing on user workflows and functionality rather than specific text content.
 * Following TDD principles with no mocking - when tests pass, features work.
 */

import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { FocusInsightsSection } from '../FocusInsightsSection'
import { SessionProvider } from 'next-auth/react'
import { AuthContextProvider } from '@/components/auth/AuthContextProvider'
import { prisma } from '@/lib/prisma'

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const mockSession = {
    user: {
      id: 'test-user-ux',
      email: '<EMAIL>',
      name: 'Test User',
      companyId: 'test-company-ux',
      role: 'EMPLOYEE',
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  }

  const mockAuthContext = {
    user: mockSession.user,
    isLoading: false,
    error: null,
  }

  return (
    <SessionProvider session={mockSession}>
      <AuthContextProvider value={mockAuthContext}>{children}</AuthContextProvider>
    </SessionProvider>
  )
}

// Helper function to create test user and company
const createTestUserAndCompany = async () => {
  const company = await prisma.company.upsert({
    where: { id: 'test-company-ux' },
    update: {},
    create: {
      id: 'test-company-ux',
      name: 'Test Company UX',
      domains: ['testux.com'],
    },
  })

  const user = await prisma.user.upsert({
    where: { id: 'test-user-ux' },
    update: {},
    create: {
      id: 'test-user-ux',
      email: '<EMAIL>',
      name: 'Test User',
      companyId: 'test-company-ux',
      role: 'EMPLOYEE',
    },
  })

  return { user, company }
}

describe('FocusInsightsSection - Real User Experience Testing', () => {
  let userData: any

  beforeEach(async () => {
    userData = await createTestUserAndCompany()
  })

  afterEach(async () => {
    // Clean up real data
    await prisma.skill.deleteMany({ where: { companyId: userData.company.id } })
    await prisma.goal.deleteMany({ where: { companyId: userData.company.id } })
    await prisma.focusTask.deleteMany({ where: { companyId: userData.company.id } })
    await prisma.user.deleteMany({ where: { companyId: userData.company.id } })
    await prisma.company.deleteMany({ where: { id: userData.company.id } })
  })

  describe('Component Rendering and Basic Functionality', () => {
    it('should render the insights section with proper structure', async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )

      // Verify main heading is present
      expect(screen.getByText('Focus Insights')).toBeInTheDocument()

      // Verify description is present
      expect(screen.getByText(/Data-driven insights to help you improve/)).toBeInTheDocument()

      // Verify all three tabs are present
      expect(screen.getByRole('tab', { name: /productivity/i })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /goals/i })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /skills/i })).toBeInTheDocument()
    })

    it('should handle tab navigation correctly', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )

      // Initially productivity tab should be active
      const productivityTab = screen.getByRole('tab', { name: /productivity/i })
      const goalsTab = screen.getByRole('tab', { name: /goals/i })
      const skillsTab = screen.getByRole('tab', { name: /skills/i })

      expect(productivityTab).toHaveAttribute('aria-selected', 'true')
      expect(goalsTab).toHaveAttribute('aria-selected', 'false')
      expect(skillsTab).toHaveAttribute('aria-selected', 'false')

      // Click on goals tab
      await user.click(goalsTab)

      await waitFor(() => {
        expect(goalsTab).toHaveAttribute('aria-selected', 'true')
        expect(productivityTab).toHaveAttribute('aria-selected', 'false')
      })

      // Click on skills tab
      await user.click(skillsTab)

      await waitFor(() => {
        expect(skillsTab).toHaveAttribute('aria-selected', 'true')
        expect(goalsTab).toHaveAttribute('aria-selected', 'false')
      })
    })
  })

  describe('Data Loading and Display', () => {
    it('should handle empty state appropriately', async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText('Focus Insights')).toBeInTheDocument()
      })

      // Should show appropriate empty state messages
      // (The exact text may vary, but there should be some indication of no data)
      const productivityContent = screen.getByRole('tabpanel', { name: /productivity/i })
      expect(productivityContent).toBeInTheDocument()
    })

    it('should load and display insights when data is available', async () => {
      // Create some test data
      await prisma.focusTask.create({
        data: {
          id: 'task-ux-1',
          title: 'Test Task',
          description: 'Test task description',
          status: 'COMPLETED',
          priority: 'high',
          userId: userData.user.id,
          companyId: userData.company.id,
          completedAt: new Date(),
        },
      })

      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )

      // Wait for component to load and process data
      await waitFor(
        () => {
          expect(screen.getByText('Focus Insights')).toBeInTheDocument()
        },
        { timeout: 5000 }
      )

      // The component should render without errors
      expect(screen.getByRole('tabpanel', { name: /productivity/i })).toBeInTheDocument()
    })
  })

  describe('Performance and Accessibility', () => {
    it('should load within reasonable time', async () => {
      const startTime = Date.now()

      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Focus Insights')).toBeInTheDocument()
      })

      const loadTime = Date.now() - startTime
      expect(loadTime).toBeLessThan(2000) // Should load within 2 seconds
    })

    it('should have proper ARIA attributes for accessibility', async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Focus Insights')).toBeInTheDocument()
      })

      // Check for proper ARIA attributes
      const tabList = screen.getByRole('tablist')
      expect(tabList).toHaveAttribute('aria-orientation', 'horizontal')

      const tabs = screen.getAllByRole('tab')
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-controls')
        expect(tab).toHaveAttribute('aria-selected')
      })

      const tabPanels = screen.getAllByRole('tabpanel')
      tabPanels.forEach(panel => {
        expect(panel).toHaveAttribute('aria-labelledby')
      })
    })

    it('should handle errors gracefully', async () => {
      // Test with invalid user data to trigger potential errors
      const mockSession = {
        user: {
          id: 'invalid-user',
          email: '<EMAIL>',
          name: 'Invalid User',
          companyId: 'invalid-company',
          role: 'EMPLOYEE',
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      }

      const mockAuthContext = {
        user: mockSession.user,
        isLoading: false,
        error: null,
      }

      render(
        <SessionProvider session={mockSession}>
          <AuthContextProvider value={mockAuthContext}>
            <FocusInsightsSection />
          </AuthContextProvider>
        </SessionProvider>
      )

      // Component should still render even with invalid data
      await waitFor(() => {
        expect(screen.getByText('Focus Insights')).toBeInTheDocument()
      })

      // Should show appropriate empty/error state
      expect(screen.getByRole('tablist')).toBeInTheDocument()
    })
  })

  describe('Real User Workflow Validation', () => {
    it('should support complete user workflow from loading to interaction', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )

      // 1. Component loads
      await waitFor(() => {
        expect(screen.getByText('Focus Insights')).toBeInTheDocument()
      })

      // 2. User can navigate between tabs
      const goalsTab = screen.getByRole('tab', { name: /goals/i })
      await user.click(goalsTab)

      await waitFor(() => {
        expect(goalsTab).toHaveAttribute('aria-selected', 'true')
      })

      // 3. User can navigate to skills tab
      const skillsTab = screen.getByRole('tab', { name: /skills/i })
      await user.click(skillsTab)

      await waitFor(() => {
        expect(skillsTab).toHaveAttribute('aria-selected', 'true')
      })

      // 4. User can return to productivity tab
      const productivityTab = screen.getByRole('tab', { name: /productivity/i })
      await user.click(productivityTab)

      await waitFor(() => {
        expect(productivityTab).toHaveAttribute('aria-selected', 'true')
      })

      // Workflow completes successfully
      expect(screen.getByText('Focus Insights')).toBeInTheDocument()
    })
  })
})
