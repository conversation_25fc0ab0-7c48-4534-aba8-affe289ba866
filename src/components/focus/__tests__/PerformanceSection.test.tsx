import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest'
import { SessionProvider } from 'next-auth/react'
import { AuthContextProvider } from '@/components/auth/AuthContextProvider'
import { prisma } from '@/lib/prisma'
import { PerformanceSection } from '../PerformanceSection'

// Mock the auth hook to return a test user
const mockUser = {
  id: 'test-user-performance-001',
  email: '<EMAIL>',
  name: 'Performance Test User',
  role: 'EMPLOYEE' as const,
  companyId: 'test-company-performance-001',
}

// Mock useAuth hook
const mockUseAuth = {
  user: mockUser,
  isAuthenticated: true,
  isLoading: false,
  hasPermission: () => true,
  hasRole: () => true,
  isRoleAtLeast: () => true,
}

// Mock the auth module
vi.mock('@/lib/auth/hooks', () => ({
  useAuth: () => mockUseAuth,
}))

// Test wrapper with proper context providers (following SkillsSection pattern)
function TestWrapper({ children }: { children: React.ReactNode }) {
  const mockSession = {
    user: {
      id: mockUser.id,
      email: mockUser.email,
      name: mockUser.name,
      role: mockUser.role,
      companyId: mockUser.companyId,
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  }

  return (
    <SessionProvider session={mockSession}>
      <AuthContextProvider>
        <div data-testid='test-wrapper'>{children}</div>
      </AuthContextProvider>
    </SessionProvider>
  )
}

describe('PerformanceSection Component - TDD Implementation', () => {
  let testUser: typeof mockUser
  let testCompany: { id: string; name: string }

  beforeAll(async () => {
    // Set up test environment
    console.log('🧪 Setting up PerformanceSection test environment...')

    // Create test company
    testCompany = await prisma.company.create({
      data: {
        id: mockUser.companyId,
        name: 'Performance Test Company',
        domains: ['emynent.com'],
        allowedEmailDomains: ['emynent.com'],
      },
    })

    // Create test user
    testUser = await prisma.user.create({
      data: {
        id: mockUser.id,
        email: mockUser.email,
        name: mockUser.name,
        role: mockUser.role,
        companyId: testCompany.id,
        onboardingCompleted: true,
      },
    })

    console.log('✅ PerformanceSection test environment ready')
  })

  afterAll(async () => {
    // Clean up test data
    await prisma.user.deleteMany({
      where: { id: testUser.id },
    })
    await prisma.company.deleteMany({
      where: { id: testCompany.id },
    })
  })

  beforeEach(() => {
    // Reset any mocks before each test
    vi.clearAllMocks()
  })

  it('should render performance section with loading state initially', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Should show loading spinner initially (or fully loaded content)
    const loadingElement = screen.queryByTestId('performance-loading')
    if (loadingElement) {
      expect(loadingElement).toBeInTheDocument()
      // Note: animation classes may load quickly, so we accept either state
    }

    // Alternative: wait for the component to fully load
    await waitFor(() => {
      expect(screen.getByText('Performance Analytics')).toBeInTheDocument()
    })
  })

  it('should display performance analytics header and metrics after loading', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
    })

    // Should show main header
    expect(screen.getByText('Performance Analytics')).toBeInTheDocument()
    expect(
      screen.getByText('Track your productivity, achievements, and growth metrics')
    ).toBeInTheDocument()

    // Should show time period selector
    expect(screen.getByLabelText('Select time period')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Last 30 days')).toBeInTheDocument()
  })

  it('should display key performance statistics cards', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
    })

    // Should show performance stats cards
    expect(screen.getByText('Task Completion')).toBeInTheDocument()
    expect(screen.getByText('Goals Achieved')).toBeInTheDocument()
    expect(screen.getByText('Skills Improved')).toBeInTheDocument()
    expect(screen.getByText('Productivity Score')).toBeInTheDocument()

    // Should show progress bars
    const progressBars = screen.getAllByRole('progressbar')
    expect(progressBars.length).toBeGreaterThanOrEqual(2) // Task Completion and Productivity Score have progress bars
  })

  it('should display performance metrics with trends and categories', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
    })

    // Should show performance metrics
    expect(screen.getByText('Task Completion Rate')).toBeInTheDocument()
    expect(screen.getByText('Goal Progress')).toBeInTheDocument()
    expect(screen.getByText('Skills Developed')).toBeInTheDocument()
    expect(screen.getByText('Daily Focus Time')).toBeInTheDocument()
    expect(screen.getByText('Platform Engagement')).toBeInTheDocument()
    expect(screen.getByText('Avg Response Time')).toBeInTheDocument()

    // Should show category badges (use getAllByText for multiple instances)
    expect(screen.getAllByText('productivity').length).toBeGreaterThan(0)
    expect(screen.getAllByText('goals').length).toBeGreaterThan(0)
    expect(screen.getAllByText('skills').length).toBeGreaterThan(0)
    expect(screen.getAllByText('engagement').length).toBeGreaterThan(0)

    // Should show trend indicators
    const trendTexts = screen.getAllByText(/vs last period/)
    expect(trendTexts.length).toBeGreaterThan(0)
  })

  it('should display performance insights section', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
    })

    // Should show insights section
    expect(screen.getByText('Performance Insights')).toBeInTheDocument()

    // Should show specific insights
    expect(screen.getByText('Strong Task Completion')).toBeInTheDocument()
    expect(screen.getByText('Skills Development')).toBeInTheDocument()
    expect(screen.getByText('Response Time')).toBeInTheDocument()

    // Should show insight descriptions
    expect(
      screen.getByText(/Your 84% completion rate is above the team average/)
    ).toBeInTheDocument()
    expect(screen.getByText(/You've improved 4 skills this month/)).toBeInTheDocument()
    expect(screen.getByText(/Your response time has improved by 0.3 hours/)).toBeInTheDocument()
  })

  it('should allow changing time periods and update data', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for initial load and ensure selector is available
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
      expect(screen.getByLabelText('Select time period')).toBeInTheDocument()
    })

    // Change time period
    const periodSelector = screen.getByLabelText('Select time period')
    fireEvent.change(periodSelector, { target: { value: '7d' } })

    // Should update to new period (check that the value changed)
    await waitFor(() => {
      expect(periodSelector).toHaveValue('7d')
    })

    // Data should reload (check that values might change based on period)
    await waitFor(() => {
      // The component should still show performance metrics after period change
      expect(screen.getByText('Task Completion Rate')).toBeInTheDocument()
    })
  })

  it('should handle performance data loading errors gracefully', async () => {
    // Mock a user without proper auth data to trigger error
    const mockErrorUser = {
      ...mockUser,
      id: undefined, // This should trigger the early return in loadPerformanceData
    }

    const mockUseAuthError = {
      ...mockUseAuth,
      user: mockErrorUser,
    }

    // Temporarily override the auth mock
    vi.doMock('@/lib/auth/hooks', () => ({
      useAuth: () => mockUseAuthError,
    }))

    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Should show loading initially
    expect(screen.getByTestId('performance-loading')).toBeInTheDocument()

    // Wait a bit and should remain in loading state since user.id is undefined
    await waitFor(
      () => {
        expect(screen.getByTestId('performance-loading')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )
  })

  it('should show appropriate trend indicators for different metric types', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
    })

    // Should show positive trend indicators
    const upTrends = screen.getAllByText(/\+\d+/)
    expect(upTrends.length).toBeGreaterThan(0)

    // Should show negative trend indicators
    const downTrends = screen.getAllByText(/-\d+/)
    expect(downTrends.length).toBeGreaterThan(0)

    // Should show stable trend
    const stableTrend = screen.getByText('No change')
    expect(stableTrend).toBeInTheDocument()
  })

  it('should render with proper accessibility attributes', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
    })

    // Check accessibility attributes
    expect(screen.getByLabelText('Select time period')).toBeInTheDocument()

    // Progress bars should have proper roles
    const progressBars = screen.getAllByRole('progressbar')
    expect(progressBars.length).toBeGreaterThan(0)

    // Cards should be properly structured
    const headings = screen.getAllByRole('heading')
    expect(headings.length).toBeGreaterThan(0)
  })

  it('should display correct performance values and units', async () => {
    render(
      <TestWrapper>
        <PerformanceSection />
      </TestWrapper>
    )

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('performance-loading')).not.toBeInTheDocument()
    })

    // Check for percentage values
    const percentageValues = screen.getAllByText(/%/)
    expect(percentageValues.length).toBeGreaterThan(0)

    // Check for time units
    expect(screen.getAllByText(/hours/).length).toBeGreaterThan(0)

    // Check for count units
    expect(screen.getAllByText(/skills/).length).toBeGreaterThan(0)

    // Check for specific metric values that should be displayed
    expect(screen.getByText('84')).toBeInTheDocument() // Task completion rate
    expect(screen.getByText('67')).toBeInTheDocument() // Goal progress
    expect(screen.getByText('85')).toBeInTheDocument() // Platform engagement
  })
})
