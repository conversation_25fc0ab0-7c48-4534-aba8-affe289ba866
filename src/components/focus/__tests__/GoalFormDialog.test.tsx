import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { GoalFormDialog } from '../GoalFormDialog'
import { vi } from 'vitest'
import { toast } from 'sonner'
import { goalsClient } from '@/lib/services/goals-client'

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock('@/lib/services/goals-client', () => ({
  goalsClient: {
    createGoal: vi.fn().mockResolvedValue({
      id: 'new-goal',
      title: 'New Goal Title',
      type: 'OBJECTIVE',
    }),
    updateGoal: vi.fn().mockResolvedValue({
      id: 'goal-1',
      title: 'Updated Goal Title',
      type: 'OBJECTIVE',
    }),
  },
}))

// Mock date-fns format function to avoid locale issues in tests
vi.mock('date-fns', async () => {
  const actual = await vi.importActual('date-fns')
  return {
    ...actual,
    format: vi.fn().mockImplementation((date, formatStr) => {
      return date ? '2025-01-15' : ''
    }),
  }
})

describe('GoalFormDialog', () => {
  const mockOnClose = vi.fn()
  const mockOnSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the create goal dialog correctly', async () => {
    render(<GoalFormDialog isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />)

    expect(screen.getByText('Create New Goal')).toBeInTheDocument()
    expect(screen.getByLabelText('Title')).toBeInTheDocument()
    expect(screen.getByLabelText('Description')).toBeInTheDocument()
    expect(screen.getByText('The type of goal you want to track')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Create Goal/i })).toBeInTheDocument()
  })

  it('renders the edit goal dialog with pre-populated fields', async () => {
    const goalToEdit = {
      id: 'goal-1',
      title: 'Existing Goal',
      description: 'Existing description',
      type: 'OBJECTIVE',
      status: 'IN_PROGRESS',
      progress: 40,
      category: 'PROFESSIONAL',
      timeframe: 'QUARTERLY',
      priority: 'HIGH',
      startDate: '2025-01-01',
      dueDate: '2025-03-31',
    }

    render(
      <GoalFormDialog
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        goalToEdit={goalToEdit}
      />
    )

    expect(screen.getByText('Edit Goal')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Existing Goal')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Existing description')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Update Goal/i })).toBeInTheDocument()
  })

  it('creates a new goal when submitting the form', async () => {
    const user = userEvent.setup()

    render(<GoalFormDialog isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />)

    // Fill in form fields
    await user.type(screen.getByLabelText('Title'), 'New Goal Title')
    await user.type(screen.getByLabelText('Description'), 'New goal description')

    // Submit the form
    await user.click(screen.getByRole('button', { name: /Create Goal/i }))

    // Wait for the async submission to complete
    await waitFor(() => {
      expect(goalsClient.createGoal).toHaveBeenCalledTimes(1)
      expect(goalsClient.createGoal).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'New Goal Title',
          description: 'New goal description',
          type: 'OBJECTIVE',
        })
      )
      expect(toast.success).toHaveBeenCalledWith('Goal created successfully')
      expect(mockOnSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'new-goal',
          title: 'New Goal Title',
        })
      )
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  it('updates an existing goal when in edit mode', async () => {
    const user = userEvent.setup()

    const goalToEdit = {
      id: 'goal-1',
      title: 'Existing Goal',
      description: 'Existing description',
      type: 'OBJECTIVE',
      status: 'IN_PROGRESS',
      progress: 40,
      category: 'PROFESSIONAL',
      timeframe: 'QUARTERLY',
      priority: 'HIGH',
    }

    render(
      <GoalFormDialog
        isOpen={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        goalToEdit={goalToEdit}
      />
    )

    // Update form fields
    await user.clear(screen.getByLabelText('Title'))
    await user.type(screen.getByLabelText('Title'), 'Updated Goal Title')

    // Submit the form
    await user.click(screen.getByRole('button', { name: /Update Goal/i }))

    // Wait for the async submission to complete
    await waitFor(() => {
      expect(goalsClient.updateGoal).toHaveBeenCalledTimes(1)
      expect(goalsClient.updateGoal).toHaveBeenCalledWith(
        'goal-1',
        expect.objectContaining({
          title: 'Updated Goal Title',
        })
      )
      expect(toast.success).toHaveBeenCalledWith('Goal updated successfully')
      expect(mockOnSuccess).toHaveBeenCalled()
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  it('handles form errors correctly', async () => {
    const user = userEvent.setup()

    // Mock createGoal to reject
    vi.mocked(goalsClient.createGoal).mockRejectedValueOnce(new Error('API Error'))

    render(<GoalFormDialog isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />)

    // Fill in form fields
    await user.type(screen.getByLabelText('Title'), 'New Goal Title')

    // Submit the form
    await user.click(screen.getByRole('button', { name: /Create Goal/i }))

    // Wait for the async submission to complete
    await waitFor(() => {
      expect(goalsClient.createGoal).toHaveBeenCalledTimes(1)
      expect(toast.error).toHaveBeenCalledWith('Failed to save goal. Please try again.')
      expect(mockOnSuccess).not.toHaveBeenCalled()
      expect(mockOnClose).not.toHaveBeenCalled()
    })
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()

    render(<GoalFormDialog isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />)

    // Submit without filling required fields
    await user.click(screen.getByRole('button', { name: /Create Goal/i }))

    // Should show validation error and not submit
    await waitFor(() => {
      expect(screen.getByText('Title is required')).toBeInTheDocument()
      expect(goalsClient.createGoal).not.toHaveBeenCalled()
    })
  })

  it('closes the dialog when cancel is clicked', async () => {
    const user = userEvent.setup()

    render(<GoalFormDialog isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />)

    await user.click(screen.getByRole('button', { name: /Cancel/i }))

    expect(mockOnClose).toHaveBeenCalledTimes(1)
    expect(goalsClient.createGoal).not.toHaveBeenCalled()
  })
})
