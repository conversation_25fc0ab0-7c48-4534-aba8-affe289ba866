import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { GoalCard } from '../GoalCard'
import { vi } from 'vitest'

// Mock goal data
const mockObjective = {
  id: '1',
  title: 'Improve Team Productivity',
  description: 'Increase team productivity through better processes',
  type: 'OBJECTIVE',
  status: 'IN_PROGRESS',
  progress: 40,
  category: 'PROFESSIONAL',
  timeframe: 'QUARTERLY',
  priority: 'HIGH',
  startDate: '2025-01-01',
  dueDate: '2025-03-31',
  keyResults: [
    {
      id: '2',
      title: 'Implement Goal System',
      description: 'Implement a comprehensive goal tracking system',
      targetValue: 100,
      currentValue: 50,
      unit: 'percent',
      status: 'IN_PROGRESS',
      dueDate: '2025-03-15',
    },
    {
      id: '3',
      title: 'Reduce Meeting Time',
      description: 'Reduce weekly meeting time by 25%',
      targetValue: 25,
      currentValue: 10,
      unit: 'percent',
      status: 'ON_TRACK',
      dueDate: '2025-02-28',
    },
  ],
}

const mockKeyResult = {
  id: '4',
  title: 'Complete Documentation',
  description: 'Finalize all system documentation',
  type: 'KEY_RESULT',
  status: 'NOT_STARTED',
  progress: 0,
  category: 'LEARNING',
  timeframe: 'MONTHLY',
  priority: 'MEDIUM',
  startDate: '2025-02-01',
  dueDate: '2025-02-28',
}

describe('GoalCard', () => {
  it('renders objective card with correct title and description', () => {
    render(<GoalCard goal={mockObjective} />)

    expect(screen.getByText('Improve Team Productivity')).toBeInTheDocument()
    expect(
      screen.getByText('Increase team productivity through better processes')
    ).toBeInTheDocument()
  })

  it('displays the correct status badge', () => {
    render(<GoalCard goal={mockObjective} />)

    const statusBadge = screen.getByText('IN PROGRESS')
    expect(statusBadge).toBeInTheDocument()
  })

  it('shows category and timeframe badges', () => {
    render(<GoalCard goal={mockObjective} />)

    expect(screen.getByText('Professional')).toBeInTheDocument()
    expect(screen.getByText('Quarterly')).toBeInTheDocument()
  })

  it('displays the progress bar with correct percentage', () => {
    render(<GoalCard goal={mockObjective} />)

    expect(screen.getByText('Progress')).toBeInTheDocument()
    expect(screen.getByText('40%')).toBeInTheDocument()
  })

  it('displays start and due dates correctly', () => {
    render(<GoalCard goal={mockObjective} />)

    expect(screen.getByText('Start Date')).toBeInTheDocument()
    expect(screen.getByText('Due Date')).toBeInTheDocument()

    // The actual date format may vary based on the user's locale, so we check for parts
    expect(screen.getByText(/Jan/)).toBeInTheDocument()
    expect(screen.getByText(/Mar/)).toBeInTheDocument()
  })

  it('shows key results when expanded', () => {
    render(<GoalCard goal={mockObjective} />)

    // Initially key results should be collapsed
    expect(screen.getByText('2 Key Results')).toBeInTheDocument()
    expect(screen.queryByText('Implement Goal System')).not.toBeInTheDocument()

    // Expand to show key results
    fireEvent.click(screen.getByText('2 Key Results'))

    // Now key results should be visible
    expect(screen.getByText('Implement Goal System')).toBeInTheDocument()
    expect(screen.getByText('Reduce Meeting Time')).toBeInTheDocument()
  })

  it('handles edit button click', () => {
    const handleEdit = vi.fn()
    render(<GoalCard goal={mockObjective} onEdit={handleEdit} />)

    fireEvent.click(screen.getByText('Edit'))
    expect(handleEdit).toHaveBeenCalledWith(mockObjective)
  })

  it('handles add key result button click', () => {
    const handleAddKeyResult = vi.fn()
    render(<GoalCard goal={mockObjective} onAddKeyResult={handleAddKeyResult} />)

    fireEvent.click(screen.getByText('Add Key Result'))
    expect(handleAddKeyResult).toHaveBeenCalledWith('1')
  })

  it('renders key result card correctly', () => {
    render(<GoalCard goal={mockKeyResult} />)

    expect(screen.getByText('Complete Documentation')).toBeInTheDocument()
    expect(screen.getByText('Finalize all system documentation')).toBeInTheDocument()
    expect(screen.getByText('NOT STARTED')).toBeInTheDocument()
    expect(screen.getByText('Learning')).toBeInTheDocument()
    expect(screen.getByText('Monthly')).toBeInTheDocument()

    // Should not have the key results section
    expect(screen.queryByText(/Key Results/)).not.toBeInTheDocument()
  })

  it('handles missing optional fields gracefully', () => {
    const minimalGoal = {
      id: '5',
      title: 'Minimal Goal',
      type: 'OBJECTIVE',
      status: 'IN_PROGRESS',
      progress: 30,
      category: 'PERSONAL',
      timeframe: 'WEEKLY',
      priority: 'LOW',
    }

    render(<GoalCard goal={minimalGoal} />)

    expect(screen.getByText('Minimal Goal')).toBeInTheDocument()
    expect(screen.getByText('No description provided')).toBeInTheDocument()

    // These fields should not be rendered
    expect(screen.queryByText('Start Date')).not.toBeInTheDocument()
    expect(screen.queryByText('Due Date')).not.toBeInTheDocument()
  })
})
