import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SkillAssessmentForm } from '../SkillAssessmentForm'
import { vi } from 'vitest'
import { toast } from 'sonner'
import { skillsClient } from '@/lib/services/skills-client'

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

vi.mock('@/lib/services/skills-client', () => ({
  skillsClient: {
    createAssessment: vi.fn().mockResolvedValue({ id: 'new-assessment-id' }),
  },
}))

describe('SkillAssessmentForm', () => {
  const mockSkill = {
    id: 'skill-1',
    name: 'React Development',
    description: 'Building UIs with React',
    category: 'TECHNICAL',
    currentLevel: 'INTERMEDIATE',
    targetLevel: 'ADVANCED',
    importance: 4,
    tags: ['frontend', 'javascript'],
  }

  const mockOnSuccess = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the form with skill information', () => {
    render(
      <SkillAssessmentForm skill={mockSkill} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Check that the skill name is displayed
    expect(screen.getByText(`${mockSkill.name} Assessment`)).toBeInTheDocument()

    // Check that proficiency slider is displayed
    expect(screen.getByText(/Proficiency Level \(/i)).toBeInTheDocument()

    // Check that form fields exist
    expect(screen.getByText(/Assessment Type/i)).toBeInTheDocument()
    expect(screen.getByText(/Assessment Notes/i)).toBeInTheDocument()
    expect(screen.getByText(/Next Steps for Improvement/i)).toBeInTheDocument()

    // Check buttons
    expect(screen.getByRole('button', { name: /Cancel/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Save Assessment/i })).toBeInTheDocument()
  })

  it('handles form submission correctly', async () => {
    const user = userEvent.setup()
    render(
      <SkillAssessmentForm skill={mockSkill} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Fill in the form
    await user.type(
      screen.getByPlaceholderText(/Provide details about your current skill level/i),
      'I have built several React applications'
    )
    await user.type(
      screen.getByPlaceholderText(/What will you do to improve this skill/i),
      'Learn about React hooks in depth'
    )

    // Submit the form
    await user.click(screen.getByRole('button', { name: /Save Assessment/i }))

    // Check that the API was called with correct data
    await waitFor(() => {
      expect(skillsClient.createAssessment).toHaveBeenCalledWith(
        expect.objectContaining({
          skillId: mockSkill.id,
          level: mockSkill.currentLevel,
          notes: 'I have built several React applications',
          nextSteps: 'Learn about React hooks in depth',
          assessmentType: 'self',
        })
      )
    })

    // Check that success functions were called
    expect(toast.success).toHaveBeenCalledWith('Skill assessment saved successfully')
    expect(mockOnSuccess).toHaveBeenCalled()
  })

  it('handles cancel button correctly', async () => {
    const user = userEvent.setup()
    render(
      <SkillAssessmentForm skill={mockSkill} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Click cancel button
    await user.click(screen.getByRole('button', { name: /Cancel/i }))

    // Check that onCancel was called
    expect(mockOnCancel).toHaveBeenCalled()

    // API should not have been called
    expect(skillsClient.createAssessment).not.toHaveBeenCalled()
  })

  it('displays error toast on API failure', async () => {
    // Mock API failure
    vi.mocked(skillsClient.createAssessment).mockRejectedValueOnce(new Error('Failed to save'))

    const user = userEvent.setup()
    render(
      <SkillAssessmentForm skill={mockSkill} onSuccess={mockOnSuccess} onCancel={mockOnCancel} />
    )

    // Submit the form
    await user.click(screen.getByRole('button', { name: /Save Assessment/i }))

    // Check that error toast was shown
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to save assessment')
    })

    // Success callback should not have been called
    expect(mockOnSuccess).not.toHaveBeenCalled()
  })
})
