'use client'

import { render, screen, waitFor, within, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { FocusInsightsSection } from '../FocusInsightsSection'
import { SessionProvider } from 'next-auth/react'
import { AuthContextProvider } from '@/components/auth/AuthContextProvider'
import { InsightsClient } from '@/lib/services/insights-client'
import { toast } from 'sonner'

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

// Create a mock insights client instance
const mockInsightsClient = {
  getInsights: vi.fn().mockResolvedValue({
    productivityInsights: [
      {
        id: 'productivity-1',
        title: 'Task Completion Rate',
        description: "You've completed 2 out of 4 tasks.",
        metric: 50,
        trend: 'neutral',
        trendValue: 0,
        category: 'productivity',
        priority: 'medium',
        actionItems: ['Review incomplete tasks', 'Schedule focus time'],
      },
    ],
    goalInsights: [
      {
        id: 'goal-1',
        title: 'Goal Progress Overview',
        description: 'You have 2 goals in progress with an average completion of 50%.',
        progress: 50,
        target: 100,
        daysRemaining: 30,
        onTrack: true,
        category: 'goals',
        actionItems: ['Focus on goals with least progress'],
      },
    ],
    skillInsights: [
      {
        id: 'skill-1',
        title: 'Priority Skill Development',
        description: 'You have 2 high-importance skills that need development.',
        currentLevel: 'BEGINNER',
        targetLevel: 'INTERMEDIATE',
        gap: 2,
        category: 'skills',
        actionItems: ['Focus on one high-priority skill at a time'],
      },
    ],
    isContextAware: false,
  }),
  submitFeedback: vi.fn().mockResolvedValue(true),
}

// Mock the insights client
vi.mock('@/lib/services/insights-client', () => {
  return {
    InsightsClient: vi.fn().mockImplementation(() => mockInsightsClient),
  }
})

// Mock feature flags
vi.mock('@/lib/feature-flags', () => ({
  hasFeature: vi.fn().mockImplementation((companyId, feature) => {
    if (feature === 'contextAwareness') {
      return false
    }
    return true
  }),
}))

// Test wrapper with auth context
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <SessionProvider
      session={{
        user: {
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          companyId: 'test-company-id',
        },
        expires: '2099-01-01T00:00:00.000Z',
      }}
    >
      <AuthContextProvider>{children}</AuthContextProvider>
    </SessionProvider>
  )
}

describe('FocusInsightsSection Component', () => {
  // Setup before tests
  beforeEach(async () => {
    // Reset all mocks to their default implementations
    vi.clearAllMocks()

    // Reset the default mock implementation
    vi.mocked(InsightsClient).mockImplementation(() => mockInsightsClient)
  })

  it('should render the component with title and description', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Verify title is rendered
    expect(screen.getByText('Focus Insights')).toBeInTheDocument()

    // Verify description is rendered
    expect(
      screen.getByText(/Data-driven insights to help you improve productivity/)
    ).toBeInTheDocument()
  })

  it('should render tab navigation with three tabs', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Verify tabs are rendered
    expect(screen.getByRole('tab', { name: /Productivity/i })).toBeInTheDocument()
    expect(screen.getByRole('tab', { name: /Goals/i })).toBeInTheDocument()
    expect(screen.getByRole('tab', { name: /Skills/i })).toBeInTheDocument()
  })

  it('should show loading state while fetching insights', async () => {
    // Create a mock that never resolves to keep loading state
    const slowMockClient = {
      getInsights: vi.fn().mockImplementation(() => new Promise(() => {})), // Never resolves
      submitFeedback: vi.fn().mockResolvedValue(true),
    }

    vi.mocked(InsightsClient).mockImplementationOnce(() => slowMockClient)

    // Render the component
    render(
      <TestWrapper>
        <FocusInsightsSection />
      </TestWrapper>
    )

    // Check for loading state immediately - should show skeletons
    // Since the promise never resolves, we should see loading state
    await waitFor(
      () => {
        const skeletons = document.querySelectorAll('.animate-pulse')
        expect(skeletons.length).toBeGreaterThan(0)
      },
      { timeout: 1000 }
    )
  })

  it('should display productivity insights when data is loaded', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Task Completion Rate')).toBeInTheDocument()
    })

    // Verify productivity tab content
    expect(screen.getByText("You've completed 2 out of 4 tasks.")).toBeInTheDocument()
    expect(screen.getByText('Completion Rate')).toBeInTheDocument()
    expect(screen.getByText('50%')).toBeInTheDocument()

    // Verify action items are displayed
    expect(screen.getByText('Suggested Actions:')).toBeInTheDocument()
    expect(screen.getByText('Review incomplete tasks')).toBeInTheDocument()
  })

  it('should switch tabs and show appropriate content', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Task Completion Rate')).toBeInTheDocument()
    })

    // Click on Goals tab
    const goalsTab = screen.getByRole('tab', { name: /Goals/i })
    await act(async () => {
      await userEvent.click(goalsTab)
    })

    // Verify goals tab content is displayed
    expect(screen.getByText('Goal Progress Overview')).toBeInTheDocument()
    expect(
      screen.getByText('You have 2 goals in progress with an average completion of 50%.')
    ).toBeInTheDocument()
    expect(screen.getByText('Days Remaining: 30')).toBeInTheDocument()
    expect(screen.getByText('Target: 100%')).toBeInTheDocument()

    // Click on Skills tab
    const skillsTab = screen.getByRole('tab', { name: /Skills/i })
    await act(async () => {
      await userEvent.click(skillsTab)
    })

    // Verify skills tab content is displayed
    expect(screen.getByText('Priority Skill Development')).toBeInTheDocument()
    expect(
      screen.getByText('You have 2 high-importance skills that need development.')
    ).toBeInTheDocument()
    expect(screen.getByText('Current Level:')).toBeInTheDocument()
    expect(screen.getByText('BEGINNER')).toBeInTheDocument()
    expect(screen.getByText('Target Level:')).toBeInTheDocument()
    expect(screen.getByText('INTERMEDIATE')).toBeInTheDocument()
  })

  it('should handle feedback submission for helpful insights', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Task Completion Rate')).toBeInTheDocument()
    })

    // Find and click the helpful button
    const helpfulButton = screen.getAllByRole('button', { name: /Helpful/i })[0]

    await act(async () => {
      await userEvent.click(helpfulButton)
    })

    // Verify feedback was submitted
    expect(mockInsightsClient.submitFeedback).toHaveBeenCalledWith('helpful', 'productivity')
  })

  it('should handle feedback submission for unhelpful insights', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Task Completion Rate')).toBeInTheDocument()
    })

    // Find and click the not helpful button
    const notHelpfulButton = screen.getAllByRole('button', { name: /Not Helpful/i })[0]

    await act(async () => {
      await userEvent.click(notHelpfulButton)
    })

    // Verify feedback was submitted
    expect(mockInsightsClient.submitFeedback).toHaveBeenCalledWith('not_helpful', 'productivity')
  })

  it('should show empty state when no insights are available', async () => {
    // Override mock to return empty insights for this test only
    const emptyMockClient = {
      getInsights: vi.fn().mockResolvedValue({
        productivityInsights: [],
        goalInsights: [],
        skillInsights: [],
        isContextAware: false,
      }),
      submitFeedback: vi.fn().mockResolvedValue(true),
    }

    // Clear and reset mock for this specific test
    vi.clearAllMocks()
    vi.mocked(InsightsClient).mockImplementation(() => emptyMockClient)

    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Wait for data to load and check for empty state
    await waitFor(
      () => {
        expect(screen.getByText('No productivity insights available')).toBeInTheDocument()
      },
      { timeout: 3000 }
    )

    // Verify empty state messages
    expect(
      screen.getByText('Complete more tasks to generate productivity insights')
    ).toBeInTheDocument()

    // Click on Goals tab
    const goalsTab = screen.getByRole('tab', { name: /Goals/i })
    await act(async () => {
      await userEvent.click(goalsTab)
    })

    // Verify empty state for goals
    expect(screen.getByText('No goal insights available')).toBeInTheDocument()

    // Click on Skills tab
    const skillsTab = screen.getByRole('tab', { name: /Skills/i })
    await act(async () => {
      await userEvent.click(skillsTab)
    })

    // Verify empty state for skills
    expect(screen.getByText('No skill insights available')).toBeInTheDocument()
  })

  it('should handle API errors gracefully', async () => {
    // Override mock to simulate API error for this test only
    const errorMockClient = {
      getInsights: vi.fn().mockRejectedValue(new Error('API Error')),
      submitFeedback: vi.fn().mockResolvedValue(true),
    }

    // Clear and reset mock for this specific test
    vi.clearAllMocks()
    vi.mocked(InsightsClient).mockImplementation(() => errorMockClient)

    await act(async () => {
      render(
        <TestWrapper>
          <FocusInsightsSection />
        </TestWrapper>
      )
    })

    // Wait for error handling to complete
    await waitFor(() => {
      // Component should still render without crashing
      expect(screen.getByText('Focus Insights')).toBeInTheDocument()
    })
  })
})
