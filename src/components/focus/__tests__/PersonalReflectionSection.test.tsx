import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, test, expect, beforeEach, vi } from 'vitest'
import PersonalReflectionSection from '../PersonalReflectionSection'

// Mock services with proper implementation
const mockReflectionService = {
  getDailyCheckIn: vi.fn(),
  createDailyCheckIn: vi.fn(),
  getWeeklyReviews: vi.fn(),
  createWeeklyReview: vi.fn(),
  getAchievementLogs: vi.fn(),
  createAchievementLog: vi.fn(),
  getGrowthInsights: vi.fn(),
  generateAIInsights: vi.fn(),
  updateReflectionEntry: vi.fn(),
  deleteReflectionEntry: vi.fn(),
}

vi.mock('@/lib/services/reflection-service', () => ({
  ReflectionService: vi.fn(() => mockReflectionService),
}))

// Mock auth hooks
vi.mock('@/lib/auth/hooks', () => ({
  useAuth: () => ({
    user: {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      companyId: 'company-1',
    },
  }),
}))

// Mock feature flags
vi.mock('@/lib/hooks/useFeatureFlag', () => ({
  useFeatureFlag: () => true,
}))

// Simple test wrapper without complex context
function TestWrapper({ children }: { children: React.ReactNode }) {
  return <div data-testid='test-wrapper'>{children}</div>
}

// Mock feature flag hook
const mockHasFeature = vi.fn()
vi.mock('@/lib/auth/rbac', () => ({
  hasFeature: mockHasFeature,
}))

// Mock session
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    companyId: 'test-company-id',
    role: 'EMPLOYEE',
  },
}

vi.mock('next-auth/react', () => ({
  useSession: () => ({ data: mockSession, status: 'authenticated' }),
}))

describe('PersonalReflectionSection', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockHasFeature.mockReturnValue(true)
  })

  // 🔴 RED: Daily Check-ins Tests
  describe('Daily Check-ins', () => {
    test('renders daily check-in form when no entry exists for today', async () => {
      mockReflectionService.getDailyCheckIn.mockResolvedValue(null)

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      expect(screen.getByText('Daily Check-in')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /complete check-in/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/how are you feeling today/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/what are your priorities/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/energy level/i)).toBeInTheDocument()
    })

    test('displays completed daily check-in when entry exists', async () => {
      const mockCheckIn = {
        id: '1',
        date: new Date().toISOString().split('T')[0],
        mood: 'energized',
        priorities: 'Complete project deliverables',
        energyLevel: 8,
        challenges: 'Time management',
        achievements: 'Completed user testing',
        learnings: 'Learned about user behavior patterns',
        gratitude: 'Grateful for team support',
        tomorrowFocus: 'Implement feedback changes',
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockReflectionService.getDailyCheckIn.mockResolvedValue(mockCheckIn)

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText("✓ Today's Check-in Complete")).toBeInTheDocument()
        expect(screen.getByText('energized')).toBeInTheDocument()
        expect(screen.getByText('Complete project deliverables')).toBeInTheDocument()
      })
    })

    test('creates daily check-in with all required fields', async () => {
      mockReflectionService.getDailyCheckIn.mockResolvedValue(null)
      mockReflectionService.createDailyCheckIn.mockResolvedValue({
        id: '1',
        mood: 'focused',
        priorities: 'Review team feedback',
        energyLevel: 7,
      })

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      // Fill out check-in form
      fireEvent.change(screen.getByLabelText(/how are you feeling today/i), {
        target: { value: 'focused' },
      })
      fireEvent.change(screen.getByLabelText(/what are your priorities/i), {
        target: { value: 'Review team feedback' },
      })
      fireEvent.change(screen.getByLabelText(/energy level/i), {
        target: { value: '7' },
      })

      fireEvent.click(screen.getByRole('button', { name: /complete check-in/i }))

      await waitFor(() => {
        expect(mockReflectionService.createDailyCheckIn).toHaveBeenCalledWith('test-user-id', {
          mood: 'focused',
          priorities: 'Review team feedback',
          energyLevel: 7,
          challenges: '',
          achievements: '',
          learnings: '',
          gratitude: '',
          tomorrowFocus: '',
        })
      })
    })

    test('validates required fields in daily check-in', async () => {
      mockReflectionService.getDailyCheckIn.mockResolvedValue(null)

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('button', { name: /complete check-in/i }))

      await waitFor(() => {
        expect(screen.getByText(/mood is required/i)).toBeInTheDocument()
        expect(screen.getByText(/priorities are required/i)).toBeInTheDocument()
        expect(mockReflectionService.createDailyCheckIn).not.toHaveBeenCalled()
      })
    })
  })

  // 🔴 RED: Weekly Reviews Tests
  describe('Weekly Reviews', () => {
    test('renders weekly review form for current week', async () => {
      mockReflectionService.getWeeklyReviews.mockResolvedValue([])

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      // Switch to Weekly Reviews tab
      fireEvent.click(screen.getByRole('tab', { name: /weekly reviews/i }))

      expect(screen.getByText("This Week's Review")).toBeInTheDocument()
      expect(screen.getByLabelText(/key accomplishments/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/challenges faced/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/lessons learned/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/next week goals/i)).toBeInTheDocument()
    })

    test('creates weekly review with comprehensive data', async () => {
      mockReflectionService.getWeeklyReviews.mockResolvedValue([])
      mockReflectionService.createWeeklyReview.mockResolvedValue({
        id: '1',
        weekStartDate: new Date(),
        accomplishments: 'Delivered feature on time',
        challenges: 'Scope creep in requirements',
        lessons: 'Better upfront planning needed',
        nextWeekGoals: 'Focus on testing phase',
      })

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('tab', { name: /weekly reviews/i }))

      fireEvent.change(screen.getByLabelText(/key accomplishments/i), {
        target: { value: 'Delivered feature on time' },
      })
      fireEvent.change(screen.getByLabelText(/challenges faced/i), {
        target: { value: 'Scope creep in requirements' },
      })
      fireEvent.change(screen.getByLabelText(/lessons learned/i), {
        target: { value: 'Better upfront planning needed' },
      })
      fireEvent.change(screen.getByLabelText(/next week goals/i), {
        target: { value: 'Focus on testing phase' },
      })

      fireEvent.click(screen.getByRole('button', { name: /submit review/i }))

      await waitFor(() => {
        expect(mockReflectionService.createWeeklyReview).toHaveBeenCalledWith('test-user-id', {
          accomplishments: 'Delivered feature on time',
          challenges: 'Scope creep in requirements',
          lessons: 'Better upfront planning needed',
          nextWeekGoals: 'Focus on testing phase',
          moodTrend: '',
          energyTrend: '',
          satisfactionRating: 0,
          improvementAreas: '',
        })
      })
    })
  })

  // 🔴 RED: Achievement Logs Tests
  describe('Achievement Logs', () => {
    test('renders achievement creation form', async () => {
      mockReflectionService.getAchievementLogs.mockResolvedValue([])

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('tab', { name: /achievement logs/i }))

      expect(screen.getByText('Log New Achievement')).toBeInTheDocument()
      expect(screen.getByLabelText(/achievement title/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/impact level/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/category/i)).toBeInTheDocument()
    })

    test('creates achievement log with proper validation', async () => {
      mockReflectionService.getAchievementLogs.mockResolvedValue([])
      mockReflectionService.createAchievementLog.mockResolvedValue({
        id: '1',
        title: 'Completed certification',
        description: 'AWS Solutions Architect',
        impactLevel: 'high',
        category: 'learning',
      })

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('tab', { name: /achievement logs/i }))

      fireEvent.change(screen.getByLabelText(/achievement title/i), {
        target: { value: 'Completed certification' },
      })
      fireEvent.change(screen.getByLabelText(/description/i), {
        target: { value: 'AWS Solutions Architect' },
      })
      fireEvent.change(screen.getByLabelText(/impact level/i), {
        target: { value: 'high' },
      })
      fireEvent.change(screen.getByLabelText(/category/i), {
        target: { value: 'learning' },
      })

      fireEvent.click(screen.getByRole('button', { name: /log achievement/i }))

      await waitFor(() => {
        expect(mockReflectionService.createAchievementLog).toHaveBeenCalledWith('test-user-id', {
          title: 'Completed certification',
          description: 'AWS Solutions Architect',
          impactLevel: 'high',
          category: 'learning',
          tags: [],
          linkedGoals: [],
          reflection: '',
        })
      })
    })
  })

  // 🔴 RED: Growth Insights Tests
  describe('Growth Insights', () => {
    test('displays AI-powered growth insights', async () => {
      const mockInsights = {
        patterns: [
          'Consistent high energy levels on Monday and Tuesday',
          'Most productive achievements in learning category',
        ],
        recommendations: [
          'Schedule challenging tasks for early week',
          'Continue focus on skill development',
        ],
        trends: {
          moodTrend: 'improving',
          energyTrend: 'stable',
          achievementGrowth: '+25%',
        },
      }

      mockReflectionService.getGrowthInsights.mockResolvedValue(mockInsights)

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('tab', { name: /growth insights/i }))

      await waitFor(() => {
        expect(
          screen.getByText('Consistent high energy levels on Monday and Tuesday')
        ).toBeInTheDocument()
        expect(screen.getByText('Schedule challenging tasks for early week')).toBeInTheDocument()
        expect(screen.getByText('improving')).toBeInTheDocument()
        expect(screen.getByText('+25%')).toBeInTheDocument()
      })
    })

    test('generates new AI insights with real-time analysis', async () => {
      mockReflectionService.getGrowthInsights.mockResolvedValue(null)
      mockReflectionService.generateAIInsights.mockResolvedValue({
        patterns: ['New pattern discovered'],
        recommendations: ['New recommendation generated'],
      })

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('tab', { name: /growth insights/i }))
      fireEvent.click(screen.getByRole('button', { name: /generate insights/i }))

      await waitFor(() => {
        expect(mockReflectionService.generateAIInsights).toHaveBeenCalledWith('test-user-id')
        expect(screen.getByText('New pattern discovered')).toBeInTheDocument()
      })
    })
  })

  // 🔴 RED: Error Handling Tests
  describe('Error Handling', () => {
    test('handles API errors gracefully during daily check-in creation', async () => {
      mockReflectionService.getDailyCheckIn.mockResolvedValue(null)
      mockReflectionService.createDailyCheckIn.mockRejectedValue(new Error('API Error'))

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.change(screen.getByLabelText(/how are you feeling today/i), {
        target: { value: 'stressed' },
      })
      fireEvent.change(screen.getByLabelText(/what are your priorities/i), {
        target: { value: 'Handle errors' },
      })

      fireEvent.click(screen.getByRole('button', { name: /complete check-in/i }))

      await waitFor(() => {
        expect(screen.getByText(/failed to save check-in/i)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
      })
    })

    test('provides fallback content when insights generation fails', async () => {
      mockReflectionService.getGrowthInsights.mockRejectedValue(new Error('AI Service Unavailable'))

      render(
        <TestWrapper>
          <PersonalReflectionSection />
        </TestWrapper>
      )

      fireEvent.click(screen.getByRole('tab', { name: /growth insights/i }))

      await waitFor(() => {
        expect(screen.getByText(/insights temporarily unavailable/i)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
      })
    })
  })
})
