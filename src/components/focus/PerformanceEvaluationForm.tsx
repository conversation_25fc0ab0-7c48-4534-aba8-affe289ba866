'use client'

import React, { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { TrendingUp, Award, Target, Clock, Plus, X } from 'lucide-react'
import { performanceClient } from '@/lib/services/performance-client'

// Validation schema for performance evaluation
const evaluationSchema = z.object({
  title: z.string().min(3, { message: 'Title must be at least 3 characters' }),
  evaluationType: z.enum(['self', 'peer', 'manager', '360']),
  metrics: z
    .array(
      z.object({
        name: z.string().min(1, { message: 'Metric name is required' }),
        value: z.number().min(0).max(100),
        category: z.string(),
      })
    )
    .min(1, { message: 'At least one metric is required' }),
  strengths: z.array(z.string()).min(1, { message: 'At least one strength is required' }),
  areasForImprovement: z
    .array(z.string())
    .min(1, { message: 'At least one area for improvement is required' }),
  rating: z.number().min(1).max(5),
  feedback: z.string().min(10, { message: 'Feedback must be at least 10 characters' }),
  goals: z.array(z.string()).optional(),
})

type EvaluationFormValues = z.infer<typeof evaluationSchema>

interface PerformanceEvaluationFormProps {
  onSuccess?: () => void
  onCancel?: () => void
}

// Default metrics for new evaluations
const defaultMetrics = [
  { name: 'Task Completion Rate', value: 75, category: 'productivity' },
  { name: 'Goal Achievement', value: 70, category: 'goals' },
  { name: 'Skill Development', value: 65, category: 'skills' },
  { name: 'Team Collaboration', value: 80, category: 'engagement' },
]

export function PerformanceEvaluationForm({ onSuccess, onCancel }: PerformanceEvaluationFormProps) {
  const [submitting, setSubmitting] = useState(false)
  const [newStrength, setNewStrength] = useState('')
  const [newImprovement, setNewImprovement] = useState('')
  const [newGoal, setNewGoal] = useState('')

  // Create form with default values
  const form = useForm<EvaluationFormValues>({
    resolver: zodResolver(evaluationSchema),
    defaultValues: {
      title: `Performance Evaluation - ${new Date().toLocaleDateString()}`,
      evaluationType: 'self',
      metrics: defaultMetrics,
      strengths: ['Communication skills', 'Problem solving'],
      areasForImprovement: ['Time management'],
      rating: 3,
      feedback: '',
      goals: [],
    },
  })

  // Handlers for array fields
  const handleAddStrength = () => {
    if (newStrength.trim()) {
      const currentStrengths = form.getValues('strengths') || []
      form.setValue('strengths', [...currentStrengths, newStrength.trim()])
      setNewStrength('')
    }
  }

  const handleRemoveStrength = (index: number) => {
    const currentStrengths = form.getValues('strengths') || []
    form.setValue(
      'strengths',
      currentStrengths.filter((_, i) => i !== index)
    )
  }

  const handleAddImprovement = () => {
    if (newImprovement.trim()) {
      const currentImprovements = form.getValues('areasForImprovement') || []
      form.setValue('areasForImprovement', [...currentImprovements, newImprovement.trim()])
      setNewImprovement('')
    }
  }

  const handleRemoveImprovement = (index: number) => {
    const currentImprovements = form.getValues('areasForImprovement') || []
    form.setValue(
      'areasForImprovement',
      currentImprovements.filter((_, i) => i !== index)
    )
  }

  const handleAddGoal = () => {
    if (newGoal.trim()) {
      const currentGoals = form.getValues('goals') || []
      form.setValue('goals', [...currentGoals, newGoal.trim()])
      setNewGoal('')
    }
  }

  const handleRemoveGoal = (index: number) => {
    const currentGoals = form.getValues('goals') || []
    form.setValue(
      'goals',
      currentGoals.filter((_, i) => i !== index)
    )
  }

  // Submit handler
  const onSubmit = async (data: EvaluationFormValues) => {
    setSubmitting(true)
    try {
      await performanceClient.createEvaluation(data)
      toast.success('Performance evaluation saved successfully')
      onSuccess?.()
    } catch (error) {
      console.error('Error creating performance evaluation:', error)
      toast.error('Failed to save performance evaluation')
    } finally {
      setSubmitting(false)
    }
  }

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'productivity':
        return <Clock className='h-4 w-4' />
      case 'skills':
        return <Award className='h-4 w-4' />
      case 'goals':
        return <Target className='h-4 w-4' />
      case 'engagement':
        return <TrendingUp className='h-4 w-4' />
      default:
        return <TrendingUp className='h-4 w-4' />
    }
  }

  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'productivity':
        return 'bg-blue-100 text-blue-800'
      case 'skills':
        return 'bg-purple-100 text-purple-800'
      case 'goals':
        return 'bg-green-100 text-green-800'
      case 'engagement':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
        <div className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            {/* Title */}
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Evaluation Title</FormLabel>
                  <FormControl>
                    <Input placeholder='Performance Evaluation' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Evaluation Type */}
            <FormField
              control={form.control}
              name='evaluationType'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Evaluation Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select evaluation type' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='self'>Self Assessment</SelectItem>
                      <SelectItem value='peer'>Peer Review</SelectItem>
                      <SelectItem value='manager'>Manager Evaluation</SelectItem>
                      <SelectItem value='360'>360° Feedback</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Overall Rating */}
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-base'>Overall Performance Rating</CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name='rating'
                render={({ field }) => (
                  <FormItem className='space-y-4'>
                    <div className='flex justify-between'>
                      <FormLabel>Rating ({field.value} out of 5)</FormLabel>
                    </div>
                    <FormControl>
                      <Slider
                        defaultValue={[field.value]}
                        max={5}
                        min={1}
                        step={0.5}
                        onValueChange={vals => field.onChange(vals[0])}
                      />
                    </FormControl>
                    <div className='flex justify-between text-xs text-muted-foreground'>
                      <span>Needs Improvement</span>
                      <span>Average</span>
                      <span>Excellent</span>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-base'>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {form.watch('metrics').map((metric, index) => (
                  <div key={index} className='space-y-2'>
                    <div className='flex justify-between items-center'>
                      <div className='flex items-center space-x-2'>
                        {getCategoryIcon(metric.category)}
                        <span>{metric.name}</span>
                        <Badge variant='outline' className={getCategoryColor(metric.category)}>
                          {metric.category}
                        </Badge>
                      </div>
                      <span className='text-sm font-medium'>{metric.value}%</span>
                    </div>
                    <FormField
                      control={form.control}
                      name={`metrics.${index}.value`}
                      render={({ field }) => (
                        <FormItem className='mb-4'>
                          <FormControl>
                            <Slider
                              defaultValue={[field.value]}
                              max={100}
                              step={1}
                              onValueChange={vals => {
                                const updatedMetrics = [...form.getValues('metrics')]
                                updatedMetrics[index].value = vals[0]
                                form.setValue('metrics', updatedMetrics)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Strengths */}
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-base'>Key Strengths</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex flex-wrap gap-2'>
                  {form.watch('strengths').map((strength, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1 px-3 py-1'
                    >
                      {strength}
                      <Button
                        type='button'
                        variant='ghost'
                        size='icon'
                        className='h-4 w-4 ml-1 p-0'
                        onClick={() => handleRemoveStrength(index)}
                      >
                        <X className='h-3 w-3' />
                        <span className='sr-only'>Remove</span>
                      </Button>
                    </Badge>
                  ))}
                </div>

                <div className='flex gap-2 mt-2'>
                  <Input
                    placeholder='Add a strength'
                    value={newStrength}
                    onChange={e => setNewStrength(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        handleAddStrength()
                      }
                    }}
                  />
                  <Button type='button' size='sm' onClick={handleAddStrength}>
                    <Plus className='h-4 w-4 mr-1' />
                    Add
                  </Button>
                </div>
                {form.formState.errors.strengths && (
                  <p className='text-sm font-medium text-destructive'>
                    {form.formState.errors.strengths.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Areas for Improvement */}
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-base'>Areas for Improvement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex flex-wrap gap-2'>
                  {form.watch('areasForImprovement').map((area, index) => (
                    <Badge
                      key={index}
                      variant='outline'
                      className='flex items-center gap-1 px-3 py-1'
                    >
                      {area}
                      <Button
                        type='button'
                        variant='ghost'
                        size='icon'
                        className='h-4 w-4 ml-1 p-0'
                        onClick={() => handleRemoveImprovement(index)}
                      >
                        <X className='h-3 w-3' />
                        <span className='sr-only'>Remove</span>
                      </Button>
                    </Badge>
                  ))}
                </div>

                <div className='flex gap-2 mt-2'>
                  <Input
                    placeholder='Add an area for improvement'
                    value={newImprovement}
                    onChange={e => setNewImprovement(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        handleAddImprovement()
                      }
                    }}
                  />
                  <Button type='button' size='sm' onClick={handleAddImprovement}>
                    <Plus className='h-4 w-4 mr-1' />
                    Add
                  </Button>
                </div>
                {form.formState.errors.areasForImprovement && (
                  <p className='text-sm font-medium text-destructive'>
                    {form.formState.errors.areasForImprovement.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Detailed Feedback */}
          <FormField
            control={form.control}
            name='feedback'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Detailed Feedback</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder='Provide comprehensive feedback on performance, achievements, and areas for growth'
                    {...field}
                    rows={5}
                  />
                </FormControl>
                <FormDescription>
                  Include specific examples, context, and actionable advice
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Development Goals */}
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-base'>Development Goals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex flex-wrap gap-2'>
                  {form.watch('goals')?.map((goal, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1 px-3 py-1'
                    >
                      {goal}
                      <Button
                        type='button'
                        variant='ghost'
                        size='icon'
                        className='h-4 w-4 ml-1 p-0'
                        onClick={() => handleRemoveGoal(index)}
                      >
                        <X className='h-3 w-3' />
                        <span className='sr-only'>Remove</span>
                      </Button>
                    </Badge>
                  ))}
                </div>

                <div className='flex gap-2 mt-2'>
                  <Input
                    placeholder='Add a development goal'
                    value={newGoal}
                    onChange={e => setNewGoal(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        handleAddGoal()
                      }
                    }}
                  />
                  <Button type='button' size='sm' onClick={handleAddGoal}>
                    <Plus className='h-4 w-4 mr-1' />
                    Add
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='flex justify-end space-x-2'>
          <Button type='button' variant='outline' onClick={onCancel} disabled={submitting}>
            Cancel
          </Button>
          <Button type='submit' disabled={submitting}>
            {submitting ? 'Saving...' : 'Save Evaluation'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
