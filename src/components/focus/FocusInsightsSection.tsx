/**
 * Focus Insights Section Component
 * AI-powered insights and recommendations based on aggregated Focus data
 * Part of Task 5.5: Develop Insights with AI-powered Suggestions
 */

'use client'

import React, { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card'
import {
  Brain,
  Lightbulb,
  TrendingUp,
  ThumbsUp,
  ThumbsDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  Target,
  Zap,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { hasFeature } from '@/lib/feature-flags'
import { useAuthContext } from '@/components/auth/AuthContextProvider'
import { toast } from 'sonner'
import { InsightsClient } from '@/lib/services/insights-client'
import {
  FocusAnalyticsData,
  ProductivityInsight,
  GoalInsight,
  SkillInsight,
} from '@/lib/services/focus-analytics-service'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'

/**
 * FocusInsightsSection Component
 * Displays AI-driven insights from the Focus section data
 */
export function FocusInsightsSection() {
  const { user } = useAuthContext()
  const [insights, setInsights] = useState<FocusAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [feedbackSubmitting, setFeedbackSubmitting] = useState<Record<string, boolean>>({})
  const insightsClient = new InsightsClient()

  // Fetch insights data on component mount
  useEffect(() => {
    const fetchInsights = async () => {
      if (!user) return

      try {
        setLoading(true)
        const data = await insightsClient.getInsights()
        setInsights(data)
      } catch (error) {
        console.error('Error fetching insights:', error)
        toast.error('Failed to load insights. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    fetchInsights()
  }, [user])

  // Handle feedback submission
  const handleFeedback = async (
    insightId: string,
    feedback: 'helpful' | 'not_helpful',
    category: string
  ) => {
    if (!user) return

    try {
      setFeedbackSubmitting(prev => ({ ...prev, [insightId]: true }))

      const success = await insightsClient.submitFeedback(feedback, category)

      if (success) {
        toast.success('Feedback submitted. Thank you!')
      } else {
        throw new Error('Failed to submit feedback')
      }
    } catch (error) {
      console.error('Error submitting feedback:', error)
      toast.error('Failed to submit feedback. Please try again later.')
    } finally {
      setFeedbackSubmitting(prev => ({ ...prev, [insightId]: false }))
    }
  }

  // Render trend icons
  const renderTrendIcon = (trend: 'up' | 'down' | 'neutral', value: number) => {
    if (trend === 'up') {
      return (
        <Badge
          variant='outline'
          className='flex items-center gap-1 bg-green-50 text-green-700 border-green-200'
        >
          <ArrowUp className='h-3 w-3' />+{value}%
        </Badge>
      )
    } else if (trend === 'down') {
      return (
        <Badge
          variant='outline'
          className='flex items-center gap-1 bg-red-50 text-red-700 border-red-200'
        >
          <ArrowDown className='h-3 w-3' />
          {value}%
        </Badge>
      )
    }
    return (
      <Badge variant='outline' className='flex items-center gap-1'>
        <ArrowRight className='h-3 w-3' />
        {value}%
      </Badge>
    )
  }

  // Render productivity insights
  const renderProductivityInsights = () => {
    if (!insights || insights.productivityInsights.length === 0) {
      return (
        <Card className='mb-4'>
          <CardHeader>
            <CardTitle className='text-gray-500'>No productivity insights available</CardTitle>
            <CardDescription>Complete more tasks to generate productivity insights</CardDescription>
          </CardHeader>
        </Card>
      )
    }

    return insights.productivityInsights.map((insight: ProductivityInsight) => (
      <Card key={insight.id} className='mb-4'>
        <CardHeader>
          <div className='flex justify-between'>
            <div>
              <CardTitle>{insight.title}</CardTitle>
              <CardDescription>{insight.description}</CardDescription>
            </div>
            {renderTrendIcon(insight.trend, insight.trendValue)}
          </div>
        </CardHeader>
        <CardContent>
          <div className='mb-4'>
            <div className='flex justify-between mb-2'>
              <span className='text-sm font-medium'>Completion Rate</span>
              <span className='text-sm font-medium'>{insight.metric}%</span>
            </div>
            <Progress value={insight.metric} className='h-2' />
          </div>

          {insight.actionItems && insight.actionItems.length > 0 && (
            <div className='mt-4'>
              <h4 className='text-sm font-semibold mb-2'>Suggested Actions:</h4>
              <ul className='list-disc pl-5 space-y-1'>
                {insight.actionItems.map((item, index) => (
                  <li key={index} className='text-sm'>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
        <CardFooter className='flex justify-between pt-2 border-t'>
          <span className='text-xs text-gray-500'>
            {insights.isContextAware ? 'Personalized for you' : 'Based on your data'}
          </span>
          <div className='flex gap-2'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => handleFeedback(insight.id, 'helpful', 'productivity')}
              disabled={feedbackSubmitting[insight.id]}
              className='text-green-600 hover:text-green-700 hover:bg-green-50'
            >
              <ThumbsUp className='h-4 w-4 mr-1' />
              Helpful
            </Button>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => handleFeedback(insight.id, 'not_helpful', 'productivity')}
              disabled={feedbackSubmitting[insight.id]}
              className='text-red-600 hover:text-red-700 hover:bg-red-50'
            >
              <ThumbsDown className='h-4 w-4 mr-1' />
              Not Helpful
            </Button>
          </div>
        </CardFooter>
      </Card>
    ))
  }

  // Render goal insights
  const renderGoalInsights = () => {
    if (!insights || insights.goalInsights.length === 0) {
      return (
        <Card className='mb-4'>
          <CardHeader>
            <CardTitle className='text-gray-500'>No goal insights available</CardTitle>
            <CardDescription>Create and work on goals to generate insights</CardDescription>
          </CardHeader>
        </Card>
      )
    }

    return insights.goalInsights.map((insight: GoalInsight) => (
      <Card key={insight.id} className='mb-4'>
        <CardHeader>
          <div className='flex justify-between'>
            <div>
              <CardTitle>{insight.title}</CardTitle>
              <CardDescription>{insight.description}</CardDescription>
            </div>
            <Badge variant={insight.onTrack ? 'outline' : 'destructive'}>
              {insight.onTrack ? 'On Track' : 'Needs Attention'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className='mb-4'>
            <div className='flex justify-between mb-2'>
              <span className='text-sm font-medium'>Average Progress</span>
              <span className='text-sm font-medium'>{insight.progress}%</span>
            </div>
            <Progress value={insight.progress} className='h-2' />
          </div>

          <div className='flex justify-between text-sm mt-2'>
            <span>Days Remaining: {insight.daysRemaining}</span>
            <span>
              <Target className='h-4 w-4 inline mr-1' />
              Target: {insight.target}%
            </span>
          </div>

          {insight.actionItems && insight.actionItems.length > 0 && (
            <div className='mt-4'>
              <h4 className='text-sm font-semibold mb-2'>Suggested Actions:</h4>
              <ul className='list-disc pl-5 space-y-1'>
                {insight.actionItems.map((item, index) => (
                  <li key={index} className='text-sm'>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
        <CardFooter className='flex justify-between pt-2 border-t'>
          <span className='text-xs text-gray-500'>
            {insights.isContextAware ? 'Personalized for you' : 'Based on your data'}
          </span>
          <div className='flex gap-2'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => handleFeedback(insight.id, 'helpful', 'goals')}
              disabled={feedbackSubmitting[insight.id]}
              className='text-green-600 hover:text-green-700 hover:bg-green-50'
            >
              <ThumbsUp className='h-4 w-4 mr-1' />
              Helpful
            </Button>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => handleFeedback(insight.id, 'not_helpful', 'goals')}
              disabled={feedbackSubmitting[insight.id]}
              className='text-red-600 hover:text-red-700 hover:bg-red-50'
            >
              <ThumbsDown className='h-4 w-4 mr-1' />
              Not Helpful
            </Button>
          </div>
        </CardFooter>
      </Card>
    ))
  }

  // Render skill insights
  const renderSkillInsights = () => {
    if (!insights || insights.skillInsights.length === 0) {
      return (
        <Card className='mb-4'>
          <CardHeader>
            <CardTitle className='text-gray-500'>No skill insights available</CardTitle>
            <CardDescription>Add skills and assessments to generate insights</CardDescription>
          </CardHeader>
        </Card>
      )
    }

    return insights.skillInsights.map((insight: SkillInsight) => (
      <Card key={insight.id} className='mb-4'>
        <CardHeader>
          <div className='flex justify-between'>
            <div>
              <CardTitle>{insight.title}</CardTitle>
              <CardDescription>{insight.description}</CardDescription>
            </div>
            <Badge variant='secondary'>Gap: {insight.gap}</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className='flex justify-between text-sm mb-4'>
            <span>
              Current Level: <Badge variant='outline'>{insight.currentLevel}</Badge>
            </span>
            <span>
              Target Level: <Badge variant='outline'>{insight.targetLevel}</Badge>
            </span>
          </div>

          {insight.actionItems && insight.actionItems.length > 0 && (
            <div className='mt-4'>
              <h4 className='text-sm font-semibold mb-2'>Suggested Actions:</h4>
              <ul className='list-disc pl-5 space-y-1'>
                {insight.actionItems.map((item, index) => (
                  <li key={index} className='text-sm'>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
        <CardFooter className='flex justify-between pt-2 border-t'>
          <span className='text-xs text-gray-500'>
            {insights.isContextAware ? 'Personalized for you' : 'Based on your data'}
          </span>
          <div className='flex gap-2'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => handleFeedback(insight.id, 'helpful', 'skills')}
              disabled={feedbackSubmitting[insight.id]}
              className='text-green-600 hover:text-green-700 hover:bg-green-50'
            >
              <ThumbsUp className='h-4 w-4 mr-1' />
              Helpful
            </Button>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => handleFeedback(insight.id, 'not_helpful', 'skills')}
              disabled={feedbackSubmitting[insight.id]}
              className='text-red-600 hover:text-red-700 hover:bg-red-50'
            >
              <ThumbsDown className='h-4 w-4 mr-1' />
              Not Helpful
            </Button>
          </div>
        </CardFooter>
      </Card>
    ))
  }

  // Render loading state
  const renderLoadingState = () => (
    <div>
      <Skeleton className='h-[200px] w-full mb-4' />
      <Skeleton className='h-[200px] w-full mb-4' />
      <Skeleton className='h-[200px] w-full' />
    </div>
  )

  return (
    <div className='p-4'>
      <div className='flex items-center mb-6'>
        <Brain className='h-6 w-6 mr-2 text-primary' />
        <h2 className='text-2xl font-semibold'>Focus Insights</h2>
      </div>

      <div className='mb-6'>
        <p className='text-muted-foreground'>
          Data-driven insights to help you improve productivity, achieve goals, and develop skills.
          {user?.companyId && hasFeature(user.companyId, 'contextAwareness') && (
            <span className='ml-1'>Personalized for your work style and preferences.</span>
          )}
        </p>
      </div>

      <Tabs defaultValue='productivity' className='w-full'>
        <TabsList className='mb-4'>
          <TabsTrigger value='productivity' className='flex items-center'>
            <Zap className='h-4 w-4 mr-2' />
            Productivity
          </TabsTrigger>
          <TabsTrigger value='goals' className='flex items-center'>
            <Target className='h-4 w-4 mr-2' />
            Goals
          </TabsTrigger>
          <TabsTrigger value='skills' className='flex items-center'>
            <Lightbulb className='h-4 w-4 mr-2' />
            Skills
          </TabsTrigger>
        </TabsList>

        <TabsContent value='productivity' className='pt-2'>
          {loading ? renderLoadingState() : renderProductivityInsights()}
        </TabsContent>

        <TabsContent value='goals' className='pt-2'>
          {loading ? renderLoadingState() : renderGoalInsights()}
        </TabsContent>

        <TabsContent value='skills' className='pt-2'>
          {loading ? renderLoadingState() : renderSkillInsights()}
        </TabsContent>
      </Tabs>
    </div>
  )
}
