'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Calendar, Clock, Target, TrendingUp, CheckCircle, AlertCircle } from 'lucide-react'
import { OKRVisualization } from './OKRVisualization'

// Types for the component
type GoalTimeframe = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'

interface Goal {
  id: string
  title: string
  description?: string
  type: string
  status: string
  progress: number
  targetValue?: number
  currentValue?: number
  unit?: string
  category: string
  timeframe: GoalTimeframe
  startDate?: string
  targetDate?: string
  priority: string
  keyResults?: Array<{
    id: string
    title: string
    description?: string
    targetValue: number
    currentValue: number
    unit: string
    status: string
    dueDate?: string
  }>
}

interface TimeframeOKRViewProps {
  goals: Goal[]
  isLoading: boolean
}

export function TimeframeOKRView({ goals, isLoading }: TimeframeOKRViewProps) {
  const [activeTimeframe, setActiveTimeframe] = useState<GoalTimeframe>('QUARTERLY')

  // Group goals by timeframe
  const goalsByTimeframe = useMemo(() => {
    const grouped: Record<GoalTimeframe, Goal[]> = {
      DAILY: [],
      WEEKLY: [],
      MONTHLY: [],
      QUARTERLY: [],
      YEARLY: [],
      CUSTOM: [],
    }

    goals.forEach(goal => {
      if (goal.type === 'OBJECTIVE') {
        if (grouped[goal.timeframe]) {
          grouped[goal.timeframe].push(goal)
        }
      }
    })

    return grouped
  }, [goals])

  // Calculate counts for each timeframe
  const timeframeCounts = useMemo(() => {
    return {
      DAILY: goalsByTimeframe.DAILY.length,
      WEEKLY: goalsByTimeframe.WEEKLY.length,
      MONTHLY: goalsByTimeframe.MONTHLY.length,
      QUARTERLY: goalsByTimeframe.QUARTERLY.length,
      YEARLY: goalsByTimeframe.YEARLY.length,
      CUSTOM: goalsByTimeframe.CUSTOM.length,
    }
  }, [goalsByTimeframe])

  // Calculate progress for each timeframe
  const timeframeProgress = useMemo(() => {
    const calculateProgress = (goals: Goal[]) => {
      if (goals.length === 0) return 0

      const totalProgress = goals.reduce((sum, goal) => sum + goal.progress, 0)
      return Math.round(totalProgress / goals.length)
    }

    return {
      DAILY: calculateProgress(goalsByTimeframe.DAILY),
      WEEKLY: calculateProgress(goalsByTimeframe.WEEKLY),
      MONTHLY: calculateProgress(goalsByTimeframe.MONTHLY),
      QUARTERLY: calculateProgress(goalsByTimeframe.QUARTERLY),
      YEARLY: calculateProgress(goalsByTimeframe.YEARLY),
      CUSTOM: calculateProgress(goalsByTimeframe.CUSTOM),
    }
  }, [goalsByTimeframe])

  // Default to Quarterly view or first timeframe with goals
  useEffect(() => {
    // Default is quarterly, but if no quarterly goals exist and there are other timeframes with goals, select the first available
    if (
      timeframeCounts.QUARTERLY === 0 &&
      (timeframeCounts.DAILY > 0 ||
        timeframeCounts.WEEKLY > 0 ||
        timeframeCounts.MONTHLY > 0 ||
        timeframeCounts.YEARLY > 0 ||
        timeframeCounts.CUSTOM > 0)
    ) {
      const firstNonEmptyTimeframe = (Object.keys(timeframeCounts) as GoalTimeframe[]).find(
        timeframe => timeframeCounts[timeframe] > 0
      )

      if (firstNonEmptyTimeframe) {
        setActiveTimeframe(firstNonEmptyTimeframe)
      }
    }
  }, [timeframeCounts])

  // Loading state
  if (isLoading) {
    return (
      <div className='py-6' data-testid='loading-skeleton'>
        <div className='animate-pulse space-y-4'>
          <div className='h-8 bg-muted rounded w-48'></div>
          <div className='h-64 bg-muted rounded'></div>
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-6'>
      {/* Timeframe Navigation Tabs */}
      <Tabs
        value={activeTimeframe}
        onValueChange={value => setActiveTimeframe(value as GoalTimeframe)}
        className='w-full'
      >
        <TabsList className='grid grid-cols-3 md:grid-cols-6 w-full'>
          <TabsTrigger value='DAILY' disabled={timeframeCounts.DAILY === 0} className='relative'>
            Daily
            {timeframeCounts.DAILY > 0 && (
              <span className='absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full text-xs w-5 h-5 flex items-center justify-center'>
                {timeframeCounts.DAILY}
              </span>
            )}
          </TabsTrigger>

          <TabsTrigger value='WEEKLY' disabled={timeframeCounts.WEEKLY === 0} className='relative'>
            Weekly
            {timeframeCounts.WEEKLY > 0 && (
              <span className='absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full text-xs w-5 h-5 flex items-center justify-center'>
                {timeframeCounts.WEEKLY}
              </span>
            )}
          </TabsTrigger>

          <TabsTrigger
            value='MONTHLY'
            disabled={timeframeCounts.MONTHLY === 0}
            className='relative'
          >
            Monthly
            {timeframeCounts.MONTHLY > 0 && (
              <span className='absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full text-xs w-5 h-5 flex items-center justify-center'>
                {timeframeCounts.MONTHLY}
              </span>
            )}
          </TabsTrigger>

          <TabsTrigger
            value='QUARTERLY'
            disabled={timeframeCounts.QUARTERLY === 0}
            className='relative'
          >
            Quarterly
            {timeframeCounts.QUARTERLY > 0 && (
              <span className='absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full text-xs w-5 h-5 flex items-center justify-center'>
                {timeframeCounts.QUARTERLY}
              </span>
            )}
          </TabsTrigger>

          <TabsTrigger value='YEARLY' disabled={timeframeCounts.YEARLY === 0} className='relative'>
            Yearly
            {timeframeCounts.YEARLY > 0 && (
              <span className='absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full text-xs w-5 h-5 flex items-center justify-center'>
                {timeframeCounts.YEARLY}
              </span>
            )}
          </TabsTrigger>

          <TabsTrigger value='CUSTOM' disabled={timeframeCounts.CUSTOM === 0} className='relative'>
            Custom
            {timeframeCounts.CUSTOM > 0 && (
              <span className='absolute -top-1 -right-1 bg-primary text-primary-foreground rounded-full text-xs w-5 h-5 flex items-center justify-center'>
                {timeframeCounts.CUSTOM}
              </span>
            )}
          </TabsTrigger>
        </TabsList>

        {/* Timeframe Content */}
        <TabsContent value='DAILY' className='mt-6'>
          <OKRVisualization objectives={goalsByTimeframe.DAILY} timeframe='DAILY' />
        </TabsContent>

        <TabsContent value='WEEKLY' className='mt-6'>
          <OKRVisualization objectives={goalsByTimeframe.WEEKLY} timeframe='WEEKLY' />
        </TabsContent>

        <TabsContent value='MONTHLY' className='mt-6'>
          <OKRVisualization objectives={goalsByTimeframe.MONTHLY} timeframe='MONTHLY' />
        </TabsContent>

        <TabsContent value='QUARTERLY' className='mt-6'>
          <OKRVisualization objectives={goalsByTimeframe.QUARTERLY} timeframe='QUARTERLY' />
        </TabsContent>

        <TabsContent value='YEARLY' className='mt-6'>
          <OKRVisualization objectives={goalsByTimeframe.YEARLY} timeframe='YEARLY' />
        </TabsContent>

        <TabsContent value='CUSTOM' className='mt-6'>
          <OKRVisualization objectives={goalsByTimeframe.CUSTOM} timeframe='CUSTOM' />
        </TabsContent>
      </Tabs>
    </div>
  )
}
