'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import {
  CalendarDays,
  TrendingUp,
  Award,
  Lightbulb,
  Target,
  Heart,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Loader2,
} from 'lucide-react'
import {
  ReflectionService,
  DailyCheckInData,
  WeeklyReviewData,
  AchievementLogData,
  GrowthInsights,
} from '@/lib/services/reflection-service'
import { hasFeature } from '@/lib/auth/rbac'

export default function PersonalReflectionSection() {
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState('daily-checkin')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Daily Check-in State
  const [dailyCheckIn, setDailyCheckIn] = useState<any>(null)
  const [checkInForm, setCheckInForm] = useState<DailyCheckInData>({
    mood: '',
    priorities: '',
    energyLevel: 5,
    challenges: '',
    achievements: '',
    learnings: '',
    gratitude: '',
    tomorrowFocus: '',
  })
  const [checkInErrors, setCheckInErrors] = useState<Record<string, string>>({})
  const [isSubmittingCheckIn, setIsSubmittingCheckIn] = useState(false)

  // Weekly Reviews State
  const [weeklyReviews, setWeeklyReviews] = useState<any[]>([])
  const [reviewForm, setReviewForm] = useState<WeeklyReviewData>({
    accomplishments: '',
    challenges: '',
    lessons: '',
    nextWeekGoals: '',
    moodTrend: '',
    energyTrend: '',
    satisfactionRating: 0,
    improvementAreas: '',
  })
  const [isSubmittingReview, setIsSubmittingReview] = useState(false)

  // Achievement Logs State
  const [achievementLogs, setAchievementLogs] = useState<any[]>([])
  const [achievementForm, setAchievementForm] = useState<AchievementLogData>({
    title: '',
    description: '',
    impactLevel: 'medium',
    category: 'project',
    tags: [],
    linkedGoals: [],
    reflection: '',
  })
  const [isSubmittingAchievement, setIsSubmittingAchievement] = useState(false)

  // Growth Insights State
  const [growthInsights, setGrowthInsights] = useState<GrowthInsights | null>(null)
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false)
  const [insightsError, setInsightsError] = useState<string | null>(null)

  const reflectionService = new ReflectionService()

  useEffect(() => {
    if (session?.user?.id) {
      loadReflectionData()
    }
  }, [session?.user?.id])

  const loadReflectionData = async () => {
    if (!session?.user?.id) return

    try {
      setLoading(true)
      await Promise.all([
        loadDailyCheckIn(),
        loadWeeklyReviews(),
        loadAchievementLogs(),
        loadGrowthInsights(),
      ])
    } catch (error) {
      console.error('Error loading reflection data:', error)
      setError('Failed to load reflection data')
    } finally {
      setLoading(false)
    }
  }

  const loadDailyCheckIn = async () => {
    try {
      const checkIn = await reflectionService.getDailyCheckIn(session!.user.id)
      setDailyCheckIn(checkIn)
    } catch (error) {
      console.error('Error loading daily check-in:', error)
    }
  }

  const loadWeeklyReviews = async () => {
    try {
      const reviews = await reflectionService.getWeeklyReviews(session!.user.id)
      setWeeklyReviews(reviews)
    } catch (error) {
      console.error('Error loading weekly reviews:', error)
    }
  }

  const loadAchievementLogs = async () => {
    try {
      const achievements = await reflectionService.getAchievementLogs(session!.user.id)
      setAchievementLogs(achievements)
    } catch (error) {
      console.error('Error loading achievement logs:', error)
    }
  }

  const loadGrowthInsights = async () => {
    try {
      const insights = await reflectionService.getGrowthInsights(session!.user.id)
      setGrowthInsights(insights)
      setInsightsError(null)
    } catch (error) {
      console.error('Error loading growth insights:', error)
      setInsightsError('Insights temporarily unavailable')
    }
  }

  // Daily Check-in Handlers
  const validateCheckInForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!checkInForm.mood.trim()) {
      errors.mood = 'Mood is required'
    }
    if (!checkInForm.priorities.trim()) {
      errors.priorities = 'Priorities are required'
    }

    setCheckInErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleCheckInSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateCheckInForm() || !session?.user?.id) return

    try {
      setIsSubmittingCheckIn(true)
      await reflectionService.createDailyCheckIn(session.user.id, checkInForm)
      await loadDailyCheckIn()

      // Reset form
      setCheckInForm({
        mood: '',
        priorities: '',
        energyLevel: 5,
        challenges: '',
        achievements: '',
        learnings: '',
        gratitude: '',
        tomorrowFocus: '',
      })
    } catch (error) {
      console.error('Error creating daily check-in:', error)
      setError('Failed to save check-in')
    } finally {
      setIsSubmittingCheckIn(false)
    }
  }

  // Weekly Review Handlers
  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!session?.user?.id) return

    try {
      setIsSubmittingReview(true)
      await reflectionService.createWeeklyReview(session.user.id, reviewForm)
      await loadWeeklyReviews()

      // Reset form
      setReviewForm({
        accomplishments: '',
        challenges: '',
        lessons: '',
        nextWeekGoals: '',
        moodTrend: '',
        energyTrend: '',
        satisfactionRating: 0,
        improvementAreas: '',
      })
    } catch (error) {
      console.error('Error creating weekly review:', error)
      setError('Failed to save weekly review')
    } finally {
      setIsSubmittingReview(false)
    }
  }

  // Achievement Log Handlers
  const handleAchievementSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!session?.user?.id) return

    try {
      setIsSubmittingAchievement(true)
      await reflectionService.createAchievementLog(session.user.id, achievementForm)
      await loadAchievementLogs()

      // Reset form
      setAchievementForm({
        title: '',
        description: '',
        impactLevel: 'medium',
        category: 'project',
        tags: [],
        linkedGoals: [],
        reflection: '',
      })
    } catch (error) {
      console.error('Error creating achievement log:', error)
      setError('Failed to save achievement')
    } finally {
      setIsSubmittingAchievement(false)
    }
  }

  // Growth Insights Handlers
  const handleGenerateInsights = async () => {
    if (!session?.user?.id) return

    try {
      setIsGeneratingInsights(true)
      setInsightsError(null)
      const insights = await reflectionService.generateAIInsights(session.user.id)
      setGrowthInsights(insights)
    } catch (error) {
      console.error('Error generating insights:', error)
      setInsightsError('Insights temporarily unavailable')
    } finally {
      setIsGeneratingInsights(false)
    }
  }

  if (loading) {
    return (
      <div className='space-y-4'>
        <Skeleton className='h-8 w-64' />
        <div className='grid gap-4'>
          <Skeleton className='h-32 w-full' />
          <Skeleton className='h-32 w-full' />
          <Skeleton className='h-32 w-full' />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant='destructive'>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>
          {error}
          <Button
            variant='outline'
            size='sm'
            className='ml-2'
            onClick={() => {
              setError(null)
              loadReflectionData()
            }}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>Personal Reflection</h2>
          <p className='text-muted-foreground'>
            Track your daily progress, weekly insights, and achievements
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='space-y-4'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='daily-checkin' className='flex items-center gap-2'>
            <CalendarDays className='h-4 w-4' />
            Daily Check-in
          </TabsTrigger>
          <TabsTrigger value='weekly-reviews' className='flex items-center gap-2'>
            <TrendingUp className='h-4 w-4' />
            Weekly Reviews
          </TabsTrigger>
          <TabsTrigger value='achievement-logs' className='flex items-center gap-2'>
            <Award className='h-4 w-4' />
            Achievement Logs
          </TabsTrigger>
          <TabsTrigger value='growth-insights' className='flex items-center gap-2'>
            <Lightbulb className='h-4 w-4' />
            Growth Insights
          </TabsTrigger>
        </TabsList>

        {/* Daily Check-in Tab */}
        <TabsContent value='daily-checkin' className='space-y-4'>
          {dailyCheckIn ? (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <CheckCircle className='h-5 w-5 text-green-500' />✓ Today's Check-in Complete
                </CardTitle>
                <CardDescription>
                  You completed your reflection for {dailyCheckIn.date}
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <Label className='text-sm font-medium'>Mood</Label>
                    <Badge variant='secondary'>{dailyCheckIn.mood}</Badge>
                  </div>
                  <div>
                    <Label className='text-sm font-medium'>Energy Level</Label>
                    <Badge variant='secondary'>{dailyCheckIn.energyLevel}/10</Badge>
                  </div>
                </div>
                <div>
                  <Label className='text-sm font-medium'>Priorities</Label>
                  <p className='text-sm text-muted-foreground'>{dailyCheckIn.priorities}</p>
                </div>
                {dailyCheckIn.achievements && (
                  <div>
                    <Label className='text-sm font-medium'>Achievements</Label>
                    <p className='text-sm text-muted-foreground'>{dailyCheckIn.achievements}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Daily Check-in</CardTitle>
                <CardDescription>
                  Take a moment to reflect on your day and set intentions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleCheckInSubmit} className='space-y-4'>
                  <div className='grid grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <Label htmlFor='mood' className='required'>
                        How are you feeling today?
                      </Label>
                      <Input
                        id='mood'
                        value={checkInForm.mood}
                        onChange={e => setCheckInForm(prev => ({ ...prev, mood: e.target.value }))}
                        placeholder='e.g., energized, focused, overwhelmed'
                        aria-required='true'
                      />
                      {checkInErrors.mood && (
                        <p className='text-sm text-destructive'>{checkInErrors.mood}</p>
                      )}
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor='energy-level'>Energy Level (1-10)</Label>
                      <Input
                        id='energy-level'
                        type='number'
                        min='1'
                        max='10'
                        value={checkInForm.energyLevel}
                        onChange={e =>
                          setCheckInForm(prev => ({
                            ...prev,
                            energyLevel: parseInt(e.target.value) || 5,
                          }))
                        }
                        aria-valuemin={1}
                        aria-valuemax={10}
                      />
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='priorities' className='required'>
                      What are your priorities today?
                    </Label>
                    <Textarea
                      id='priorities'
                      value={checkInForm.priorities}
                      onChange={e =>
                        setCheckInForm(prev => ({ ...prev, priorities: e.target.value }))
                      }
                      placeholder='Focus areas and key tasks...'
                      aria-required='true'
                    />
                    {checkInErrors.priorities && (
                      <p className='text-sm text-destructive'>{checkInErrors.priorities}</p>
                    )}
                  </div>

                  <div className='grid grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <Label htmlFor='challenges'>Challenges</Label>
                      <Textarea
                        id='challenges'
                        value={checkInForm.challenges}
                        onChange={e =>
                          setCheckInForm(prev => ({ ...prev, challenges: e.target.value }))
                        }
                        placeholder='What obstacles are you facing?'
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor='achievements'>Achievements</Label>
                      <Textarea
                        id='achievements'
                        value={checkInForm.achievements}
                        onChange={e =>
                          setCheckInForm(prev => ({ ...prev, achievements: e.target.value }))
                        }
                        placeholder='What did you accomplish?'
                      />
                    </div>
                  </div>

                  <Button type='submit' disabled={isSubmittingCheckIn} className='w-full'>
                    {isSubmittingCheckIn ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Saving...
                      </>
                    ) : (
                      'Complete Check-in'
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Weekly Reviews Tab */}
        <TabsContent value='weekly-reviews' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>This Week's Review</CardTitle>
              <CardDescription>Reflect on your week and plan for the next one</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleReviewSubmit} className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='accomplishments'>Key Accomplishments</Label>
                  <Textarea
                    id='accomplishments'
                    value={reviewForm.accomplishments}
                    onChange={e =>
                      setReviewForm(prev => ({ ...prev, accomplishments: e.target.value }))
                    }
                    placeholder='What did you achieve this week?'
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='challenges-faced'>Challenges Faced</Label>
                  <Textarea
                    id='challenges-faced'
                    value={reviewForm.challenges}
                    onChange={e => setReviewForm(prev => ({ ...prev, challenges: e.target.value }))}
                    placeholder='What obstacles did you encounter?'
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='lessons-learned'>Lessons Learned</Label>
                  <Textarea
                    id='lessons-learned'
                    value={reviewForm.lessons}
                    onChange={e => setReviewForm(prev => ({ ...prev, lessons: e.target.value }))}
                    placeholder='What insights did you gain?'
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='next-week-goals'>Next Week Goals</Label>
                  <Textarea
                    id='next-week-goals'
                    value={reviewForm.nextWeekGoals}
                    onChange={e =>
                      setReviewForm(prev => ({ ...prev, nextWeekGoals: e.target.value }))
                    }
                    placeholder='What do you want to focus on next week?'
                  />
                </div>

                <Button type='submit' disabled={isSubmittingReview} className='w-full'>
                  {isSubmittingReview ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Saving...
                    </>
                  ) : (
                    'Submit Review'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Achievement Logs Tab */}
        <TabsContent value='achievement-logs' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Log New Achievement</CardTitle>
              <CardDescription>Record and celebrate your accomplishments</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAchievementSubmit} className='space-y-4'>
                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='achievement-title'>Achievement Title</Label>
                    <Input
                      id='achievement-title'
                      value={achievementForm.title}
                      onChange={e =>
                        setAchievementForm(prev => ({ ...prev, title: e.target.value }))
                      }
                      placeholder='What did you achieve?'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='impact-level'>Impact Level</Label>
                    <select
                      id='impact-level'
                      value={achievementForm.impactLevel}
                      onChange={e =>
                        setAchievementForm(prev => ({
                          ...prev,
                          impactLevel: e.target.value as any,
                        }))
                      }
                      className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                    >
                      <option value='low'>Low</option>
                      <option value='medium'>Medium</option>
                      <option value='high'>High</option>
                    </select>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='category'>Category</Label>
                    <select
                      id='category'
                      value={achievementForm.category}
                      onChange={e =>
                        setAchievementForm(prev => ({ ...prev, category: e.target.value as any }))
                      }
                      className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                    >
                      <option value='project'>Project</option>
                      <option value='learning'>Learning</option>
                      <option value='leadership'>Leadership</option>
                      <option value='personal'>Personal</option>
                      <option value='team'>Team</option>
                    </select>
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='description'>Description</Label>
                  <Textarea
                    id='description'
                    value={achievementForm.description}
                    onChange={e =>
                      setAchievementForm(prev => ({ ...prev, description: e.target.value }))
                    }
                    placeholder='Describe your achievement...'
                  />
                </div>

                <Button type='submit' disabled={isSubmittingAchievement} className='w-full'>
                  {isSubmittingAchievement ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Saving...
                    </>
                  ) : (
                    'Log Achievement'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Growth Insights Tab */}
        <TabsContent value='growth-insights' className='space-y-4'>
          {insightsError ? (
            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertDescription>
                {insightsError}
                <Button
                  variant='outline'
                  size='sm'
                  className='ml-2'
                  onClick={() => {
                    setInsightsError(null)
                    loadGrowthInsights()
                  }}
                >
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          ) : growthInsights ? (
            <div className='space-y-4'>
              {/* Patterns */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <TrendingUp className='h-5 w-5' />
                    Patterns Discovered
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className='space-y-2'>
                    {growthInsights.patterns.map((pattern, index) => (
                      <li key={index} className='text-sm'>
                        • {pattern}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Target className='h-5 w-5' />
                    Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className='space-y-2'>
                    {growthInsights.recommendations.map((recommendation, index) => (
                      <li key={index} className='text-sm'>
                        • {recommendation}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* Trends */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Heart className='h-5 w-5' />
                    Current Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-3 gap-4'>
                    <div className='text-center'>
                      <p className='text-sm font-medium'>Mood</p>
                      <Badge variant='outline'>{growthInsights.trends.moodTrend}</Badge>
                    </div>
                    <div className='text-center'>
                      <p className='text-sm font-medium'>Energy</p>
                      <Badge variant='outline'>{growthInsights.trends.energyTrend}</Badge>
                    </div>
                    <div className='text-center'>
                      <p className='text-sm font-medium'>Growth</p>
                      <Badge variant='outline'>{growthInsights.trends.achievementGrowth}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Lightbulb className='h-5 w-5' />
                  AI-Powered Growth Insights
                </CardTitle>
                <CardDescription>
                  Generate personalized insights based on your reflection data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={handleGenerateInsights}
                  disabled={isGeneratingInsights}
                  className='w-full'
                >
                  {isGeneratingInsights ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Generating Insights...
                    </>
                  ) : (
                    <>
                      <RefreshCw className='mr-2 h-4 w-4' />
                      Generate Insights
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
