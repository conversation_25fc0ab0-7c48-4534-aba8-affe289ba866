'use client'

import React, { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  Target,
  CheckCircle,
  AlertCircle,
  Clock,
  BarChart,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Goal, KeyResult } from '@/lib/services/goals-client'

// Status indicator component
const StatusIndicator = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return { color: 'bg-green-500', icon: CheckCircle, text: 'text-green-700' }
      case 'ON_TRACK':
        return { color: 'bg-green-500', icon: CheckCircle, text: 'text-green-700' }
      case 'IN_PROGRESS':
        return { color: 'bg-blue-500', icon: Clock, text: 'text-blue-700' }
      case 'AT_RISK':
        return { color: 'bg-amber-500', icon: AlertCircle, text: 'text-amber-700' }
      case 'NOT_STARTED':
        return { color: 'bg-gray-400', icon: Clock, text: 'text-gray-600' }
      case 'CANCELLED':
        return { color: 'bg-red-500', icon: AlertCircle, text: 'text-red-700' }
      default:
        return { color: 'bg-gray-400', icon: Clock, text: 'text-gray-600' }
    }
  }

  const { color, icon: Icon, text } = getStatusConfig(status)

  return (
    <Badge variant='outline' className='flex items-center gap-1.5'>
      <div className={cn('w-2 h-2 rounded-full', color)} />
      <span className={text}>{status.replace('_', ' ')}</span>
      <Icon className={cn('h-3.5 w-3.5', text)} />
    </Badge>
  )
}

interface GoalCardProps {
  goal: Goal
  onEdit?: (goal: Goal) => void
  onAddKeyResult?: (goalId: string) => void
  onUpdateProgress?: (goalId: string, progress: number) => void
}

export function GoalCard({ goal, onEdit, onAddKeyResult, onUpdateProgress }: GoalCardProps) {
  const [expanded, setExpanded] = useState(false)

  // Get formatted date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No date set'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  // Handle key results expansion toggle
  const toggleExpand = () => {
    setExpanded(!expanded)
  }

  // Determine the category badge styles
  const getCategoryBadge = (category: string) => {
    switch (category) {
      case 'PERSONAL':
        return <Badge variant='secondary'>Personal</Badge>
      case 'PROFESSIONAL':
        return <Badge variant='default'>Professional</Badge>
      case 'LEARNING':
        return (
          <Badge variant='outline' className='bg-purple-50 text-purple-700 border-purple-200'>
            Learning
          </Badge>
        )
      case 'TEAM':
        return (
          <Badge variant='outline' className='bg-blue-50 text-blue-700 border-blue-200'>
            Team
          </Badge>
        )
      case 'COMPANY':
        return (
          <Badge variant='outline' className='bg-indigo-50 text-indigo-700 border-indigo-200'>
            Company
          </Badge>
        )
      default:
        return <Badge variant='outline'>{category}</Badge>
    }
  }

  // Display timeframe in human readable format
  const getTimeframeDisplay = (timeframe: string) => {
    switch (timeframe) {
      case 'DAILY':
        return 'Daily'
      case 'WEEKLY':
        return 'Weekly'
      case 'MONTHLY':
        return 'Monthly'
      case 'QUARTERLY':
        return 'Quarterly'
      case 'YEARLY':
        return 'Yearly'
      case 'CUSTOM':
        return 'Custom'
      default:
        return timeframe
    }
  }

  return (
    <Card className='mb-4 overflow-hidden transition-all duration-200 hover:shadow-md'>
      <CardHeader className='pb-3'>
        <div className='flex justify-between items-start'>
          <div className='space-y-1.5'>
            <CardTitle className='flex items-center'>
              {goal.type === 'OBJECTIVE' && <Target className='mr-2 h-5 w-5 text-primary' />}
              {goal.type === 'KEY_RESULT' && <BarChart className='mr-2 h-5 w-5 text-primary' />}
              {goal.title}
            </CardTitle>
            <CardDescription>{goal.description || 'No description provided'}</CardDescription>
          </div>
          <StatusIndicator status={goal.status} />
        </div>
      </CardHeader>

      <CardContent className='pb-2'>
        <div className='flex flex-wrap gap-2 mb-3'>
          {getCategoryBadge(goal.category)}
          <Badge variant='outline' className='flex items-center gap-1'>
            <Calendar className='h-3.5 w-3.5' />
            {getTimeframeDisplay(goal.timeframe)}
          </Badge>
          {goal.priority && (
            <Badge
              variant={
                goal.priority === 'HIGH'
                  ? 'destructive'
                  : goal.priority === 'MEDIUM'
                    ? 'default'
                    : 'secondary'
              }
            >
              {goal.priority.charAt(0) + goal.priority.slice(1).toLowerCase()}
            </Badge>
          )}
        </div>

        <div className='space-y-2'>
          <div className='flex justify-between text-sm'>
            <span>Progress</span>
            <span className='font-semibold'>{goal.progress}%</span>
          </div>
          <Progress value={goal.progress} className='h-2' />
        </div>

        <div className='mt-4 grid grid-cols-2 gap-2 text-sm text-muted-foreground'>
          {goal.startDate && (
            <div>
              <span className='block font-medium text-xs'>Start Date</span>
              <span>{formatDate(goal.startDate)}</span>
            </div>
          )}
          {goal.dueDate && (
            <div>
              <span className='block font-medium text-xs'>Due Date</span>
              <span>{formatDate(goal.dueDate)}</span>
            </div>
          )}
        </div>
      </CardContent>

      {goal.type === 'OBJECTIVE' && goal.keyResults && goal.keyResults.length > 0 && (
        <>
          <div
            className='px-6 py-2 cursor-pointer hover:bg-muted/50 flex items-center text-sm font-medium'
            onClick={toggleExpand}
          >
            {expanded ? (
              <ChevronDown className='h-4 w-4 mr-1' />
            ) : (
              <ChevronRight className='h-4 w-4 mr-1' />
            )}
            {goal.keyResults.length} Key {goal.keyResults.length === 1 ? 'Result' : 'Results'}
          </div>

          {expanded && (
            <div className='px-6 py-2 bg-muted/30'>
              {goal.keyResults.map((keyResult: KeyResult) => (
                <div
                  key={keyResult.id}
                  className='mb-3 pb-3 border-b border-border last:mb-0 last:pb-0 last:border-0'
                >
                  <div className='flex justify-between items-start mb-2'>
                    <h4 className='text-sm font-medium'>{keyResult.title}</h4>
                    <StatusIndicator status={keyResult.status} />
                  </div>
                  {keyResult.description && (
                    <p className='text-sm text-muted-foreground mb-2'>{keyResult.description}</p>
                  )}
                  <div className='flex justify-between text-xs mb-1'>
                    <span>
                      Progress: {Math.round((keyResult.currentValue / keyResult.targetValue) * 100)}
                      %
                    </span>
                    <span>
                      {keyResult.currentValue} / {keyResult.targetValue} {keyResult.unit}
                    </span>
                  </div>
                  <Progress
                    value={(keyResult.currentValue / keyResult.targetValue) * 100}
                    className='h-1.5'
                  />
                  {keyResult.dueDate && (
                    <div className='mt-2 text-xs text-muted-foreground'>
                      Due: {formatDate(keyResult.dueDate)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </>
      )}

      <CardFooter className='pt-2 flex justify-end gap-2'>
        {onEdit && (
          <Button variant='outline' size='sm' onClick={() => onEdit(goal)}>
            Edit
          </Button>
        )}
        {goal.type === 'OBJECTIVE' && onAddKeyResult && (
          <Button variant='default' size='sm' onClick={() => onAddKeyResult(goal.id)}>
            Add Key Result
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
