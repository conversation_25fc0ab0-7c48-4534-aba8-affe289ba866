'use client'

import React, { useState, useEffect } from 'react'
import { z } from 'zod'
import { toast } from 'sonner'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar as CalendarIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { goalsClient, type Goal } from '@/lib/services/goals-client'

// Form schema validation
const goalFormSchema = z
  .object({
    title: z.string().min(1, 'Title is required').max(255, 'Title is too long'),
    description: z.string().optional(),
    type: z.enum(['OBJECTIVE', 'KEY_RESULT', 'MILESTONE', 'HABIT']),
    category: z.enum(['PERSONAL', 'PROFESSIONAL', 'LEARNING', 'TEAM', 'COMPANY']),
    timeframe: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY', 'CUSTOM']),
    priority: z.enum(['LOW', 'MEDIUM', 'HIGH']),
    parentGoalId: z.string().optional(),
    startDate: z.date().optional(),
    dueDate: z.date().optional(),
  })
  .refine(data => !data.startDate || !data.dueDate || data.startDate < data.dueDate, {
    message: 'Start date must be before due date',
    path: ['dueDate'],
  })

type FormValues = z.infer<typeof goalFormSchema>

interface GoalFormDialogProps {
  isOpen: boolean
  onClose: () => void
  goalToEdit?: Goal
  parentGoalId?: string
  onSuccess: (goal: Goal) => void
}

export function GoalFormDialog({
  isOpen,
  onClose,
  goalToEdit,
  parentGoalId,
  onSuccess,
}: GoalFormDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = !!goalToEdit

  // Initialize form with default values or editing values
  const form = useForm<FormValues>({
    resolver: zodResolver(goalFormSchema),
    defaultValues: {
      title: goalToEdit?.title || '',
      description: goalToEdit?.description || '',
      type: (goalToEdit?.type as any) || 'OBJECTIVE',
      category: (goalToEdit?.category as any) || 'PROFESSIONAL',
      timeframe: (goalToEdit?.timeframe as any) || 'QUARTERLY',
      priority: (goalToEdit?.priority as any) || 'MEDIUM',
      parentGoalId: goalToEdit?.parentGoalId || parentGoalId,
      startDate: goalToEdit?.startDate ? new Date(goalToEdit.startDate) : undefined,
      dueDate: goalToEdit?.dueDate ? new Date(goalToEdit.dueDate) : undefined,
    },
  })

  // Update form when editing a different goal
  useEffect(() => {
    if (goalToEdit) {
      form.reset({
        title: goalToEdit.title,
        description: goalToEdit.description || '',
        type: (goalToEdit.type as any) || 'OBJECTIVE',
        category: (goalToEdit.category as any) || 'PROFESSIONAL',
        timeframe: (goalToEdit.timeframe as any) || 'QUARTERLY',
        priority: (goalToEdit.priority as any) || 'MEDIUM',
        parentGoalId: goalToEdit.parentGoalId,
        startDate: goalToEdit.startDate ? new Date(goalToEdit.startDate) : undefined,
        dueDate: goalToEdit.dueDate ? new Date(goalToEdit.dueDate) : undefined,
      })
    } else if (parentGoalId) {
      form.reset({
        title: '',
        description: '',
        type: 'KEY_RESULT',
        category: 'PROFESSIONAL',
        timeframe: 'QUARTERLY',
        priority: 'MEDIUM',
        parentGoalId: parentGoalId,
        startDate: undefined,
        dueDate: undefined,
      })
    } else {
      form.reset({
        title: '',
        description: '',
        type: 'OBJECTIVE',
        category: 'PROFESSIONAL',
        timeframe: 'QUARTERLY',
        priority: 'MEDIUM',
        parentGoalId: undefined,
        startDate: undefined,
        dueDate: undefined,
      })
    }
  }, [goalToEdit, parentGoalId, form])

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true)

      // Format the dates for API
      const formattedValues = {
        ...values,
        startDate: values.startDate ? format(values.startDate, 'yyyy-MM-dd') : undefined,
        dueDate: values.dueDate ? format(values.dueDate, 'yyyy-MM-dd') : undefined,
      }

      let result

      if (isEditing && goalToEdit) {
        result = await goalsClient.updateGoal(goalToEdit.id, formattedValues)
        toast.success('Goal updated successfully')
      } else {
        result = await goalsClient.createGoal(formattedValues)
        toast.success('Goal created successfully')
      }

      onSuccess(result)
      onClose()
    } catch (error) {
      console.error('Error saving goal:', error)
      toast.error('Failed to save goal. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className='sm:max-w-[600px]'>
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Goal' : 'Create New Goal'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update your goal details to track your progress more effectively.'
              : 'Define a new goal to keep track of your objectives and key results.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter goal title' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Describe the goal and what success looks like'
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='type'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select type' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='OBJECTIVE'>Objective</SelectItem>
                        <SelectItem value='KEY_RESULT'>Key Result</SelectItem>
                        <SelectItem value='MILESTONE'>Milestone</SelectItem>
                        <SelectItem value='HABIT'>Habit</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>The type of goal you want to track</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='category'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select category' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='PERSONAL'>Personal</SelectItem>
                        <SelectItem value='PROFESSIONAL'>Professional</SelectItem>
                        <SelectItem value='LEARNING'>Learning</SelectItem>
                        <SelectItem value='TEAM'>Team</SelectItem>
                        <SelectItem value='COMPANY'>Company</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='timeframe'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Timeframe</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select timeframe' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='DAILY'>Daily</SelectItem>
                        <SelectItem value='WEEKLY'>Weekly</SelectItem>
                        <SelectItem value='MONTHLY'>Monthly</SelectItem>
                        <SelectItem value='QUARTERLY'>Quarterly</SelectItem>
                        <SelectItem value='YEARLY'>Yearly</SelectItem>
                        <SelectItem value='CUSTOM'>Custom</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='priority'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select priority' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='LOW'>Low</SelectItem>
                        <SelectItem value='MEDIUM'>Medium</SelectItem>
                        <SelectItem value='HIGH'>High</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='startDate'
                render={({ field }) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant='outline'
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                            <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className='w-auto p-0' align='start'>
                        <Calendar
                          mode='single'
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='dueDate'
                render={({ field }) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant='outline'
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                            <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className='w-auto p-0' align='start'>
                        <Calendar
                          mode='single'
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type='button' variant='outline' onClick={onClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : isEditing ? 'Update Goal' : 'Create Goal'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
