'use client'

import React, { useState } from 'react'
import { z } from 'zod'
import { toast } from 'sonner'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar as CalendarIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { goalsClient } from '@/lib/services/goals-client'

// Form validation schema
const keyResultSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title is too long'),
  description: z.string().optional(),
  targetValue: z.number().min(0, 'Target value must be a positive number'),
  currentValue: z.number().min(0, 'Current value must be a positive number'),
  unit: z.string().min(1, 'Unit is required'),
  dueDate: z.date().optional(),
})

type FormValues = z.infer<typeof keyResultSchema>

interface KeyResultFormProps {
  objectiveId: string
  keyResultToEdit?: {
    id: string
    title: string
    description?: string
    targetValue: number
    currentValue: number
    unit: string
    dueDate?: string
  }
  onSuccess: () => void
  onCancel: () => void
}

export function KeyResultForm({
  objectiveId,
  keyResultToEdit,
  onSuccess,
  onCancel,
}: KeyResultFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = !!keyResultToEdit

  // Initialize form with default values or editing values
  const form = useForm<FormValues>({
    resolver: zodResolver(keyResultSchema),
    defaultValues: {
      title: keyResultToEdit?.title || '',
      description: keyResultToEdit?.description || '',
      targetValue: keyResultToEdit?.targetValue || 100,
      currentValue: keyResultToEdit?.currentValue || 0,
      unit: keyResultToEdit?.unit || 'percent',
      dueDate: keyResultToEdit?.dueDate ? new Date(keyResultToEdit.dueDate) : undefined,
    },
  })

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true)

      // Format the dates for API
      const formattedValues = {
        ...values,
        dueDate: values.dueDate ? format(values.dueDate, 'yyyy-MM-dd') : undefined,
      }

      if (isEditing && keyResultToEdit) {
        await goalsClient.updateKeyResult(keyResultToEdit.id, formattedValues)
        toast.success('Key result updated successfully')
      } else {
        await goalsClient.createKeyResult(objectiveId, formattedValues)
        toast.success('Key result added successfully')
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving key result:', error)
      toast.error('Failed to save key result. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
        <FormField
          control={form.control}
          name='title'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder='Enter key result title' {...field} />
              </FormControl>
              <FormDescription>What specific outcome will indicate success?</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='description'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder='Describe how this key result helps achieve the objective'
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className='grid grid-cols-3 gap-4'>
          <FormField
            control={form.control}
            name='targetValue'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Target Value</FormLabel>
                <FormControl>
                  <Input
                    type='number'
                    placeholder='100'
                    {...field}
                    onChange={e => field.onChange(parseFloat(e.target.value))}
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='currentValue'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Current Value</FormLabel>
                <FormControl>
                  <Input
                    type='number'
                    placeholder='0'
                    {...field}
                    onChange={e => field.onChange(parseFloat(e.target.value))}
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='unit'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unit</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Select unit' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value='percent'>Percent (%)</SelectItem>
                    <SelectItem value='count'>Count (#)</SelectItem>
                    <SelectItem value='currency'>Currency ($)</SelectItem>
                    <SelectItem value='hours'>Hours (hrs)</SelectItem>
                    <SelectItem value='days'>Days</SelectItem>
                    <SelectItem value='tasks'>Tasks</SelectItem>
                    <SelectItem value='custom'>Custom</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name='dueDate'
          render={({ field }) => (
            <FormItem className='flex flex-col'>
              <FormLabel>Due Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant='outline'
                      className={cn(
                        'w-full pl-3 text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                      <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className='w-auto p-0' align='start'>
                  <Calendar
                    mode='single'
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormDescription>When should this key result be completed?</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className='flex justify-end gap-2'>
          <Button type='button' variant='outline' onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type='submit' disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : isEditing ? 'Update Key Result' : 'Add Key Result'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
