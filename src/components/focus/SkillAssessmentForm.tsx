'use client'

import React, { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Card, CardContent } from '@/components/ui/card'
import { skillsClient, type Skill } from '@/lib/services/skills-client'

// Validation schema for skill assessment
const assessmentSchema = z.object({
  skillId: z.string(),
  score: z.number().min(0).max(100),
  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),
  notes: z.string().optional(),
  assessmentType: z.enum(['self', 'peer', 'manager', '360']).default('self'),
  nextSteps: z.string().optional(),
})

type AssessmentFormValues = z.infer<typeof assessmentSchema>

interface SkillAssessmentFormProps {
  skill: Skill
  onSuccess?: () => void
  onCancel?: () => void
}

export function SkillAssessmentForm({ skill, onSuccess, onCancel }: SkillAssessmentFormProps) {
  const [submitting, setSubmitting] = useState(false)

  // Calculate default score based on current level
  const getDefaultScore = (level: string) => {
    switch (level) {
      case 'BEGINNER':
        return 25
      case 'INTERMEDIATE':
        return 50
      case 'ADVANCED':
        return 75
      case 'EXPERT':
        return 100
      default:
        return 0
    }
  }

  // Create form with default values
  const form = useForm<AssessmentFormValues>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      skillId: skill.id,
      score: getDefaultScore(skill.currentLevel),
      level: skill.currentLevel,
      notes: '',
      assessmentType: 'self',
      nextSteps: '',
    },
  })

  // Watch score to update level automatically
  const score = form.watch('score')

  // Update level based on score
  React.useEffect(() => {
    let level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT' = 'BEGINNER'

    if (score <= 25) {
      level = 'BEGINNER'
    } else if (score <= 50) {
      level = 'INTERMEDIATE'
    } else if (score <= 75) {
      level = 'ADVANCED'
    } else {
      level = 'EXPERT'
    }

    form.setValue('level', level)
  }, [score, form])

  // Submit handler
  const onSubmit = async (data: AssessmentFormValues) => {
    setSubmitting(true)
    try {
      await skillsClient.createAssessment({
        skillId: data.skillId,
        score: data.score,
        level: data.level,
        notes: data.notes,
        assessmentType: data.assessmentType,
        nextSteps: data.nextSteps,
      })

      toast.success('Skill assessment saved successfully')
      onSuccess?.()
    } catch (error) {
      console.error('Error creating assessment:', error)
      toast.error('Failed to save assessment')
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-medium'>{skill.name} Assessment</h3>
          </div>

          <Card>
            <CardContent className='pt-6'>
              {/* Proficiency Slider */}
              <FormField
                control={form.control}
                name='score'
                render={({ field }) => (
                  <FormItem className='space-y-4'>
                    <div className='flex justify-between'>
                      <FormLabel>Proficiency Level ({field.value}%)</FormLabel>
                      <span className='text-sm font-medium'>
                        {form.watch('level').charAt(0) + form.watch('level').slice(1).toLowerCase()}
                      </span>
                    </div>
                    <FormControl>
                      <Slider
                        defaultValue={[field.value]}
                        max={100}
                        step={1}
                        onValueChange={vals => field.onChange(vals[0])}
                      />
                    </FormControl>
                    <div className='flex justify-between text-xs text-muted-foreground'>
                      <span>Beginner</span>
                      <span>Intermediate</span>
                      <span>Advanced</span>
                      <span>Expert</span>
                    </div>
                    <FormDescription>
                      Drag the slider to indicate your current proficiency level
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Assessment Type */}
          <FormField
            control={form.control}
            name='assessmentType'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Assessment Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder='Select assessment type' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value='self'>Self Assessment</SelectItem>
                    <SelectItem value='peer'>Peer Review</SelectItem>
                    <SelectItem value='manager'>Manager Evaluation</SelectItem>
                    <SelectItem value='360'>360° Feedback</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>Choose the type of assessment you're conducting</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Notes */}
          <FormField
            control={form.control}
            name='notes'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Assessment Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder='Provide details about your current skill level and achievements'
                    {...field}
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  Document specific examples that demonstrate your skill level
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Next Steps */}
          <FormField
            control={form.control}
            name='nextSteps'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Next Steps for Improvement</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder='What will you do to improve this skill?'
                    {...field}
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  Outline specific actions you'll take to develop this skill further
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex justify-end space-x-2'>
          <Button type='button' variant='outline' onClick={onCancel} disabled={submitting}>
            Cancel
          </Button>
          <Button type='submit' disabled={submitting}>
            {submitting ? 'Saving...' : 'Save Assessment'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
