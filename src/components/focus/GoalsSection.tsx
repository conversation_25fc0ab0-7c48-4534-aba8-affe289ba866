'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from '@/lib/auth/hooks'
import { goalsClient, type Goal } from '@/lib/services/goals-client'
import { GoalsService } from '@/services/goals-service'
import type { KeyResult } from '@prisma/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  MoreVertical,
  Target,
  TrendingUp,
  Calendar,
  Users,
  Trophy,
  Clock,
  Bell,
  Download,
  BarChart3,
  PieChart,
  Brain,
  Sparkles,
  CheckCircle,
  GitBranch,
  AlertCircle,
  Link as LinkIcon,
  ChevronDown,
  ChevronRight,
  ListFilter,
  CalendarDays,
  BarChart2,
  Layers,
  Search,
} from 'lucide-react'
import { toast } from 'sonner'
import { TimeframeOKRView } from './TimeframeOKRView'
import { GoalCard } from './GoalCard'
import { GoalFormDialog } from './GoalFormDialog'
import { KeyResultForm } from './KeyResultForm'
import { OKRVisualization } from './OKRVisualization'

// Types for Goals
interface Goal {
  id: string
  title: string
  description: string | null
  type: 'OBJECTIVE' | 'KEY_RESULT' | 'MILESTONE' | 'HABIT'
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'ON_TRACK' | 'AT_RISK' | 'COMPLETED' | 'CANCELLED'
  progress: number
  targetValue: number | null
  currentValue: number | null
  unit: string | null
  category: 'PERSONAL' | 'PROFESSIONAL' | 'LEARNING' | 'HEALTH' | 'FINANCIAL' | 'TEAM' | 'COMPANY'
  timeframe: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' | 'CUSTOM'
  priority: 'LOW' | 'MEDIUM' | 'HIGH'
  startDate: string | null
  targetDate: string | null
  completedAt: string | null
  updatedAt: string
  parentGoalId?: string | null
  keyResults?: KeyResult[]
  subGoals?: Goal[]
}

interface KeyResult {
  id: string
  title: string
  description: string | null
  targetValue: number
  currentValue: number
  unit: string | null
  status: 'NOT_STARTED' | 'IN_PROGRESS' | 'ON_TRACK' | 'AT_RISK' | 'COMPLETED' | 'CANCELLED'
  dueDate: string | null
  completedAt: string | null
}

interface GoalStatistics {
  total: number
  completed: number
  inProgress: number
  atRisk: number
  averageProgress: number
  byCategory: Record<string, number>
  byTimeframe: Record<string, number>
  completedThisMonth: number
}

// Add new interface for enhanced OKR tracking
interface OKRProgress {
  objectives: number
  keyResults: number
  completedObjectives: number
  completedKeyResults: number
  onTrackKeyResults: number
  atRiskKeyResults: number
}

/**
 * GoalsSection Component - Enhanced OKR/Goals Management
 * Following TDD approach with comprehensive AI-first functionality
 */
export function GoalsSection() {
  const { user } = useAuth()
  const [goals, setGoals] = useState<Goal[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedTimeframe, setSelectedTimeframe] = useState('all')
  const [showGoalForm, setShowGoalForm] = useState(false)
  const [showKeyResultForm, setShowKeyResultForm] = useState(false)
  const [currentGoal, setCurrentGoal] = useState<Goal | null>(null)
  const [currentObjectiveId, setCurrentObjectiveId] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'list' | 'timeframe'>('list')

  // Analytics state
  const [analytics, setAnalytics] = useState({
    totalGoals: 0,
    completedGoals: 0,
    inProgressGoals: 0,
    atRiskGoals: 0,
  })

  // Load goals data
  const loadGoals = useCallback(async () => {
    try {
      setIsLoading(true)
      const data = await goalsClient.getGoals()
      setGoals(data)

      // Load analytics
      const analyticsData = await goalsClient.getAnalytics()
      setAnalytics(analyticsData)
    } catch (error) {
      console.error('Error loading goals:', error)
      toast.error('Failed to load goals. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    loadGoals()
  }, [loadGoals])

  // Filtered goals based on search, category, and timeframe
  const filteredGoals = goals.filter(goal => {
    const matchesSearch = searchQuery
      ? goal.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (goal.description?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false)
      : true

    const matchesCategory = selectedCategory === 'all' || goal.category === selectedCategory
    const matchesTimeframe = selectedTimeframe === 'all' || goal.timeframe === selectedTimeframe

    return matchesSearch && matchesCategory && matchesTimeframe
  })

  // Handle goal creation/update
  const handleGoalSuccess = (goal: Goal) => {
    loadGoals() // Reload all goals to get fresh data
    setShowGoalForm(false)
    setCurrentGoal(null)
  }

  // Handle key result creation
  const handleKeyResultSuccess = () => {
    loadGoals() // Reload all goals to get fresh data
    setShowKeyResultForm(false)
    setCurrentObjectiveId(null)
  }

  // Handle edit goal
  const handleEditGoal = (goal: Goal) => {
    setCurrentGoal(goal)
    setShowGoalForm(true)
  }

  // Handle add key result
  const handleAddKeyResult = (goalId: string) => {
    setCurrentObjectiveId(goalId)
    setShowKeyResultForm(true)
  }

  // Group goals by timeframe for timeframe view
  const goalsByTimeframe = {
    DAILY: goals.filter(goal => goal.timeframe === 'DAILY'),
    WEEKLY: goals.filter(goal => goal.timeframe === 'WEEKLY'),
    MONTHLY: goals.filter(goal => goal.timeframe === 'MONTHLY'),
    QUARTERLY: goals.filter(goal => goal.timeframe === 'QUARTERLY'),
    YEARLY: goals.filter(goal => goal.timeframe === 'YEARLY'),
    CUSTOM: goals.filter(goal => goal.timeframe === 'CUSTOM'),
  }

  // Reset filters
  const resetFilters = () => {
    setSearchQuery('')
    setSelectedCategory('all')
    setSelectedTimeframe('all')
  }

  // Show reset button if any filter is active
  const showResetButton = searchQuery || selectedCategory !== 'all' || selectedTimeframe !== 'all'

  if (isLoading) {
    return <div data-testid='goals-loading'>Loading goals...</div>
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-semibold tracking-tight'>Goals & OKRs</h2>
          <p className='text-sm text-muted-foreground'>
            {analytics.totalGoals} total goals • {analytics.completedGoals} completed
          </p>
        </div>
        <Button onClick={() => setShowGoalForm(true)}>
          <Plus className='mr-2 h-4 w-4' />
          New Goal
        </Button>
      </div>

      {/* Filters */}
      <div className='flex flex-wrap gap-3'>
        <div className='flex-1 min-w-[240px]'>
          <Input
            placeholder='Search goals...'
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className='w-full'
            icon={Search}
          />
        </div>
        <Select
          value={selectedCategory || ''}
          onValueChange={value => setSelectedCategory(value || null)}
        >
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='All Categories' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Categories</SelectItem>
            <SelectItem value='PERSONAL'>Personal</SelectItem>
            <SelectItem value='PROFESSIONAL'>Professional</SelectItem>
            <SelectItem value='LEARNING'>Learning</SelectItem>
            <SelectItem value='TEAM'>Team</SelectItem>
            <SelectItem value='COMPANY'>Company</SelectItem>
          </SelectContent>
        </Select>
        <Select
          value={selectedTimeframe || ''}
          onValueChange={value => setSelectedTimeframe(value || null)}
        >
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='All Timeframes' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Timeframes</SelectItem>
            <SelectItem value='DAILY'>Daily</SelectItem>
            <SelectItem value='WEEKLY'>Weekly</SelectItem>
            <SelectItem value='MONTHLY'>Monthly</SelectItem>
            <SelectItem value='QUARTERLY'>Quarterly</SelectItem>
            <SelectItem value='YEARLY'>Yearly</SelectItem>
          </SelectContent>
        </Select>
        <div className='flex border rounded-md overflow-hidden'>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size='sm'
            className='rounded-none px-3'
            onClick={() => setViewMode('list')}
          >
            <ListFilter className='h-4 w-4 mr-1' />
            List
          </Button>
          <Button
            variant={viewMode === 'timeframe' ? 'default' : 'ghost'}
            size='sm'
            className='rounded-none px-3'
            onClick={() => setViewMode('timeframe')}
          >
            <CalendarDays className='h-4 w-4 mr-1' />
            Timeframe
          </Button>
        </div>

        {/* Reset filters button */}
        {showResetButton && (
          <Button
            variant='ghost'
            size='sm'
            onClick={resetFilters}
            className='text-muted-foreground'
          >
            Reset Filters
          </Button>
        )}
      </div>

      {/* Goal content */}
      {viewMode === 'list' ? (
        <div className='space-y-6'>
          {filteredGoals.length > 0 ? (
            filteredGoals.map(goal => (
              <GoalCard
                key={goal.id}
                goal={goal}
                onEdit={handleEditGoal}
                onAddKeyResult={goal.type === 'OBJECTIVE' ? handleAddKeyResult : undefined}
              />
            ))
          ) : (
            <div className='text-center py-12 border rounded-lg bg-muted/30'>
              <h3 className='text-lg font-medium mb-2'>No goals found</h3>
              <p className='text-muted-foreground mb-4'>
                {searchQuery || selectedCategory || selectedTimeframe
                  ? 'Try adjusting your filters to see more goals'
                  : 'Create your first goal to start tracking your progress'}
              </p>
              <Button onClick={() => setShowGoalForm(true)}>
                <Plus className='mr-2 h-4 w-4' />
                Create Goal
              </Button>
            </div>
          )}
        </div>
      ) : (
        <TimeframeOKRView goals={goals} isLoading={isLoading} />
      )}

      {/* Goal Form Dialog */}
      <GoalFormDialog
        isOpen={showGoalForm}
        onClose={() => {
          setShowGoalForm(false)
          setCurrentGoal(null)
        }}
        goalToEdit={currentGoal || undefined}
        onSuccess={handleGoalSuccess}
      />

      {/* Key Result Form Dialog */}
      <Dialog open={showKeyResultForm} onOpenChange={setShowKeyResultForm}>
        <DialogContent className='sm:max-w-[600px]'>
          <DialogHeader>
            <DialogTitle>Add Key Result</DialogTitle>
            <DialogDescription>
              Add a measurable key result to track progress towards your objective.
            </DialogDescription>
          </DialogHeader>
          {currentObjectiveId && (
            <KeyResultForm
              objectiveId={currentObjectiveId}
              onSuccess={handleKeyResultSuccess}
              onCancel={() => setShowKeyResultForm(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
