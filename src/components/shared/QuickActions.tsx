'use client'

import { SupportModal } from '@/components/navigation/SupportModal'
import { Button } from '@/components/ui/button'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useQuickActions } from '@/hooks/useQuickActions'
import { cn } from '@/lib/utils'
import {
  FoldVertical,
  HelpCircle,
  Maximize,
  Minimize,
  RefreshCw,
  UnfoldVertical,
} from 'lucide-react'
import React from 'react'

interface QuickActionsProps {
  /** Whether to show the Quick Actions section */
  show?: boolean
  /** Additional CSS classes */
  className?: string
  /** Test ID for the Quick Actions section */
  testId?: string
  /** Layout orientation */
  orientation?: 'horizontal' | 'vertical'
  /** Custom button size */
  buttonSize?: 'sm' | 'default' | 'lg'
  /** Whether to show tooltips */
  showTooltips?: boolean
  /** Custom tooltip side position */
  tooltipSide?: 'top' | 'right' | 'bottom' | 'left'
  /** Additional callback when any action is triggered */
  onActionTrigger?: (action: string) => void
}

/**
 * Reusable Quick Actions component for consistent sidebar functionality
 * Provides Support, Collapse All, Refresh AI, and Sidebar Toggle actions
 */
export function QuickActions({
  show = true,
  className,
  testId = 'quick-actions-section',
  orientation = 'horizontal',
  buttonSize = 'default',
  showTooltips = true,
  tooltipSide = 'right',
  onActionTrigger,
}: QuickActionsProps) {
  const { state, handlers, config } = useQuickActions()

  // Don't render if disabled
  if (!show) {
    return null
  }

  const {
    handleSupportToggle,
    handleCollapseAllDropdowns,
    handleRefreshAI,
    handleSidebarToggle,
    setSupportModalOpen,
  } = handlers

  const { supportModalOpen, allDropdownsCollapsed } = state

  const { isContextAware, isMinimized } = config

  // Handle action trigger callback
  const triggerAction = (action: string, handler: () => void) => {
    handler()
    onActionTrigger?.(action)
  }

  // Button wrapper component for consistent tooltip behavior
  const ActionButton = ({
    children,
    tooltip,
    ...props
  }: {
    children: React.ReactNode
    tooltip: string
  } & React.ComponentProps<typeof Button>) => {
    const button = (
      <Button
        variant='ghost'
        size={buttonSize === 'default' ? 'icon' : buttonSize}
        className={cn(
          'h-8 w-8 transition-colors hover:bg-muted',
          buttonSize === 'sm' && 'h-6 w-6',
          buttonSize === 'lg' && 'h-10 w-10'
        )}
        {...props}
      >
        {children}
      </Button>
    )

    if (!showTooltips) {
      return button
    }

    return (
      <Tooltip>
        <TooltipTrigger asChild>{button}</TooltipTrigger>
        <TooltipContent side={tooltipSide}>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    )
  }

  const content = (
    <>
      {/* Support Button */}
      <ActionButton
        onClick={() => triggerAction('support', handleSupportToggle)}
        data-testid='quick-access-support'
        aria-label='Open support'
        tooltip='Support'
      >
        <HelpCircle className='h-4 w-4' />
      </ActionButton>

      {/* Collapse All Dropdowns Button */}
      <ActionButton
        onClick={() => triggerAction('collapse-all', handleCollapseAllDropdowns)}
        data-testid='quick-access-collapse-all'
        aria-label={allDropdownsCollapsed ? 'Expand all dropdowns' : 'Collapse all dropdowns'}
        tooltip={allDropdownsCollapsed ? 'Expand All' : 'Collapse All'}
      >
        {allDropdownsCollapsed ? (
          <UnfoldVertical className='h-4 w-4' />
        ) : (
          <FoldVertical className='h-4 w-4' />
        )}
      </ActionButton>

      {/* Refresh AI Button - Only show if context-aware features are enabled */}
      {isContextAware && (
        <ActionButton
          onClick={() => triggerAction('refresh-ai', handleRefreshAI)}
          data-testid='quick-access-refresh-ai'
          aria-label='Refresh AI recommendations'
          tooltip='Refresh AI'
        >
          <RefreshCw className='h-4 w-4' />
        </ActionButton>
      )}

      {/* Minimize/Expand Sidebar Button */}
      <ActionButton
        onClick={() => triggerAction('sidebar-toggle', handleSidebarToggle)}
        data-testid='sidebar-collapse-toggle'
        aria-label={isMinimized ? 'Expand sidebar' : 'Minimize sidebar'}
        tooltip={isMinimized ? 'Expand' : 'Minimize'}
      >
        {isMinimized ? <Maximize className='h-4 w-4' /> : <Minimize className='h-4 w-4' />}
      </ActionButton>
    </>
  )

  return (
    <TooltipProvider>
      <div className={cn('border-t border-border p-3 mt-auto', className)} data-testid={testId}>
        <div
          className={cn(
            'flex gap-1',
            orientation === 'horizontal' ? 'justify-start' : 'justify-center flex-col space-y-1',
            // Responsive layout based on sidebar state
            !isMinimized && orientation === 'horizontal'
              ? 'justify-start'
              : 'justify-center flex-col space-y-1'
          )}
        >
          {content}
        </div>
      </div>

      {/* Support Modal */}
      <SupportModal open={supportModalOpen} onOpenChange={setSupportModalOpen} />
    </TooltipProvider>
  )
}

export default QuickActions
