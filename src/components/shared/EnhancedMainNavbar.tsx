'use client'

import { useCustomTheme } from '@/app/providers/theme-provider'
import { AISearchBar } from '@/components/navigation/AISearchBar'
import { UserProfileSection } from '@/components/navigation/UserProfileSection'
import { Button } from '@/components/ui/button'
import { useNavigationContext } from '@/lib/context/NavigationContextProvider'
import { componentThemes, spacingPatterns } from '@/lib/design-system/themes'
import { cn } from '@/lib/utils'
import { ChevronRight, PanelLeftClose, PanelLeftOpen } from 'lucide-react'
import { useTheme } from 'next-themes'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'

interface BreadcrumbItem {
  label: string
  href?: string
  isCurrentPage?: boolean
}

export function EnhancedMainNavbar() {
  const { toggleSidebarFull, isSidebarFullyCollapsed } = useNavigationContext()
  const { resolvedTheme } = useTheme()
  const { colorScheme } = useCustomTheme()
  const pathname = usePathname()

  // Debug: Log theme information including colorScheme
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Theme debug information available in development
      const debugInfo = {
        resolvedTheme,
        colorScheme,
        primaryColor: getComputedStyle(document.documentElement).getPropertyValue('--primary'),
        timestamp: new Date().toISOString(),
      }
      // Debug information stored for development purposes
      void debugInfo
    }
  }, [resolvedTheme, colorScheme])

  const generateBreadcrumbs = (path: string): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = []

    // Always start with Home
    breadcrumbs.push({
      label: 'Home',
      href: '/dashboard',
    })

    // Split the path into segments
    const segments = path.split('/').filter(Boolean)

    if (segments.length === 0 || (segments.length === 1 && segments[0] === 'dashboard')) {
      // We're at the dashboard, mark it as current
      breadcrumbs[0].isCurrentPage = true
      return breadcrumbs
    }

    // Build breadcrumbs based on path segments
    let currentHref = ''

    segments.forEach((segment, index) => {
      currentHref += `/${segment}`
      const isLast = index === segments.length - 1

      // Convert segment to readable label
      let label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      // Handle special cases and role-specific breadcrumbs
      if (segment === 'superadmin') {
        label = 'SuperAdmin'
      } else if (segment === 'dashboard') {
        label = 'Dashboard'
      } else if (segment === 'settings') {
        label = 'Settings'
      } else if (segment === 'appearance') {
        label = 'Appearance'
      } else if (segment === 'companies') {
        label = 'Companies'
      } else if (segment === 'admin') {
        label = 'Admin'
      } else if (segment === 'focus') {
        label = 'Focus'
      } else if (segment === 'today') {
        label = 'Today'
      }

      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentHref,
        isCurrentPage: isLast,
      })
    })

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs(pathname)

  return (
    <nav
      data-testid='main-navbar'
      className={cn(
        'w-full h-14 border-b transition-all duration-300',
        // Follow design system patterns for navigation
        'bg-background/95 backdrop-blur-md supports-[backdrop-filter]:bg-background/80',
        'border-border/50',
        // Enhanced shadow following design system
        'shadow-sm',
        // Remove fixed positioning as parent container handles positioning
        'flex-shrink-0'
      )}
      role='navigation'
      aria-label='Main navigation'
    >
      <div
        className={cn(
          'w-full h-14 flex items-center justify-between',
          // Use design system spacing
          spacingPatterns.padding.section
        )}
      >
        {/* Left side - Burger menu + Breadcrumbs following design system patterns */}
        <div className={cn('flex items-center flex-1 min-w-0', spacingPatterns.itemGap)}>
          {/* Burger Menu Button - Using design system interactive patterns */}
          <div className='flex items-center h-14'>
            <Button
              variant='ghost'
              size='icon'
              className={cn(
                'h-9 w-9 flex-shrink-0 rounded-lg transition-colors duration-200',
                // Follow design system interactive patterns
                componentThemes.patterns.interactive.ghost,
                'border-0 shadow-none group'
              )}
              onClick={toggleSidebarFull}
              aria-label={isSidebarFullyCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
              data-testid='navbar-burger-menu'
            >
              {isSidebarFullyCollapsed ? (
                <PanelLeftOpen
                  className='h-4 w-4 text-muted-foreground transition-colors duration-200 group-hover:text-primary'
                  data-testid='expand-icon'
                />
              ) : (
                <PanelLeftClose
                  className='h-4 w-4 text-muted-foreground transition-colors duration-200 group-hover:text-primary'
                  data-testid='collapse-icon'
                />
              )}
            </Button>
          </div>

          {/* Breadcrumbs - Following design system link patterns */}
          <nav className='flex items-center space-x-2 text-sm' aria-label='Breadcrumb'>
            <ol className='flex items-center space-x-2'>
              {breadcrumbs.map((breadcrumb, index) => (
                <li key={index} className='flex items-center space-x-2'>
                  {index > 0 && (
                    <ChevronRight
                      className='h-4 w-4 text-muted-foreground flex-shrink-0'
                      aria-hidden='true'
                    />
                  )}
                  {breadcrumb.isCurrentPage ? (
                    <span
                      className={cn(
                        'text-sm font-medium transition-colors truncate',
                        // Use design system primary text styling
                        'text-primary'
                      )}
                      aria-current='page'
                      data-testid='breadcrumb-current'
                    >
                      {breadcrumb.label}
                    </span>
                  ) : (
                    <Link
                      href={breadcrumb.href!}
                      className={cn(
                        'text-sm transition-colors truncate',
                        // Follow design system link patterns
                        componentThemes.patterns.interactive.link.replace(
                          'underline-offset-4 hover:underline',
                          ''
                        ),
                        'text-muted-foreground hover:text-foreground no-underline'
                      )}
                      data-testid='breadcrumb-link'
                    >
                      {breadcrumb.label}
                    </Link>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        </div>

        {/* Center - AI Search Bar */}
        <div className='flex-1 max-w-md mx-4 hidden md:block'>
          <AISearchBar />
        </div>

        {/* Right side - User Profile */}
        <div className='flex items-center space-x-3'>
          <UserProfileSection />
        </div>
      </div>
    </nav>
  )
}
