'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Shield } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

const routeLabels: Record<string, string> = {
  '/superadmin': 'Dashboard',
  '/superadmin/companies': 'Companies',
  '/superadmin/users': 'Users',
  '/superadmin/audit-logs': 'Audit Logs',
  '/superadmin/design-system': 'Design System',
  '/superadmin/design-system/components': 'Components',
  '/superadmin/design-system/themes': 'Themes',
  '/superadmin/ai-models': 'AI Models',
  '/superadmin/ai-models/performance': 'Performance',
  '/superadmin/ai-models/training': 'Training',
  '/superadmin/system/metrics': 'System Metrics',
  '/superadmin/system/performance': 'Performance Monitoring',
  '/superadmin/system/errors': 'Error Tracking',
}

export function SuperAdminBreadcrumbs() {
  const pathname = usePathname()

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      {
        label: 'Super Admin Panel',
        href: '/superadmin',
        isActive: pathname === '/superadmin',
      },
    ]

    // If we're not on the root SuperAdmin page, add specific breadcrumbs
    if (pathname !== '/superadmin') {
      const pathSegments = pathname.split('/').filter(Boolean)
      let currentPath = ''

      // Start from the second segment (skip 'superadmin')
      for (let i = 1; i < pathSegments.length; i++) {
        currentPath += `/${pathSegments[i]}`
        const fullPath = `/superadmin${currentPath}`

        const label =
          routeLabels[fullPath] ||
          pathSegments[i].charAt(0).toUpperCase() + pathSegments[i].slice(1)

        breadcrumbs.push({
          label,
          href: i === pathSegments.length - 1 ? undefined : fullPath,
          isActive: i === pathSegments.length - 1,
        })
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  return (
    <nav aria-label='Breadcrumb' className='flex items-center space-x-2'>
      {breadcrumbs.map((item, index) => (
        <div key={index} className='flex items-center space-x-2'>
          {index > 0 && <ChevronRight className='h-5 w-5 text-muted-foreground' />}

          {/* Add icon for the first item (Super Admin Panel) */}
          {index === 0 && <Shield className='h-6 w-6 text-primary' />}

          {item.href && !item.isActive ? (
            <Link
              href={item.href}
              className={cn(
                'text-lg font-semibold transition-colors',
                'hover:text-foreground text-muted-foreground'
              )}
            >
              {item.label}
            </Link>
          ) : (
            <span
              className={cn(
                'text-lg font-semibold',
                item.isActive ? 'text-foreground' : 'text-muted-foreground'
              )}
            >
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  )
}
