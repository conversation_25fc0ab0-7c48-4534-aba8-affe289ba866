'use client'

import { UnifiedSidebar } from '@/components/shared/Sidebar'
import { superadminNavigationZones } from '@/lib/navigation/superadmin'
import { Bot, Crown } from 'lucide-react'
import { useSession } from 'next-auth/react'

export default function SuperAdminSidebar() {
  const { data: session } = useSession()

  return (
    <UnifiedSidebar
      zones={superadminNavigationZones}
      title='Emynent'
      titleIcon={Bot}
      titleHref='/dashboard'
      sectionTitle='Super Admin'
      sectionIcon={Crown}
      sectionHref='/superadmin'
      showBackButton={false}
      showCollapseToggle={false}
      userContext={{
        role: session?.user?.role || 'SUPERADMIN',
        companyId: session?.user?.companyId || '',
      }}
      showQuickActions={true}
      testId='superadmin-sidebar'
    />
  )
}
