'use client'

import { Role } from '@prisma/client'
import { Shield } from 'lucide-react'
import { useSession } from 'next-auth/react'

interface SuperAdminLayoutProps {
  children: React.ReactNode
}

export default function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  const { data: session, status } = useSession()

  // Show loading state while session is loading
  if (status === 'loading') {
    return (
      <div className='h-screen flex items-center justify-center bg-background'>
        <div className='text-center'>
          <Shield className='h-8 w-8 text-muted-foreground mx-auto mb-2 animate-pulse' />
          <p className='text-sm text-muted-foreground'>Loading...</p>
        </div>
      </div>
    )
  }

  // Check if user is superadmin
  if (!session || session.user?.role !== Role.SUPERADMIN) {
    return (
      <div className='h-screen flex items-center justify-center bg-background'>
        <div className='text-center p-4'>
          <Shield className='h-8 w-8 text-destructive mx-auto mb-2' />
          <p className='text-sm text-destructive font-medium'>Unauthorized Access</p>
          <p className='text-xs text-muted-foreground mt-1'>SuperAdmin privileges required</p>
        </div>
      </div>
    )
  }

  // Return children directly - layout and sidebar are handled by main layout with SmartSidebar
  return <>{children}</>
}
