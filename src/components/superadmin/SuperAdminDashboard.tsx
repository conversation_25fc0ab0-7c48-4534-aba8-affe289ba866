/**
 * SuperAdminDashboard Component
 *
 * Provides a dashboard interface for SuperAdmin users with links
 * to existing, working SuperAdmin functionality.
 */

'use client'

import React from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Building,
  Palette,
  Brain,
  LayoutDashboard,
  BarChart3,
  Activity,
  ArrowUpRight,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Minus,
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface DashboardCard {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  status: 'healthy' | 'warning' | 'error'
  metrics: {
    value: string | number
    label: string
    trend: 'up' | 'down' | 'stable'
  }
  priority: number
  isNew?: boolean
}

const dashboardCards: DashboardCard[] = [
  {
    id: 'company-management',
    title: 'Company Management',
    description: 'Organizations, users, subscriptions, and billing management',
    icon: Building,
    href: '/superadmin',
    status: 'healthy',
    metrics: { value: 12, label: 'Active Companies', trend: 'up' },
    priority: 1,
  },
  {
    id: 'design-system',
    title: 'Design System',
    description: 'Live component editing, theming, and design system management',
    icon: Palette,
    href: '/superadmin/design-system',
    status: 'healthy',
    metrics: { value: 47, label: 'Components', trend: 'stable' },
    priority: 2,
  },
  {
    id: 'ai-models',
    title: 'AI Models',
    description: 'AI configuration, performance monitoring, and cost tracking',
    icon: Brain,
    href: '/superadmin/ai-models',
    status: 'healthy',
    metrics: { value: '$127', label: 'Monthly Cost', trend: 'down' },
    priority: 3,
    isNew: true,
  },
  {
    id: 'system-overview',
    title: 'System Overview',
    description: 'Platform health, performance metrics, and system logs',
    icon: LayoutDashboard,
    href: '/settings/advanced/logs',
    status: 'healthy',
    metrics: { value: '99.9%', label: 'Uptime', trend: 'up' },
    priority: 4,
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'Usage insights, user behavior, and platform analytics',
    icon: BarChart3,
    href: '/settings/advanced/logs',
    status: 'healthy',
    metrics: { value: '2.4K', label: 'Active Users', trend: 'up' },
    priority: 5,
  },
  {
    id: 'system-health',
    title: 'System Health',
    description: 'Performance monitoring, error tracking, and system logs',
    icon: Activity,
    href: '/settings/advanced/logs',
    status: 'healthy',
    metrics: { value: '< 100ms', label: 'Avg Response', trend: 'stable' },
    priority: 6,
  },
]

const getStatusIcon = (status: DashboardCard['status']) => {
  switch (status) {
    case 'healthy':
      return <CheckCircle className='h-4 w-4 text-green-500' />
    case 'warning':
      return <AlertTriangle className='h-4 w-4 text-yellow-500' />
    case 'error':
      return <AlertTriangle className='h-4 w-4 text-red-500' />
    default:
      return <CheckCircle className='h-4 w-4 text-gray-400' />
  }
}

const getTrendIcon = (trend: DashboardCard['metrics']['trend']) => {
  switch (trend) {
    case 'up':
      return <TrendingUp className='h-3 w-3 text-green-500' />
    case 'down':
      return <TrendingDown className='h-3 w-3 text-red-500' />
    case 'stable':
      return <Minus className='h-3 w-3 text-gray-500' />
    default:
      return <Minus className='h-3 w-3 text-gray-400' />
  }
}

export function SuperAdminDashboard() {
  const sortedCards = dashboardCards.sort((a, b) => a.priority - b.priority)

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4'>
        <div>
          <h1 className='text-2xl lg:text-3xl font-bold tracking-tight text-foreground'>
            SuperAdmin Dashboard
          </h1>
          <p className='text-muted-foreground mt-1 lg:mt-2 text-sm lg:text-base'>
            Platform administration and system management
          </p>
        </div>
        <div className='flex items-center gap-2'>
          <Badge variant='outline' className='text-xs lg:text-sm'>
            Super Admin Access
          </Badge>
        </div>
      </div>

      {/* Quick Stats */}
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
        <Card className='border-border bg-card'>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Total Companies</p>
                <p className='text-xl font-bold text-foreground'>12</p>
              </div>
              <Building className='h-6 w-6 text-primary opacity-70' />
            </div>
          </CardContent>
        </Card>

        <Card className='border-border bg-card'>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Active Users</p>
                <p className='text-xl font-bold text-foreground'>2,847</p>
              </div>
              <BarChart3 className='h-6 w-6 text-primary opacity-70' />
            </div>
          </CardContent>
        </Card>

        <Card className='border-border bg-card'>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>System Health</p>
                <p className='text-xl font-bold text-foreground'>99.9%</p>
              </div>
              <Activity className='h-6 w-6 text-green-500 opacity-70' />
            </div>
          </CardContent>
        </Card>

        <Card className='border-border bg-card'>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Components</p>
                <p className='text-xl font-bold text-foreground'>47</p>
              </div>
              <Palette className='h-6 w-6 text-primary opacity-70' />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Management Modules */}
      <div>
        <h2 className='text-lg font-semibold text-foreground mb-4'>Management Modules</h2>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {sortedCards.map(card => {
            const Icon = card.icon
            return (
              <Card
                key={card.id}
                className={cn(
                  'border-border bg-card hover:bg-accent/50 transition-colors group cursor-pointer',
                  'hover:shadow-md hover:border-primary/20'
                )}
              >
                <Link href={card.href} className='block h-full'>
                  <CardHeader className='pb-3'>
                    <div className='flex items-start justify-between'>
                      <div className='flex items-center gap-3'>
                        <div
                          className={cn(
                            'p-2 rounded-lg',
                            'bg-primary/10 group-hover:bg-primary/20 transition-colors'
                          )}
                        >
                          <Icon className='h-5 w-5 text-primary' />
                        </div>
                        <div>
                          <CardTitle className='text-base font-semibold text-card-foreground group-hover:text-foreground'>
                            {card.title}
                            {card.isNew && (
                              <Badge variant='secondary' className='ml-2 text-xs'>
                                New
                              </Badge>
                            )}
                          </CardTitle>
                        </div>
                      </div>
                      <div className='flex items-center gap-2'>
                        {getStatusIcon(card.status)}
                        <ArrowUpRight className='h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors' />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    <CardDescription className='text-sm text-muted-foreground mb-4 min-h-[2.5rem]'>
                      {card.description}
                    </CardDescription>
                    <div className='flex items-center justify-between text-sm'>
                      <div className='flex items-center gap-2'>
                        <span className='font-semibold text-foreground'>{card.metrics.value}</span>
                        <span className='text-muted-foreground'>{card.metrics.label}</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        {getTrendIcon(card.metrics.trend)}
                      </div>
                    </div>
                  </CardContent>
                </Link>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className='text-lg font-semibold text-foreground mb-4'>Quick Actions</h2>
        <div className='flex flex-wrap gap-2'>
          <Button asChild variant='outline' size='sm'>
            <Link href='/superadmin'>
              <Building className='h-4 w-4 mr-2' />
              Manage Companies
            </Link>
          </Button>
          <Button asChild variant='outline' size='sm'>
            <Link href='/superadmin/design-system'>
              <Palette className='h-4 w-4 mr-2' />
              Edit Design System
            </Link>
          </Button>
          <Button asChild variant='outline' size='sm'>
            <Link href='/superadmin/ai-models'>
              <Brain className='h-4 w-4 mr-2' />
              AI Configuration
            </Link>
          </Button>
          <Button asChild variant='outline' size='sm'>
            <Link href='/settings/advanced/logs'>
              <Activity className='h-4 w-4 mr-2' />
              System Logs
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

export default SuperAdminDashboard
