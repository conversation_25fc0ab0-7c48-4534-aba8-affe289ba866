import { PrismaClient } from '@prisma/client'
import { randomBytes } from 'crypto'

/**
 * Enhanced Database Test Isolation Manager
 * Provides process-level isolation, atomic transactions, and race condition prevention
 */
export class DatabaseTestIsolation {
  private static instance: DatabaseTestIsolation | null = null
  private prismaClient: PrismaClient
  private createdRecords: Map<string, Array<{ table: string; id: string }>> = new Map()
  private processId: string
  private isolationLevel: 'process' | 'test' | 'suite'

  private constructor(isolationLevel: 'process' | 'test' | 'suite' = 'process') {
    this.processId = `pid_${process.pid}_${Date.now()}_${randomBytes(4).toString('hex')}`
    this.isolationLevel = isolationLevel
    this.prismaClient = new PrismaClient({
      datasources: {
        db: {
          url:
            process.env.DATABASE_URL ||
            'postgresql://postgres:postgres@localhost:5432/emynent_test',
        },
      },
      log: process.env.VITEST_VERBOSE ? ['query', 'info', 'warn', 'error'] : [],
    })
  }

  static getInstance(isolationLevel?: 'process' | 'test' | 'suite'): DatabaseTestIsolation {
    if (!DatabaseTestIsolation.instance) {
      DatabaseTestIsolation.instance = new DatabaseTestIsolation(isolationLevel)
    }
    return DatabaseTestIsolation.instance
  }

  /**
   * Generate unique test ID with process isolation
   */
  generateUniqueId(prefix = 'test'): string {
    const timestamp = Date.now().toString(36)
    const random = randomBytes(6).toString('hex')
    const uniqueId = `${prefix}-${this.processId}-${timestamp}-${random}`
    return uniqueId
  }

  /**
   * Generate unique email for testing with process isolation
   */
  generateUniqueEmail(prefix = 'test'): string {
    const uniqueId = this.generateUniqueId(prefix)
    return `${uniqueId}@test.emynent.com`
  }

  /**
   * Initialize test isolation with atomic transaction
   */
  async startTestIsolation(testId: string): Promise<PrismaClient> {
    const fullTestId = `${this.processId}_${testId}`

    // Initialize tracking for this test with process isolation
    this.createdRecords.set(fullTestId, [])

    // Ensure clean start with atomic operation
    await this.ensureCleanTestSpace(fullTestId)

    return this.prismaClient
  }

  /**
   * Ensure clean test space with atomic transaction
   */
  private async ensureCleanTestSpace(testId: string): Promise<void> {
    try {
      await this.prismaClient.$transaction(async tx => {
        // Clean any leftover test data from this process/test
        await tx.session.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { userId: { contains: this.processId } }],
          },
        })

        await tx.account.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { userId: { contains: this.processId } }],
          },
        })

        await tx.userContext.deleteMany({
          where: {
            OR: [{ userId: { contains: this.processId } }],
          },
        })

        await tx.user.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { email: { contains: this.processId } }],
          },
        })

        await tx.company.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { name: { contains: this.processId } }],
          },
        })
      })
    } catch (error) {
      console.warn(`Test space cleanup warning for ${testId}:`, error.message)
      // Continue - this is pre-cleanup, failures are acceptable
    }
  }

  /**
   * Track created record for cleanup with process isolation
   */
  trackCreatedRecord(testId: string, table: string, id: string): void {
    const fullTestId = `${this.processId}_${testId}`
    const records = this.createdRecords.get(fullTestId) || []
    records.push({ table, id })
    this.createdRecords.set(fullTestId, records)
  }

  /**
   * Create test company with atomic transaction and process isolation
   */
  async createTestCompany(testId: string, name?: string): Promise<any> {
    const uniqueId = this.generateUniqueId('company')
    const companyName = name || `Test Company ${uniqueId}`
    const domain = `${uniqueId}.test.emynent.com`

    try {
      const company = await this.prismaClient.$transaction(async tx => {
        return await tx.company.create({
          data: {
            id: uniqueId,
            name: companyName,
            domains: [domain],
            allowedEmailDomains: [domain],
            isActive: true,
            subscriptionStatus: 'ACTIVE',
            currentPlan: 'ENTERPRISE',
            maxUsers: 1000,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        })
      })

      this.trackCreatedRecord(testId, 'company', uniqueId)
      return company
    } catch (error) {
      console.error('Failed to create test company:', error)
      throw error
    }
  }

  /**
   * Create test user with atomic transaction and process isolation
   */
  async createTestUser(testId: string, companyId: string, userData?: Partial<any>): Promise<any> {
    const uniqueId = this.generateUniqueId('user')
    const uniqueEmail = this.generateUniqueEmail('user')

    try {
      const user = await this.prismaClient.$transaction(async tx => {
        return await tx.user.create({
          data: {
            id: uniqueId,
            email: uniqueEmail,
            name: `Test User ${uniqueId}`,
            companyId,
            role: 'EMPLOYEE',
            onboardingCompleted: true,
            emailVerified: new Date(),
            ...userData,
          },
        })
      })

      this.trackCreatedRecord(testId, 'user', uniqueId)
      return user
    } catch (error) {
      console.error('Failed to create test user:', error)
      throw error
    }
  }

  /**
   * Create test user context with atomic transaction
   */
  async createTestUserContext(
    testId: string,
    userId: string,
    companyId: string,
    contextData?: any
  ): Promise<any> {
    try {
      const userContext = await this.prismaClient.$transaction(async tx => {
        return await tx.userContext.create({
          data: {
            userId,
            role: 'EMPLOYEE',
            companyId,
            preferences: {
              theme: 'system',
              colorScheme: 'emynent-default',
              ...contextData?.preferences,
            },
            recentActions: {
              lastLogin: new Date().toISOString(),
              ...contextData?.recentActions,
            },
            historicalData: {
              loginCount: 1,
              ...contextData?.historicalData,
            },
            ...contextData,
          },
        })
      })

      this.trackCreatedRecord(testId, 'userContext', userId)
      return userContext
    } catch (error) {
      console.error('Failed to create test user context:', error)
      throw error
    }
  }

  /**
   * Cleanup test data with atomic transaction and proper dependency order
   */
  async cleanupTest(testId: string): Promise<void> {
    const fullTestId = `${this.processId}_${testId}`

    try {
      const records = this.createdRecords.get(fullTestId) || []

      if (records.length === 0) {
        // No tracked records, but clean by process ID pattern as fallback
        await this.forceCleanupTestRecords(testId)
        return
      }

      // Delete in proper dependency order using atomic transaction
      await this.prismaClient.$transaction(async tx => {
        // Extract unique IDs by table type
        const userIds = records.filter(r => r.table === 'user').map(r => r.id)
        const companyIds = records.filter(r => r.table === 'company').map(r => r.id)

        // Clean dependent records first
        if (userIds.length > 0) {
          await tx.session.deleteMany({
            where: { userId: { in: userIds } },
          })

          await tx.account.deleteMany({
            where: { userId: { in: userIds } },
          })

          await tx.userContext.deleteMany({
            where: { userId: { in: userIds } },
          })

          await tx.user.deleteMany({
            where: { id: { in: userIds } },
          })
        }

        // Clean companies last
        if (companyIds.length > 0) {
          await tx.company.deleteMany({
            where: { id: { in: companyIds } },
          })
        }
      })

      // Clear tracking for this test
      this.createdRecords.delete(fullTestId)
    } catch (error) {
      console.error('Failed to cleanup test:', error)
      // Force cleanup if normal cleanup fails
      await this.forceCleanupTestRecords(testId)
    }
  }

  /**
   * Force cleanup all test records with process isolation
   */
  private async forceCleanupTestRecords(testId: string): Promise<void> {
    try {
      await this.prismaClient.$transaction(async tx => {
        // Delete all records with process ID patterns
        await tx.session.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { userId: { contains: this.processId } }],
          },
        })

        await tx.account.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { userId: { contains: this.processId } }],
          },
        })

        await tx.userContext.deleteMany({
          where: {
            userId: { contains: this.processId },
          },
        })

        await tx.user.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { email: { contains: this.processId } }],
          },
        })

        await tx.company.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { name: { contains: this.processId } }],
          },
        })
      })

      // Clear tracking for this process
      const fullTestId = `${this.processId}_${testId}`
      this.createdRecords.delete(fullTestId)
    } catch (error) {
      console.warn('Force cleanup failed:', error)
    }
  }

  /**
   * Global cleanup for process shutdown
   */
  async globalCleanup(): Promise<void> {
    try {
      // Clean all records created by this process
      await this.prismaClient.$transaction(async tx => {
        await tx.session.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { userId: { contains: this.processId } }],
          },
        })

        await tx.account.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { userId: { contains: this.processId } }],
          },
        })

        await tx.userContext.deleteMany({
          where: {
            userId: { contains: this.processId },
          },
        })

        await tx.user.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { email: { contains: this.processId } }],
          },
        })

        await tx.company.deleteMany({
          where: {
            OR: [{ id: { contains: this.processId } }, { name: { contains: this.processId } }],
          },
        })
      })

      // Clear all tracking
      this.createdRecords.clear()
    } catch (error) {
      console.warn('Global cleanup failed:', error)
    }
  }

  /**
   * Close database connection and cleanup
   */
  async close(): Promise<void> {
    try {
      await this.globalCleanup()
      if (this.prismaClient) {
        await this.prismaClient.$disconnect()
      }
    } catch (error) {
      console.warn('Close cleanup failed:', error)
    } finally {
      DatabaseTestIsolation.instance = null
    }
  }

  /**
   * Get Prisma client for direct access
   */
  getPrismaClient(): PrismaClient {
    return this.prismaClient
  }

  /**
   * Execute with atomic transaction wrapper
   */
  async withTransaction<T>(fn: (tx: any) => Promise<T>): Promise<T> {
    return await this.prismaClient.$transaction(fn)
  }
}

/**
 * Enhanced test helper functions
 */
export function getTestIsolation(
  isolationLevel?: 'process' | 'test' | 'suite'
): DatabaseTestIsolation {
  return DatabaseTestIsolation.getInstance(isolationLevel)
}

export function generateTestId(prefix = 'test'): string {
  return getTestIsolation().generateUniqueId(prefix)
}

export function generateTestEmail(prefix = 'test'): string {
  return getTestIsolation().generateUniqueEmail(prefix)
}
