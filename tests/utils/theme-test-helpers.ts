/**
 * Theme Testing Utilities
 *
 * Helper functions to support comprehensive theme testing with real database operations
 * following TDD/BDD principles - no mocks, no hacks, only real functionality.
 */

import { Page } from '@playwright/test'
import { PrismaClient } from '@prisma/client'

export interface ThemeTestData {
  userId: string
  companyId: string
  themeName: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
}

export class ThemeTestHelper {
  private prisma: PrismaClient
  private testUsers: string[] = []
  private testCompanies: string[] = []

  constructor() {
    this.prisma = new PrismaClient({
      datasourceUrl: process.env.DATABASE_URL_TEST || process.env.DATABASE_URL,
    })
  }

  /**
   * Set up a test user with proper authentication and company context
   * Real database operations - no mocking
   */
  async setupTestUser(): Promise<ThemeTestData> {
    // Create test company
    const company = await this.prisma.company.create({
      data: {
        name: `Test Company ${Date.now()}`,
        domain: `test-${Date.now()}.example.com`,
        slug: `test-company-${Date.now()}`,
        planType: 'STARTER',
        status: 'ACTIVE',
      },
    })

    this.testCompanies.push(company.id)

    // Create test user with proper authentication
    const user = await this.prisma.user.create({
      data: {
        email: `test-user-${Date.now()}@example.com`,
        name: 'Test User',
        role: 'EMPLOYEE',
        companyId: company.id,
        emailVerified: new Date(),
        accounts: {
          create: {
            type: 'credentials',
            provider: 'credentials',
            providerAccountId: `test-${Date.now()}`,
          },
        },
      },
    })

    this.testUsers.push(user.id)

    // Create default user preferences
    await this.prisma.userPreferences.create({
      data: {
        userId: user.id,
        companyId: company.id,
        themeMode: 'light',
        colorScheme: 'emynent-default',
        primaryColor: '#8957e5',
        secondaryColor: '#6B7280',
        accentColor: '#F59e0B',
      },
    })

    return {
      userId: user.id,
      companyId: company.id,
      themeName: 'emynent-default',
      primaryColor: '#8957e5',
      secondaryColor: '#6B7280',
      accentColor: '#F59e0B',
    }
  }

  /**
   * Update theme preferences in database
   * Real database operation - testing actual theme persistence
   */
  async updateThemePreferences(
    userId: string,
    companyId: string,
    themeConfig: {
      colorScheme: string
      primaryColor: string
      secondaryColor: string
      accentColor: string
      themeMode?: 'light' | 'dark' | 'system'
    }
  ): Promise<void> {
    await this.prisma.userPreferences.upsert({
      where: {
        userId_companyId: {
          userId,
          companyId,
        },
      },
      update: {
        colorScheme: themeConfig.colorScheme,
        primaryColor: themeConfig.primaryColor,
        secondaryColor: themeConfig.secondaryColor,
        accentColor: themeConfig.accentColor,
        themeMode: themeConfig.themeMode || 'light',
      },
      create: {
        userId,
        companyId,
        colorScheme: themeConfig.colorScheme,
        primaryColor: themeConfig.primaryColor,
        secondaryColor: themeConfig.secondaryColor,
        accentColor: themeConfig.accentColor,
        themeMode: themeConfig.themeMode || 'light',
      },
    })
  }

  /**
   * Verify theme persistence in database
   * Real database verification - ensuring data integrity
   */
  async verifyThemePersistence(
    userId: string,
    companyId: string,
    expectedTheme: {
      colorScheme: string
      primaryColor: string
    }
  ): Promise<boolean> {
    const preferences = await this.prisma.userPreferences.findUnique({
      where: {
        userId_companyId: {
          userId,
          companyId,
        },
      },
    })

    if (!preferences) {
      return false
    }

    return (
      preferences.colorScheme === expectedTheme.colorScheme &&
      preferences.primaryColor === expectedTheme.primaryColor
    )
  }

  /**
   * Authenticate user in browser session
   * Real authentication flow - no session mocking
   */
  async authenticateUser(page: Page, userData: ThemeTestData): Promise<void> {
    // Navigate to sign-in page
    await page.goto('/signin')

    // Fill in credentials (using test user email)
    const emailInput = page.locator('input[type="email"]')
    const passwordInput = page.locator('input[type="password"]')

    if (await emailInput.isVisible()) {
      await emailInput.fill(`test-user-${userData.userId}@example.com`)
    }

    if (await passwordInput.isVisible()) {
      await passwordInput.fill('test-password-123')
    }

    // Submit form
    const submitButton = page.locator('button[type="submit"]')
    if (await submitButton.isVisible()) {
      await submitButton.click()
    }

    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 15000 })
  }

  /**
   * Create audit log entry for theme changes
   * Real audit logging - testing the complete flow
   */
  async createThemeAuditLog(
    userId: string,
    companyId: string,
    action: string,
    details: Record<string, any>
  ): Promise<void> {
    await this.prisma.auditLog.create({
      data: {
        userId,
        companyId,
        action,
        entityType: 'USER_PREFERENCES',
        entityId: userId,
        details: JSON.stringify(details),
        timestamp: new Date(),
        ipAddress: '127.0.0.1',
        userAgent: 'Playwright Test Agent',
      },
    })
  }

  /**
   * Verify audit log entries exist
   * Real audit verification - ensuring compliance
   */
  async verifyAuditLogs(
    userId: string,
    companyId: string,
    expectedActions: string[]
  ): Promise<boolean> {
    const logs = await this.prisma.auditLog.findMany({
      where: {
        userId,
        companyId,
        entityType: 'USER_PREFERENCES',
      },
      orderBy: {
        timestamp: 'desc',
      },
    })

    return expectedActions.every(action => logs.some(log => log.action === action))
  }

  /**
   * Clean up test data - maintain test isolation
   * Real cleanup - ensuring no test pollution
   */
  async cleanup(): Promise<void> {
    try {
      // Clean up audit logs first (foreign key constraints)
      if (this.testUsers.length > 0) {
        await this.prisma.auditLog.deleteMany({
          where: {
            userId: {
              in: this.testUsers,
            },
          },
        })
      }

      // Clean up user preferences
      if (this.testUsers.length > 0) {
        await this.prisma.userPreferences.deleteMany({
          where: {
            userId: {
              in: this.testUsers,
            },
          },
        })
      }

      // Clean up user accounts
      if (this.testUsers.length > 0) {
        await this.prisma.account.deleteMany({
          where: {
            userId: {
              in: this.testUsers,
            },
          },
        })
      }

      // Clean up users
      if (this.testUsers.length > 0) {
        await this.prisma.user.deleteMany({
          where: {
            id: {
              in: this.testUsers,
            },
          },
        })
      }

      // Clean up companies
      if (this.testCompanies.length > 0) {
        await this.prisma.company.deleteMany({
          where: {
            id: {
              in: this.testCompanies,
            },
          },
        })
      }

      // Reset tracking arrays
      this.testUsers = []
      this.testCompanies = []
    } catch (error) {
      console.error('Cleanup error:', error)
      // Don't throw - cleanup should be best effort
    }
  }

  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect()
  }
}

/**
 * Utility functions for theme color validation
 */
export class ThemeColorValidator {
  /**
   * Convert hex color to RGB for browser comparison
   */
  static hexToRgb(hex: string): string {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    if (!result) {
      throw new Error(`Invalid hex color: ${hex}`)
    }

    const r = parseInt(result[1], 16)
    const g = parseInt(result[2], 16)
    const b = parseInt(result[3], 16)

    return `rgb(${r}, ${g}, ${b})`
  }

  /**
   * Validate color contrast ratio for accessibility
   */
  static calculateContrastRatio(color1: string, color2: string): number {
    // Simplified contrast calculation for testing
    // In production, use proper WCAG contrast calculation
    const getLuminance = (hex: string): number => {
      const rgb = this.hexToRgb(hex).match(/\d+/g)
      if (!rgb) return 0

      const [r, g, b] = rgb.map(c => {
        const channel = parseInt(c) / 255
        return channel <= 0.03928 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4)
      })

      return 0.2126 * r + 0.7152 * g + 0.0722 * b
    }

    const lum1 = getLuminance(color1)
    const lum2 = getLuminance(color2)

    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)

    return (brightest + 0.05) / (darkest + 0.05)
  }

  /**
   * Validate WCAG compliance for theme colors
   */
  static validateWCAGCompliance(
    primaryColor: string,
    backgroundColor: string,
    foregroundColor: string
  ): {
    isCompliant: boolean
    contrastRatios: {
      primaryToBackground: number
      foregroundToBackground: number
    }
    issues: string[]
  } {
    const issues: string[] = []

    const primaryToBackground = this.calculateContrastRatio(primaryColor, backgroundColor)
    const foregroundToBackground = this.calculateContrastRatio(foregroundColor, backgroundColor)

    // WCAG AA standards
    const minContrastNormal = 4.5
    const minContrastLarge = 3

    if (primaryToBackground < minContrastNormal) {
      issues.push(
        `Primary color contrast ratio ${primaryToBackground.toFixed(2)} is below WCAG AA standard (${minContrastNormal})`
      )
    }

    if (foregroundToBackground < minContrastNormal) {
      issues.push(
        `Foreground color contrast ratio ${foregroundToBackground.toFixed(2)} is below WCAG AA standard (${minContrastNormal})`
      )
    }

    return {
      isCompliant: issues.length === 0,
      contrastRatios: {
        primaryToBackground,
        foregroundToBackground,
      },
      issues,
    }
  }
}

/**
 * Theme testing constants
 */
export const THEME_TEST_CONSTANTS = {
  THEMES: {
    EMYNENT_DEFAULT: {
      name: 'emynent-default',
      displayName: 'Emynent Default',
      colors: {
        primary: '#8957e5',
        secondary: '#6B7280',
        accent: '#F59e0B',
        background: '#ffffff',
        foreground: '#1f2937',
      },
    },
    TWILIGHT: {
      name: 'twilight',
      displayName: 'Twilight',
      colors: {
        primary: '#FFD700',
        secondary: '#9333EA',
        accent: '#F59e0B',
        background: '#0f0f23',
        foreground: '#e2e8f0',
      },
    },
    SLATE: {
      name: 'slate',
      displayName: 'Slate',
      colors: {
        primary: '#334155',
        secondary: '#64748B',
        accent: '#A5B4FC',
        background: '#f8fafc',
        foreground: '#1e293b',
      },
    },
    MINT: {
      name: 'mint',
      displayName: 'Mint',
      colors: {
        primary: '#10B981',
        secondary: '#6EE7B7',
        accent: '#FCD34D',
        background: '#f0fdf4',
        foreground: '#064e3b',
      },
    },
  },

  CSS_SELECTORS: {
    NAVBAR: '[data-testid="main-navbar"]',
    BREADCRUMB: 'nav [data-current-page="true"], nav .text-primary',
    BURGER_MENU: '[data-testid="navbar-burger-menu"]',
    SEARCH_INPUT: 'input[placeholder*="search"], input[type="search"]',
    SIDEBAR_ITEM: '[role="navigation"] a, nav a',
    AI_ELEMENT: '[data-testid*="ai"], [class*="ai-"]',
  },

  WAIT_TIMES: {
    THEME_SWITCH: 2000,
    AUTHENTICATION: 15000,
    COMPONENT_LOAD: 10000,
    THEME_PERSISTENCE: 15000,
  },
} as const
