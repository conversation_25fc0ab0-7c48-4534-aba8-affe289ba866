import React from 'react'
import { render } from '@testing-library/react'
import { ThemeProvider } from '@/app/providers/theme-provider'
import { getTestIsolation, generateTestId, generateTestEmail } from './database-test-isolation'

/**
 * Enhanced Test Wrapper with proper context and cleanup
 */
export function TestWrapper({
  children,
  themeMode = 'light',
  withThemeProvider = true,
  withDomCleanup = true,
}: {
  children: React.ReactNode
  themeMode?: 'light' | 'dark' | 'system'
  withThemeProvider?: boolean
  withDomCleanup?: boolean
}) {
  React.useEffect(() => {
    if (!withDomCleanup) return

    // Set up DOM cleanup to prevent "node to be removed is not a child" errors
    const originalRemoveChild = Node.prototype.removeChild
    Node.prototype.removeChild = function (child: Node) {
      if (this.contains(child)) {
        return originalRemoveChild.call(this, child)
      }
      return child
    }

    return () => {
      Node.prototype.removeChild = originalRemoveChild
    }
  }, [withDomCleanup])

  if (!withThemeProvider) {
    return <div data-testid='test-wrapper'>{children}</div>
  }

  return (
    <div data-testid='test-wrapper'>
      <ThemeProvider attribute='class' defaultTheme={themeMode} enableSystem={true}>
        {children}
      </ThemeProvider>
    </div>
  )
}

/**
 * Enhanced render function with theme provider and cleanup
 */
export function renderWithTheme(
  ui: React.ReactElement,
  options?: {
    themeMode?: 'light' | 'dark' | 'system'
    withThemeProvider?: boolean
    withDomCleanup?: boolean
  }
) {
  const { themeMode = 'light', withThemeProvider = true, withDomCleanup = true } = options || {}

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper
      themeMode={themeMode}
      withThemeProvider={withThemeProvider}
      withDomCleanup={withDomCleanup}
    >
      {children}
    </TestWrapper>
  )

  const result = render(ui, { wrapper: Wrapper })

  // Enhanced cleanup function
  const originalCleanup = result.unmount
  result.unmount = () => {
    try {
      originalCleanup()
    } catch (error) {
      // Silently handle DOM cleanup errors that don't affect test validity
      console.warn('DOM cleanup warning:', error.message)
    }
  }

  return result
}

/**
 * Database-aware test wrapper that provides isolation
 */
export async function withDatabaseIsolation<T>(
  testFn: (testId: string) => Promise<T>,
  testName?: string
): Promise<T> {
  const testId = generateTestId(testName || 'test')
  const isolation = getTestIsolation()

  try {
    // Start test isolation
    await isolation.startTestIsolation(testId)

    // Run the test
    const result = await testFn(testId)

    return result
  } finally {
    // Always cleanup, even if test fails
    await isolation.cleanupTest(testId)
  }
}

/**
 * Create test data factory with proper isolation
 */
export class TestDataFactory {
  private testId: string
  private isolation: any

  constructor(testId: string) {
    this.testId = testId
    this.isolation = getTestIsolation()
  }

  async createCompany(data?: any) {
    return this.isolation.createTestCompany(this.testId, data?.name)
  }

  async createUser(companyId: string, data?: any) {
    return this.isolation.createTestUser(this.testId, companyId, data)
  }

  async createUserContext(userId: string, companyId: string, data?: any) {
    return this.isolation.createTestUserContext(this.testId, userId, companyId, data)
  }

  async withTransaction<T>(fn: (tx: any) => Promise<T>): Promise<T> {
    return this.isolation.withTransaction(fn)
  }
}

/**
 * Enhanced test helpers for common patterns
 */
export async function createTestEnvironment(testName?: string) {
  const testId = generateTestId(testName || 'test')
  const factory = new TestDataFactory(testId)
  const isolation = getTestIsolation()

  await isolation.startTestIsolation(testId)

  const cleanup = async () => {
    await isolation.cleanupTest(testId)
  }

  return { testId, factory, cleanup }
}

/**
 * Mock API fetch for testing
 */
export function mockFetch(responses: Array<{ url: string; response: any; status?: number }>) {
  const originalFetch = global.fetch

  global.fetch = jest.fn().mockImplementation((url: string) => {
    const matchedResponse = responses.find(r => url.includes(r.url))

    if (matchedResponse) {
      return Promise.resolve({
        ok: (matchedResponse.status || 200) < 400,
        status: matchedResponse.status || 200,
        json: () => Promise.resolve(matchedResponse.response),
        text: () => Promise.resolve(JSON.stringify(matchedResponse.response)),
      })
    }

    return Promise.reject(new Error(`Unmocked fetch to ${url}`))
  })

  return () => {
    global.fetch = originalFetch
  }
}

/**
 * Wait for element with enhanced error handling
 */
export async function waitForElementSafe(
  getElement: () => HTMLElement | null,
  timeout = 5000
): Promise<HTMLElement> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()

    const check = () => {
      try {
        const element = getElement()
        if (element) {
          resolve(element)
          return
        }
      } catch (error) {
        // Element might not be in DOM yet, continue waiting
      }

      if (Date.now() - startTime > timeout) {
        reject(new Error(`Element not found within ${timeout}ms`))
        return
      }

      setTimeout(check, 100)
    }

    check()
  })
}
