import { createServer } from 'net'
import { sign, verify } from 'jsonwebtoken'
import { config } from 'dotenv'
import path from 'path'

// Load test environment configuration
config({ path: path.join(__dirname, '../config/test.env') })

/**
 * Test Environment Configuration
 */
export const TEST_CONFIG = {
  // Database
  DATABASE_URL:
    process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/emynent_test',

  // Authentication
  NEXTAUTH_SECRET:
    process.env.NEXTAUTH_SECRET || 'test-secret-for-websocket-testing-only-not-for-production',
  JWT_EXPIRATION: process.env.JWT_EXPIRATION || '1h',

  // WebSocket
  WEBSOCKET_PORT_RANGE_START: parseInt(process.env.WEBSOCKET_PORT_RANGE_START || '8100'),
  WEBSOCKET_PORT_RANGE_END: parseInt(process.env.WEBSOCKET_PORT_RANGE_END || '8200'),
  WEBSOCKET_TEST_TIMEOUT: parseInt(process.env.WEBSOCKET_TEST_TIMEOUT || '5000'),
  CONNECTION_TIMEOUT: parseInt(process.env.WEBSOCKET_CONNECTION_TIMEOUT || '3000'),

  // Performance
  MAX_CONNECTIONS: parseInt(process.env.MAX_WEBSOCKET_CONNECTIONS || '100'),
  HEARTBEAT_INTERVAL: parseInt(process.env.HEARTBEAT_INTERVAL || '30000'),

  // CI/CD
  IS_CI: process.env.CI === 'true' || process.env.GITHUB_ACTIONS === 'true',
  VERBOSE: process.env.TEST_VERBOSE === 'true',
}

/**
 * Find an available port within the specified range
 */
export async function findAvailablePort(
  startPort: number = TEST_CONFIG.WEBSOCKET_PORT_RANGE_START,
  endPort: number = TEST_CONFIG.WEBSOCKET_PORT_RANGE_END
): Promise<number> {
  for (let port = startPort; port <= endPort; port++) {
    if (await isPortAvailable(port)) {
      return port
    }
  }
  throw new Error(`No available ports found in range ${startPort}-${endPort}`)
}

/**
 * Check if a specific port is available
 */
export function isPortAvailable(port: number): Promise<boolean> {
  return new Promise(resolve => {
    const server = createServer()

    server.listen(port, () => {
      server.once('close', () => resolve(true))
      server.close()
    })

    server.on('error', () => resolve(false))
  })
}

/**
 * Generate a valid JWT token for testing
 */
export function generateTestJWTToken(payload: {
  userId: string
  email: string
  role: string
  companyId: string
}): string {
  const tokenPayload = {
    sub: payload.userId,
    email: payload.email,
    role: payload.role,
    companyId: payload.companyId,
    aud: 'websocket-design-system',
    iss: 'emynent-platform',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour
  }

  return sign(tokenPayload, TEST_CONFIG.NEXTAUTH_SECRET)
}

/**
 * Verify a JWT token for testing
 */
export function verifyTestJWTToken(token: string): any {
  try {
    return verify(token, TEST_CONFIG.NEXTAUTH_SECRET)
  } catch (error) {
    throw new Error(`Invalid test JWT token: ${error.message}`)
  }
}

/**
 * Create test user data
 */
export function createTestUser(
  overrides: Partial<{
    userId: string
    email: string
    role: string
    companyId: string
    userName: string
  }> = {}
) {
  return {
    userId: overrides.userId || 'test-user-' + Date.now(),
    email: overrides.email || '<EMAIL>',
    role: overrides.role || 'SUPERADMIN',
    companyId: overrides.companyId || 'test-company-' + Date.now(),
    userName: overrides.userName || 'Test User',
  }
}

/**
 * Wait for a specified amount of time
 */
export function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Retry a function with exponential backoff
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 100
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxAttempts) {
        throw lastError
      }

      const delay = baseDelay * Math.pow(2, attempt - 1)
      await wait(delay)
    }
  }

  throw lastError!
}

/**
 * Setup test environment
 */
export async function setupTestEnvironment() {
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.NEXTAUTH_SECRET = TEST_CONFIG.NEXTAUTH_SECRET

  if (TEST_CONFIG.VERBOSE) {
    console.log('🧪 Test environment setup:', {
      NODE_ENV: process.env.NODE_ENV,
      DATABASE_URL: TEST_CONFIG.DATABASE_URL,
      PORT_RANGE: `${TEST_CONFIG.WEBSOCKET_PORT_RANGE_START}-${TEST_CONFIG.WEBSOCKET_PORT_RANGE_END}`,
      IS_CI: TEST_CONFIG.IS_CI,
    })
  }
}

/**
 * Cleanup test environment
 */
export async function cleanupTestEnvironment() {
  // Cleanup logic can be added here
  if (TEST_CONFIG.VERBOSE) {
    console.log('🧹 Test environment cleanup completed')
  }
}

/**
 * Create a timeout promise for testing
 */
export function createTimeout(ms: number, message?: string): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(message || `Operation timed out after ${ms}ms`))
    }, ms)
  })
}

/**
 * Race a promise against a timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage?: string
): Promise<T> {
  return Promise.race([promise, createTimeout(timeoutMs, timeoutMessage)])
}
