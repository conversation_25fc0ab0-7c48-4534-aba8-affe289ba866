/**
 * Test isolation utilities for Emynent testing
 */

export interface TestIsolation {
  close: () => Promise<void>
  cleanup: () => void
}

class TestIsolationManager implements TestIsolation {
  private static instance: TestIsolationManager | null = null

  static getInstance(): TestIsolationManager {
    if (!TestIsolationManager.instance) {
      TestIsolationManager.instance = new TestIsolationManager()
    }
    return TestIsolationManager.instance
  }

  cleanup(): void {
    try {
      // Clean up any global test state synchronously
      if (typeof window !== 'undefined') {
        window.localStorage.clear()
        window.sessionStorage.clear()
      }
    } catch (error) {
      console.warn('Test isolation cleanup warning:', error)
    }
  }

  async close(): Promise<void> {
    try {
      // Clean up any global test state
      this.cleanup()
    } catch (error) {
      console.warn('Test isolation cleanup warning:', error)
    }
  }
}

export function getTestIsolation(): TestIsolation {
  return TestIsolationManager.getInstance()
}
