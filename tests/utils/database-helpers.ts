import { PrismaClient } from '@prisma/client'
import { execSync } from 'child_process'
import { TEST_CONFIG } from './test-helpers'

let testPrisma: PrismaClient | null = null

/**
 * Get or create a test Prisma client
 */
export function getTestPrismaClient(): PrismaClient {
  if (!testPrisma) {
    testPrisma = new PrismaClient({
      datasources: {
        db: {
          url: TEST_CONFIG.DATABASE_URL,
        },
      },
      log: TEST_CONFIG.VERBOSE ? ['query', 'info', 'warn', 'error'] : ['error'],
    })
  }
  return testPrisma
}

/**
 * Setup test database
 */
export async function setupTestDatabase(): Promise<void> {
  try {
    // Create test database if it doesn't exist
    await createTestDatabaseIfNotExists()

    // Run migrations
    await runMigrations()

    // Seed test data if needed
    await seedTestData()

    if (TEST_CONFIG.VERBOSE) {
      console.log('✅ Test database setup completed')
    }
  } catch (error) {
    console.error('❌ Test database setup failed:', error)
    throw error
  }
}

/**
 * Cleanup test database
 */
export async function cleanupTestDatabase(): Promise<void> {
  try {
    const prisma = getTestPrismaClient()

    // Clean up all tables in reverse dependency order
    // Handle tables that might not exist gracefully
    try {
      await prisma.componentCollaboration.deleteMany()
    } catch (error) {
      // Table might not exist, continue
    }

    try {
      await prisma.designSystemComponent.deleteMany()
    } catch (error) {
      // Table might not exist, continue
    }

    try {
      await prisma.componentVersion.deleteMany()
    } catch (error) {
      // Table might not exist, continue
    }

    try {
      await prisma.auditLog.deleteMany()
    } catch (error) {
      // Table might not exist, continue
    }

    try {
      await prisma.session.deleteMany()
    } catch (error) {
      // Table might not exist, continue
    }

    try {
      await prisma.account.deleteMany()
    } catch (error) {
      // Table might not exist, continue
    }

    await prisma.user.deleteMany()
    await prisma.company.deleteMany()

    if (TEST_CONFIG.VERBOSE) {
      console.log('🧹 Test database cleanup completed')
    }
  } catch (error) {
    console.error('❌ Test database cleanup failed:', error)
    throw error
  }
}

/**
 * Create test database if it doesn't exist
 */
async function createTestDatabaseIfNotExists(): Promise<void> {
  try {
    // Extract database name from URL
    const dbUrl = new URL(TEST_CONFIG.DATABASE_URL)
    const dbName = dbUrl.pathname.slice(1) // Remove leading slash

    // Create database if it doesn't exist
    const createDbUrl = `${dbUrl.protocol}//${dbUrl.username}:${dbUrl.password}@${dbUrl.host}/postgres`

    const createDbCommand = `psql "${createDbUrl}" -c "CREATE DATABASE ${dbName};" 2>/dev/null || true`

    if (TEST_CONFIG.VERBOSE) {
      console.log(`📊 Creating test database: ${dbName}`)
    }

    execSync(createDbCommand, { stdio: 'pipe' })
  } catch (error) {
    // Database might already exist, which is fine
    if (TEST_CONFIG.VERBOSE) {
      console.log('📊 Test database already exists or creation skipped')
    }
  }
}

/**
 * Run database migrations
 */
async function runMigrations(): Promise<void> {
  try {
    if (TEST_CONFIG.VERBOSE) {
      console.log('🔄 Running database migrations...')
    }

    // Set the database URL for migrations
    process.env.DATABASE_URL = TEST_CONFIG.DATABASE_URL

    execSync('npx prisma migrate deploy', {
      stdio: TEST_CONFIG.VERBOSE ? 'inherit' : 'pipe',
      env: {
        ...process.env,
        DATABASE_URL: TEST_CONFIG.DATABASE_URL,
      },
    })

    if (TEST_CONFIG.VERBOSE) {
      console.log('✅ Database migrations completed')
    }
  } catch (error) {
    console.error('❌ Database migrations failed:', error)
    throw error
  }
}

/**
 * Seed test data
 */
async function seedTestData(): Promise<void> {
  try {
    const prisma = getTestPrismaClient()

    // Create test company with proper error handling
    const testCompany = await prisma.company.upsert({
      where: { id: 'test-company-id' },
      update: {
        name: 'Test Company',
        domains: ['test.com'],
        allowedEmailDomains: ['test.com'],
        isActive: true,
        subscriptionStatus: 'ACTIVE',
        currentPlan: 'ENTERPRISE',
        maxUsers: 1000,
        updatedAt: new Date(),
      },
      create: {
        id: 'test-company-id',
        name: 'Test Company',
        domains: ['test.com'],
        allowedEmailDomains: ['test.com'],
        isActive: true,
        subscriptionStatus: 'ACTIVE',
        currentPlan: 'ENTERPRISE',
        maxUsers: 1000,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })

    // Ensure company exists before creating user
    if (!testCompany) {
      throw new Error('Failed to create test company')
    }

    // Create test user with proper error handling
    await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        name: 'Test User',
        role: 'SUPERADMIN',
        companyId: testCompany.id,
        emailVerified: new Date(),
        onboardingCompleted: true,
        updatedAt: new Date(),
      },
      create: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'SUPERADMIN',
        companyId: testCompany.id,
        emailVerified: new Date(),
        onboardingCompleted: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })

    if (TEST_CONFIG.VERBOSE) {
      console.log('🌱 Test data seeded successfully')
    }
  } catch (error) {
    console.error('❌ Test data seeding failed:', error)
    throw error
  }
}

/**
 * Reset test database to clean state
 */
export async function resetTestDatabase(): Promise<void> {
  await cleanupTestDatabase()
  await seedTestData()
}

/**
 * Close test database connection
 */
export async function closeTestDatabase(): Promise<void> {
  if (testPrisma) {
    await testPrisma.$disconnect()
    testPrisma = null
  }
}
<<<<<<< HEAD
=======

export async function createTestAnalyticsEvent(data: any) {
  const prisma = getTestPrismaClient()
  return prisma.analyticsEvent.create({
    data: {
      userId: 'test-user-id',
      companyId: 'test-company-id',
      eventType: 'navigation',
      ...data,
    },
  })
}
>>>>>>> feature/sidebar-navigation
