/**
 * Phase 3 Intelligence Layer Tests
 * NO MOCKS - REAL FUNCTIONALITY TESTING
 *
 * These tests verify that the Intelligence Layer works with real database operations,
 * real context capture, real behavioral analytics, and real AI integration.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { realContextCaptureService } from '@/services/intelligence/real-context-capture'
import { realBehavioralAnalyticsService } from '@/services/intelligence/real-behavioral-analytics'
import { redis } from '@/lib/redis'

const prisma = new PrismaClient()

describe('🧠 Phase 3: Intelligence Layer - Real Functionality Tests', () => {
  let testUserId: string
  let testCompanyId: string
  let testUserEmail: string

  beforeAll(async () => {
    // Clean up any existing test data first
    await prisma.contextEvent.deleteMany({
      where: {
        OR: [
          { userId: { contains: 'intelligence-test' } },
          { companyId: { contains: 'test-intelligence' } },
        ],
      },
    })
    await prisma.behavioralPattern.deleteMany({
      where: { userId: { contains: 'intelligence-test' } },
    })
    await prisma.user.deleteMany({
      where: { email: '<EMAIL>' },
    })
    await prisma.company.deleteMany({
      where: { name: 'Phase3 Intelligence Company' },
    })

    // Create test company first to satisfy foreign key constraint
    const testCompany = await prisma.company.create({
      data: {
        name: 'Phase3 Intelligence Company',
        domains: ['phase3-intelligence-company.com'],
        allowedEmailDomains: ['phase3-intelligence-company.com'],
      },
    })
    testCompanyId = testCompany.id

    // Create test user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Phase3 Intelligence User',
        role: 'EMPLOYEE',
        companyId: testCompanyId, // Use the created company ID
        onboardingCompleted: true,
        emailVerified: new Date(),
        themeMode: 'system',
        colorScheme: 'emynent-light',
        emailNotifications: true,
        inAppNotifications: true,
        weeklyDigest: false,
        contextData: {},
        lastContextUpdate: new Date(),
      },
    })
    testUserId = user.id
    testUserEmail = user.email
  })

  afterAll(async () => {
    // Clean up test data
    try {
      await prisma.contextEvent.deleteMany({
        where: { userId: testUserId },
      })
      await prisma.behavioralPattern.deleteMany({
        where: { userId: testUserId },
      })
      await prisma.user.deleteMany({
        where: { id: testUserId },
      })
      await prisma.company.deleteMany({
        where: { id: testCompanyId },
      })
    } catch (error) {
      console.warn('Cleanup error (expected in some test scenarios):', error.message)
    }
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Clear any existing test data but keep the user
    await prisma.contextEvent.deleteMany({
      where: { userId: testUserId },
    })
    await prisma.behavioralPattern.deleteMany({
      where: { userId: testUserId },
    })

    // Clear Redis cache
    try {
      await redis.del(`user_context:${testUserId}`)
    } catch (error) {
      // Redis might not be available in test environment
    }
  })

  describe('Real Context Capture Service', () => {
    it('should capture real user context from database', async () => {
      const context = await realContextCaptureService.captureUserContext(testUserId)

      expect(context).toBeDefined()
      expect(context.userId).toBe(testUserId)
      expect(context.companyId).toBe(testCompanyId)
      expect(context.role).toBe('EMPLOYEE')
      expect(context.preferences).toBeDefined()
      expect(context.preferences.theme).toBe('system')
      expect(context.preferences.colorScheme).toBe('emynent-light')
      expect(context.behavior).toBeDefined()
      expect(context.behavior.recentEvents).toBeInstanceOf(Array)
      expect(context.behavior.patterns).toBeDefined()
      expect(context.behavior.engagement).toBeDefined()
      expect(context.sessionData).toBeDefined()
      expect(context.lastActive).toBeInstanceOf(Date)
    })

    it('should track real events in database', async () => {
      const eventResult = await realContextCaptureService.trackEvent({
        userId: testUserId,
        eventType: 'page_view',
        eventData: { page: '/dashboard', timestamp: new Date().toISOString() },
        pageUrl: '/dashboard',
        sessionId: 'test-session-123',
        durationMs: 5000,
      })

      expect(eventResult.success).toBe(true)
      expect(eventResult.eventId).toBeDefined()

      // Verify event was stored in database
      const storedEvent = await prisma.contextEvent.findFirst({
        where: {
          userId: testUserId,
          eventType: 'page_view',
        },
      })

      expect(storedEvent).toBeDefined()
      expect(storedEvent!.eventType).toBe('page_view')
      expect(storedEvent!.pageUrl).toBe('/dashboard')
      expect(storedEvent!.sessionId).toBe('test-session-123')
      expect(storedEvent!.durationMs).toBe(5000)
      expect(storedEvent!.eventData).toEqual({ page: '/dashboard', timestamp: expect.any(String) })
    })

    it('should update user context data', async () => {
      const updateResult = await realContextCaptureService.updateUserContext(testUserId, {
        preferences: {
          theme: 'light',
          newFeature: true,
        },
      })

      expect(updateResult).toBe(true)

      // Verify update in database
      const updatedUser = await prisma.user.findUnique({
        where: { id: testUserId },
      })

      expect(updatedUser!.contextData).toEqual({
        preferences: {
          theme: 'light',
          newFeature: true,
        },
      })
      expect(updatedUser!.lastContextUpdate).toBeDefined()
    })

    it('should handle non-existent user gracefully', async () => {
      await expect(
        realContextCaptureService.captureUserContext('non-existent-user')
      ).rejects.toThrow('Failed to capture user context')
    })
  })

  describe('Real Behavioral Analytics Service', () => {
    beforeEach(async () => {
      // Create test events for analysis
      const testEvents = [
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/dashboard' },
          pageUrl: '/dashboard',
          timestamp: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/settings' },
          pageUrl: '/settings',
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/dashboard' },
          pageUrl: '/dashboard',
          timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'feature_use',
          eventData: { feature: 'theme_switcher' },
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'feature_use',
          eventData: { feature: 'settings_panel' },
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        },
      ]

      await prisma.contextEvent.createMany({
        data: testEvents,
      })
    })

    it('should analyze real user behavior patterns', async () => {
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '7d',
        analysisTypes: ['navigation', 'feature_usage', 'time_based'],
        includeRecommendations: true,
      })

      expect(analysis).toBeDefined()
      expect(analysis.userId).toBe(testUserId)
      expect(analysis.patterns).toBeDefined()
      expect(analysis.confidence).toBeGreaterThanOrEqual(0)
      expect(analysis.analyzedAt).toBeInstanceOf(Date)
      expect(analysis.recommendations).toBeInstanceOf(Array)

      // Check navigation patterns
      if (analysis.patterns.navigation) {
        expect(analysis.patterns.navigation.mostVisitedPages).toBeInstanceOf(Array)
        expect(analysis.patterns.navigation.commonTransitions).toBeInstanceOf(Array)
        expect(analysis.patterns.navigation.confidence).toBeGreaterThanOrEqual(0)

        // Should detect dashboard as most visited
        const dashboardVisits = analysis.patterns.navigation.mostVisitedPages.find(
          page => page.page === '/dashboard'
        )
        expect(dashboardVisits).toBeDefined()
        expect(dashboardVisits!.count).toBe(2)
      }

      // Check feature usage patterns
      if (analysis.patterns.featureUsage) {
        expect(analysis.patterns.featureUsage.mostUsedFeatures).toBeInstanceOf(Array)
        expect(analysis.patterns.featureUsage.usageFrequency).toBeDefined()
        expect(analysis.patterns.featureUsage.confidence).toBeGreaterThanOrEqual(0)
      }

      // Check time patterns
      if (analysis.patterns.timePatterns) {
        expect(analysis.patterns.timePatterns.peakHours).toBeInstanceOf(Array)
        expect(analysis.patterns.timePatterns.activeDays).toBeInstanceOf(Array)
        expect(analysis.patterns.timePatterns.confidence).toBeGreaterThanOrEqual(0)
      }
    })

    it('should calculate real engagement metrics', async () => {
      const metrics = await realBehavioralAnalyticsService.getEngagementMetrics(testUserId)

      expect(metrics).toBeDefined()
      expect(metrics.level).toMatch(/^(low|medium|high)$/)
      expect(metrics.score).toBeGreaterThanOrEqual(0)
      expect(metrics.score).toBeLessThanOrEqual(100)
      expect(metrics.factors).toBeInstanceOf(Array)

      // With 5 events over multiple days, should have some engagement
      expect(metrics.score).toBeGreaterThan(0)
    })

    it('should detect patterns from event data', async () => {
      const events = [
        {
          type: 'page_view',
          data: {},
          timestamp: new Date(),
          pageUrl: '/dashboard',
        },
        {
          type: 'page_view',
          data: {},
          timestamp: new Date(),
          pageUrl: '/settings',
        },
        {
          type: 'feature_use',
          data: { feature: 'theme_switcher' },
          timestamp: new Date(),
        },
      ]

      const result = await realBehavioralAnalyticsService.detectPatterns({
        userId: testUserId,
        events,
        patternTypes: ['navigation', 'feature_usage'],
        confidenceThreshold: 0.1,
      })

      expect(result).toBeDefined()
      expect(result.patterns).toBeDefined()
      expect(result.confidence).toBeGreaterThanOrEqual(0)
      expect(result.detectedAt).toBeInstanceOf(Date)
    })

    it('should store behavioral patterns in database', async () => {
      // First create some events to analyze
      const testEvents = [
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/dashboard' },
          pageUrl: '/dashboard',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/settings' },
          pageUrl: '/settings',
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        },
      ]

      await prisma.contextEvent.createMany({
        data: testEvents,
      })

      // Now analyze the behavior
      await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '7d',
        analysisTypes: ['navigation'],
        includeRecommendations: false,
      })

      // Check if patterns were stored
      const storedPatterns = await prisma.behavioralPattern.findMany({
        where: { userId: testUserId },
      })

      expect(storedPatterns.length).toBeGreaterThan(0)

      const navigationPattern = storedPatterns.find(p => p.patternType === 'navigation')
      expect(navigationPattern).toBeDefined()
      expect(navigationPattern!.patternData).toBeDefined()
      expect(navigationPattern!.confidenceScore).toBeGreaterThanOrEqual(0)
    })

    it('should handle empty event data gracefully', async () => {
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: 'user-with-no-events',
        timeframe: '7d',
        analysisTypes: ['navigation'],
        includeRecommendations: false,
      })

      expect(analysis.patterns).toEqual({})
      expect(analysis.confidence).toBe(0)
      expect(analysis.recommendations).toEqual([])
    })
  })

  describe('Database Schema Integrity', () => {
    it('should have created Intelligence Layer tables', async () => {
      // Test ContextEvent table
      const contextEventCount = await prisma.contextEvent.count()
      expect(contextEventCount).toBeGreaterThanOrEqual(0)

      // Test BehavioralPattern table
      const behavioralPatternCount = await prisma.behavioralPattern.count()
      expect(behavioralPatternCount).toBeGreaterThanOrEqual(0)

      // Test AIInsight table (if implemented)
      try {
        const aiInsightCount = await prisma.aIInsight.count()
        expect(aiInsightCount).toBeGreaterThanOrEqual(0)
      } catch (error) {
        // AIInsight table might not be implemented yet
        console.warn('AIInsight table not found - this is expected if not yet implemented')
      }
    })

    it('should have updated User model with context fields', async () => {
      const user = await prisma.user.findUnique({
        where: { id: testUserId },
      })

      expect(user).toBeDefined()
      expect(user!.contextData).toBeDefined()
      expect(user!.lastContextUpdate).toBeDefined()
    })

    it('should enforce database constraints', async () => {
      // Test unique constraint on behavioral patterns
      await prisma.behavioralPattern.create({
        data: {
          userId: testUserId,
          patternType: 'test_pattern',
          patternData: { test: true },
          confidenceScore: 0.5,
        },
      })

      // Attempting to create duplicate should fail
      await expect(
        prisma.behavioralPattern.create({
          data: {
            userId: testUserId,
            patternType: 'test_pattern',
            patternData: { test: false },
            confidenceScore: 0.7,
          },
        })
      ).rejects.toThrow()
    })
  })

  describe('Integration Tests', () => {
    it('should integrate context capture with behavioral analytics', async () => {
      // Track several events
      const events = [
        { eventType: 'page_view', eventData: {}, pageUrl: '/dashboard' },
        { eventType: 'page_view', eventData: {}, pageUrl: '/settings' },
        { eventType: 'feature_use', eventData: { feature: 'theme_switcher' } },
      ]

      for (const event of events) {
        await realContextCaptureService.trackEvent({
          userId: testUserId,
          ...event,
        })
      }

      // Capture context (should include recent events)
      const context = await realContextCaptureService.captureUserContext(testUserId)
      expect(context.behavior.recentEvents.length).toBeGreaterThanOrEqual(3)

      // Analyze behavior (should detect patterns from tracked events)
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '1d',
        analysisTypes: ['navigation', 'feature_usage'],
        includeRecommendations: false,
      })

      expect(analysis.patterns).toBeDefined()
      expect(Object.keys(analysis.patterns).length).toBeGreaterThan(0)
    })

    it('should maintain data consistency across services', async () => {
      // Track an event
      const trackResult = await realContextCaptureService.trackEvent({
        userId: testUserId,
        eventType: 'page_view',
        eventData: { page: '/test-consistency' },
        pageUrl: '/test-consistency',
      })

      expect(trackResult.success).toBe(true)
      expect(trackResult.eventId).toBeDefined()

      // Verify event was actually stored in database
      const storedEvent = await prisma.contextEvent.findUnique({
        where: { id: trackResult.eventId },
      })
      expect(storedEvent).toBeDefined()
      expect(storedEvent!.pageUrl).toBe('/test-consistency')

      // Verify event appears in context
      const context = await realContextCaptureService.captureUserContext(testUserId)
      const testEvent = context.behavior.recentEvents.find(
        event => event.pageUrl === '/test-consistency'
      )
      expect(testEvent).toBeDefined()

      // Verify event is included in analytics
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '1d',
        analysisTypes: ['navigation'],
        includeRecommendations: false,
      })

      if (analysis.patterns.navigation) {
        const testPage = analysis.patterns.navigation.mostVisitedPages.find(
          page => page.page === '/test-consistency'
        )
        expect(testPage).toBeDefined()
      }
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent context captures', async () => {
      const promises = Array.from({ length: 5 }, () =>
        realContextCaptureService.captureUserContext(testUserId)
      )

      const results = await Promise.all(promises)

      results.forEach(context => {
        expect(context).toBeDefined()
        expect(context.userId).toBe(testUserId)
      })
    })

    it('should handle large event datasets efficiently', async () => {
      // Create many events
      const manyEvents = Array.from({ length: 50 }, (_, i) => ({
        userId: testUserId,
        companyId: testCompanyId,
        eventType: 'page_view',
        eventData: { page: `/page-${i}` },
        pageUrl: `/page-${i}`,
        timestamp: new Date(Date.now() - i * 60 * 1000), // 1 minute apart
      }))

      await prisma.contextEvent.createMany({
        data: manyEvents,
      })

      const startTime = Date.now()
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '1d',
        analysisTypes: ['navigation'],
        includeRecommendations: false,
      })
      const endTime = Date.now()

      expect(analysis).toBeDefined()
      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
    })
  })
})

describe('🔄 Phase 3 Integration with Existing Systems', () => {
  it('should not break existing authentication', async () => {
    // This test ensures Intelligence Layer doesn't interfere with auth
    const user = await prisma.user.findFirst({
      where: { email: { contains: '@' } },
    })

    if (user) {
      expect(user.id).toBeDefined()
      expect(user.email).toBeDefined()
      expect(user.role).toBeDefined()
    }
  })

  it('should not break existing theme system', async () => {
    // This test ensures Intelligence Layer doesn't interfere with themes
    const user = await prisma.user.findFirst({
      where: {
        themeMode: {
          not: undefined,
        },
      },
    })

    if (user) {
      expect(user.themeMode).toBeDefined()
      expect(user.colorScheme).toBeDefined()
    }
  })

  it('should maintain database performance', async () => {
    // Simple performance check
    const startTime = Date.now()
    await prisma.user.count()
    await prisma.company.count()
    const endTime = Date.now()

    expect(endTime - startTime).toBeLessThan(1000) // Should be fast
  })
})
