/**
 * Intelligence Layer Performance Cache Tests
 * Tests caching functionality, performance monitoring, and optimization features
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  IntelligenceCache,
  intelligenceCache,
  PerformanceMonitor,
  CacheWarmer,
  cacheWarmer,
} from '@/lib/intelligence/performance-cache'
import { redis } from '@/lib/redis'
import { hasFeature } from '@/lib/feature-flags'

// Mock dependencies
vi.mock('@/lib/redis', () => ({
  redis: {
    get: vi.fn(),
    setex: vi.fn(),
    del: vi.fn(),
    keys: vi.fn(),
    pipeline: vi.fn(() => ({
      get: vi.fn(),
      exec: vi.fn(),
    })),
  },
}))

vi.mock('@/lib/feature-flags', () => ({
  hasFeature: vi.fn(),
}))

// Global test constants
const mockCompanyId = 'company-123'
const mockUserId = 'user-456'
const testData = { test: 'data', timestamp: new Date() }

describe('IntelligenceCache', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    intelligenceCache.resetMetrics()
    vi.mocked(hasFeature).mockResolvedValue(true)
  })

  afterEach(() => {
    intelligenceCache.resetMetrics()
  })

  describe('Basic Cache Operations', () => {
    it('should set and get cached data successfully', async () => {
      const mockCachedData = JSON.stringify(testData)
      vi.mocked(redis.get).mockResolvedValue(mockCachedData)
      vi.mocked(redis.setex).mockResolvedValue('OK')

      // Set data
      const setResult = await intelligenceCache.set('context', 'test-key', testData, mockCompanyId)
      expect(setResult).toBe(true)
      expect(redis.setex).toHaveBeenCalledWith(
        'intel:context:company-123:test-key',
        3600,
        mockCachedData
      )

      // Get data
      const getData = await intelligenceCache.get('context', 'test-key', mockCompanyId)
      // Note: Dates get serialized as strings in JSON, so we need to account for that
      const expectedData = { ...testData, timestamp: testData.timestamp.toISOString() }
      expect(getData).toEqual(expectedData)
      expect(redis.get).toHaveBeenCalledWith('intel:context:company-123:test-key')
    })

    it('should return null when cache miss occurs', async () => {
      vi.mocked(redis.get).mockResolvedValue(null)

      const result = await intelligenceCache.get('context', 'nonexistent-key', mockCompanyId)
      expect(result).toBeNull()
    })

    it('should handle feature flag disabled', async () => {
      vi.mocked(hasFeature).mockResolvedValue(false)

      const setResult = await intelligenceCache.set('context', 'test-key', testData, mockCompanyId)
      expect(setResult).toBe(false)

      const getResult = await intelligenceCache.get('context', 'test-key', mockCompanyId)
      expect(getResult).toBeNull()
    })

    it('should delete cached data', async () => {
      vi.mocked(redis.del).mockResolvedValue(1)

      const result = await intelligenceCache.delete('context', 'test-key', mockCompanyId)
      expect(result).toBe(true)
      expect(redis.del).toHaveBeenCalledWith('intel:context:company-123:test-key')
    })

    it('should handle custom TTL', async () => {
      vi.mocked(redis.setex).mockResolvedValue('OK')
      const customTtl = 7200 // 2 hours

      await intelligenceCache.set('context', 'test-key', testData, mockCompanyId, customTtl)
      expect(redis.setex).toHaveBeenCalledWith(
        'intel:context:company-123:test-key',
        customTtl,
        JSON.stringify(testData)
      )
    })
  })

  describe('Advanced Cache Operations', () => {
    it('should implement getOrSet pattern correctly', async () => {
      vi.mocked(redis.get).mockResolvedValue(null) // Cache miss
      vi.mocked(redis.setex).mockResolvedValue('OK')

      const fetcher = vi.fn().mockResolvedValue(testData)

      const result = await intelligenceCache.getOrSet(
        'recommendations',
        'test-key',
        mockCompanyId,
        fetcher
      )

      expect(fetcher).toHaveBeenCalledOnce()
      expect(result).toEqual(testData)
      expect(redis.setex).toHaveBeenCalled()
    })

    it('should use cached data in getOrSet when available', async () => {
      const cachedData = JSON.stringify(testData)
      vi.mocked(redis.get).mockResolvedValue(cachedData)

      const fetcher = vi.fn().mockResolvedValue({ different: 'data' })

      const result = await intelligenceCache.getOrSet(
        'recommendations',
        'test-key',
        mockCompanyId,
        fetcher
      )

      expect(fetcher).not.toHaveBeenCalled()
      // Note: Dates get serialized as strings in JSON, so we need to account for that
      const expectedData = { ...testData, timestamp: testData.timestamp.toISOString() }
      expect(result).toEqual(expectedData)
    })

    it('should handle batch get operations', async () => {
      const keys = ['key1', 'key2', 'key3']
      const mockPipeline = {
        get: vi.fn(),
        exec: vi.fn().mockResolvedValue([
          [null, JSON.stringify({ data: 'value1' })],
          [null, null],
          [null, JSON.stringify({ data: 'value3' })],
        ]),
      }
      vi.mocked(redis.pipeline).mockReturnValue(mockPipeline as any)

      const results = await intelligenceCache.batchGet('analytics', keys, mockCompanyId)

      expect(results.size).toBe(3)
      expect(results.get('key1')).toEqual({ data: 'value1' })
      expect(results.get('key2')).toBeNull()
      expect(results.get('key3')).toEqual({ data: 'value3' })
    })

    it('should invalidate cache by type', async () => {
      const mockKeys = ['intel:context:company-123:key1', 'intel:context:company-123:key2']
      vi.mocked(redis.keys).mockResolvedValue(mockKeys)
      vi.mocked(redis.del).mockResolvedValue(2)

      const result = await intelligenceCache.invalidateByType('context', mockCompanyId)
      expect(result).toBe(2)
      expect(redis.keys).toHaveBeenCalledWith('intel:context:company-123:*')
      expect(redis.del).toHaveBeenCalledWith(...mockKeys)
    })
  })

  describe('Performance Metrics', () => {
    it('should track cache hits and misses', async () => {
      // Cache hit
      vi.mocked(redis.get).mockResolvedValue(JSON.stringify(testData))
      await intelligenceCache.get('context', 'test-key', mockCompanyId)

      // Cache miss
      vi.mocked(redis.get).mockResolvedValue(null)
      await intelligenceCache.get('context', 'test-key-2', mockCompanyId)

      const metrics = intelligenceCache.getMetrics('context')
      expect(metrics).toMatchObject({
        cacheHits: 1,
        cacheMisses: 1,
        totalRequests: 2,
      })

      const hitRatio = intelligenceCache.getCacheHitRatio('context')
      expect(hitRatio).toBe(0.5)
    })

    it('should track response times', async () => {
      vi.mocked(redis.get).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(JSON.stringify(testData)), 50))
      )

      await intelligenceCache.get('context', 'test-key', mockCompanyId)

      const metrics = intelligenceCache.getMetrics('context')
      expect(metrics.avgResponseTime).toBeGreaterThan(0)
    })

    it('should track error rates', async () => {
      vi.mocked(redis.get).mockRejectedValue(new Error('Redis connection failed'))

      await intelligenceCache.get('context', 'test-key', mockCompanyId)

      const metrics = intelligenceCache.getMetrics('context')
      expect(metrics.errorRate).toBeGreaterThan(0)
    })

    it('should reset metrics correctly', async () => {
      // Generate some metrics
      vi.mocked(redis.get).mockResolvedValue(JSON.stringify(testData))
      await intelligenceCache.get('context', 'test-key', mockCompanyId)

      // Reset specific type
      intelligenceCache.resetMetrics('context')
      const contextMetrics = intelligenceCache.getMetrics('context')
      expect(contextMetrics.totalRequests).toBe(0)

      // Generate metrics for multiple types
      await intelligenceCache.get('recommendations', 'test-key', mockCompanyId)

      // Reset all metrics
      intelligenceCache.resetMetrics()
      const allMetrics = intelligenceCache.getMetrics() as Map<string, any>
      expect(allMetrics.size).toBe(0)
    })
  })

  describe('Data Compression', () => {
    it('should apply base64 encoding for large data objects', async () => {
      const largeData = { data: 'x'.repeat(2000) } // Large object
      vi.mocked(redis.setex).mockResolvedValue('OK')

      await intelligenceCache.set('insights', 'large-key', largeData, mockCompanyId)

      // Should be called with base64 encoded data (note: base64 increases size)
      const setCall = vi.mocked(redis.setex).mock.calls[0]
      const serializedData = setCall[2]
      const originalJson = JSON.stringify(largeData)

      // Base64 encoding should be applied (data will be different from original JSON)
      expect(serializedData).not.toEqual(originalJson)

      // Base64 encoding actually increases size by ~33%, so we check it's base64 encoded
      expect(serializedData).toMatch(/^[A-Za-z0-9+/]+=*$/) // Base64 pattern

      // Verify it can be decoded back to original
      const decoded = Buffer.from(serializedData, 'base64').toString('utf-8')
      expect(decoded).toEqual(originalJson)
    })

    it('should not compress small data objects', async () => {
      const smallData = { data: 'small' }
      vi.mocked(redis.setex).mockResolvedValue('OK')

      await intelligenceCache.set('analytics', 'small-key', smallData, mockCompanyId)

      const setCall = vi.mocked(redis.setex).mock.calls[0]
      const serializedData = setCall[2]
      expect(serializedData).toEqual(JSON.stringify(smallData))
    })
  })

  describe('Error Handling', () => {
    it('should handle Redis connection errors gracefully', async () => {
      vi.mocked(redis.get).mockRejectedValue(new Error('Connection failed'))
      vi.mocked(redis.setex).mockRejectedValue(new Error('Connection failed'))

      const getResult = await intelligenceCache.get('context', 'test-key', mockCompanyId)
      expect(getResult).toBeNull()

      const setResult = await intelligenceCache.set('context', 'test-key', testData, mockCompanyId)
      expect(setResult).toBe(false)
    })

    it('should handle serialization errors', async () => {
      const circularData = { self: null as any }
      circularData.self = circularData // Create circular reference

      const setResult = await intelligenceCache.set(
        'context',
        'circular-key',
        circularData,
        mockCompanyId
      )
      expect(setResult).toBe(false)
    })
  })
})

describe('PerformanceMonitor', () => {
  beforeEach(() => {
    vi.clearAllTimers()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should measure operation duration', async () => {
    const operation = 'test-operation'
    const mockAsyncFn = vi.fn().mockImplementation(() => Promise.resolve('test-result'))

    const { result, duration } = await PerformanceMonitor.measureAsync(operation, mockAsyncFn)

    expect(mockAsyncFn).toHaveBeenCalledOnce()
    expect(result).toBe('test-result')
    expect(duration).toBeGreaterThanOrEqual(0)
  })

  it('should measure synchronous operations', () => {
    const mockSyncFn = vi.fn().mockReturnValue('test-result')

    const { result, duration } = PerformanceMonitor.measure('sync-operation', mockSyncFn)

    expect(result).toBe('test-result')
    expect(duration).toBeGreaterThanOrEqual(0)
  })

  it('should handle timer operations', () => {
    const operation = 'timer-test'

    PerformanceMonitor.startTimer(operation)
    vi.advanceTimersByTime(100)
    const duration = PerformanceMonitor.endTimer(operation)

    expect(duration).toBe(100)
  })

  it('should return 0 for non-existent timer', () => {
    const duration = PerformanceMonitor.endTimer('non-existent')
    expect(duration).toBe(0)
  })
})

describe('CacheWarmer', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(hasFeature).mockResolvedValue(true)
    vi.mocked(redis.setex).mockResolvedValue('OK')
  })

  it('should warm context cache for multiple users', async () => {
    const userIds = ['user1', 'user2', 'user3']

    await cacheWarmer.warmContextCache(mockCompanyId, userIds)

    expect(redis.setex).toHaveBeenCalledTimes(3)
    userIds.forEach(userId => {
      expect(redis.setex).toHaveBeenCalledWith(
        `intel:context:${mockCompanyId}:user:${userId}`,
        3600,
        JSON.stringify({ warmed: true })
      )
    })
  })

  it('should warm recommendations cache for multiple users', async () => {
    const userIds = ['user1', 'user2']

    await cacheWarmer.warmRecommendationsCache(mockCompanyId, userIds)

    expect(redis.setex).toHaveBeenCalledTimes(2)
    userIds.forEach(userId => {
      expect(redis.setex).toHaveBeenCalledWith(
        `intel:rec:${mockCompanyId}:user:${userId}`,
        1800,
        JSON.stringify({ warmed: true })
      )
    })
  })

  it('should handle cache warming errors gracefully', async () => {
    vi.mocked(redis.setex).mockRejectedValue(new Error('Cache warming failed'))
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    await cacheWarmer.warmContextCache(mockCompanyId, ['user1'])

    expect(consoleSpy).toHaveBeenCalledWith(
      'Cache set error for intel:context:company-123:user:user1:',
      expect.any(Error)
    )

    consoleSpy.mockRestore()
  })
})

describe('Integration Tests', () => {
  it('should work end-to-end with real cache operations', async () => {
    vi.mocked(hasFeature).mockResolvedValue(true)
    vi.mocked(redis.get).mockResolvedValue(null) // Cache miss
    vi.mocked(redis.setex).mockResolvedValue('OK')

    const fetcher = vi.fn().mockResolvedValue({ userId: mockUserId, data: 'fresh' })

    // First call should fetch and cache
    const result1 = await intelligenceCache.getOrSet(
      'context',
      `user:${mockUserId}`,
      mockCompanyId,
      fetcher
    )

    expect(fetcher).toHaveBeenCalledOnce()
    expect(result1).toEqual({ userId: mockUserId, data: 'fresh' })

    // Simulate cache hit for second call
    vi.mocked(redis.get).mockResolvedValue(JSON.stringify({ userId: mockUserId, data: 'cached' }))

    const result2 = await intelligenceCache.getOrSet(
      'context',
      `user:${mockUserId}`,
      mockCompanyId,
      fetcher
    )

    expect(fetcher).toHaveBeenCalledOnce() // Should not be called again
    expect(result2).toEqual({ userId: mockUserId, data: 'cached' })

    // Check metrics
    const metrics = intelligenceCache.getMetrics('context')
    expect(metrics.totalRequests).toBe(2)
    expect(metrics.cacheHits).toBe(1)
    expect(metrics.cacheMisses).toBe(1)
  })
})
