/**
 * AI Recommendation Service Tests
 * Comprehensive test coverage for the AI Recommendation System
 * Part of Phase 3: Intelligence Layer Testing
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { AIRecommendationService } from '@/services/ai-recommendations'
import { hasFeature } from '@/lib/feature-flags'
import { getUserContext } from '@/services/context-service'
import { UserContext, RecommendationType, AIRecommendation } from '@/types/intelligence'

// Mock only external dependencies, not internal services
vi.mock('@/lib/feature-flags')
vi.mock('@/services/context-service')

// DO NOT MOCK PRISMA - Use real database for integration testing
// The test setup provides a real test database

const mockHasFeature = vi.mocked(hasFeature)
const mockGetUserContext = vi.mocked(getUserContext)

describe('AI Recommendation Service', () => {
  let service: AIRecommendationService
  let mockUserContext: UserContext

  beforeEach(() => {
    service = AIRecommendationService.getInstance()

    // Clear cache between tests
    service.clearCache()

    // Reset mocks
    vi.clearAllMocks()

    // Setup default mock user context
    mockUserContext = {
      userId: 'test-user-123',
      companyId: 'test-company-456',
      role: 'EMPLOYEE',
      preferences: {
        theme: 'dark',
        onboardingCompleted: true,
      },
      recentActions: [
        {
          action: 'page_visit',
          data: { page: '/dashboard' },
          timestamp: new Date().toISOString(),
        },
      ],
      historicalData: {
        loginCount: 25,
        lastLogin: new Date().toISOString(),
        averageSessionDuration: 1800,
      },
    }

    // Default mock implementations
    mockHasFeature.mockResolvedValue(true)
    mockGetUserContext.mockResolvedValue(mockUserContext)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = AIRecommendationService.getInstance()
      const instance2 = AIRecommendationService.getInstance()
      expect(instance1).toBe(instance2)
    })
  })

  describe('generateRecommendations', () => {
    it('should generate recommendations when feature is enabled', async () => {
      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
        type: 'role_based' as RecommendationType,
        limit: 5,
      }

      const result = await service.generateRecommendations(request)

      expect(result).toBeDefined()
      expect(result.recommendations).toBeInstanceOf(Array)
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.reasoning).toBeTruthy()
      expect(result.metadata).toBeDefined()
      expect(result.metadata.fallbackUsed).toBe(false)
    })

    it('should return fallback recommendations when feature is disabled', async () => {
      mockHasFeature.mockResolvedValue(false)

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
      }

      const result = await service.generateRecommendations(request)

      expect(result).toBeDefined()
      expect(result.recommendations).toBeInstanceOf(Array)
      expect(result.metadata.fallbackUsed).toBe(true)
      expect(result.reasoning).toContain('AI service unavailability')
    })

    it('should handle errors gracefully and return fallback', async () => {
      mockGetUserContext.mockRejectedValue(new Error('Context service error'))

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
      }

      const result = await service.generateRecommendations(request)

      expect(result).toBeDefined()
      expect(result.metadata.fallbackUsed).toBe(true)
    })

    it('should respect the limit parameter', async () => {
      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
        limit: 2,
      }

      const result = await service.generateRecommendations(request)

      expect(result.recommendations.length).toBeLessThanOrEqual(2)
    })

    it('should use cache for repeated requests', async () => {
      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
      }

      // First request
      const result1 = await service.generateRecommendations(request)

      // Second request (should use cache)
      const result2 = await service.generateRecommendations(request)

      expect(result1).toEqual(result2)
      expect(mockHasFeature).toHaveBeenCalledTimes(2) // Called twice in first request (aiRecommendations + contextAwareness), cached for second
    })
  })

  describe('Role-based Recommendations', () => {
    it('should generate manager-specific recommendations', async () => {
      const managerContext = {
        ...mockUserContext,
        role: 'MANAGER' as const,
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: managerContext,
        type: 'role_based' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      const managerRecs = result.recommendations.filter(
        rec => rec.metadata?.category === 'management'
      )
      expect(managerRecs.length).toBeGreaterThan(0)
    })

    it('should generate superadmin-specific recommendations', async () => {
      const superadminContext = {
        ...mockUserContext,
        role: 'SUPERADMIN' as const,
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: superadminContext,
        type: 'role_based' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      const adminRecs = result.recommendations.filter(
        rec => rec.metadata?.category === 'administration'
      )
      expect(adminRecs.length).toBeGreaterThan(0)
    })

    it('should not generate role-specific recommendations for employees', async () => {
      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext, // EMPLOYEE role
        type: 'role_based' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      // Employee should get general recommendations, not management-specific ones
      const managementRecs = result.recommendations.filter(
        rec => rec.metadata?.category === 'management'
      )
      expect(managementRecs.length).toBe(0)
    })
  })

  describe('Skill Development Recommendations', () => {
    it('should generate skill recommendations based on recent activities', async () => {
      const contextWithActivities = {
        ...mockUserContext,
        recentActions: [
          {
            action: 'page_visit',
            data: { page: '/dashboard/team' },
            timestamp: new Date().toISOString(),
          },
          {
            action: 'feature_use',
            data: { feature: 'project_management' },
            timestamp: new Date().toISOString(),
          },
        ],
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: contextWithActivities,
        type: 'skill_development' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      const skillRecs = result.recommendations.filter(rec => rec.type === 'skill_development')
      expect(skillRecs.length).toBeGreaterThan(0)
    })

    it('should handle empty recent actions gracefully', async () => {
      const contextWithoutActivities = {
        ...mockUserContext,
        recentActions: [],
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: contextWithoutActivities,
        type: 'skill_development' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      expect(result.recommendations).toBeInstanceOf(Array)
      // Should still return some recommendations, even without recent activities
    })
  })

  describe('UI Personalization Recommendations', () => {
    it('should recommend theme optimization for system theme users', async () => {
      const systemThemeContext = {
        ...mockUserContext,
        preferences: {
          ...mockUserContext.preferences,
          theme: 'system',
        },
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: systemThemeContext,
        type: 'ui_personalization' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      const themeRecs = result.recommendations.filter(rec => rec.metadata?.tags?.includes('theme'))
      expect(themeRecs.length).toBeGreaterThan(0)
    })

    it('should not recommend theme changes for users with specific themes', async () => {
      const specificThemeContext = {
        ...mockUserContext,
        preferences: {
          ...mockUserContext.preferences,
          theme: 'dark',
        },
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: specificThemeContext,
        type: 'ui_personalization' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      // Should have fewer or no theme-related recommendations
      const themeRecs = result.recommendations.filter(rec =>
        rec.title.toLowerCase().includes('theme')
      )
      expect(themeRecs.length).toBe(0)
    })
  })

  describe('Content Recommendations', () => {
    it('should recommend onboarding completion for incomplete users', async () => {
      const incompleteOnboardingContext = {
        ...mockUserContext,
        preferences: {
          ...mockUserContext.preferences,
          onboardingCompleted: false,
        },
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: incompleteOnboardingContext,
        type: 'content' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      const onboardingRecs = result.recommendations.filter(
        rec => rec.metadata?.category === 'onboarding'
      )
      expect(onboardingRecs.length).toBeGreaterThan(0)
      expect(onboardingRecs[0].priority).toBeGreaterThan(0.8) // High priority
    })

    it('should not recommend onboarding for completed users', async () => {
      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext, // onboardingCompleted: true
        type: 'content' as RecommendationType,
      }

      const result = await service.generateRecommendations(request)

      const onboardingRecs = result.recommendations.filter(
        rec => rec.metadata?.category === 'onboarding'
      )
      expect(onboardingRecs.length).toBe(0)
    })
  })

  describe('Confidence Calculation', () => {
    it('should calculate higher confidence with complete context', async () => {
      const completeContext = {
        ...mockUserContext,
        recentActions: Array(15)
          .fill(null)
          .map((_, i) => ({
            action: 'page_visit',
            data: { page: `/page-${i}` },
            timestamp: new Date().toISOString(),
          })),
        preferences: {
          theme: 'dark',
          onboardingCompleted: true,
          language: 'en',
          notifications: true,
        },
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: completeContext,
      }

      const result = await service.generateRecommendations(request)

      expect(result.confidence).toBeGreaterThan(0.7)
    })

    it('should calculate lower confidence with minimal context', async () => {
      const minimalContext = {
        ...mockUserContext,
        recentActions: [],
        preferences: {},
      }

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: minimalContext,
      }

      const result = await service.generateRecommendations(request)

      expect(result.confidence).toBeLessThan(0.8)
    })
  })

  describe('Recommendation Prioritization', () => {
    it('should sort recommendations by priority and confidence', async () => {
      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
        limit: 10,
      }

      const result = await service.generateRecommendations(request)

      // Check that recommendations are sorted by priority * confidence
      for (let i = 0; i < result.recommendations.length - 1; i++) {
        const current = result.recommendations[i]
        const next = result.recommendations[i + 1]

        const currentScore = current.priority * current.confidence
        const nextScore = next.priority * next.confidence

        expect(currentScore).toBeGreaterThanOrEqual(nextScore)
      }
    })
  })

  describe('Error Handling', () => {
    it('should handle feature flag check failures', async () => {
      mockHasFeature.mockRejectedValue(new Error('Feature flag service error'))

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
      }

      const result = await service.generateRecommendations(request)

      expect(result).toBeDefined()
      expect(result.metadata.fallbackUsed).toBe(true)
    })

    it('should handle context service failures', async () => {
      mockGetUserContext.mockResolvedValue(null)

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
      }

      const result = await service.generateRecommendations(request)

      expect(result).toBeDefined()
      expect(result.metadata.fallbackUsed).toBe(true)
    })
  })

  describe('Performance', () => {
    it('should complete recommendation generation within reasonable time', async () => {
      const startTime = Date.now()

      const request = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        context: mockUserContext,
      }

      const result = await service.generateRecommendations(request)

      const endTime = Date.now()
      const processingTime = endTime - startTime

      expect(processingTime).toBeLessThan(1000) // Should complete within 1 second
      expect(result.metadata.processingTime).toBeLessThan(1000)
    })

    it('should handle concurrent requests efficiently', async () => {
      const requests = Array(5)
        .fill(null)
        .map((_, i) => ({
          userId: `test-user-${i}`,
          companyId: 'test-company-456',
          context: { ...mockUserContext, userId: `test-user-${i}` },
        }))

      const startTime = Date.now()
      const results = await Promise.all(requests.map(req => service.generateRecommendations(req)))
      const endTime = Date.now()

      expect(results).toHaveLength(5)
      expect(endTime - startTime).toBeLessThan(2000) // Should handle 5 concurrent requests within 2 seconds
      results.forEach(result => {
        expect(result).toBeDefined()
        expect(result.recommendations).toBeInstanceOf(Array)
      })
    })
  })

  describe('Integration with Legacy Functions', () => {
    it('should work with generateSkillRecommendations function', async () => {
      // Import the legacy function
      const { generateSkillRecommendations } = await import('@/services/ai-recommendations')

      const result = await generateSkillRecommendations('test-user-123', 'test-company-456')

      expect(result).toBeInstanceOf(Array)
      // Should return empty array when feature is disabled or mock recommendations when enabled
    })

    it('should work with storeRecommendation function', async () => {
      const { storeRecommendation } = await import('@/services/ai-recommendations')

      const recommendation = {
        userId: 'test-user-123',
        type: 'skill' as const,
        content: 'Test recommendation',
        priority: 'medium' as const,
      }

      const result = await storeRecommendation(recommendation)

      expect(result).toBeTruthy() // Should return a recommendation ID
    })

    it('should work with completeRecommendation function', async () => {
      const { completeRecommendation } = await import('@/services/ai-recommendations')

      const result = await completeRecommendation('test-rec-123', 'test-user-123')

      expect(result).toBe(true) // Should return success
    })
  })
})
