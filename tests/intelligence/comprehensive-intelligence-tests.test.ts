/**
 * Comprehensive Intelligence Layer Tests
 * Following TDD principles with detailed functionality verification
 *
 * These tests ensure all Intelligence Layer components work correctly
 * with real data, proper error handling, and edge cases.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { realContextCaptureService } from '@/services/intelligence/real-context-capture'
import { realBehavioralAnalyticsService } from '@/services/intelligence/real-behavioral-analytics'
import { redis } from '@/lib/redis'

const prisma = new PrismaClient()

describe('🧠 Intelligence Layer - Comprehensive Functionality Tests', () => {
  let testUserId: string
  let testCompanyId: string
  let secondTestUserId: string
  let testUserEmail: string

  // Helper function to clean up test data
  async function cleanupTestData() {
    // Clean up in correct order to avoid foreign key constraints
    const companyId = testCompanyId || 'test-intelligence-company'

    await prisma.contextEvent.deleteMany({
      where: { companyId: companyId },
    })
    await prisma.featureFlag.deleteMany({
      where: { companyId: companyId },
    })

    // Clean up by user IDs if they exist
    const userIds = [testUserId, secondTestUserId].filter(Boolean)
    if (userIds.length > 0) {
      await prisma.behavioralPattern.deleteMany({
        where: { userId: { in: userIds } },
      })
      await prisma.aIInsight.deleteMany({
        where: { userId: { in: userIds } },
      })
      await prisma.userContext.deleteMany({
        where: { userId: { in: userIds } },
      })
    }

    await prisma.user.deleteMany({
      where: { companyId: companyId },
    })
    await prisma.company.deleteMany({
      where: { id: companyId },
    })
  }

  beforeAll(async () => {
    // Clean up any existing test data first
    await cleanupTestData()

    // Create test company first to satisfy foreign key constraint
    const testCompany = await prisma.company.upsert({
      where: { id: 'test-intelligence-company' },
      update: {},
      create: {
        id: 'test-intelligence-company',
        name: 'Test Intelligence Company',
        domains: ['test-intelligence.com'],
        allowedEmailDomains: ['test-intelligence.com'],
        isActive: true,
        subscriptionStatus: 'ACTIVE',
      },
    })
    testCompanyId = testCompany.id

    // Create test user
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        companyId: testCompanyId,
        role: 'EMPLOYEE',
      },
      create: {
        email: '<EMAIL>',
        name: 'Intelligence User',
        role: 'EMPLOYEE',
        companyId: testCompanyId, // Use the created company ID
        onboardingCompleted: true,
        emailVerified: new Date(),
        themeMode: 'dark',
        colorScheme: 'emynent-dark',
        emailNotifications: true,
        inAppNotifications: true,
        weeklyDigest: false,
        contextData: {},
        lastContextUpdate: new Date(),
      },
    })
    testUserId = user.id
    testUserEmail = user.email

    // Create second test user for multi-user scenarios
    const secondUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        companyId: testCompanyId,
        role: 'MANAGER',
      },
      create: {
        email: '<EMAIL>',
        name: 'Second Intelligence User',
        role: 'MANAGER',
        companyId: testCompanyId,
        themeMode: 'light',
        colorScheme: 'emynent-light',
        onboardingCompleted: true,
        emailVerified: new Date(),
        emailNotifications: true,
        inAppNotifications: true,
        weeklyDigest: false,
        contextData: {},
        lastContextUpdate: new Date(),
      },
    })
    secondTestUserId = secondUser.id
  })

  afterAll(async () => {
    await cleanupTestData()
    await prisma.$disconnect()
  })
  beforeEach(async () => {
    // Ensure user has correct data before each test
    await prisma.user.updateMany({
      where: { id: { in: [testUserId, secondTestUserId] } },
      data: {
        companyId: testCompanyId,
        themeMode: 'dark',
        colorScheme: 'emynent-dark',
        emailNotifications: true,
        inAppNotifications: true,
        weeklyDigest: false,
      },
    })

    // Ensure second user has correct data
    await prisma.user.updateMany({
      where: { id: secondTestUserId },
      data: {
        themeMode: 'light',
        colorScheme: 'emynent-light',
      },
    })
  })

  afterEach(async () => {
    // Only clean up data that might interfere with other tests
    // Leave events alone since tests should handle multiple events
    await prisma.behavioralPattern.deleteMany({
      where: { userId: testUserId },
    })
    await prisma.aIInsight.deleteMany({
      where: { userId: testUserId },
    })
    await prisma.userContext.deleteMany({
      where: { userId: testUserId },
    })
    await prisma.featureFlag.deleteMany({
      where: { companyId: testCompanyId },
    })
  })

  describe('Context Capture Service - Detailed Tests', () => {
    describe('User Context Capture', () => {
      it('should capture complete user context with all required fields', async () => {
        const context = await realContextCaptureService.captureUserContext(testUserId)

        // Verify core structure
        expect(context).toBeDefined()
        expect(context.userId).toBe(testUserId)
        expect(context.companyId).toBe(testCompanyId)
        expect(context.role).toBe('EMPLOYEE')
        expect(context.lastActive).toBeInstanceOf(Date)

        // Verify preferences structure
        expect(context.preferences).toBeDefined()
        expect(context.preferences.theme).toBe('dark')
        expect(context.preferences.colorScheme).toBe('emynent-dark')
        expect(context.preferences.notifications).toBeDefined()
        expect(context.preferences.notifications.email).toBe(true)
        expect(context.preferences.notifications.inApp).toBe(true)

        // Verify behavior structure
        expect(context.behavior).toBeDefined()
        expect(context.behavior.recentEvents).toBeInstanceOf(Array)
        expect(context.behavior.patterns).toBeDefined()
        expect(context.behavior.engagement).toBeDefined()
        expect(context.behavior.engagement.level).toMatch(/^(low|medium|high)$/)
        expect(context.behavior.engagement.score).toBeGreaterThanOrEqual(0)
        expect(context.behavior.engagement.score).toBeLessThanOrEqual(100)

        // Verify session data structure
        expect(context.sessionData).toBeDefined()
        expect(context.sessionData.lastActive).toBeInstanceOf(Date)
        expect(context.sessionData.deviceInfo).toBeDefined()
        expect(context.sessionData.location).toBeDefined()
      })

      it('should handle user with no recent activity', async () => {
        const context = await realContextCaptureService.captureUserContext(secondTestUserId)

        expect(context.behavior.recentEvents).toHaveLength(0)
        expect(context.behavior.engagement.level).toBe('low')
        expect(context.behavior.engagement.score).toBe(0)
        expect(context.behavior.engagement.factors).toHaveLength(0)
      })

      it('should cache context data in Redis', async () => {
        await realContextCaptureService.captureUserContext(testUserId)

        // Second call should be faster (cached)
        const startTime = Date.now()
        const cachedContext = await realContextCaptureService.captureUserContext(testUserId)
        const endTime = Date.now()

        expect(cachedContext).toBeDefined()
        expect(endTime - startTime).toBeLessThan(100) // Should be very fast from cache
      })

      it('should handle non-existent user gracefully', async () => {
        await expect(
          realContextCaptureService.captureUserContext('non-existent-user-id')
        ).rejects.toThrow('Failed to capture user context')
      })
    })

    describe('Event Tracking', () => {
      it('should track page view events with complete data', async () => {
        const eventResult = await realContextCaptureService.trackEvent({
          userId: testUserId,
          eventType: 'page_view',
          eventData: {
            page: '/dashboard',
            timestamp: new Date().toISOString(),
            referrer: '/settings',
          },
          pageUrl: '/dashboard',
          sessionId: 'test-session-123',
          durationMs: 5000,
        })

        expect(eventResult.success).toBe(true)
        expect(eventResult.eventId).toBeDefined()
        expect(eventResult.error).toBeUndefined()

        // Verify event was stored correctly
        const storedEvent = await prisma.contextEvent.findUnique({
          where: { id: eventResult.eventId },
        })

        expect(storedEvent).toBeDefined()
        expect(storedEvent!.userId).toBe(testUserId)
        expect(storedEvent!.companyId).toBe(testCompanyId)
        expect(storedEvent!.eventType).toBe('page_view')
        expect(storedEvent!.pageUrl).toBe('/dashboard')
        expect(storedEvent!.sessionId).toBe('test-session-123')
        expect(storedEvent!.durationMs).toBe(5000)
        expect(storedEvent!.eventData).toEqual({
          page: '/dashboard',
          timestamp: expect.any(String),
          referrer: '/settings',
        })
      })

      it('should track feature usage events', async () => {
        const eventResult = await realContextCaptureService.trackEvent({
          userId: testUserId,
          eventType: 'feature_use',
          eventData: {
            feature: 'theme_switcher',
            fromTheme: 'light',
            toTheme: 'dark',
            context: 'settings_panel',
          },
        })

        expect(eventResult.success).toBe(true)

        const storedEvent = await prisma.contextEvent.findUnique({
          where: { id: eventResult.eventId },
        })

        expect(storedEvent!.eventType).toBe('feature_use')
        expect(storedEvent!.eventData).toEqual({
          feature: 'theme_switcher',
          fromTheme: 'light',
          toTheme: 'dark',
          context: 'settings_panel',
        })
      })

      it('should update user lastContextUpdate timestamp', async () => {
        const beforeTime = new Date()

        await realContextCaptureService.trackEvent({
          userId: testUserId,
          eventType: 'test_event',
          eventData: {},
        })

        const updatedUser = await prisma.user.findUnique({
          where: { id: testUserId },
        })

        expect(updatedUser!.lastContextUpdate).toBeDefined()
        expect(updatedUser!.lastContextUpdate!.getTime()).toBeGreaterThanOrEqual(
          beforeTime.getTime()
        )
      })

      it('should invalidate cache after tracking event', async () => {
        // First, populate cache
        await realContextCaptureService.captureUserContext(testUserId)

        // Track an event (should invalidate cache)
        await realContextCaptureService.trackEvent({
          userId: testUserId,
          eventType: 'cache_test',
          eventData: {},
        })

        // Next context capture should include the new event
        const context = await realContextCaptureService.captureUserContext(testUserId)
        const cacheTestEvent = context.behavior.recentEvents.find(
          event => event.type === 'cache_test'
        )

        expect(cacheTestEvent).toBeDefined()
      })

      it('should handle tracking for non-existent user', async () => {
        const eventResult = await realContextCaptureService.trackEvent({
          userId: 'non-existent-user',
          eventType: 'test_event',
          eventData: {},
        })

        expect(eventResult.success).toBe(false)
        expect(eventResult.error).toContain('not found')
      })
    })

    describe('Context Updates', () => {
      it('should update user context data', async () => {
        const updateResult = await realContextCaptureService.updateUserContext(testUserId, {
          preferences: {
            theme: 'light',
            newFeature: true,
            customSettings: {
              dashboardLayout: 'grid',
              notificationFrequency: 'daily',
            },
          },
        })

        expect(updateResult).toBe(true)

        // Verify update in database
        const updatedUser = await prisma.user.findUnique({
          where: { id: testUserId },
        })

        expect(updatedUser!.contextData).toEqual({
          preferences: {
            theme: 'light',
            newFeature: true,
            customSettings: {
              dashboardLayout: 'grid',
              notificationFrequency: 'daily',
            },
          },
        })
        expect(updatedUser!.lastContextUpdate).toBeDefined()
      })

      it('should handle partial context updates', async () => {
        // First update
        await realContextCaptureService.updateUserContext(testUserId, {
          preferences: { theme: 'dark' },
        })

        // Second update (should replace, not merge)
        const updateResult = await realContextCaptureService.updateUserContext(testUserId, {
          preferences: { colorScheme: 'custom' },
        })

        expect(updateResult).toBe(true)

        const updatedUser = await prisma.user.findUnique({
          where: { id: testUserId },
        })

        expect(updatedUser!.contextData).toEqual({
          preferences: { colorScheme: 'custom' },
        })
      })
    })
  })

  describe('Behavioral Analytics Service - Detailed Tests', () => {
    beforeEach(async () => {
      // Create comprehensive test events for analysis
      const testEvents = [
        // Navigation events
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/dashboard' },
          pageUrl: '/dashboard',
          timestamp: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
          durationMs: 30000,
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/settings' },
          pageUrl: '/settings',
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          durationMs: 45000,
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/dashboard' },
          pageUrl: '/dashboard',
          timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
          durationMs: 25000,
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'page_view',
          eventData: { page: '/profile' },
          pageUrl: '/profile',
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          durationMs: 15000,
        },
        // Feature usage events
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'feature_use',
          eventData: { feature: 'theme_switcher' },
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'feature_use',
          eventData: { feature: 'settings_panel' },
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        },
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'feature_use',
          eventData: { feature: 'theme_switcher' },
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        },
        // Collaboration events
        {
          userId: testUserId,
          companyId: testCompanyId,
          eventType: 'collaboration_start',
          eventData: { type: 'document_edit', collaborators: 2 },
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        },
      ]

      await prisma.contextEvent.createMany({
        data: testEvents,
      })
    })

    describe('Behavior Analysis', () => {
      it('should analyze navigation patterns correctly', async () => {
        const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
          userId: testUserId,
          timeframe: '7d',
          analysisTypes: ['navigation'],
          includeRecommendations: false,
        })

        expect(analysis.patterns.navigation).toBeDefined()

        const navPattern = analysis.patterns.navigation!
        expect(navPattern.mostVisitedPages).toBeInstanceOf(Array)
        expect(navPattern.commonTransitions).toBeInstanceOf(Array)
        expect(navPattern.confidence).toBeGreaterThan(0)

        // Dashboard should be most visited (at least 2 visits from this test)
        const dashboardVisits = navPattern.mostVisitedPages.find(page => page.page === '/dashboard')
        expect(dashboardVisits).toBeDefined()
        expect(dashboardVisits!.count).toBeGreaterThanOrEqual(2)

        // Settings should be visited (at least 1 visit from this test)
        const settingsVisits = navPattern.mostVisitedPages.find(page => page.page === '/settings')
        expect(settingsVisits).toBeDefined()
        expect(settingsVisits!.count).toBeGreaterThanOrEqual(1)

        // Should detect transitions
        expect(navPattern.commonTransitions.length).toBeGreaterThan(0)
      })

      it('should analyze feature usage patterns correctly', async () => {
        const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
          userId: testUserId,
          timeframe: '7d',
          analysisTypes: ['feature_usage'],
          includeRecommendations: false,
        })

        expect(analysis.patterns.featureUsage).toBeDefined()

        const featurePattern = analysis.patterns.featureUsage!
        expect(featurePattern.mostUsedFeatures).toBeInstanceOf(Array)
        expect(featurePattern.usageFrequency).toBeDefined()
        expect(featurePattern.confidence).toBeGreaterThan(0)

        // Theme switcher should be most used (at least 2 times from this test)
        const themeSwitcherUsage = featurePattern.mostUsedFeatures.find(
          feature => feature.feature === 'theme_switcher'
        )
        expect(themeSwitcherUsage).toBeDefined()
        expect(themeSwitcherUsage!.count).toBeGreaterThanOrEqual(2)
        expect(themeSwitcherUsage!.lastUsed).toBeInstanceOf(Date)

        // Settings panel should be used at least once
        expect(featurePattern.usageFrequency['settings_panel']).toBeGreaterThanOrEqual(1)
      })

      it('should analyze time-based patterns correctly', async () => {
        const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
          userId: testUserId,
          timeframe: '7d',
          analysisTypes: ['time_based'],
          includeRecommendations: false,
        })

        expect(analysis.patterns.timePatterns).toBeDefined()

        const timePattern = analysis.patterns.timePatterns!
        expect(timePattern.peakHours).toBeInstanceOf(Array)
        expect(timePattern.activeDays).toBeInstanceOf(Array)
        expect(timePattern.sessionDuration).toBeGreaterThanOrEqual(0)
        expect(timePattern.confidence).toBeGreaterThan(0)

        // Should have detected activity across multiple days
        expect(timePattern.activeDays.length).toBeGreaterThan(0)
        expect(timePattern.peakHours.length).toBeGreaterThan(0)
      })

      it('should generate recommendations when requested', async () => {
        const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
          userId: testUserId,
          timeframe: '7d',
          analysisTypes: ['navigation', 'feature_usage'],
          includeRecommendations: true,
        })

        expect(analysis.recommendations).toBeInstanceOf(Array)

        if (analysis.recommendations.length > 0) {
          analysis.recommendations.forEach(recommendation => {
            expect(typeof recommendation).toBe('string')
            expect(recommendation.length).toBeGreaterThan(0)
          })
        }
      })

      it('should store patterns in database', async () => {
        await realBehavioralAnalyticsService.analyzeUserBehavior({
          userId: testUserId,
          timeframe: '7d',
          analysisTypes: ['navigation', 'feature_usage'],
          includeRecommendations: false,
        })

        const storedPatterns = await prisma.behavioralPattern.findMany({
          where: { userId: testUserId },
        })

        expect(storedPatterns.length).toBeGreaterThan(0)

        const navigationPattern = storedPatterns.find(p => p.patternType === 'navigation')
        expect(navigationPattern).toBeDefined()
        expect(navigationPattern!.patternData).toBeDefined()
        expect(navigationPattern!.confidenceScore).toBeGreaterThanOrEqual(0)
        expect(navigationPattern!.lastUpdated).toBeInstanceOf(Date)

        const featurePattern = storedPatterns.find(p => p.patternType === 'feature_usage')
        expect(featurePattern).toBeDefined()
      })
    })

    describe('Engagement Metrics', () => {
      it('should calculate detailed engagement metrics', async () => {
        const metrics = await realBehavioralAnalyticsService.getEngagementMetrics(testUserId)

        expect(metrics.level).toMatch(/^(low|medium|high)$/)
        expect(metrics.score).toBeGreaterThanOrEqual(0)
        expect(metrics.score).toBeLessThanOrEqual(100)
        expect(metrics.factors).toBeInstanceOf(Array)

        // With 8 events over 6 days, should have medium engagement
        expect(metrics.score).toBeGreaterThan(10)
        expect(metrics.level).toMatch(/^(medium|high)$/)

        // Should detect engagement factors
        if (metrics.factors.length > 0) {
          const validFactors = [
            'high_activity',
            'consistent_usage',
            'feature_exploration',
            'collaborative',
          ]
          metrics.factors.forEach(factor => {
            expect(validFactors).toContain(factor)
          })
        }
      })

      it('should handle user with no activity', async () => {
        const metrics = await realBehavioralAnalyticsService.getEngagementMetrics(secondTestUserId)

        expect(metrics.level).toBe('low')
        expect(metrics.score).toBe(0)
        expect(metrics.factors).toHaveLength(0)
      })
    })

    describe('Pattern Detection', () => {
      it('should detect patterns from provided events', async () => {
        const events = [
          {
            type: 'page_view',
            data: {},
            timestamp: new Date(),
            pageUrl: '/dashboard',
          },
          {
            type: 'page_view',
            data: {},
            timestamp: new Date(),
            pageUrl: '/settings',
          },
          {
            type: 'page_view',
            data: {},
            timestamp: new Date(),
            pageUrl: '/dashboard',
          },
          {
            type: 'feature_use',
            data: { feature: 'theme_switcher' },
            timestamp: new Date(),
          },
        ]

        const result = await realBehavioralAnalyticsService.detectPatterns({
          userId: testUserId,
          events,
          patternTypes: ['navigation', 'feature_usage'],
          confidenceThreshold: 0.1,
        })

        expect(result.patterns).toBeDefined()
        expect(result.confidence).toBeGreaterThanOrEqual(0)
        expect(result.detectedAt).toBeInstanceOf(Date)

        if (result.patterns.navigation) {
          expect(result.patterns.navigation.mostVisitedPages.length).toBeGreaterThan(0)
          const dashboardPage = result.patterns.navigation.mostVisitedPages.find(
            page => page.page === '/dashboard'
          )
          expect(dashboardPage?.count).toBe(2)
        }
      })

      it('should respect confidence threshold', async () => {
        const events = [
          {
            type: 'page_view',
            data: {},
            timestamp: new Date(),
            pageUrl: '/test',
          },
        ]

        const result = await realBehavioralAnalyticsService.detectPatterns({
          userId: testUserId,
          events,
          patternTypes: ['navigation'],
          confidenceThreshold: 0.9, // Very high threshold
        })

        // With only one event, confidence should be low
        expect(result.confidence).toBeLessThan(0.9)
      })
    })
  })

  describe('Integration and Performance Tests', () => {
    it('should handle concurrent context captures efficiently', async () => {
      const promises = Array.from({ length: 10 }, () =>
        realContextCaptureService.captureUserContext(testUserId)
      )

      const startTime = Date.now()
      const results = await Promise.all(promises)
      const endTime = Date.now()

      expect(endTime - startTime).toBeLessThan(2000) // Should complete within 2 seconds

      results.forEach(context => {
        expect(context.userId).toBe(testUserId)
      })
    })

    it('should handle large datasets efficiently', async () => {
      // Create many events
      const manyEvents = Array.from({ length: 100 }, (_, i) => ({
        userId: testUserId,
        companyId: testCompanyId,
        eventType: 'page_view',
        eventData: { page: `/page-${i}` },
        pageUrl: `/page-${i}`,
        timestamp: new Date(Date.now() - i * 60 * 1000), // 1 minute apart
      }))

      await prisma.contextEvent.createMany({
        data: manyEvents,
      })

      const startTime = Date.now()
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '1d',
        analysisTypes: ['navigation'],
        includeRecommendations: false,
      })
      const endTime = Date.now()

      expect(analysis).toBeDefined()
      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
      expect(analysis.patterns.navigation?.mostVisitedPages.length).toBeGreaterThan(0)
    })

    it('should maintain data consistency across services', async () => {
      // Track multiple events
      const events = [
        { eventType: 'page_view', pageUrl: '/consistency-test-1' },
        { eventType: 'page_view', pageUrl: '/consistency-test-2' },
        { eventType: 'feature_use', eventData: { feature: 'consistency_test' } },
      ]

      for (const event of events) {
        const result = await realContextCaptureService.trackEvent({
          userId: testUserId,
          ...event,
          eventData: event.eventData || {},
        })
        expect(result.success).toBe(true)
      }

      // Verify events appear in context
      const context = await realContextCaptureService.captureUserContext(testUserId)
      expect(context.behavior.recentEvents.length).toBeGreaterThanOrEqual(3)

      // Verify events are included in analytics
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '1d',
        analysisTypes: ['navigation', 'feature_usage'],
        includeRecommendations: false,
      })

      // Check that analytics found some patterns
      expect(analysis.patterns).toBeDefined()

      if (analysis.patterns.navigation) {
        // Should have some pages in the analysis
        expect(analysis.patterns.navigation.mostVisitedPages.length).toBeGreaterThan(0)

        // Check if our test pages are included (they might be, but not required due to timing)
        const testPages = analysis.patterns.navigation.mostVisitedPages.filter(page =>
          page.page.includes('consistency-test')
        )
        // If test pages are found, they should have valid counts
        if (testPages.length > 0) {
          testPages.forEach(page => {
            expect(page.count).toBeGreaterThan(0)
          })
        }
      }

      if (analysis.patterns.featureUsage) {
        // Should have some features in the analysis
        expect(Object.keys(analysis.patterns.featureUsage.usageFrequency).length).toBeGreaterThan(0)

        // Check if our test feature is included (it might be, but not required due to timing)
        if (analysis.patterns.featureUsage.usageFrequency['consistency_test']) {
          expect(
            analysis.patterns.featureUsage.usageFrequency['consistency_test']
          ).toBeGreaterThanOrEqual(1)
        }
      }
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking database failures
      // For now, we test that the service doesn't crash with invalid data

      const eventResult = await realContextCaptureService.trackEvent({
        userId: testUserId,
        eventType: '', // Invalid event type
        eventData: {},
      })

      // Should handle gracefully
      expect(eventResult).toBeDefined()
      expect(typeof eventResult.success).toBe('boolean')
    })

    it('should handle malformed event data', async () => {
      const eventResult = await realContextCaptureService.trackEvent({
        userId: testUserId,
        eventType: 'test_malformed',
        eventData: {
          circular: null as any,
        },
      })

      // Create circular reference
      eventResult.eventData = { circular: eventResult.eventData }

      // Should not crash
      expect(eventResult).toBeDefined()
    })

    it('should handle empty timeframes in analytics', async () => {
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '0d', // Invalid timeframe
        analysisTypes: ['navigation'],
        includeRecommendations: false,
      })

      expect(analysis).toBeDefined()
      expect(analysis.patterns).toBeDefined()
    })

    it('should handle invalid pattern types', async () => {
      const analysis = await realBehavioralAnalyticsService.analyzeUserBehavior({
        userId: testUserId,
        timeframe: '7d',
        analysisTypes: ['invalid_pattern_type'] as any,
        includeRecommendations: false,
      })

      expect(analysis).toBeDefined()
      expect(analysis.patterns).toBeDefined()
    })
  })
})

describe('🔄 Intelligence Layer API Integration Tests', () => {
  // These tests would verify the API endpoints work correctly
  // They require setting up a test server environment

  it('should be ready for API endpoint testing', () => {
    expect(realContextCaptureService).toBeDefined()
    expect(realBehavioralAnalyticsService).toBeDefined()

    // Verify all required methods exist
    expect(typeof realContextCaptureService.captureUserContext).toBe('function')
    expect(typeof realContextCaptureService.trackEvent).toBe('function')
    expect(typeof realContextCaptureService.updateUserContext).toBe('function')

    expect(typeof realBehavioralAnalyticsService.analyzeUserBehavior).toBe('function')
    expect(typeof realBehavioralAnalyticsService.getEngagementMetrics).toBe('function')
    expect(typeof realBehavioralAnalyticsService.detectPatterns).toBe('function')
  })
})
