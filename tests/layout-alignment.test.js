/**
 * Layout Alignment Test
 * Tests horizontal border alignment between navbar, sidebar, and page content
 */

describe('Layout Alignment Tests', () => {
  beforeEach(() => {
    cy.visit('/superadmin')
    cy.get('[data-testid="main-navbar"]').should('be.visible')
  })

  it('should have aligned horizontal borders between navbar and content', () => {
    // Check navbar border bottom position
    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarRect = $navbar[0].getBoundingClientRect()
      const navbarBottom = navbarRect.bottom

      // Check if main content starts immediately after navbar
      cy.get('main').then($main => {
        const mainRect = $main[0].getBoundingClientRect()
        const mainTop = mainRect.top

        // The main content should start exactly where navbar ends
        expect(Math.abs(mainTop - navbarBottom)).to.be.lessThan(2) // Allow 1px tolerance
      })
    })
  })

  it('should have consistent left margin between navbar and content', () => {
    // Check navbar left margin
    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarStyles = window.getComputedStyle($navbar[0])
      const navbarMarginLeft = parseInt(navbarStyles.marginLeft)

      // Check main content left margin/positioning
      cy.get('main').then($main => {
        const mainStyles = window.getComputedStyle($main[0])
        const mainMarginLeft = parseInt(mainStyles.marginLeft)
        const mainPaddingLeft = parseInt(mainStyles.paddingLeft)

        // The alignment should be consistent
        // This test checks if the content area aligns with navbar
        expect(navbarMarginLeft).to.equal(0) // Navbar should be positioned by width calc
        expect(mainPaddingLeft).to.be.greaterThan(0) // Main should have padding
      })
    })
  })

  it('should have sidebar and content borders properly aligned', () => {
    // Check if sidebar is visible and get its width
    cy.get('[data-testid="smart-sidebar"]')
      .should('be.visible')
      .then($sidebar => {
        const sidebarRect = $sidebar[0].getBoundingClientRect()
        const sidebarRight = sidebarRect.right

        // Check if main content starts where sidebar ends
        cy.get('main').then($main => {
          const mainRect = $main[0].getBoundingClientRect()
          const mainLeft = mainRect.left

          // Content should start where sidebar ends (with potential small gap)
          expect(Math.abs(mainLeft - sidebarRight)).to.be.lessThan(5) // Allow small tolerance
        })
      })
  })

  it('should have consistent border styling across components', () => {
    // Check navbar border
    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarStyles = window.getComputedStyle($navbar[0])
      const navbarBorderBottom = navbarStyles.borderBottomWidth
      const navbarBorderColor = navbarStyles.borderBottomColor

      // Check if page cards have consistent border styling
      cy.get('.rounded-lg.border.bg-card')
        .first()
        .then($card => {
          const cardStyles = window.getComputedStyle($card[0])
          const cardBorderWidth = cardStyles.borderWidth
          const cardBorderColor = cardStyles.borderColor

          // Borders should have consistent width
          expect(navbarBorderBottom).to.equal(cardBorderWidth)
          // Colors should be from the same design system
          expect(navbarBorderColor).to.be.a('string')
          expect(cardBorderColor).to.be.a('string')
        })
    })
  })

  it('should maintain alignment when sidebar state changes', () => {
    // Test with expanded sidebar
    cy.get('[data-testid="navbar-burger-menu"]').click()
    cy.wait(500) // Wait for transition

    // Check alignment after sidebar toggle
    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarRect = $navbar[0].getBoundingClientRect()

      cy.get('main').then($main => {
        const mainRect = $main[0].getBoundingClientRect()

        // Check if content area width matches navbar width
        expect(Math.abs(navbarRect.width - mainRect.width)).to.be.lessThan(10)
      })
    })

    // Toggle back and test again
    cy.get('[data-testid="navbar-burger-menu"]').click()
    cy.wait(500)

    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarRect = $navbar[0].getBoundingClientRect()

      cy.get('main').then($main => {
        const mainRect = $main[0].getBoundingClientRect()

        // Check alignment is maintained
        expect(Math.abs(navbarRect.width - mainRect.width)).to.be.lessThan(10)
      })
    })
  })

  it('should have proper spacing and padding consistency', () => {
    // Check navbar padding
    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarStyles = window.getComputedStyle($navbar[0])
      const navbarPaddingX = parseInt(navbarStyles.paddingLeft)

      // Check main content padding
      cy.get('main').then($main => {
        const mainStyles = window.getComputedStyle($main[0])
        const mainPaddingX = parseInt(mainStyles.paddingLeft)

        // Padding should be consistent with design system
        expect(navbarPaddingX).to.be.greaterThan(0)
        expect(mainPaddingX).to.be.greaterThan(0)

        // Should follow 6px increments (1.5rem = 24px standard)
        expect(navbarPaddingX % 6).to.equal(0)
        expect(mainPaddingX % 6).to.equal(0)
      })
    })
  })

  it('should test SuperAdmin specific layout alignment', () => {
    // Navigate to SuperAdmin page
    cy.visit('/superadmin')
    cy.get('h2').contains('Superadmin Dashboard').should('be.visible')

    // Check if SuperAdmin content aligns with navbar
    cy.get('h2')
      .contains('Superadmin Dashboard')
      .then($header => {
        const headerRect = $header[0].getBoundingClientRect()

        // Check if header starts at appropriate distance from navbar
        cy.get('[data-testid="main-navbar"]').then($navbar => {
          const navbarRect = $navbar[0].getBoundingClientRect()
          const expectedTop = navbarRect.bottom + 56 + 32 // navbar + pt-14 + space-y-8 first element

          // Allow some tolerance for spacing
          expect(Math.abs(headerRect.top - expectedTop)).to.be.lessThan(20)
        })
      })

    // Check card alignment
    cy.get('.rounded-lg.border.bg-card')
      .first()
      .then($card => {
        const cardRect = $card[0].getBoundingClientRect()

        // Check if cards align with main content area
        cy.get('main').then($main => {
          const mainRect = $main[0].getBoundingClientRect()
          const mainLeftPadding = 24 // px-6 = 24px

          expect(Math.abs(cardRect.left - (mainRect.left + mainLeftPadding))).to.be.lessThan(5)
        })
      })
  })

  it('should test Settings page layout alignment', () => {
    // Navigate to Settings page
    cy.visit('/settings/platform/appearance')

    // Check settings page alignment
    cy.get('main').then($main => {
      const mainRect = $main[0].getBoundingClientRect()

      // Check if settings content aligns properly
      cy.get('[data-testid="main-navbar"]').then($navbar => {
        const navbarRect = $navbar[0].getBoundingClientRect()

        // Main content should start right after navbar
        expect(Math.abs(mainRect.top - navbarRect.bottom)).to.be.lessThan(2)

        // Width should account for sidebar
        expect(mainRect.width).to.be.greaterThan(200) // Should have reasonable width
      })
    })
  })

  it('should verify responsive layout alignment', () => {
    // Test mobile viewport
    cy.viewport(768, 1024)
    cy.reload()

    // Check if layout maintains alignment on smaller screens
    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarRect = $navbar[0].getBoundingClientRect()

      cy.get('main').then($main => {
        const mainRect = $main[0].getBoundingClientRect()

        // On mobile, content should span full width (minus sidebar if visible)
        expect(mainRect.top).to.be.greaterThan(navbarRect.bottom - 2)
      })
    })

    // Test desktop viewport
    cy.viewport(1920, 1080)
    cy.reload()

    // Verify alignment on large screens
    cy.get('[data-testid="main-navbar"]').then($navbar => {
      const navbarRect = $navbar[0].getBoundingClientRect()

      cy.get('main').then($main => {
        const mainRect = $main[0].getBoundingClientRect()

        expect(Math.abs(mainRect.top - navbarRect.bottom)).to.be.lessThan(2)
      })
    })
  })
})
