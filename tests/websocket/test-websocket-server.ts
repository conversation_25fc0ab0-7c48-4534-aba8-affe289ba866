import { WebSocketServer, WebSocket } from 'ws'
import { createServer, Server } from 'http'
import { verify } from 'jsonwebtoken'
import {
  findAvailablePort,
  TEST_CONFIG,
  generateTestJWTToken,
  createTestUser,
  withTimeout,
  wait,
} from '../utils/test-helpers'
import {
  setupTestDatabase,
  cleanupTestDatabase,
  closeTestDatabase,
} from '../utils/database-helpers'

export interface TestWebSocketServerOptions {
  port?: number
  enableLogging?: boolean
  maxConnections?: number
  heartbeatInterval?: number
}

export interface TestConnection {
  id: string
  userId: string
  email: string
  role: string
  companyId: string
  connectedAt: Date
  lastPing?: Date
}

export class TestWebSocketServer {
  private server: Server | null = null
  private wss: WebSocketServer | null = null
  private port: number = 0
  private connections = new Map<string, TestConnection>()
  private heartbeatInterval: NodeJS.Timeout | null = null
  private options: Required<TestWebSocketServerOptions>

  constructor(options: TestWebSocketServerOptions = {}) {
    this.options = {
      port: options.port || 0, // 0 means auto-discover
      enableLogging: options.enableLogging ?? TEST_CONFIG.VERBOSE,
      maxConnections: options.maxConnections || TEST_CONFIG.MAX_CONNECTIONS,
      heartbeatInterval: options.heartbeatInterval || TEST_CONFIG.HEARTBEAT_INTERVAL,
    }
  }

  /**
   * Start the test WebSocket server
   */
  async start(): Promise<number> {
    try {
      // Setup test database
      await setupTestDatabase()

      // Find available port if not specified
      if (this.options.port === 0) {
        this.port = await findAvailablePort()
      } else {
        this.port = this.options.port
      }

      // Create HTTP server
      this.server = createServer()

      // Create WebSocket server
      this.wss = new WebSocketServer({
        server: this.server,
        verifyClient: this.verifyClient.bind(this),
      })

      // Setup WebSocket event handlers
      this.setupWebSocketHandlers()

      // Start heartbeat
      this.startHeartbeat()

      // Start HTTP server
      await new Promise<void>((resolve, reject) => {
        this.server!.listen(this.port, (error?: Error) => {
          if (error) {
            reject(error)
          } else {
            resolve()
          }
        })
      })

      if (this.options.enableLogging) {
        console.log(`🧪 Test WebSocket server started on port ${this.port}`)
      }

      return this.port
    } catch (error) {
      await this.stop()
      throw error
    }
  }

  /**
   * Stop the test WebSocket server
   */
  async stop(): Promise<void> {
    try {
      // Stop heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
      }

      // Close all WebSocket connections
      if (this.wss) {
        this.wss.clients.forEach(ws => {
          ws.terminate()
        })
        this.wss.close()
        this.wss = null
      }

      // Close HTTP server
      if (this.server) {
        await new Promise<void>(resolve => {
          this.server!.close(() => resolve())
        })
        this.server = null
      }

      // Clear connections
      this.connections.clear()

      // Cleanup test database
      await cleanupTestDatabase()
      await closeTestDatabase()

      if (this.options.enableLogging) {
        console.log('🧪 Test WebSocket server stopped')
      }
    } catch (error) {
      console.error('❌ Error stopping test WebSocket server:', error)
      throw error
    }
  }

  /**
   * Get server statistics
   */
  getStats() {
    return {
      port: this.port,
      totalConnections: this.connections.size,
      activeConnections: this.wss?.clients.size || 0,
      connections: Array.from(this.connections.values()),
    }
  }

  /**
   * Broadcast message to all connected clients
   */
  broadcast(message: any): void {
    const messageStr = JSON.stringify(message)
    this.wss?.clients.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(messageStr)
      }
    })
  }

  /**
   * Send message to specific user
   */
  sendToUser(userId: string, message: any): boolean {
    const messageStr = JSON.stringify(message)
    let sent = false

    this.wss?.clients.forEach((ws: any) => {
      if (ws.userId === userId && ws.readyState === WebSocket.OPEN) {
        ws.send(messageStr)
        sent = true
      }
    })

    return sent
  }

  /**
   * Verify client connection
   */
  private verifyClient(info: any): boolean {
    try {
      const url = new URL(info.req.url, `http://${info.req.headers.host}`)
      const token = url.searchParams.get('token')

      if (!token) {
        if (this.options.enableLogging) {
          console.log('❌ WebSocket connection rejected: No token provided')
        }
        return false
      }

      // Verify JWT token
      const decoded = verify(token, TEST_CONFIG.NEXTAUTH_SECRET) as any

      if (!decoded.sub || !decoded.email || !decoded.role) {
        if (this.options.enableLogging) {
          console.log('❌ WebSocket connection rejected: Invalid token payload')
        }
        return false
      }

      // Check connection limit
      if (this.connections.size >= this.options.maxConnections) {
        if (this.options.enableLogging) {
          console.log('❌ WebSocket connection rejected: Max connections reached')
        }
        return false
      }

      // Store user info for later use
      info.req.userInfo = {
        userId: decoded.sub,
        email: decoded.email,
        role: decoded.role,
        companyId: decoded.companyId,
      }

      return true
    } catch (error) {
      if (this.options.enableLogging) {
        console.log('❌ WebSocket connection rejected: Token verification failed', error.message)
      }
      return false
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    this.wss!.on('connection', (ws: any, req: any) => {
      const userInfo = req.userInfo
      const connectionId = `${userInfo.userId}-${Date.now()}`

      // Store connection info
      const connection: TestConnection = {
        id: connectionId,
        userId: userInfo.userId,
        email: userInfo.email,
        role: userInfo.role,
        companyId: userInfo.companyId,
        connectedAt: new Date(),
      }

      this.connections.set(connectionId, connection)

      // Store user info on WebSocket for easy access
      ws.userId = userInfo.userId
      ws.connectionId = connectionId
      ws.isAlive = true

      if (this.options.enableLogging) {
        console.log(`✅ WebSocket connection established: ${userInfo.email} (${userInfo.role})`)
      }

      // Handle messages
      ws.on('message', (data: Buffer) => {
        try {
          const message = JSON.parse(data.toString())
          this.handleMessage(ws, message, connection)
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error)
        }
      })

      // Handle pong (heartbeat response)
      ws.on('pong', () => {
        ws.isAlive = true
        connection.lastPing = new Date()
      })

      // Handle connection close
      ws.on('close', () => {
        this.connections.delete(connectionId)
        if (this.options.enableLogging) {
          console.log(`🔌 WebSocket connection closed: ${userInfo.email}`)
        }
      })

      // Handle errors
      ws.on('error', (error: Error) => {
        console.error(`❌ WebSocket error for ${userInfo.email}:`, error)
        this.connections.delete(connectionId)
      })

      // Send welcome message
      ws.send(
        JSON.stringify({
          type: 'welcome',
          data: {
            connectionId,
            serverTime: new Date().toISOString(),
            userInfo: {
              userId: userInfo.userId,
              email: userInfo.email,
              role: userInfo.role,
            },
          },
        })
      )
    })
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(ws: any, message: any, connection: TestConnection): void {
    if (this.options.enableLogging) {
      console.log(`📨 Message from ${connection.email}:`, message)
    }

    switch (message.type) {
      case 'ping':
        ws.send(
          JSON.stringify({
            type: 'pong',
            data: { timestamp: new Date().toISOString() },
          })
        )
        break

      case 'echo':
        ws.send(
          JSON.stringify({
            type: 'echo',
            data: message.data,
          })
        )
        break

      case 'broadcast':
        this.broadcast({
          type: 'broadcast',
          from: connection.email,
          data: message.data,
        })
        break

      default:
        ws.send(
          JSON.stringify({
            type: 'error',
            data: { message: `Unknown message type: ${message.type}` },
          })
        )
    }
  }

  /**
   * Start heartbeat to detect dead connections
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.wss?.clients.forEach((ws: any) => {
        if (!ws.isAlive) {
          ws.terminate()
          return
        }

        ws.isAlive = false
        ws.ping()
      })
    }, this.options.heartbeatInterval)
  }
}

/**
 * Create a test WebSocket client
 */
export class TestWebSocketClient {
  private ws: WebSocket | null = null
  private url: string
  private token: string
  private messageHandlers = new Map<string, (data: any) => void>()
  private connected = false

  constructor(serverPort: number, userInfo: ReturnType<typeof createTestUser>) {
    this.token = generateTestJWTToken(userInfo)
    this.url = `ws://localhost:${serverPort}?token=${this.token}`
  }

  /**
   * Connect to the WebSocket server
   */
  async connect(timeoutMs: number = TEST_CONFIG.CONNECTION_TIMEOUT): Promise<void> {
    return withTimeout(
      new Promise<void>((resolve, reject) => {
        this.ws = new WebSocket(this.url)

        this.ws.on('open', () => {
          this.connected = true
          resolve()
        })

        this.ws.on('error', error => {
          reject(error)
        })

        this.ws.on('message', data => {
          try {
            const message = JSON.parse(data.toString())

            // Handle all matching handlers (for waitForMessage with unique IDs)
            this.messageHandlers.forEach((handler, handlerId) => {
              if (handlerId.startsWith(message.type + '_') || handlerId === message.type) {
                // Pass the full message structure, not just message.data
                handler(message)
              }
            })
          } catch (error) {
            console.error('❌ Error parsing message:', error)
          }
        })

        this.ws.on('close', () => {
          this.connected = false
        })
      }),
      timeoutMs,
      'WebSocket connection timeout'
    )
  }

  /**
   * Disconnect from the WebSocket server
   */
  async disconnect(): Promise<void> {
    if (this.ws) {
      this.ws.close()
      this.ws = null
      this.connected = false
    }
  }

  /**
   * Send a message to the server
   */
  send(message: any): void {
    if (this.ws && this.connected) {
      this.ws.send(JSON.stringify(message))
    } else {
      throw new Error('WebSocket not connected')
    }
  }

  /**
   * Register a message handler
   */
  onMessage(type: string, handler: (data: any) => void): void {
    this.messageHandlers.set(type, handler)
  }

  /**
   * Wait for a specific message type
   */
  async waitForMessage(type: string, timeoutMs: number = 5000): Promise<any> {
    return withTimeout(
      new Promise(resolve => {
        // Create a unique handler ID to avoid conflicts
        const handlerId = `${type}_${Date.now()}_${Math.random()}`

        const handler = (data: any) => {
          // Remove this specific handler after it fires
          this.messageHandlers.delete(handlerId)
          resolve(data)
        }

        // Store with unique ID
        this.messageHandlers.set(handlerId, handler)

        // Also update the message listener to handle multiple handlers for same type
        if (!this.ws) {
          throw new Error('WebSocket not connected')
        }
      }),
      timeoutMs,
      `Timeout waiting for message type: ${type}`
    )
  }

  /**
   * Check if client is connected
   */
  isConnected(): boolean {
    return this.connected && this.ws?.readyState === WebSocket.OPEN
  }
}
