import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { SuperAdminLayout } from '@/components/superadmin/SuperAdminLayout'
import { Role } from '@prisma/client'

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(() => '/superadmin'),
}))

// Mock next-auth
jest.mock('next-auth/react')

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
}

const mockSuperAdminSession = {
  user: {
    id: 'superadmin-user-id',
    email: '<EMAIL>',
    role: Role.SUPERADMIN,
    companyId: 'test-company-id',
  },
  expires: '2025-12-31',
}

beforeEach(() => {
  jest.clearAllMocks()
  ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
  ;(useSession as jest.Mock).mockReturnValue({
    data: mockSuperAdminSession,
    status: 'authenticated',
  })
})

describe('Phase 1: SuperAdmin Sidebar Repositioning', () => {
  describe('Task 1: Reposition Super Admin Sidebar', () => {
    it('should position SuperAdmin sidebar immediately adjacent to main sidebar', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const superadminSidebar = screen.getByTestId('superadmin-sidebar')
      const layout = screen.getByTestId('superadmin-layout')

      // Should have dual-sidebar layout structure
      expect(layout).toHaveClass('flex')
      expect(superadminSidebar).toBeInTheDocument()

      // SuperAdmin sidebar should be positioned to create dual-sidebar layout
      const computedStyle = window.getComputedStyle(superadminSidebar)
      expect(computedStyle.position).not.toBe('absolute') // Not floating
      expect(superadminSidebar).toHaveClass('flex-shrink-0') // Fixed positioning
    })

    it('should have identical width to main sidebar', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const superadminSidebar = screen.getByTestId('superadmin-sidebar')

      // Should match main sidebar width (256px = w-64 in Tailwind)
      expect(superadminSidebar).toHaveClass('w-64')
    })

    it('should maintain full viewport height to align with main sidebar', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const superadminSidebar = screen.getByTestId('superadmin-sidebar')

      // Should have full height
      expect(superadminSidebar).toHaveClass('h-full')
    })

    it('should have no visual gaps between sidebars', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const layout = screen.getByTestId('superadmin-layout')

      // Layout should be flush with no gaps
      expect(layout).toHaveClass('flex')
      expect(layout).not.toHaveClass('gap-') // No gap classes
    })
  })

  describe('Task 2: Update Main Content Area Layout', () => {
    it('should create proper layout structure: [Main Sidebar][Super Admin Sidebar][Main Content Area]', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const layout = screen.getByTestId('superadmin-layout')
      const superadminSidebar = screen.getByTestId('superadmin-sidebar')

      // Layout structure verification
      expect(layout).toHaveClass('flex')
      expect(superadminSidebar.parentElement).toHaveClass('flex-shrink-0')

      // Content area should be flex-1 to take remaining space
      const contentArea = layout.querySelector('.flex-1')
      expect(contentArea).toBeInTheDocument()
    })

    it('should ensure content area starts after both sidebars', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const testContent = screen.getByTestId('test-content')
      const layout = screen.getByTestId('superadmin-layout')

      // Content should be in the main content area, not overlapping sidebars
      expect(testContent).toBeInTheDocument()

      // The layout should have proper flex structure
      const contentWrapper = testContent.closest('main')
      expect(contentWrapper).toHaveClass('flex-1')
    })

    it('should preserve existing responsive breakpoints', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const layout = screen.getByTestId('superadmin-layout')

      // Should maintain responsive classes
      expect(layout).toHaveClass('h-screen')
      expect(layout).toHaveClass('bg-background')
    })

    it('should calculate content width to accommodate dual-sidebar layout', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      // Content area should use remaining viewport width after both sidebars
      const contentArea = screen.getByRole('main')
      expect(contentArea).toHaveClass('flex-1')
      expect(contentArea).toHaveClass('overflow-auto')
    })
  })

  describe('Task 3: Remove Redundant Navigation Section', () => {
    it('should not display duplicate navigation elements', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      // Should not have main navigation section in the content
      expect(screen.queryByText('Main Navigation')).not.toBeInTheDocument()

      // Should not have duplicate links
      const dashboardLinks = screen.queryAllByText('Dashboard')
      const settingsLinks = screen.queryAllByText('Settings')
      const adminLinks = screen.queryAllByText('Admin Panel')

      // These should not exist as duplicate navigation
      expect(dashboardLinks).toHaveLength(0)
      expect(settingsLinks).toHaveLength(0)
      expect(adminLinks).toHaveLength(0)
    })

    it('should preserve only SuperAdmin-specific functionality', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const superadminSidebar = screen.getByTestId('superadmin-sidebar')

      // Should have SuperAdmin-specific navigation groups
      expect(screen.getByText('System Management')).toBeInTheDocument()
      expect(screen.getByText('Design System')).toBeInTheDocument()
      expect(screen.getByText('AI & Intelligence')).toBeInTheDocument()
      expect(screen.getByText('System Health')).toBeInTheDocument()
    })

    it('should have no broken references after removal', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      // Check for console errors (would indicate broken references)
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()

      // All navigation links should be functional
      const companyLink = screen.getByTestId('nav-companies')
      const designLink = screen.getByTestId('nav-design-editor')

      expect(companyLink).toHaveAttribute('href', '/superadmin/companies')
      expect(designLink).toHaveAttribute('href', '/superadmin/design-system')

      expect(consoleErrorSpy).not.toHaveBeenCalled()
      consoleErrorSpy.mockRestore()
    })
  })

  describe('Integration Tests', () => {
    it('should maintain SuperAdmin access control', async () => {
      ;(useSession as jest.Mock).mockReturnValue({
        data: { user: { role: Role.ADMIN } }, // Non-SuperAdmin user
        status: 'authenticated',
      })

      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      // Should redirect or show access denied
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/unauthorized')
      })
    })

    it('should handle loading states properly', async () => {
      ;(useSession as jest.Mock).mockReturnValue({
        data: null,
        status: 'loading',
      })

      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      // Should show loading indicator
      expect(screen.getByRole('status')).toBeInTheDocument()
    })

    it('should preserve all existing SuperAdmin routes', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      // All expected routes should be present
      const expectedRoutes = [
        '/superadmin/companies',
        '/superadmin/design-system',
        '/superadmin/ai-models',
        '/superadmin/system/metrics',
      ]

      expectedRoutes.forEach(route => {
        const link = screen.getByRole('link', { name: new RegExp(route.split('/').pop()!, 'i') })
        expect(link).toHaveAttribute('href', route)
      })
    })

    it('should support sidebar collapse functionality', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const toggleButton = screen.getByTestId('superadmin-sidebar-toggle')
      const sidebar = screen.getByTestId('superadmin-sidebar')

      // Initial state should be expanded
      expect(sidebar).toHaveClass('w-64')

      // Click to collapse
      fireEvent.click(toggleButton)

      // Should be collapsed
      expect(sidebar).toHaveClass('w-16')
    })
  })

  describe('Accessibility', () => {
    it('should maintain proper ARIA labels and navigation structure', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const sidebar = screen.getByTestId('superadmin-sidebar')

      // Should have proper ARIA attributes
      expect(sidebar).toHaveAttribute('role', 'navigation')
      expect(sidebar).toHaveAttribute('aria-label', 'Super Admin Navigation')
    })

    it('should support keyboard navigation', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const firstLink = screen.getByTestId('nav-companies')

      // Should be focusable
      firstLink.focus()
      expect(document.activeElement).toBe(firstLink)
    })
  })

  describe('Performance', () => {
    it('should not impact page load performance', async () => {
      const startTime = performance.now()

      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render quickly (under 100ms for this test)
      expect(renderTime).toBeLessThan(100)
    })

    it('should maintain smooth transitions', async () => {
      render(
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      )

      const sidebar = screen.getByTestId('superadmin-sidebar')

      // Should have transition classes
      expect(sidebar).toHaveClass('transition-all')
      expect(sidebar).toHaveClass('duration-300')
    })
  })
})
