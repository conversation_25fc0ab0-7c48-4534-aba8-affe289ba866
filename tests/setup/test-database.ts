/**
 * 🧪 AI-First Test Database Setup
 *
 * Real PostgreSQL and Redis test instances for comprehensive theme system testing.
 * No mocks, no hacks - only real data and real functionality.
 */

import { PrismaClient } from '@prisma/client'
import Redis from 'ioredis'
import { beforeAll, afterAll } from 'vitest'
import bcrypt from 'bcryptjs'

// Test database configuration
const TEST_DATABASE_URL =
  process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/emynent_test'
const TEST_REDIS_URL = process.env.TEST_REDIS_URL || 'redis://localhost:6379/1'

export let testPrisma: PrismaClient
export let testRedis: Redis

// Test data for comprehensive theme testing with proper atomic isolation
function generateUniqueTestData() {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  const processId = process.pid || Math.floor(Math.random() * 10000)
  const testId = process.env.VITEST_WORKER_ID || Math.floor(Math.random() * 1000)
  const unique = `${timestamp}-${random}-${processId}-${testId}`

  const COMPANY_ID = `test-company-${unique}`

  return {
    TEST_USERS: {
      employee: {
        id: `test-employee-${unique}`,
        email: `employee-${unique}@test.com`,
        name: 'Test Employee',
        role: 'EMPLOYEE' as const,
        companyId: COMPANY_ID,
        themeMode: 'light',
        colorScheme: 'emynent-light',
      },
      manager: {
        id: `test-manager-${unique}`,
        email: `manager-${unique}@test.com`,
        name: 'Test Manager',
        role: 'MANAGER' as const,
        companyId: COMPANY_ID,
        themeMode: 'dark',
        colorScheme: 'emynent-dark',
      },
      admin: {
        id: `test-admin-${unique}`,
        email: `admin-${unique}@test.com`,
        name: 'Test Admin',
        role: 'ADMIN' as const,
        companyId: COMPANY_ID,
        themeMode: 'system',
        colorScheme: 'nightowl',
      },
    },
    TEST_COMPANIES: {
      primary: {
        id: COMPANY_ID,
        name: 'Test Company',
        domains: ['test.com'],
        subscriptionStatus: 'ACTIVE' as const,
        allowedEmailDomains: ['test.com'],
        isActive: true,
      },
    },
    TEST_THEME_CONTEXTS: {
      employee: {
        userId: `test-employee-${unique}`,
        role: 'EMPLOYEE',
        companyId: COMPANY_ID,
        preferences: {
          preferredColorSchemes: ['emynent-light', 'syntaxlight'],
          customColors: [
            {
              id: 'custom-1',
              name: 'Brand Blue',
              value: '#1E40AF',
              element: 'primary' as const,
            },
          ],
          gradientSettings: {
            enabled: true,
            type: 'linear' as const,
            angle: 45,
          },
        },
        recentActions: [
          {
            action: 'theme_switch',
            timestamp: Date.now() - 3600000, // 1 hour ago
            data: {
              from: 'emynent-dark',
              to: 'emynent-light',
            },
          },
        ],
        historicalData: {
          themeUsagePatterns: {
            'emynent-light': {
              usage: 70,
              satisfaction: 8.5,
            },
            'emynent-dark': {
              usage: 30,
              satisfaction: 7.2,
            },
          },
          satisfaction: {
            'emynent-light': {
              overall: 8.5,
              aspects: {
                readability: 9,
                aesthetics: 8,
                productivity: 8.5,
              },
              sampleSize: 15,
            },
          },
        },
      },
    },
  }
}

// Generate unique test data for this test run
const testData = generateUniqueTestData()
export const TEST_USERS = testData.TEST_USERS
export const TEST_COMPANIES = testData.TEST_COMPANIES
export const TEST_THEME_CONTEXTS = testData.TEST_THEME_CONTEXTS

/**
 * Initialize test databases
 */
export async function setupTestDatabases(): Promise<void> {
  console.log('📊 Setting up test databases...')

  try {
    // Initialize Prisma with test database
    testPrisma = new PrismaClient({
      datasources: {
        db: {
          url: TEST_DATABASE_URL,
        },
      },
    })

    // Initialize Redis with test database
    testRedis = new Redis(TEST_REDIS_URL, {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    })

    // Test connections
    await testPrisma.$connect()
    await testRedis.ping()

    console.log('✅ Test databases setup complete')
  } catch (error) {
    console.error('❌ Test database setup failed:', error)
    throw error
  }
}

/**
 * Clean all test data
 */
export async function cleanTestData(): Promise<void> {
  try {
    // Clean in reverse dependency order
    await testPrisma.userContext.deleteMany({
      where: { userId: { startsWith: 'test-' } },
    })

    await testPrisma.user.deleteMany({
      where: { id: { startsWith: 'test-' } },
    })

    await testPrisma.company.deleteMany({
      where: { id: { startsWith: 'test-' } },
    })

    // Clean Redis test data
    const keys = await testRedis.keys('theme:*:test-*')
    const aiKeys = await testRedis.keys('ai:*:test-*')
    const allKeys = [...keys, ...aiKeys]
    if (allKeys.length > 0) {
      await testRedis.del(...allKeys)
    }

    console.log('✅ Test data cleaned')
  } catch (error) {
    console.error('❌ Test data cleanup failed:', error)
    throw error
  }
}

/**
 * Seed test data with atomic transaction
 */
export async function seedTestData(): Promise<void> {
  try {
    // Use transaction for atomic operations to prevent race conditions
    await testPrisma.$transaction(async tx => {
      // Create test company with upsert to handle duplicates
      await tx.company.upsert({
        where: { id: TEST_COMPANIES.primary.id },
        update: {
          name: TEST_COMPANIES.primary.name,
          domains: TEST_COMPANIES.primary.domains,
          subscriptionStatus: TEST_COMPANIES.primary.subscriptionStatus,
          allowedEmailDomains: TEST_COMPANIES.primary.allowedEmailDomains,
          isActive: TEST_COMPANIES.primary.isActive,
        },
        create: {
          id: TEST_COMPANIES.primary.id,
          name: TEST_COMPANIES.primary.name,
          domains: TEST_COMPANIES.primary.domains,
          subscriptionStatus: TEST_COMPANIES.primary.subscriptionStatus,
          allowedEmailDomains: TEST_COMPANIES.primary.allowedEmailDomains,
          isActive: TEST_COMPANIES.primary.isActive,
        },
      })

      // Create test users with upsert to handle duplicates
      for (const userData of Object.values(TEST_USERS)) {
        await tx.user.upsert({
          where: { id: userData.id },
          update: {
            email: userData.email,
            name: userData.name,
            role: userData.role,
            companyId: userData.companyId,
            themeMode: userData.themeMode,
            colorScheme: userData.colorScheme,
          },
          create: {
            ...userData,
            password: await bcrypt.hash('test123', 10),
            emailVerified: new Date(),
            onboardingCompleted: true,
          },
        })
      }

      // Create user contexts with theme data using upsert
      for (const [key, contextData] of Object.entries(TEST_THEME_CONTEXTS)) {
        await tx.userContext.upsert({
          where: { userId: contextData.userId },
          update: {
            role: contextData.role,
            companyId: contextData.companyId,
            preferences: contextData.preferences,
            recentActions: contextData.recentActions,
            historicalData: contextData.historicalData,
          },
          create: contextData,
        })
      }
    })

    console.log('✅ Test data seeded atomically')
  } catch (error) {
    console.error('❌ Test data seeding failed:', error)
    throw error
  }
}

/**
 * Teardown test databases
 */
export async function teardownTestDatabases(): Promise<void> {
  try {
    await cleanTestData()

    if (testPrisma) {
      await testPrisma.$disconnect()
    }

    if (testRedis) {
      testRedis.disconnect()
    }

    console.log('✅ Test database teardown complete')
  } catch (error) {
    console.error('❌ Test database teardown failed:', error)
    throw error
  }
}

// Global test setup - only database connections, not data
beforeAll(async () => {
  await setupTestDatabases()
}, 30000) // 30 second timeout

afterAll(async () => {
  await teardownTestDatabases()
}, 30000)

// Each test file should handle its own data lifecycle using:
// beforeEach(() => { cleanTestData(); seedTestData(); })
// afterEach(() => { cleanTestData(); })
