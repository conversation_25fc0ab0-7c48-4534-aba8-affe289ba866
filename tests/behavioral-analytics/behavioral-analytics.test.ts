/**
 * Behavioral Analytics System Tests
 * Tests for Phase 3: Intelligence Layer - Task 3.3
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  BehavioralAnalyticsService,
  behavioralAnalyticsService,
  trackUserBehavior,
  getUserBehaviorPatterns,
  generateUserAnalyticsReport,
} from '@/services/behavioral-analytics'
import { UserBehaviorEvent, BehaviorPattern } from '@/types/intelligence'

// Mock dependencies
vi.mock('@/lib/feature-flags', () => ({
  hasFeature: vi.fn().mockResolvedValue(true),
}))

vi.mock('@/services/context-service', () => ({
  contextService: {
    updateUserActivity: vi.fn().mockResolvedValue(true),
  },
}))

describe('🧠 Behavioral Analytics System', () => {
  let analyticsService: BehavioralAnalyticsService
  const mockUserId = 'test-user-123'
  const mockCompanyId = 'test-company-456'
  const mockSessionId = 'test-session-789'

  beforeEach(() => {
    analyticsService = BehavioralAnalyticsService.getInstance()
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Clear any cached data
    vi.clearAllMocks()
  })

  describe('Service Initialization', () => {
    it('should create singleton instance', () => {
      const instance1 = BehavioralAnalyticsService.getInstance()
      const instance2 = BehavioralAnalyticsService.getInstance()
      expect(instance1).toBe(instance2)
    })

    it('should export service instance', () => {
      expect(behavioralAnalyticsService).toBeInstanceOf(BehavioralAnalyticsService)
    })
  })

  describe('Event Tracking', () => {
    const mockConfig = {
      userId: mockUserId,
      companyId: mockCompanyId,
      sessionId: mockSessionId,
      options: {
        enableRealTime: true,
        enablePatternDetection: true,
      },
    }

    it('should track page visit events', async () => {
      const page = '/dashboard'
      const metadata = { title: 'Dashboard' }

      await analyticsService.trackPageVisit(mockConfig, page, metadata)

      // Verify event was processed (would check database in real implementation)
      expect(true).toBe(true) // Placeholder assertion
    })

    it('should track interaction events', async () => {
      const element = 'theme-toggle-button'
      const action = 'click'
      const metadata = { x: 100, y: 200 }

      await analyticsService.trackInteraction(mockConfig, element, action, metadata)

      // Verify event was processed
      expect(true).toBe(true) // Placeholder assertion
    })

    it('should track feature usage events', async () => {
      const feature = 'theme_switch'
      const action = 'toggle'
      const metadata = { from: 'light', to: 'dark', duration: 1500 }

      await analyticsService.trackFeatureUsage(mockConfig, feature, action, metadata)

      // Verify event was processed
      expect(true).toBe(true) // Placeholder assertion
    })

    it('should track error events', async () => {
      const error = 'Network request failed'
      const context = 'api_call'
      const metadata = { status: 500, url: '/api/data' }

      await analyticsService.trackError(mockConfig, error, context, metadata)

      // Verify event was processed
      expect(true).toBe(true) // Placeholder assertion
    })

    it('should handle tracking failures gracefully', async () => {
      // Mock feature flag to return false (analytics disabled)
      const { hasFeature } = await import('@/lib/feature-flags')
      vi.mocked(hasFeature).mockResolvedValueOnce(false)

      // Should not throw error when analytics is disabled
      await expect(analyticsService.trackPageVisit(mockConfig, '/test')).resolves.not.toThrow()
    })
  })

  describe('Pattern Detection', () => {
    it('should detect navigation patterns', async () => {
      const patterns = await analyticsService.getUserPatterns(mockUserId, mockCompanyId)

      expect(Array.isArray(patterns)).toBe(true)
      // In real implementation, would verify specific pattern types
    })

    it('should detect usage patterns', async () => {
      const patterns = await analyticsService.getUserPatterns(mockUserId, mockCompanyId)

      // Should return patterns array
      expect(patterns).toBeDefined()
      expect(Array.isArray(patterns)).toBe(true)
    })

    it('should cache pattern results', async () => {
      const timeRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-07'),
      }

      // First call
      const patterns1 = await analyticsService.getUserPatterns(mockUserId, mockCompanyId, timeRange)

      // Second call should use cache
      const patterns2 = await analyticsService.getUserPatterns(mockUserId, mockCompanyId, timeRange)

      expect(patterns1).toEqual(patterns2)
    })

    it('should return empty patterns when analytics disabled', async () => {
      const { hasFeature } = await import('@/lib/feature-flags')
      vi.mocked(hasFeature).mockResolvedValueOnce(false)

      const patterns = await analyticsService.getUserPatterns(mockUserId, mockCompanyId)

      expect(patterns).toEqual([])
    })
  })

  describe('Analytics Reports', () => {
    const timeRange = {
      start: new Date('2024-01-01'),
      end: new Date('2024-01-07'),
    }

    it('should generate analytics report', async () => {
      const report = await analyticsService.generateAnalyticsReport(
        mockUserId,
        mockCompanyId,
        timeRange
      )

      expect(report).toBeDefined()
      expect(report.userId).toBe(mockUserId)
      expect(report.timeRange).toEqual(timeRange)
      expect(typeof report.totalEvents).toBe('number')
      expect(typeof report.uniqueSessions).toBe('number')
      expect(Array.isArray(report.patterns)).toBe(true)
      expect(Array.isArray(report.insights)).toBe(true)
      expect(Array.isArray(report.topPages)).toBe(true)
      expect(Array.isArray(report.topFeatures)).toBe(true)
      expect(typeof report.errorRate).toBe('number')
      expect(typeof report.engagementScore).toBe('number')
      expect(report.generatedAt).toBeInstanceOf(Date)
    })

    it('should return empty report when analytics disabled', async () => {
      const { hasFeature } = await import('@/lib/feature-flags')
      vi.mocked(hasFeature).mockResolvedValueOnce(false)

      const report = await analyticsService.generateAnalyticsReport(
        mockUserId,
        mockCompanyId,
        timeRange
      )

      expect(report.totalEvents).toBe(0)
      expect(report.uniqueSessions).toBe(0)
      expect(report.patterns).toEqual([])
      expect(report.insights).toContain('Analytics not available - feature may be disabled')
    })

    it('should calculate engagement score correctly', async () => {
      const report = await analyticsService.generateAnalyticsReport(
        mockUserId,
        mockCompanyId,
        timeRange
      )

      expect(report.engagementScore).toBeGreaterThanOrEqual(0)
      expect(report.engagementScore).toBeLessThanOrEqual(1)
    })

    it('should calculate error rate correctly', async () => {
      const report = await analyticsService.generateAnalyticsReport(
        mockUserId,
        mockCompanyId,
        timeRange
      )

      expect(report.errorRate).toBeGreaterThanOrEqual(0)
      expect(report.errorRate).toBeLessThanOrEqual(1)
    })
  })

  describe('Utility Functions', () => {
    it('should export trackUserBehavior function', async () => {
      const config = {
        userId: mockUserId,
        companyId: mockCompanyId,
        sessionId: mockSessionId,
      }

      const event = {
        type: 'page_visit' as const,
        action: 'navigate',
        target: '/test',
        metadata: { test: true },
      }

      await expect(trackUserBehavior(config, event)).resolves.not.toThrow()
    })

    it('should export getUserBehaviorPatterns function', async () => {
      const patterns = await getUserBehaviorPatterns(mockUserId, mockCompanyId)

      expect(Array.isArray(patterns)).toBe(true)
    })

    it('should export generateUserAnalyticsReport function', async () => {
      const timeRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-07'),
      }

      const report = await generateUserAnalyticsReport(mockUserId, mockCompanyId, timeRange)

      expect(report).toBeDefined()
      expect(report.userId).toBe(mockUserId)
    })
  })

  describe('Pattern Analysis', () => {
    it('should detect navigation sequences', () => {
      // Test navigation pattern detection logic
      const mockEvents: UserBehaviorEvent[] = [
        {
          id: '1',
          userId: mockUserId,
          sessionId: mockSessionId,
          timestamp: new Date('2024-01-01T10:00:00Z'),
          type: 'page_visit',
          action: 'navigate',
          target: '/dashboard',
          metadata: {},
        },
        {
          id: '2',
          userId: mockUserId,
          sessionId: mockSessionId,
          timestamp: new Date('2024-01-01T10:01:00Z'),
          type: 'page_visit',
          action: 'navigate',
          target: '/settings',
          metadata: {},
        },
        {
          id: '3',
          userId: mockUserId,
          sessionId: mockSessionId,
          timestamp: new Date('2024-01-01T10:02:00Z'),
          type: 'page_visit',
          action: 'navigate',
          target: '/dashboard',
          metadata: {},
        },
      ]

      // In real implementation, would test pattern detection algorithms
      expect(mockEvents.length).toBe(3)
      expect(mockEvents[0].type).toBe('page_visit')
    })

    it('should detect feature usage patterns', () => {
      const mockEvents: UserBehaviorEvent[] = [
        {
          id: '1',
          userId: mockUserId,
          sessionId: mockSessionId,
          timestamp: new Date(),
          type: 'feature_usage',
          action: 'toggle',
          target: 'theme_switch',
          metadata: { feature: 'theme_switch' },
        },
      ]

      expect(mockEvents[0].type).toBe('feature_usage')
      expect(mockEvents[0].target).toBe('theme_switch')
    })

    it('should detect temporal patterns', () => {
      const now = new Date()
      const mockEvents: UserBehaviorEvent[] = Array.from({ length: 10 }, (_, i) => ({
        id: `event_${i}`,
        userId: mockUserId,
        sessionId: mockSessionId,
        timestamp: new Date(now.getTime() + i * 3600000), // 1 hour apart
        type: 'page_visit',
        action: 'navigate',
        target: '/dashboard',
        metadata: {},
      }))

      // Should have events spread across different hours
      const hours = mockEvents.map(e => e.timestamp.getHours())
      const uniqueHours = new Set(hours)
      expect(uniqueHours.size).toBeGreaterThan(1)
    })
  })

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      // Mock an error in the service
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // Force an error by passing invalid data
      await analyticsService.trackEvent(
        {
          userId: '',
          companyId: '',
          sessionId: '',
        },
        {
          type: 'page_visit',
          action: 'navigate',
          target: '/test',
        }
      )

      // Should not throw, but may log error
      expect(true).toBe(true)

      consoleSpy.mockRestore()
    })

    it('should handle pattern detection errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // Should return empty array on error
      const patterns = await analyticsService.getUserPatterns('', '')
      expect(Array.isArray(patterns)).toBe(true)

      consoleSpy.mockRestore()
    })

    it('should handle report generation errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const timeRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-07'),
      }

      // Should return report with mock data even with invalid inputs (robust fallback)
      const report = await analyticsService.generateAnalyticsReport('', '', timeRange)
      expect(report.totalEvents).toBeGreaterThanOrEqual(0) // Should handle gracefully
      expect(report).toBeDefined()
      expect(report.userId).toBe('') // Should preserve the input userId

      consoleSpy.mockRestore()
    })
  })

  describe('Integration with Feature Flags', () => {
    it('should respect feature flag settings', async () => {
      const { hasFeature } = await import('@/lib/feature-flags')

      // Test when feature is enabled
      vi.mocked(hasFeature).mockResolvedValueOnce(true)
      const patterns1 = await analyticsService.getUserPatterns(mockUserId, mockCompanyId)
      expect(Array.isArray(patterns1)).toBe(true)

      // Test when feature is disabled
      vi.mocked(hasFeature).mockResolvedValueOnce(false)
      const patterns2 = await analyticsService.getUserPatterns(mockUserId, mockCompanyId)
      expect(patterns2).toEqual([])
    })

    it('should check feature flags for each operation', async () => {
      const { hasFeature } = await import('@/lib/feature-flags')
      const hasFeatureSpy = vi.mocked(hasFeature)

      await analyticsService.getUserPatterns(mockUserId, mockCompanyId)
      expect(hasFeatureSpy).toHaveBeenCalledWith(mockCompanyId, 'behavioralAnalytics')

      await analyticsService.generateAnalyticsReport(mockUserId, mockCompanyId, {
        start: new Date(),
        end: new Date(),
      })
      expect(hasFeatureSpy).toHaveBeenCalledWith(mockCompanyId, 'behavioralAnalytics')
    })
  })
})
