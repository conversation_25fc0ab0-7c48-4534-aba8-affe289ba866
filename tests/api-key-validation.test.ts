import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/api-key'
import { beforeAll, describe, expect, it } from 'vitest'

describe('API Key Validation - Critical Fixes', () => {
  beforeAll(() => {
    // Ensure environment variables are set
    process.env.SUPERADMIN_API_KEY = 'emynent-superadmin-key-2025-secure-dev-12345'
    process.env.NEXT_PUBLIC_SUPERADMIN_API_KEY = 'emynent-superadmin-key-2025-secure-dev-12345'
  })

  it('should validate correct API key from NextRequest', async () => {
    // Mock NextRequest with correct API key
    const request = {
      headers: {
        get: (key: string) => {
          if (key === 'x-api-key') {
            return 'emynent-superadmin-key-2025-secure-dev-12345'
          }
          return null
        },
      },
    }

    const result = await validateApiKey(request)
    expect(result).toBe(true)
  })

  it('should reject incorrect API key', async () => {
    // Mock NextRequest with wrong API key
    const request = {
      headers: {
        get: (key: string) => {
          if (key === 'x-api-key') {
            return 'wrong-api-key'
          }
          return null
        },
      },
    }

    const result = await validateApiKey(request)
    expect(result).toBe(false)
  })

  it('should reject missing API key', async () => {
    // Mock NextRequest without API key
    const request = {
      headers: {
        get: (key: string) => null,
      },
    }

    const result = await validateApiKey(request)
    expect(result).toBe(false)
  })

  it('should validate correct API key from Express request', async () => {
    // Mock Express request with correct API key
    const request = {
      headers: {
        'x-api-key': 'emynent-superadmin-key-2025-secure-dev-12345',
      },
    }

    const result = await validateApiKey(request)
    expect(result).toBe(true)
  })

  it('should handle malformed request gracefully', async () => {
    // Mock malformed request
    const request = {}

    const result = await validateApiKey(request)
    expect(result).toBe(false)
  })

  it('should have environment variables set correctly', () => {
    expect(process.env.SUPERADMIN_API_KEY).toBe('emynent-superadmin-key-2025-secure-dev-12345')
    expect(process.env.NEXT_PUBLIC_SUPERADMIN_API_KEY).toBe(
      'emynent-superadmin-key-2025-secure-dev-12345'
    )
  })
})
