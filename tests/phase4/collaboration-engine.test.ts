/**
 * Phase 4.1: Real-Time Collaboration Engine Tests
 * Comprehensive testing for collaboration functionality
 * Tests real WebSocket connections, session management, and real-time features
 * FOLLOWS TDD PRINCIPLES: Real WebSocket server, real collaboration engine, no mocks
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { WebSocket, WebSocketServer } from 'ws'
import {
  RealTimeCollaborationEngine,
  CollaborationEvent,
  CollaborationSession,
} from '@/lib/collaboration/real-time-engine'
import { prisma } from '@/lib/prisma'
import { redis } from '@/lib/redis'

// Test WebSocket server setup
let wsServer: WebSocketServer
let wsPort: number

// Test data setup
const testUser1 = {
  id: 'test-user-collab-1',
  email: '<EMAIL>',
  name: 'Collaboration Test User 1',
  companyId: 'test-company-collab',
}

const testUser2 = {
  id: 'test-user-collab-2',
  email: '<EMAIL>',
  name: 'Collaboration Test User 2',
  companyId: 'test-company-collab',
}

const testCompany = {
  id: 'test-company-collab',
  name: 'Collaboration Test Company',
  domains: ['collab-test.com'],
}

describe('Phase 4.1: Real-Time Collaboration Engine', () => {
  let engine: RealTimeCollaborationEngine

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.user.deleteMany({
      where: { id: { in: [testUser1.id, testUser2.id] } },
    })
    await prisma.company.deleteMany({
      where: { id: testCompany.id },
    })

    // Create test company and users
    await prisma.company.create({
      data: testCompany,
    })

    await prisma.user.createMany({
      data: [testUser1, testUser2],
    })

    // Clear Redis cache
    await redis.flushdb()

    // Setup WebSocket server for testing
    wsPort = 8080 + Math.floor(Math.random() * 1000)
    wsServer = new WebSocketServer({ port: wsPort })

    // Initialize collaboration engine
    engine = new RealTimeCollaborationEngine()

    // Setup WebSocket server to handle collaboration messages
    wsServer.on('connection', ws => {
      let registeredUserId: string | null = null
      let registeredSessionId: string | null = null

      ws.on('message', async data => {
        try {
          const message = JSON.parse(data.toString())

          switch (message.type) {
            case 'register':
              // Register client with collaboration session
              registeredUserId = message.userId
              registeredSessionId = message.sessionId
              engine.registerConnection(message.userId, ws)
              ws.send(
                JSON.stringify({
                  type: 'registered',
                  sessionId: message.sessionId,
                  userId: message.userId,
                })
              )
              break

            case 'cursor_update':
              await engine.updateCursor(message.sessionId, message.userId, message.x, message.y)
              // Broadcast to other clients in the session
              wsServer.clients.forEach(client => {
                if (client !== ws && client.readyState === WebSocket.OPEN) {
                  client.send(
                    JSON.stringify({
                      type: 'cursor_update',
                      userId: message.userId,
                      x: message.x,
                      y: message.y,
                    })
                  )
                }
              })
              break

            case 'content_change':
              await engine.handleContentChange(message.sessionId, message.userId, message.changes)
              // Broadcast to other clients in the session
              wsServer.clients.forEach(client => {
                if (client !== ws && client.readyState === WebSocket.OPEN) {
                  client.send(
                    JSON.stringify({
                      type: 'content_change',
                      userId: message.userId,
                      changes: message.changes,
                    })
                  )
                }
              })
              break
          }
        } catch (error) {
          console.error('WebSocket message error:', error)
        }
      })
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.user.deleteMany({
      where: { id: { in: [testUser1.id, testUser2.id] } },
    })
    await prisma.company.deleteMany({
      where: { id: testCompany.id },
    })

    // Clear Redis cache
    await redis.flushdb()

    // Close WebSocket server
    if (wsServer) {
      wsServer.close()
    }
  })

  describe('Session Management', () => {
    it('should create a new collaboration session', async () => {
      const documentId = 'doc_123'
      const userId = testUser1.id
      const userName = testUser1.name

      const session = await engine.createSession(documentId, userId, userName)

      expect(session).toBeDefined()
      expect(session.documentId).toBe(documentId)
      expect(session.participants).toHaveLength(1)
      expect(session.participants[0].userId).toBe(userId)
      expect(session.participants[0].name).toBe(userName)
      expect(session.createdAt).toBeInstanceOf(Date)
      expect(session.updatedAt).toBeInstanceOf(Date)
    })

    it('should allow users to join existing sessions', async () => {
      // Create initial session
      const session = await engine.createSession('doc_123', testUser1.id, testUser1.name)

      // Join session with another user
      const joinedSession = await engine.joinSession(session.id, testUser2.id, testUser2.name)

      expect(joinedSession).toBeDefined()
      expect(joinedSession!.participants).toHaveLength(2)
      expect(joinedSession!.participants.find(p => p.userId === testUser2.id)).toBeDefined()
    })

    it('should not duplicate participants when joining same session twice', async () => {
      const session = await engine.createSession('doc_123', testUser1.id, testUser1.name)

      // Join twice with same user
      await engine.joinSession(session.id, testUser1.id, testUser1.name)
      const finalSession = await engine.joinSession(session.id, testUser1.id, testUser1.name)

      expect(finalSession!.participants).toHaveLength(1)
    })

    it('should return null when joining non-existent session', async () => {
      const result = await engine.joinSession('non_existent', testUser1.id, testUser1.name)
      expect(result).toBeNull()
    })

    it('should allow users to leave sessions', async () => {
      const session = await engine.createSession('doc_123', testUser1.id, testUser1.name)
      await engine.joinSession(session.id, testUser2.id, testUser2.name)

      const success = await engine.leaveSession(session.id, testUser1.id)

      expect(success).toBe(true)
      const updatedSession = await engine.getSession(session.id)
      expect(updatedSession!.participants).toHaveLength(1)
      expect(updatedSession!.participants[0].userId).toBe(testUser2.id)
    })

    it('should clean up empty sessions when last user leaves', async () => {
      const session = await engine.createSession('doc_123', testUser1.id, testUser1.name)

      await engine.leaveSession(session.id, testUser1.id)

      const deletedSession = await engine.getSession(session.id)
      expect(deletedSession).toBeUndefined()
    })

    it('should return false when leaving non-existent session', async () => {
      const success = await engine.leaveSession('non_existent', testUser1.id)
      expect(success).toBe(false)
    })
  })

  describe('Real-time Features', () => {
    let session: CollaborationSession

    beforeEach(async () => {
      session = await engine.createSession('doc_123', testUser1.id, testUser1.name)
      await engine.joinSession(session.id, testUser2.id, testUser2.name)
    })

    it('should update cursor positions', async () => {
      const success = await engine.updateCursor(session.id, testUser1.id, 100, 200)

      expect(success).toBe(true)
      const updatedSession = await engine.getSession(session.id)
      const participant = updatedSession!.participants.find(p => p.userId === testUser1.id)
      expect(participant!.cursor).toEqual({ x: 100, y: 200 })
    })

    it('should return false when updating cursor for non-existent session', async () => {
      const success = await engine.updateCursor('non_existent', testUser1.id, 100, 200)
      expect(success).toBe(false)
    })

    it('should return false when updating cursor for non-participant', async () => {
      const success = await engine.updateCursor(session.id, 'non_participant', 100, 200)
      expect(success).toBe(false)
    })

    it('should handle content changes', async () => {
      const changes = { type: 'insert', position: 10, content: 'Hello World' }
      const success = await engine.handleContentChange(session.id, testUser1.id, changes)

      expect(success).toBe(true)
    })

    it('should return false when handling content change for non-existent session', async () => {
      const changes = { type: 'insert', position: 10, content: 'Hello World' }
      const success = await engine.handleContentChange('non_existent', testUser1.id, changes)
      expect(success).toBe(false)
    })
  })

  describe('WebSocket Integration', () => {
    let client1: WebSocket
    let client2: WebSocket
    let session: CollaborationSession

    beforeEach(async () => {
      session = await engine.createSession('doc_websocket_test', testUser1.id, testUser1.name)

      // Setup WebSocket clients
      client1 = new WebSocket(`ws://localhost:${wsPort}`)
      client2 = new WebSocket(`ws://localhost:${wsPort}`)
    })

    afterEach(() => {
      if (client1.readyState === WebSocket.OPEN) {
        client1.close()
      }
      if (client2.readyState === WebSocket.OPEN) {
        client2.close()
      }
    })

    it('should establish WebSocket connections', async () => {
      const client1Promise = new Promise<void>(resolve => {
        client1.on('open', () => {
          expect(client1.readyState).toBe(WebSocket.OPEN)
          resolve()
        })
      })

      const client2Promise = new Promise<void>(resolve => {
        client2.on('open', () => {
          expect(client2.readyState).toBe(WebSocket.OPEN)
          resolve()
        })
      })

      await Promise.all([client1Promise, client2Promise])
    })

    it('should register clients with collaboration session', async () => {
      const registrationPromise = new Promise<void>(resolve => {
        client1.on('message', (data: Buffer) => {
          const message = JSON.parse(data.toString())

          if (message.type === 'registered') {
            expect(message.sessionId).toBe(session.id)
            expect(message.userId).toBe(testUser1.id)
            resolve()
          }
        })
      })

      client1.on('open', () => {
        // Register client with session
        const registerMessage = {
          type: 'register',
          sessionId: session.id,
          userId: testUser1.id,
          userName: testUser1.name,
        }

        client1.send(JSON.stringify(registerMessage))
      })

      await registrationPromise
    })

    it('should broadcast cursor updates to other participants', async () => {
      const cursorUpdatePromise = new Promise<void>(resolve => {
        let client1Ready = false
        let client2Ready = false

        const checkReady = () => {
          if (client1Ready && client2Ready) {
            // Send cursor update from client1
            const cursorUpdate = {
              type: 'cursor_update',
              sessionId: session.id,
              userId: testUser1.id,
              x: 150,
              y: 250,
            }

            client1.send(JSON.stringify(cursorUpdate))
          }
        }

        client1.on('message', (data: Buffer) => {
          const message = JSON.parse(data.toString())
          if (message.type === 'registered') {
            client1Ready = true
            checkReady()
          }
        })

        client2.on('message', (data: Buffer) => {
          const message = JSON.parse(data.toString())

          if (message.type === 'registered') {
            client2Ready = true
            checkReady()
          } else if (message.type === 'cursor_update') {
            expect(message.userId).toBe(testUser1.id)
            expect(message.x).toBe(150)
            expect(message.y).toBe(250)
            resolve()
          }
        })
      })

      client1.on('open', () => {
        const registerMessage = {
          type: 'register',
          sessionId: session.id,
          userId: testUser1.id,
          userName: testUser1.name,
        }
        client1.send(JSON.stringify(registerMessage))
      })

      client2.on('open', () => {
        const registerMessage = {
          type: 'register',
          sessionId: session.id,
          userId: testUser2.id,
          userName: testUser2.name,
        }
        client2.send(JSON.stringify(registerMessage))
      })

      await cursorUpdatePromise
    })

    it('should broadcast content changes to participants', async () => {
      const contentChangePromise = new Promise<void>(resolve => {
        let client1Ready = false
        let client2Ready = false

        const checkReady = () => {
          if (client1Ready && client2Ready) {
            // Send content change from client1
            const contentChange = {
              type: 'content_change',
              sessionId: session.id,
              userId: testUser1.id,
              changes: {
                type: 'insert',
                position: 5,
                content: 'Hello WebSocket!',
              },
            }

            client1.send(JSON.stringify(contentChange))
          }
        }

        client1.on('message', (data: Buffer) => {
          const message = JSON.parse(data.toString())
          if (message.type === 'registered') {
            client1Ready = true
            checkReady()
          }
        })

        client2.on('message', (data: Buffer) => {
          const message = JSON.parse(data.toString())

          if (message.type === 'registered') {
            client2Ready = true
            checkReady()
          } else if (message.type === 'content_change') {
            expect(message.userId).toBe(testUser1.id)
            expect(message.changes.type).toBe('insert')
            expect(message.changes.content).toBe('Hello WebSocket!')
            resolve()
          }
        })
      })

      client1.on('open', () => {
        const registerMessage = {
          type: 'register',
          sessionId: session.id,
          userId: testUser1.id,
          userName: testUser1.name,
        }
        client1.send(JSON.stringify(registerMessage))
      })

      client2.on('open', () => {
        const registerMessage = {
          type: 'register',
          sessionId: session.id,
          userId: testUser2.id,
          userName: testUser2.name,
        }
        client2.send(JSON.stringify(registerMessage))
      })

      await contentChangePromise
    })

    it('should handle client disconnections gracefully', async () => {
      // First, add a second participant to ensure session doesn't get deleted
      await engine.joinSession(session.id, testUser2.id, testUser2.name)

      const disconnectionPromise = new Promise<void>(resolve => {
        client1.on('message', (data: Buffer) => {
          const message = JSON.parse(data.toString())

          if (message.type === 'registered') {
            // Close connection to simulate disconnection
            client1.close()

            // Verify session still exists but participant is removed
            setTimeout(async () => {
              const updatedSession = await engine.getSession(session.id)
              expect(updatedSession).toBeDefined()
              if (updatedSession) {
                expect(
                  updatedSession.participants.find(p => p.userId === testUser1.id)
                ).toBeUndefined()
                // Verify testUser2 is still in the session
                expect(
                  updatedSession.participants.find(p => p.userId === testUser2.id)
                ).toBeDefined()
              }
              resolve()
            }, 100)
          }
        })
      })

      client1.on('open', () => {
        const registerMessage = {
          type: 'register',
          sessionId: session.id,
          userId: testUser1.id,
          userName: testUser1.name,
        }
        client1.send(JSON.stringify(registerMessage))
      })

      await disconnectionPromise
    })
  })

  describe('Persistence and Recovery', () => {
    it('should persist session data in Redis', async () => {
      const session = await engine.createSession('doc_persist', testUser1.id, testUser1.name)

      // Check if session is stored in Redis
      const redisKey = `collaboration:session:${session.id}`
      const storedSession = await redis.get(redisKey)

      expect(storedSession).toBeDefined()
      const parsedSession = JSON.parse(storedSession!)
      expect(parsedSession.id).toBe(session.id)
      expect(parsedSession.documentId).toBe('doc_persist')
    })

    it('should recover sessions from Redis on restart', async () => {
      const session = await engine.createSession('doc_recover', testUser1.id, testUser1.name)
      const originalSessionId = session.id

      // Create new engine instance to simulate restart
      const newEngine = new RealTimeCollaborationEngine()

      // Should be able to recover session
      const recoveredSession = await newEngine.getSession(originalSessionId)
      expect(recoveredSession).toBeDefined()
      expect(recoveredSession!.documentId).toBe('doc_recover')
    })

    it('should handle Redis failures gracefully', async () => {
      // Temporarily break Redis connection
      const originalSet = redis.set
      redis.set = () => {
        throw new Error('Redis connection failed')
      }

      // Should still create session in memory
      const session = await engine.createSession('doc_redis_fail', testUser1.id, testUser1.name)
      expect(session).toBeDefined()
      expect(session.documentId).toBe('doc_redis_fail')

      // Restore Redis
      redis.set = originalSet
    })
  })

  describe('Conflict Resolution', () => {
    let session: CollaborationSession

    beforeEach(async () => {
      session = await engine.createSession('doc_conflict', testUser1.id, testUser1.name)
      await engine.joinSession(session.id, testUser2.id, testUser2.name)
    })

    it('should handle simultaneous content changes', async () => {
      const change1 = { type: 'insert', position: 10, content: 'User1 Text' }
      const change2 = { type: 'insert', position: 10, content: 'User2 Text' }

      // Simulate simultaneous changes
      const [result1, result2] = await Promise.all([
        engine.handleContentChange(session.id, testUser1.id, change1),
        engine.handleContentChange(session.id, testUser2.id, change2),
      ])

      expect(result1).toBe(true)
      expect(result2).toBe(true)

      // Both changes should be processed
      const updatedSession = await engine.getSession(session.id)
      expect(updatedSession).toBeDefined()
    })

    it('should resolve cursor position conflicts', async () => {
      // Both users update cursor to same position
      await Promise.all([
        engine.updateCursor(session.id, testUser1.id, 100, 100),
        engine.updateCursor(session.id, testUser2.id, 100, 100),
      ])

      const updatedSession = await engine.getSession(session.id)
      const user1Cursor = updatedSession!.participants.find(p => p.userId === testUser1.id)!.cursor
      const user2Cursor = updatedSession!.participants.find(p => p.userId === testUser2.id)!.cursor

      expect(user1Cursor).toEqual({ x: 100, y: 100 })
      expect(user2Cursor).toEqual({ x: 100, y: 100 })
    })

    it('should maintain operation order for conflict resolution', async () => {
      const operations = [
        { type: 'insert', position: 0, content: 'A' },
        { type: 'insert', position: 1, content: 'B' },
        { type: 'insert', position: 2, content: 'C' },
      ]

      // Apply operations in sequence
      for (const op of operations) {
        await engine.handleContentChange(session.id, testUser1.id, op)
      }

      // Verify operations were applied in order
      const updatedSession = await engine.getSession(session.id)
      expect(updatedSession).toBeDefined()
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent sessions', async () => {
      const sessionPromises = []

      // Create 10 concurrent sessions
      for (let i = 0; i < 10; i++) {
        sessionPromises.push(
          engine.createSession(`doc_concurrent_${i}`, testUser1.id, testUser1.name)
        )
      }

      const sessions = await Promise.all(sessionPromises)

      expect(sessions).toHaveLength(10)
      sessions.forEach((session, index) => {
        expect(session.documentId).toBe(`doc_concurrent_${index}`)
      })
    })

    it('should handle high-frequency cursor updates', async () => {
      const session = await engine.createSession('doc_high_freq', testUser1.id, testUser1.name)

      const updatePromises = []

      // Send 100 cursor updates rapidly
      for (let i = 0; i < 100; i++) {
        updatePromises.push(engine.updateCursor(session.id, testUser1.id, i, i))
      }

      const results = await Promise.all(updatePromises)

      // All updates should succeed
      expect(results.every(result => result === true)).toBe(true)

      // Final cursor position should be the last update
      const updatedSession = await engine.getSession(session.id)
      const participant = updatedSession!.participants.find(p => p.userId === testUser1.id)
      expect(participant!.cursor).toEqual({ x: 99, y: 99 })
    })

    it('should clean up inactive sessions', async () => {
      const session = await engine.createSession('doc_cleanup', testUser1.id, testUser1.name)

      // Simulate session inactivity by manually setting old timestamp
      const updatedSession = await engine.getSession(session.id)
      if (updatedSession) {
        updatedSession.updatedAt = new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
      }

      // Trigger cleanup (this would normally be done by a background job)
      await engine.cleanupInactiveSessions(60 * 60 * 1000) // 1 hour threshold

      // Session should be removed
      const cleanedSession = await engine.getSession(session.id)
      expect(cleanedSession).toBeUndefined()
    })
  })

  describe('Security and Validation', () => {
    it('should validate user permissions for session access', async () => {
      const session = await engine.createSession('doc_security', testUser1.id, testUser1.name)

      // Try to join with invalid user
      const result = await engine.joinSession(session.id, 'invalid-user', 'Invalid User')

      // Should handle gracefully (implementation dependent)
      expect(typeof result).toBe('object')
    })

    it('should sanitize content changes', async () => {
      const session = await engine.createSession('doc_sanitize', testUser1.id, testUser1.name)

      const maliciousChange = {
        type: 'insert',
        position: 0,
        content: '<script>alert("xss")</script>',
      }

      const success = await engine.handleContentChange(session.id, testUser1.id, maliciousChange)

      expect(success).toBe(true)
      // Content should be sanitized (implementation dependent)
    })

    it('should rate limit operations per user', async () => {
      const session = await engine.createSession('doc_rate_limit', testUser1.id, testUser1.name)

      const operations = []

      // Try to send many operations rapidly
      for (let i = 0; i < 1000; i++) {
        operations.push(
          engine.handleContentChange(session.id, testUser1.id, {
            type: 'insert',
            position: i,
            content: `Content ${i}`,
          })
        )
      }

      const results = await Promise.all(operations)

      // Some operations might be rate limited
      expect(results.some(result => result === true)).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid session IDs gracefully', async () => {
      const result = await engine.joinSession('invalid-session-id', testUser1.id, testUser1.name)
      expect(result).toBeNull()
    })

    it('should handle malformed WebSocket messages', async () => {
      const malformedMessagePromise = new Promise<void>(resolve => {
        const client = new WebSocket(`ws://localhost:${wsPort}`)

        client.on('open', () => {
          // Send malformed JSON
          client.send('invalid json')

          // Should not crash the server
          setTimeout(() => {
            expect(client.readyState).toBe(WebSocket.OPEN)
            client.close()
            resolve()
          }, 100)
        })
      })

      await malformedMessagePromise
    })

    it('should recover from temporary network failures', async () => {
      const session = await engine.createSession('doc_network_fail', testUser1.id, testUser1.name)

      // Simulate network failure by temporarily breaking Redis
      const originalGet = redis.get
      redis.get = () => {
        throw new Error('Network failure')
      }

      // Operations should still work with fallback
      const success = await engine.updateCursor(session.id, testUser1.id, 50, 50)
      expect(typeof success).toBe('boolean')

      // Restore Redis
      redis.get = originalGet
    })
  })
})
