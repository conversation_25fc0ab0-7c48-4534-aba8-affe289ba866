import { describe, it, expect, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { AnalyticsDashboard } from '@/components/analytics/AnalyticsDashboard'

// NO MOCKS - Using real database and services
// This follows TDD principles with real functionality

describe('AnalyticsDashboard', () => {
  beforeEach(async () => {
    // Clean setup - no mocks, using real data from seeded database
  })

  it('should render the analytics dashboard', async () => {
    render(<AnalyticsDashboard />)

    // Wait for the dashboard to load with real data
    await waitFor(
      () => {
        expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })

  it('should display key metrics cards', async () => {
    render(<AnalyticsDashboard />)

    await waitFor(
      () => {
        // These should show real numbers from our seeded data
        expect(screen.getByText('Total Users')).toBeInTheDocument()
        expect(screen.getByText('Page Views')).toBeInTheDocument()
        expect(screen.getByText('Active Sessions')).toBeInTheDocument()
        expect(screen.getByText('Conversion Rate')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })

  it('should display charts with real data', async () => {
    render(<AnalyticsDashboard />)

    await waitFor(
      () => {
        // Check for chart containers
        expect(screen.getByText('User Activity Over Time')).toBeInTheDocument()
        expect(screen.getByText('Feature Usage')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })

  it('should show real metrics values from database', async () => {
    render(<AnalyticsDashboard />)

    await waitFor(
      () => {
        // Since we seeded 3 users, we should see real numbers
        const dashboard = screen.getByTestId('analytics-dashboard')
        expect(dashboard).toBeInTheDocument()

        // Look for actual metric values (not zeros or N/A)
        const metricCards = screen.getAllByTestId(/metric-card/)
        expect(metricCards.length).toBeGreaterThan(0)
      },
      { timeout: 10000 }
    )
  })

  it('should handle loading states properly', async () => {
    render(<AnalyticsDashboard />)

    // Should show loading initially
    expect(screen.getByText('Loading...')).toBeInTheDocument()

    // Then show real data
    await waitFor(
      () => {
        expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
        expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })

  it('should display time range selector', async () => {
    render(<AnalyticsDashboard />)

    await waitFor(
      () => {
        // Check for the default selected value
        expect(screen.getByText('Last 7 days')).toBeInTheDocument()
        // Check that the selector is present (it's a combobox)
        expect(screen.getByRole('combobox')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })

  it('should show export functionality', async () => {
    render(<AnalyticsDashboard />)

    await waitFor(
      () => {
        expect(screen.getByText('Export Data')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })

  it('should display real-time updates toggle', async () => {
    render(<AnalyticsDashboard />)

    await waitFor(
      () => {
        expect(screen.getByText('Real-time Updates')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })
})
