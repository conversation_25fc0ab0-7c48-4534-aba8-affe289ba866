{"timestamp": "2025-05-25T20:26:53.007Z", "summary": {"total": 42, "passed": 41, "failed": 0, "warnings": 1, "passRate": 97.6}, "details": [{"name": "File: src/types/design-system.ts", "status": "passed", "details": ""}, {"name": "File: src/lib/design-system/component-registry.ts", "status": "passed", "details": ""}, {"name": "File: src/app/(protected)/superadmin/design-system/page.tsx", "status": "passed", "details": ""}, {"name": "File: src/components/ui/separator.tsx", "status": "passed", "details": ""}, {"name": "File: src/app/api/superadmin/design-system/route.ts", "status": "passed", "details": ""}, {"name": "File: src/app/api/design-system/hot-swap/route.ts", "status": "passed", "details": ""}, {"name": "File: src/app/api/design-system/collaborate/route.ts", "status": "passed", "details": ""}, {"name": "File: src/app/api/design-system/analytics/usage/route.ts", "status": "passed", "details": ""}, {"name": "File: src/app/api/design-system/performance/metrics/route.ts", "status": "passed", "details": ""}, {"name": "File: src/app/api/websocket/design-system/route.ts", "status": "passed", "details": ""}, {"name": "File: src/app/(protected)/superadmin/design-system/components/ComponentEditor/index.tsx", "status": "passed", "details": ""}, {"name": "File: src/app/(protected)/superadmin/design-system/components/ComponentEditor/WYSIWYGEditor.tsx", "status": "passed", "details": ""}, {"name": "File: src/app/(protected)/superadmin/design-system/components/ComponentEditor/LivePreview.tsx", "status": "passed", "details": ""}, {"name": "File: src/app/(protected)/superadmin/design-system/components/ComponentEditor/PropsEditor.tsx", "status": "passed", "details": ""}, {"name": "File: src/app/(protected)/superadmin/design-system/components/ComponentEditor/StyleEditor.tsx", "status": "passed", "details": ""}, {"name": "File: tests/critical/baseline-protection.test.ts", "status": "passed", "details": ""}, {"name": "Dependency: ws", "status": "passed", "details": ""}, {"name": "Dependency: @radix-ui/react-separator", "status": "passed", "details": ""}, {"name": "Dependency: @types/ws", "status": "passed", "details": ""}, {"name": "Type: ComponentDefinition", "status": "passed", "details": ""}, {"name": "Type: ComponentCategory", "status": "passed", "details": ""}, {"name": "Type: ComponentStyling", "status": "passed", "details": ""}, {"name": "Type: ComponentInteractions", "status": "passed", "details": ""}, {"name": "Type: WebSocketMessage", "status": "passed", "details": ""}, {"name": "Type: HotSwapResult", "status": "passed", "details": ""}, {"name": "Type: PerformanceMetrics", "status": "passed", "details": ""}, {"name": "ComponentCategor<PERSON>", "status": "passed", "details": ""}, {"name": "Registry Export: componentRegistry", "status": "passed", "details": ""}, {"name": "Registry Export: getAllComponents", "status": "passed", "details": ""}, {"name": "Registry Export: getComponent", "status": "passed", "details": ""}, {"name": "Registry Export: updateComponent", "status": "passed", "details": ""}, {"name": "Registry Export: addComponent", "status": "passed", "details": ""}, {"name": "Registry Export: validateComponent", "status": "passed", "details": ""}, {"name": "API: Superadmin Design System", "status": "passed", "details": ""}, {"name": "API: Hot Swap", "status": "passed", "details": ""}, {"name": "API: Collaboration", "status": "passed", "details": ""}, {"name": "API: Analytics", "status": "passed", "details": ""}, {"name": "API: Performance Metrics", "status": "passed", "details": ""}, {"name": "API: WebSocket", "status": "passed", "details": ""}, {"name": "Baseline Protection Tests", "status": "passed", "details": ""}, {"name": "Build Compilation", "status": "passed", "details": ""}, {"name": "TypeScript Compilation", "status": "warning", "details": "Command failed: npx tsc --noEmit"}]}