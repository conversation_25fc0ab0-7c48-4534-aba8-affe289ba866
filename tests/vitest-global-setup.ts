/**
 * Vitest Global Setup for Analytics Tests
 * Separate from Playwright global setup
 */

import { config as dotenvConfig } from 'dotenv'
import { resolve } from 'path'
import { execSync } from 'child_process'

async function vitestGlobalSetup() {
  console.log('🧪 Setting up Vitest test environment...')

  // Load test environment variables
  dotenvConfig({ path: resolve(__dirname, 'test.env') })

  // Set test database URLs
  process.env.TEST_DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/emynent_test'
  process.env.TEST_ANALYTICS_DATABASE_URL =
    'postgresql://postgres:postgres@localhost:5432/emynent_analytics_test'

  try {
    console.log('📊 Setting up test databases...')

    // Setup main database
    process.env.DATABASE_URL = process.env.TEST_DATABASE_URL
    execSync('npx prisma db push --accept-data-loss --skip-generate', { stdio: 'pipe' })
    execSync('npx prisma generate', { stdio: 'pipe' })

    // Setup analytics database - set the required env var for the schema
    process.env.ANALYTICS_DATABASE_URL = process.env.TEST_ANALYTICS_DATABASE_URL
    execSync(
      'npx prisma db push --schema=./prisma/analytics-schema.prisma --accept-data-loss --skip-generate',
      { stdio: 'pipe' }
    )
    execSync('npx prisma generate --schema=./prisma/analytics-schema.prisma', { stdio: 'pipe' })

    console.log('✅ Test databases setup complete')
  } catch (error) {
    console.error('❌ Database setup failed:', error)
    throw new Error('Database setup failed.')
  }

  console.log('🎉 Vitest global test environment ready!')
}

export default vitestGlobalSetup
