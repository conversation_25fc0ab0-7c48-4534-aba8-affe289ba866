import { describe, expect, it } from 'vitest'

describe('AINavigationSidebar Module Resolution', () => {
  it('should be able to import the component', async () => {
    // Dynamic import to test module resolution
    const module = await import('../../../src/components/navigation/AINavigationSidebar')

    expect(module).toBeDefined()
    expect(module.AINavigationSidebar).toBeDefined()
    expect(typeof module.AINavigationSidebar).toBe('function')
  })
})
