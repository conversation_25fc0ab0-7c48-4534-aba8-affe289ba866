/**
 * @vitest-environment jsdom
 */

import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'
import { NavigationContextProvider } from '@/lib/context/NavigationContextProvider'
import { Role } from '@prisma/client'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock navigation context
const mockNavigationContext = {
  toggleSidebarMinimize: vi.fn(),
  isSidebarFullyCollapsed: false,
  isSidebarMinimized: false,
  getSidebarState: vi.fn(() => 'expanded'),
  getSidebarWidth: vi.fn(() => 'w-64'),
}

vi.mock('@/lib/context/NavigationContextProvider', async () => {
  const actual = await vi.importActual('@/lib/context/NavigationContextProvider')
  return {
    ...actual,
    useNavigationContext: () => mockNavigationContext,
  }
})

// Mock behavioral tracking
vi.mock('@/lib/analytics/behavioral-tracking', () => ({
  trackBehavioralEvent: vi.fn(),
}))

// Mock feature flags
vi.mock('@/lib/services/feature-flag-service', () => ({
  hasFeatureSync: vi.fn(() => false),
}))

// Mock session data
const mockSession = {
  user: {
    id: 'user-1',
    email: '<EMAIL>',
    role: Role.EMPLOYEE,
    companyId: 'company-1',
  },
  expires: '2024-12-31',
}

// Test wrapper with all necessary providers
function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider session={mockSession}>
      <NavigationContextProvider>{children}</NavigationContextProvider>
    </SessionProvider>
  )
}

describe('AIFirstSidebar - Original Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render the sidebar with Emynent logo and collapse button', async () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    // Check if sidebar renders
    const sidebar = screen.getByTestId('ai-first-sidebar')
    expect(sidebar).toBeInTheDocument()

    // Check if Emynent logo is present
    expect(screen.getByText('Emynent')).toBeInTheDocument()
  })

  it('should have Quick Actions section with icon-only buttons', async () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    // Check Quick Actions section exists
    const quickActionsSection = screen.getByTestId('quick-access-section')
    expect(quickActionsSection).toBeInTheDocument()

    // Check individual Quick Action buttons (icon only)
    const supportButton = screen.getByTestId('quick-access-support')
    expect(supportButton).toBeInTheDocument()
    expect(supportButton).toHaveAttribute('aria-label', 'Open support')

    const collapseAllButton = screen.getByTestId('quick-access-collapse-all')
    expect(collapseAllButton).toBeInTheDocument()

    const sidebarToggleButton = screen.getByTestId('quick-access-sidebar-toggle')
    expect(sidebarToggleButton).toBeInTheDocument()
  })

  it('should call toggleSidebarMinimize when sidebar toggle button is clicked', async () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    const sidebarToggleButton = screen.getByTestId('quick-access-sidebar-toggle')
    fireEvent.click(sidebarToggleButton)

    expect(mockNavigationContext.toggleSidebarMinimize).toHaveBeenCalledTimes(1)
  })

  it('should handle collapse all dropdowns functionality', async () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    const collapseAllButton = screen.getByTestId('quick-access-collapse-all')
    fireEvent.click(collapseAllButton)

    // Should not throw errors and button should be clickable
    expect(collapseAllButton).toBeInTheDocument()
  })

  it('should render navigation groups when expanded', async () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    // Wait for navigation items to load
    await waitFor(() => {
      // Should have navigation groups
      expect(screen.getByText('Personal')).toBeInTheDocument()
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText('Organisation')).toBeInTheDocument()
    })
  })
})

/**
 * AI Features Explanation:
 *
 * 1. **Support Button**: Opens a support modal for user assistance
 * 2. **Collapse All Dropdowns**: Collapses/expands all navigation submenus
 * 3. **Sidebar Toggle**: Minimizes/expands the entire sidebar (original feature)
 *
 * AI Enhancements (when implemented):
 * - **AI Recommendations**: Show personalized navigation suggestions based on user behavior
 * - **Behavioral Tracking**: Track user navigation patterns for optimization
 * - **Context Awareness**: Adapt navigation based on user role and recent activity
 * - **Smart Notifications**: Display relevant badges and alerts on navigation items
 */
