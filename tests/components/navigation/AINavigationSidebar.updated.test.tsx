/**
 * @vitest-environment jsdom
 */

import { AINavigationSidebar } from '@/components/navigation/AINavigationSidebar'
import { NavigationContextProvider } from '@/lib/context/NavigationContextProvider'
import { Role } from '@prisma/client'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock session data
const mockSession = {
  user: {
    id: 'user-1',
    email: '<EMAIL>',
    role: Role.EMPLOYEE,
    companyId: 'company-1',
  },
  expires: '2024-12-31',
}

// Test wrapper with all necessary providers
function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider session={mockSession}>
      <NavigationContextProvider>{children}</NavigationContextProvider>
    </SessionProvider>
  )
}

describe('AINavigationSidebar - Current Functionality', () => {
  const defaultProps = {
    userRole: Role.EMPLOYEE,
    behaviorData: {
      recentVisits: ['/dashboard', '/focus', '/grow'],
      clickFrequency: { focus: 8, grow: 5, dashboard: 12 },
      timeSpent: { focus: 3600, grow: 2700, dashboard: 1800 },
      lastActiveTime: new Date().toISOString(),
    },
    userContext: {
      role: 'employee',
      department: 'Engineering',
      experienceLevel: 'intermediate',
      recentGoals: ['Learn React', 'Improve TypeScript skills'],
    },
    recommendations: [
      {
        id: 'rec-1',
        type: 'navigation',
        title: 'Review your goals',
        description: 'Time to check your progress',
        confidence: 0.9,
        action: { type: 'navigate', target: '/goals' },
      },
    ],
    collapsed: false,
    onToggle: vi.fn(),
    intentPredictions: [
      {
        id: 'pred-1',
        action: 'navigate',
        target: '/grow/skills',
        intent: 'skills_development',
        confidence: 0.85,
        reason: 'User frequently visits skills development resources',
      },
    ],
    onActionPredict: vi.fn(),
    onBehaviorTrack: vi.fn(),
    superAdminAccess: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render the sidebar component without errors', () => {
      expect(() => {
        render(
          <TestWrapper>
            <AINavigationSidebar {...defaultProps} />
          </TestWrapper>
        )
      }).not.toThrow()
    })

    it('should display main navigation sections', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} />
        </TestWrapper>
      )

      // Check for main navigation items
      await waitFor(() => {
        expect(screen.getByText('Personal')).toBeInTheDocument()
        expect(screen.getByText('Team')).toBeInTheDocument()
        expect(screen.getByText('Organisation')).toBeInTheDocument()
      })
    })
  })

  describe('Collapse/Expand Functionality', () => {
    it('should show collapse/expand button', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} />
        </TestWrapper>
      )

      // Look for chevron icons that indicate collapse/expand functionality
      await waitFor(() => {
        const chevronElements = screen
          .getAllByRole('button')
          .filter(
            button =>
              button.getAttribute('aria-label')?.includes('sidebar') ||
              button.textContent?.includes('ChevronLeft') ||
              button.textContent?.includes('ChevronRight')
          )
        expect(chevronElements.length).toBeGreaterThan(0)
      })
    })

    it('should call onToggle when collapse button is clicked', async () => {
      const onToggle = vi.fn()
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} onToggle={onToggle} />
        </TestWrapper>
      )

      // Find and click the toggle button
      const toggleButtons = screen.getAllByRole('button')
      const toggleButton = toggleButtons.find(
        button =>
          button.getAttribute('aria-label')?.includes('Toggle sidebar') ||
          button.getAttribute('aria-label')?.includes('Collapse sidebar') ||
          button.getAttribute('aria-label')?.includes('Expand sidebar')
      )

      if (toggleButton) {
        fireEvent.click(toggleButton)
        expect(onToggle).toHaveBeenCalled()
      }
    })
  })

  describe('Quick Access Section', () => {
    it('should display AI recommendations', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Review your goals')).toBeInTheDocument()
      })
    })

    it('should show confidence levels for recommendations', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} />
        </TestWrapper>
      )

      await waitFor(() => {
        // Look for confidence percentage (90% for our mock recommendation)
        expect(screen.getByText(/90%/)).toBeInTheDocument()
      })
    })
  })

  describe('Quick Actions Section', () => {
    it('should display quick actions at the bottom', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} />
        </TestWrapper>
      )

      await waitFor(() => {
        // Look for common quick action elements
        const actionButtons = screen.getAllByRole('button')
        expect(actionButtons.length).toBeGreaterThan(3) // Should have multiple action buttons
      })
    })
  })

  describe('Intent Predictions', () => {
    it('should display user intent predictions', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} />
        </TestWrapper>
      )

      await waitFor(() => {
        // Should show skills development intent
        const elements = screen.queryAllByText(/skills/i)
        expect(elements.length).toBeGreaterThan(0)
      })
    })
  })

  describe('Behavioral Intelligence', () => {
    it('should call onBehaviorTrack when navigation items are clicked', async () => {
      const onBehaviorTrack = vi.fn()
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} onBehaviorTrack={onBehaviorTrack} />
        </TestWrapper>
      )

      // Find a navigation link and click it
      const navigationLinks = screen.getAllByRole('link')
      if (navigationLinks.length > 0) {
        fireEvent.click(navigationLinks[0])
        expect(onBehaviorTrack).toHaveBeenCalled()
      }
    })
  })

  describe('SuperAdmin Access', () => {
    it('should show superadmin sections when superAdminAccess is true', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} superAdminAccess={true} />
        </TestWrapper>
      )

      await waitFor(() => {
        // Look for superadmin-specific elements
        const superAdminElements = screen.queryAllByText(/superadmin|admin/i)
        expect(superAdminElements.length).toBeGreaterThan(0)
      })
    })

    it('should not show superadmin sections when superAdminAccess is false', async () => {
      render(
        <TestWrapper>
          <AINavigationSidebar {...defaultProps} superAdminAccess={false} />
        </TestWrapper>
      )

      // Should not have any obvious superadmin references
      expect(screen.queryByText(/superadmin panel/i)).not.toBeInTheDocument()
    })
  })
})
