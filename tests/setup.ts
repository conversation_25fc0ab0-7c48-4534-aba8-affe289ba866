import '@testing-library/jest-dom'
import { configure } from '@testing-library/react'
import React from 'react'
import { afterAll, afterEach, beforeAll, beforeEach, vi } from 'vitest'
import { getTestIsolation } from './utils/test-isolation'

// Configure testing library
configure({
  // Automatically wrap async utilities in act()
  asyncUtilTimeout: 3000,
  // Reduce act() warnings for Link components and navigation
  reactStrictMode: false,
})

// Suppress React act() warnings for Next.js Link components during testing
const originalConsoleError = console.error
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: An update to') ||
        args[0].includes('act(...)') ||
        args[0].includes('ForwardRef(LinkComponent)') ||
        args[0].includes('When testing, code that causes React state updates'))
    ) {
      return // Suppress React act() warnings for Link components
    }
    originalConsoleError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalConsoleError
})

// Global test setup for Emynent - OPTIMIZED FOR PERFORMANCE
let servicesVerified = false
let prismaInstance: any = null
let redisInstance: any = null

beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/emynent_test'
  process.env.REDIS_URL = 'redis://localhost:6379'

  // Ensure DOM is properly initialized for jsdom environment
  if (typeof document !== 'undefined') {
    // Create a basic document structure if needed
    if (!document.body) {
      document.body = document.createElement('body')
    }

    // Ensure activeElement is available
    if (!document.activeElement) {
      Object.defineProperty(document, 'activeElement', {
        value: document.body,
        writable: true,
        configurable: true,
      })
    }
  }

  // Only verify services once per test run (not per test file)
  if (!servicesVerified) {
    try {
      // Test database connection and cache instance
      const { prisma } = await import('../src/lib/prisma')
      prismaInstance = prisma
      await prisma.$queryRaw`SELECT 1`

      // Test Redis connection and cache instance
      const { redis } = await import('../src/lib/redis')
      redisInstance = redis
      if (redis.ping) {
        await redis.ping()
      }

      servicesVerified = true
    } catch (error) {
      console.warn('⚠️ Some services unavailable, using fallbacks')
      // Don't throw error for missing services in test environment
    }
  }
})

afterAll(async () => {
  try {
    // Only disconnect if we have instances and this is the last cleanup
    if (prismaInstance && typeof prismaInstance.$disconnect === 'function') {
      await prismaInstance.$disconnect()
    }

    if (redisInstance && typeof redisInstance.quit === 'function') {
      await redisInstance.quit()
    }
  } catch (error) {
    // Ignore cleanup errors
  }
})

// Cleanup function to reset test state
async function cleanupTestState() {
  try {
    // Clear any localStorage/sessionStorage
    if (typeof window !== 'undefined') {
      window.localStorage.clear()
      window.sessionStorage.clear()
    }

    // Reset any global state
    if (typeof document !== 'undefined') {
      document.body.innerHTML = ''
      document.head.innerHTML = ''
    }

    // Clear any timers
    if (typeof vi !== 'undefined') {
      vi.clearAllTimers()
    }
  } catch (error) {
    // Ignore cleanup errors
    console.warn('Test cleanup warning:', error)
  }
}

// Ensure jsdom environment is available
if (typeof window !== 'undefined') {
  // Simulate focus behavior in test environment
  Object.defineProperty(window, 'focus', {
    value: () => {
      document.body.focus()
    },
  })
}

// Setup test isolation for each test
beforeEach(async () => {
  // Clean up state before each test
  await cleanupTestState()

  // Get test isolation instance
  const testIsolation = getTestIsolation()

  // Clear the instance state for each test
  testIsolation.cleanup()
})

afterEach(async () => {
  // Clean up after each test
  await cleanupTestState()

  // Clear any remaining vi mocks
  if (typeof vi !== 'undefined') {
    vi.clearAllMocks()
    vi.clearAllTimers()
  }
})

// Mock fetch for testing
global.fetch = vi.fn()

// Mock window.matchMedia for responsive components
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock scrollIntoView
Element.prototype.scrollIntoView = vi.fn()

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
}

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
}

// Mock next/router
vi.mock('next/router', () => ({
  useRouter: () => ({
    route: '/dashboard',
    pathname: '/dashboard',
    query: {},
    asPath: '/dashboard',
    push: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
    back: vi.fn(),
    prefetch: vi.fn(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
  }),
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  }),
  usePathname: () => '/dashboard',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock next-auth
vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user',
        email: '<EMAIL>',
        role: 'EMPLOYEE',
        companyId: 'test-company',
      },
      expires: '2024-01-01',
    },
    status: 'authenticated',
  }),
  signIn: vi.fn(),
  signOut: vi.fn(),
  getSession: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    span: 'span',
    button: 'button',
    a: 'a',
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => children,
  useAnimation: () => ({
    start: vi.fn(),
    stop: vi.fn(),
    set: vi.fn(),
  }),
}))

// Mock chart libraries that might cause issues
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => children,
  LineChart: 'div',
  Line: 'div',
  XAxis: 'div',
  YAxis: 'div',
  CartesianGrid: 'div',
  Tooltip: 'div',
  Legend: 'div',
}))

// Mock theme provider
vi.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: vi.fn(),
    resolvedTheme: 'light',
    themes: ['light', 'dark', 'system'],
  }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
}))

// Export cleanup utilities for manual use in tests
export { cleanupTestState }
