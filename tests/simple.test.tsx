/**
 * Simple test to verify testing infrastructure works
 */
import { describe, expect, it } from 'vitest'

describe('Simple Infrastructure Test', () => {
  it('should run basic assertions', () => {
    expect(1 + 1).toBe(2)
    expect('hello').toBe('hello')
    expect(true).toBe(true)
  })

  it('should have access to DOM environment', () => {
    const div = document.createElement('div')
    div.textContent = 'test content'
    expect(div.textContent).toBe('test content')
    expect(div.tagName).toBe('DIV')
  })

  it('should support async operations', async () => {
    const result = await Promise.resolve('async works')
    expect(result).toBe('async works')
  })
})
