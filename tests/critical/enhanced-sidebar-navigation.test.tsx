/**
 * Enhanced Sidebar Navigation Tests
 *
 * Testing intuitive navigation improvements:
 * - Zone renaming (Personal Growth Zone → Personal, Team Connection Zone → Team, Organization Impact Zone → Organisation)
 * - Zone minimization capability with independent state management
 * - Better chevron positioning (absolute when collapsed)
 * - Settings integration within main sidebar
 * - Maintain all existing functionality and performance
 *
 * Performance targets: <100ms render, <300ms interactions
 * Accessibility: WCAG 2.1 AA compliance
 */

import { render, screen, fireEvent, waitFor, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { SessionProvider } from 'next-auth/react'
import { usePathname } from 'next/navigation'
import { Role } from '@prisma/client'

// Component under test
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'

// Context providers
import { NavigationProvider } from '@/lib/context/NavigationContextProvider'
import { UserContextProvider } from '@/lib/context/UserContextProvider'
import { AIModelProvider } from '@/lib/context/AIModelProvider'

// Mock dependencies
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(() => '/dashboard'),
}))

vi.mock('@/lib/analytics/behavioral-tracking', () => ({
  trackBehavioralEvent: vi.fn(),
}))

vi.mock('@/lib/services/feature-flag-service', () => ({
  hasFeatureSync: vi.fn(() => true),
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', { value: localStorageMock })

// Test session data
const mockSession = {
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    role: Role.EMPLOYEE,
    companyId: 'company-123',
  },
  expires: '2024-12-31',
}

const mockSuperAdminSession = {
  ...mockSession,
  user: {
    ...mockSession.user,
    role: Role.SUPERADMIN,
  },
}

// Test wrapper component
function TestWrapper({
  children,
  session = mockSession,
}: {
  children: React.ReactNode
  session?: any
}) {
  return (
    <SessionProvider session={session}>
      <NavigationProvider>
        <UserContextProvider>
          <AIModelProvider>{children}</AIModelProvider>
        </UserContextProvider>
      </NavigationProvider>
    </SessionProvider>
  )
}

describe('Enhanced Sidebar Navigation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockImplementation(() => {})
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('1. Intuitive Navigation - Zone Renaming & Structure', () => {
    it('should display renamed zones with correct structure', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Verify zone renaming
      expect(screen.getByText('Personal')).toBeInTheDocument()
      // Use getAllByText to handle multiple "Team" elements (zone header + nav item)
      expect(screen.getAllByText('Team')).toHaveLength(2)
      expect(screen.getByText('Organisation')).toBeInTheDocument()

      // Verify old zone names are not present
      expect(screen.queryByText('Personal Growth Zone')).not.toBeInTheDocument()
      expect(screen.queryByText('Team Connection Zone')).not.toBeInTheDocument()
      expect(screen.queryByText('Organization Impact Zone')).not.toBeInTheDocument()

      // Verify zone structure
      const personalZone = screen.getByTestId('personal-growth-zone')
      const teamZone = screen.getByTestId('team-connection-zone')
      const organisationZone = screen.getByTestId('organization-impact-zone')

      expect(personalZone).toBeInTheDocument()
      expect(teamZone).toBeInTheDocument()
      expect(organisationZone).toBeInTheDocument()

      // Verify navigation items are properly grouped
      within(personalZone).getByTestId('nav-focus')
      within(personalZone).getByTestId('nav-grow')
      within(personalZone).getByTestId('nav-customize')

      within(teamZone).getByTestId('nav-team')
      within(teamZone).getByTestId('nav-connect')
      within(teamZone).getByTestId('nav-celebrate')

      within(organisationZone).getByTestId('nav-vision')
      within(organisationZone).getByTestId('nav-explore')
      within(organisationZone).getByTestId('nav-pulse')
    })

    it('should support zone minimization with independent state management', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Initially all zones should be expanded
      expect(screen.getByTestId('nav-focus')).toBeVisible()
      expect(screen.getByTestId('nav-team')).toBeVisible()
      expect(screen.getByTestId('nav-vision')).toBeVisible()

      // Collapse Personal zone
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      // Personal zone items should be hidden, others remain visible
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.getByTestId('nav-team')).toBeVisible()
        expect(screen.getByTestId('nav-vision')).toBeVisible()
      })

      // Verify localStorage persistence
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'sidebar-zone-personalgrowth-collapsed',
        'true'
      )

      // Collapse Team zone independently
      const teamToggle = screen.getByTestId('zone-toggle-teamconnection')
      await user.click(teamToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
        expect(screen.getByTestId('nav-vision')).toBeVisible()
      })

      // Expand Personal zone while Team remains collapsed
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.getByTestId('nav-focus')).toBeVisible()
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
        expect(screen.getByTestId('nav-vision')).toBeVisible()
      })
    })

    it('should restore zone states from localStorage on load', async () => {
      // Mock localStorage with collapsed states
      localStorageMock.getItem.mockImplementation(key => {
        if (key === 'sidebar-zone-personalgrowth-collapsed') return 'true'
        if (key === 'sidebar-zone-teamconnection-collapsed') return 'false'
        if (key === 'sidebar-zone-organizationimpact-collapsed') return 'true'
        return null
      })

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Verify zones are restored to their saved states
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument() // Personal collapsed
        expect(screen.getByTestId('nav-team')).toBeVisible() // Team expanded
        expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument() // Organisation collapsed
      })
    })

    it('should maintain zone visibility when sidebar is collapsed', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Collapse a zone first
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })

      // Collapse entire sidebar
      const sidebarToggle = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(sidebarToggle)

      // When sidebar is collapsed, all navigation items should be visible (icons only)
      await waitFor(() => {
        expect(screen.getByTestId('nav-focus')).toBeVisible()
        expect(screen.getByTestId('nav-team')).toBeVisible()
        expect(screen.getByTestId('nav-vision')).toBeVisible()
      })

      // Zone headers should not be visible when collapsed
      expect(screen.queryByText('Personal')).not.toBeInTheDocument()
      expect(screen.queryByText('Team')).not.toBeInTheDocument()
      expect(screen.queryByText('Organisation')).not.toBeInTheDocument()
    })

    it('should support minimize/expand all zones functionality', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Initially all zones should be expanded
      expect(screen.getByTestId('nav-focus')).toBeVisible()
      expect(screen.getByTestId('nav-team')).toBeVisible()
      expect(screen.getByTestId('nav-vision')).toBeVisible()

      // Find the toggle all zones button
      const toggleAllButton = screen.getByTestId('toggle-all-zones')
      expect(toggleAllButton).toBeInTheDocument()
      expect(toggleAllButton).toHaveTextContent('Minimize All Zones')

      // Click to minimize all zones
      await user.click(toggleAllButton)

      // All zone items should be hidden
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument()
      })

      // Button text should change to "Expand All Zones"
      expect(toggleAllButton).toHaveTextContent('Expand All Zones')

      // Verify localStorage persistence for all zones
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'sidebar-zone-personalgrowth-collapsed',
        'true'
      )
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'sidebar-zone-teamconnection-collapsed',
        'true'
      )
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'sidebar-zone-organizationimpact-collapsed',
        'true'
      )

      // Click to expand all zones
      await user.click(toggleAllButton)

      // All zone items should be visible again
      await waitFor(() => {
        expect(screen.getByTestId('nav-focus')).toBeVisible()
        expect(screen.getByTestId('nav-team')).toBeVisible()
        expect(screen.getByTestId('nav-vision')).toBeVisible()
      })

      // Button text should change back to "Minimize All Zones"
      expect(toggleAllButton).toHaveTextContent('Minimize All Zones')
    })

    it('should handle mixed zone states correctly with toggle all button', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Manually collapse only the Personal zone
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.getByTestId('nav-team')).toBeVisible()
        expect(screen.getByTestId('nav-vision')).toBeVisible()
      })

      // With mixed states, button should show "Minimize All Zones" (since some zones are expanded)
      const toggleAllButton = screen.getByTestId('toggle-all-zones')
      expect(toggleAllButton).toHaveTextContent('Minimize All Zones')

      // Click to minimize all zones
      await user.click(toggleAllButton)

      // All zones should now be collapsed
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument()
      })

      // Button should now show "Expand All Zones"
      expect(toggleAllButton).toHaveTextContent('Expand All Zones')
    })

    it('should hide toggle all zones button when sidebar is collapsed', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Toggle all zones button should be visible when sidebar is expanded
      expect(screen.getByTestId('toggle-all-zones')).toBeVisible()

      // Collapse the sidebar
      const sidebarToggle = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(sidebarToggle)

      // Toggle all zones button should not be visible when sidebar is collapsed
      await waitFor(() => {
        expect(screen.queryByTestId('toggle-all-zones')).not.toBeInTheDocument()
      })
    })
  })

  describe('2. Improved Header Layout & Spacing', () => {
    it('should display enhanced header with proper spacing and branding', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const header = screen.getByRole('banner')
      expect(header).toHaveClass('h-16', 'flex', 'items-center', 'border-b', 'border-border')

      // Verify branding elements
      const brandLink = screen.getByRole('link', { name: /emynent/i })
      expect(brandLink).toBeInTheDocument()
      expect(brandLink).toHaveAttribute('href', '/dashboard')

      // Verify Bot icon is present
      const botIcon =
        header.querySelector('[data-testid="bot-icon"]') || header.querySelector('svg') // Fallback to any SVG
      expect(botIcon).toBeInTheDocument()
    })

    it('should position chevron correctly based on sidebar state', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')

      // Initially expanded - chevron should be positioned normally
      expect(collapseToggle).toHaveClass('mr-4', 'h-8', 'w-8')
      expect(collapseToggle).not.toHaveClass('absolute')

      // Collapse sidebar
      await user.click(collapseToggle)

      // When collapsed - chevron should be absolutely positioned
      await waitFor(() => {
        expect(collapseToggle).toHaveClass('absolute', 'top-4', 'right-2', 'h-8', 'w-8')
        expect(collapseToggle).not.toHaveClass('mr-4')
      })

      // Expand again
      await user.click(collapseToggle)

      // Should return to normal positioning
      await waitFor(() => {
        expect(collapseToggle).toHaveClass('mr-4', 'h-8', 'w-8')
        expect(collapseToggle).not.toHaveClass('absolute')
      })
    })

    it('should show appropriate chevron icons for expand/collapse states', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')

      // Initially expanded - should show ChevronLeft
      expect(collapseToggle).toHaveAttribute('aria-label', 'Collapse sidebar')
      expect(collapseToggle).toHaveAttribute('aria-expanded', 'true')

      // Collapse sidebar
      await user.click(collapseToggle)

      // Should show ChevronRight and update aria attributes
      await waitFor(() => {
        expect(collapseToggle).toHaveAttribute('aria-label', 'Expand sidebar')
        expect(collapseToggle).toHaveAttribute('aria-expanded', 'false')
      })
    })

    it('should adapt header layout when collapsed', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Initially expanded - full branding visible
      expect(screen.getByText('Emynent')).toBeVisible()

      // Collapse sidebar
      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(collapseToggle)

      // When collapsed - only icon should be visible, text hidden
      await waitFor(() => {
        expect(screen.queryByText('Emynent')).not.toBeInTheDocument()
      })

      // Bot icon should still be visible and centered
      const header = screen.getByRole('banner')
      const iconContainer = header.querySelector('[class*="justify-center"]')
      expect(iconContainer).toBeInTheDocument()
    })
  })

  describe('3. Settings Integration within Main Sidebar', () => {
    it('should include settings as part of Personal zone', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const personalZone = screen.getByTestId('personal-growth-zone')
      const settingsLink = within(personalZone).getByTestId('nav-customize')

      expect(settingsLink).toBeInTheDocument()
      expect(settingsLink).toHaveAttribute('href', '/settings')
      expect(within(settingsLink).getByText('Customize')).toBeInTheDocument()
    })

    it('should highlight settings when on settings pages', async () => {
      const mockUsePathname = vi.mocked(usePathname)
      mockUsePathname.mockReturnValue('/settings/appearance')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const settingsLink = screen.getByTestId('nav-customize')
      expect(settingsLink).toHaveClass('bg-primary/10', 'text-primary')
    })

    it('should maintain settings accessibility and navigation', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const settingsLink = screen.getByTestId('nav-customize')

      // Verify accessibility attributes
      expect(settingsLink).toHaveAttribute('role', 'link')
      expect(settingsLink).toHaveAttribute('aria-description', 'Your personal settings')

      // Verify keyboard navigation
      settingsLink.focus()
      expect(settingsLink).toHaveFocus()

      // Verify click tracking
      await user.click(settingsLink)
      expect(settingsLink).toHaveAttribute('data-clicked', 'true')
    })
  })

  describe('4. AI-Enhanced Navigation with Contextual Recommendations', () => {
    it('should display AI provider indicator when context-aware', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const aiIndicator = screen.getByTestId('ai-provider-indicator')
      expect(aiIndicator).toBeInTheDocument()
      expect(aiIndicator).toHaveTextContent(/AI: (OpenAI|Claude|Gemini|DeepSeek)/)
    })

    it('should show AI enhancement indicators on navigation items', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Wait for AI recommendations to load
      await waitFor(() => {
        const navigationContext = screen.getByTestId('navigation-context')
        expect(navigationContext).toHaveAttribute('data-initialized', 'true')
      })

      // Check for AI enhancement indicators (sparkles, activity dots, badges)
      const navigationItems = screen.getAllByRole('link')
      const enhancedItems = navigationItems.filter(
        item =>
          item.querySelector('[data-testid*="sparkles"]') ||
          item.querySelector('[class*="bg-primary rounded-full"]') ||
          item.querySelector('[class*="badge"]')
      )

      // At least some items should have AI enhancements
      expect(enhancedItems.length).toBeGreaterThanOrEqual(0)
    })

    it('should display AI recommendations section', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const aiRecommendations = screen.getByTestId('ai-recommendations')
      expect(aiRecommendations).toBeInTheDocument()

      // Should have AI Insights header
      expect(within(aiRecommendations).getByText('AI Insights')).toBeInTheDocument()

      // Should have AI provider selector
      const providerSelector = within(aiRecommendations).getByTestId('ai-provider-selector')
      expect(providerSelector).toBeInTheDocument()
    })

    it('should handle AI provider switching', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const providerSelector = screen.getByTestId('ai-provider-selector')
      const switchButton = within(providerSelector).getByRole('button', {
        name: /switch ai model/i,
      })

      await user.click(switchButton)

      // Should show provider options
      await waitFor(() => {
        expect(screen.getByText('OpenAI')).toBeInTheDocument()
        expect(screen.getByText('Claude')).toBeInTheDocument()
        expect(screen.getByText('Gemini')).toBeInTheDocument()
        expect(screen.getByText('DeepSeek')).toBeInTheDocument()
      })

      // Select a different provider
      await user.click(screen.getByText('Claude'))

      // Provider selector should close
      await waitFor(() => {
        expect(screen.queryByText('OpenAI')).not.toBeInTheDocument()
      })
    })
  })

  describe('5. Performance Optimization for Smooth Interactions', () => {
    it('should render sidebar within performance targets (<100ms)', async () => {
      const startTime = performance.now()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const sidebar = screen.getByTestId('sidebar')
      expect(sidebar).toBeInTheDocument()

      const renderTime = performance.now() - startTime
      expect(renderTime).toBeLessThan(100) // <100ms render target
    })

    it('should handle zone toggle interactions within performance targets (<300ms)', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')

      const startTime = performance.now()
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })

      const interactionTime = performance.now() - startTime
      expect(interactionTime).toBeLessThan(300) // <300ms interaction target
    })

    it('should handle sidebar collapse/expand within performance targets', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')

      const startTime = performance.now()
      await user.click(collapseToggle)

      await waitFor(() => {
        const sidebar = screen.getByTestId('sidebar')
        expect(sidebar).toHaveClass('w-16')
      })

      const interactionTime = performance.now() - startTime
      expect(interactionTime).toBeLessThan(300) // <300ms interaction target
    })

    it('should optimize re-renders with proper memoization', async () => {
      const user = userEvent.setup()
      let renderCount = 0

      const TestComponent = () => {
        renderCount++
        return <AIFirstSidebar />
      }

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      )

      const initialRenderCount = renderCount

      // Interact with zone toggle
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })

      // Should not cause excessive re-renders
      const finalRenderCount = renderCount
      expect(finalRenderCount - initialRenderCount).toBeLessThanOrEqual(2)
    })

    it('should handle toggle all zones within performance targets (<300ms)', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const toggleAllButton = screen.getByTestId('toggle-all-zones')

      const startTime = performance.now()
      await user.click(toggleAllButton)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument()
      })

      const interactionTime = performance.now() - startTime
      expect(interactionTime).toBeLessThan(300) // <300ms interaction target
    })
  })

  describe('6. Accessibility Compliance (WCAG 2.1 AA)', () => {
    it('should provide proper ARIA labels and roles', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Sidebar should have proper navigation role
      const nav = screen.getByRole('navigation', { name: 'Main navigation' })
      expect(nav).toBeInTheDocument()

      // Header should have banner role
      const header = screen.getByRole('banner')
      expect(header).toBeInTheDocument()

      // Collapse toggle should have proper aria attributes
      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')
      expect(collapseToggle).toHaveAttribute('aria-label')
      expect(collapseToggle).toHaveAttribute('aria-expanded')

      // Zone toggles should have proper aria labels
      const zoneToggles = screen.getAllByRole('button', { name: /expand|collapse/i })
      zoneToggles.forEach(toggle => {
        expect(toggle).toHaveAttribute('aria-label')
      })
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Tab through navigation items
      await user.tab()

      // Should focus on first interactive element
      const firstFocusable = document.activeElement
      expect(firstFocusable).toBeInTheDocument()

      // Continue tabbing through navigation
      await user.tab()
      await user.tab()

      // Should be able to navigate through all interactive elements
      const focusedElement = document.activeElement
      expect(focusedElement).toBeInTheDocument()
    })

    it('should handle keyboard activation of controls', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')
      collapseToggle.focus()

      // Activate with Enter key
      await user.keyboard('{Enter}')

      await waitFor(() => {
        const sidebar = screen.getByTestId('sidebar')
        expect(sidebar).toHaveClass('w-16')
      })

      // Activate with Space key
      await user.keyboard(' ')

      await waitFor(() => {
        const sidebar = screen.getByTestId('sidebar')
        expect(sidebar).toHaveClass('w-64')
      })
    })

    it('should provide sufficient color contrast and focus indicators', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Navigation items should have proper contrast classes
      const navigationItems = screen.getAllByRole('link')
      // Filter out the brand link which has different styling
      const navLinks = navigationItems.filter(
        item =>
          item.getAttribute('data-testid')?.startsWith('nav-') ||
          item.getAttribute('href')?.startsWith('/superadmin')
      )
      navLinks.forEach(item => {
        expect(item).toHaveClass('transition-colors')
        // Non-active items should have hover states defined
        // Active items have different styling (bg-primary/10 text-primary)
        if (!item.className.includes('bg-primary/10')) {
          expect(item.className).toMatch(/hover:/)
        }
      })

      // Zone headers should have proper contrast
      // Filter to only get the actual zone headers (not navigation items with same text)
      const zoneHeaders = screen
        .getAllByText(/Personal|Team|Organisation/)
        .filter(element => element.className.includes('text-muted-foreground'))
      zoneHeaders.forEach(header => {
        expect(header).toHaveClass('text-muted-foreground')
      })

      // Ensure we found the expected zone headers
      expect(zoneHeaders.length).toBeGreaterThan(0)
    })

    it('should provide proper accessibility for toggle all zones button', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const toggleAllButton = screen.getByTestId('toggle-all-zones')

      // Should have proper aria-label
      expect(toggleAllButton).toHaveAttribute('aria-label', 'Minimize all navigation zones')

      // Should be keyboard accessible
      toggleAllButton.focus()
      expect(toggleAllButton).toHaveFocus()

      // Should work with Enter key
      await user.keyboard('{Enter}')
      await waitFor(() => {
        expect(toggleAllButton).toHaveAttribute('aria-label', 'Expand all navigation zones')
      })

      // Should work with Space key
      await user.keyboard(' ')
      await waitFor(() => {
        expect(toggleAllButton).toHaveAttribute('aria-label', 'Minimize all navigation zones')
      })

      // Should have proper color contrast classes
      expect(toggleAllButton).toHaveClass('text-muted-foreground', 'hover:text-foreground')
    })
  })

  describe('7. State Persistence & Memory', () => {
    it('should persist sidebar collapse state across sessions', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(collapseToggle)

      // Should save to localStorage
      expect(localStorageMock.setItem).toHaveBeenCalledWith('sidebar-collapsed', 'true')
    })

    it('should persist individual zone states independently', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Collapse different zones
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      const teamToggle = screen.getByTestId('zone-toggle-teamconnection')

      await user.click(personalToggle)
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'sidebar-zone-personalgrowth-collapsed',
        'true'
      )

      await user.click(teamToggle)
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'sidebar-zone-teamconnection-collapsed',
        'true'
      )
    })

    it('should restore all states correctly on component mount', async () => {
      // Mock localStorage with various states
      localStorageMock.getItem.mockImplementation(key => {
        if (key === 'sidebar-collapsed') return 'false'
        if (key === 'sidebar-zone-personalgrowth-collapsed') return 'true'
        if (key === 'sidebar-zone-teamconnection-collapsed') return 'false'
        if (key === 'sidebar-zone-organizationimpact-collapsed') return 'true'
        return null
      })

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Verify sidebar is expanded
      const sidebar = screen.getByTestId('sidebar')
      expect(sidebar).toHaveClass('w-64')

      // Verify zone states are restored
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument() // Personal collapsed
        expect(screen.getByTestId('nav-team')).toBeVisible() // Team expanded
        expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument() // Organisation collapsed
      })
    })

    it('should handle missing localStorage gracefully', async () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage not available')
      })

      // Should not crash and use default states
      expect(() => {
        render(
          <TestWrapper>
            <AIFirstSidebar />
          </TestWrapper>
        )
      }).not.toThrow()

      // Should render with default expanded state
      const sidebar = screen.getByTestId('sidebar')
      expect(sidebar).toHaveClass('w-64')
    })

    it('should maintain SuperAdmin access when applicable', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Should show SuperAdmin section
      expect(screen.getByText('System')).toBeInTheDocument()
      expect(screen.getByRole('link', { name: /super admin/i })).toBeInTheDocument()
    })
  })
})
