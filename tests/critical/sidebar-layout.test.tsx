import { test, expect } from '@playwright/test'

test.describe('AI First Sidebar Layout', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to a protected page that shows the sidebar
    await page.goto('/dashboard')

    // Wait for the sidebar to be visible
    await page.waitForSelector('[data-testid="ai-first-sidebar"]', { state: 'visible' })
  })

  test('should display AI recommendations section at the bottom of the sidebar', async ({
    page,
  }) => {
    // Verify the sidebar is present
    const sidebar = page.locator('[data-testid="ai-first-sidebar"]')
    await expect(sidebar).toBeVisible()

    // Verify AI recommendations section exists and is at the bottom
    const aiRecommendations = page.locator('[data-testid="ai-recommendations"]')
    await expect(aiRecommendations).toBeVisible()

    // Get the sidebar's bounding box
    const sidebarBox = await sidebar.boundingBox()
    const aiRecommendationsBox = await aiRecommendations.boundingBox()

    // Verify AI recommendations is positioned at the bottom
    expect(aiRecommendationsBox).toBeTruthy()
    expect(sidebarBox).toBeTruthy()

    if (aiRecommendationsBox && sidebarBox) {
      // AI recommendations should be near the bottom of the sidebar
      const distanceFromBottom =
        sidebarBox.y + sidebarBox.height - (aiRecommendationsBox.y + aiRecommendationsBox.height)
      expect(distanceFromBottom).toBeLessThan(20) // Allow for small padding
    }
  })

  test('should display "Other" navigation section above AI recommendations', async ({ page }) => {
    // Verify the "Other" zone exists
    const otherZone = page.locator('[data-testid="other-zone"]')
    await expect(otherZone).toBeVisible()

    // Verify AI recommendations section exists
    const aiRecommendations = page.locator('[data-testid="ai-recommendations"]')
    await expect(aiRecommendations).toBeVisible()

    // Get bounding boxes
    const otherZoneBox = await otherZone.boundingBox()
    const aiRecommendationsBox = await aiRecommendations.boundingBox()

    // Verify "Other" zone is above AI recommendations
    expect(otherZoneBox).toBeTruthy()
    expect(aiRecommendationsBox).toBeTruthy()

    if (otherZoneBox && aiRecommendationsBox) {
      expect(otherZoneBox.y + otherZoneBox.height).toBeLessThanOrEqual(aiRecommendationsBox.y)
    }
  })

  test('should display SuperAdmin section in the "Other" zone when user is superadmin', async ({
    page,
  }) => {
    // Check if SuperAdmin link exists (depends on user role)
    const superAdminLink = page.locator('a[href="/superadmin"]')

    // If SuperAdmin link exists, verify it's in the correct position
    if ((await superAdminLink.count()) > 0) {
      await expect(superAdminLink).toBeVisible()

      // Verify it's within the navigation area but above AI recommendations
      const aiRecommendations = page.locator('[data-testid="ai-recommendations"]')
      const superAdminBox = await superAdminLink.boundingBox()
      const aiRecommendationsBox = await aiRecommendations.boundingBox()

      if (superAdminBox && aiRecommendationsBox) {
        expect(superAdminBox.y + superAdminBox.height).toBeLessThanOrEqual(aiRecommendationsBox.y)
      }
    }
  })

  test('should maintain proper layout when sidebar is collapsed', async ({ page }) => {
    // Collapse the sidebar
    const collapseButton = page.locator('[data-testid="sidebar-collapse-toggle"]')
    await collapseButton.click()

    // Wait for animation to complete
    await page.waitForTimeout(500)

    // Verify sidebar is collapsed
    const sidebar = page.locator('[data-testid="ai-first-sidebar"]')
    const sidebarBox = await sidebar.boundingBox()
    expect(sidebarBox?.width).toBeLessThan(100) // Should be around 64px when collapsed

    // AI recommendations should be hidden when collapsed
    const aiRecommendations = page.locator('[data-testid="ai-recommendations"]')
    await expect(aiRecommendations).not.toBeVisible()
  })

  test('should display navigation zones in correct order', async ({ page }) => {
    // Get all zone elements
    const zones = page.locator('[data-testid$="-zone"]')
    const zoneCount = await zones.count()

    expect(zoneCount).toBeGreaterThan(0)

    // Verify "Other" zone is the last navigation zone
    const otherZone = page.locator('[data-testid="other-zone"]')
    await expect(otherZone).toBeVisible()

    // Get all zone positions
    const zonePositions = []
    for (let i = 0; i < zoneCount; i++) {
      const zone = zones.nth(i)
      const box = await zone.boundingBox()
      if (box) {
        const testId = await zone.getAttribute('data-testid')
        zonePositions.push({ testId, y: box.y })
      }
    }

    // Sort by Y position
    zonePositions.sort((a, b) => a.y - b.y)

    // "Other" zone should be last in the navigation area
    const otherZonePosition = zonePositions.find(zone => zone.testId === 'other-zone')
    expect(otherZonePosition).toBeTruthy()

    // It should be the last zone before AI recommendations
    const lastZone = zonePositions[zonePositions.length - 1]
    expect(lastZone.testId).toBe('other-zone')
  })

  test('should have proper accessibility attributes', async ({ page }) => {
    const sidebar = page.locator('[data-testid="ai-first-sidebar"]')

    // Check for navigation role
    const nav = sidebar.locator('nav')
    await expect(nav).toHaveAttribute('role', 'navigation')
    await expect(nav).toHaveAttribute('aria-label', 'Main navigation')

    // Check header role
    const header = sidebar.locator('header')
    await expect(header).toHaveAttribute('role', 'banner')

    // Check collapse button accessibility
    const collapseButton = page.locator('[data-testid="sidebar-collapse-toggle"]')
    await expect(collapseButton).toHaveAttribute('aria-label')
    await expect(collapseButton).toHaveAttribute('aria-expanded')
  })

  test('should handle zone collapse/expand functionality', async ({ page }) => {
    // Find a zone toggle button
    const zoneToggle = page.locator('[data-testid^="zone-toggle-"]').first()

    if ((await zoneToggle.count()) > 0) {
      // Click to collapse
      await zoneToggle.click()
      await page.waitForTimeout(300) // Wait for animation

      // Click to expand
      await zoneToggle.click()
      await page.waitForTimeout(300) // Wait for animation

      // Verify the zone is still functional
      await expect(zoneToggle).toBeVisible()
    }
  })

  test('should display AI provider selector in recommendations section', async ({ page }) => {
    const aiRecommendations = page.locator('[data-testid="ai-recommendations"]')
    await expect(aiRecommendations).toBeVisible()

    // Look for AI provider selector elements within the recommendations section
    const providerSelector = aiRecommendations.locator(
      'select, [role="combobox"], button:has-text("AI Provider")'
    )

    // The provider selector should be present (even if it's a simple display)
    if ((await providerSelector.count()) > 0) {
      await expect(providerSelector.first()).toBeVisible()
    }
  })
})
