/**
 * Comprehensive E2E Tests: Theme Color Implementation in Navigation Components
 *
 * Following TDD/BDD principles to test the complete theme system integration
 * with navigation components including breadcrumbs, navbar, and sidebar elements.
 *
 * Behavior-Driven Development Scenarios:
 * 1. GIVEN a user navigates to the dashboard
 *    WHEN the page loads
 *    THEN the navigation elements should display the current theme colors
 *
 * 2. GIVEN a user changes the theme
 *    WHEN the theme is applied
 *    THEN all navigation elements should update to the new theme colors
 *
 * 3. GIVEN a user refreshes the page
 *    WHEN the page reloads
 *    THEN the theme colors should persist across navigation elements
 */

import { test, expect, Page, BrowserContext } from '@playwright/test'

interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  foreground: string
}

interface ThemeTestConfig {
  name: string
  colors: ThemeColors
  selector: string
}

const THEME_CONFIGS: ThemeTestConfig[] = [
  {
    name: 'Emynent Default',
    colors: {
      primary: '#8957e5', // Current implementation (purple)
      secondary: '#6B7280',
      accent: '#F59e0B',
      background: '#ffffff',
      foreground: '#1f2937',
    },
    selector: 'text=Emynent Default',
  },
  {
    name: 'Twilight',
    colors: {
      primary: '#FFD700', // Gold
      secondary: '#9333EA',
      accent: '#F59e0B',
      background: '#0f0f23',
      foreground: '#e2e8f0',
    },
    selector: 'text=Twilight',
  },
  {
    name: 'Slate',
    colors: {
      primary: '#334155',
      secondary: '#64748B',
      accent: '#A5B4FC',
      background: '#f8fafc',
      foreground: '#1e293b',
    },
    selector: 'text=Slate',
  },
  {
    name: 'Mint',
    colors: {
      primary: '#10B981',
      secondary: '#6EE7B7',
      accent: '#FCD34D',
      background: '#f0fdf4',
      foreground: '#064e3b',
    },
    selector: 'text=Mint',
  },
]

test.describe('🎨 Theme Color Implementation in Navigation Components (Comprehensive E2E)', () => {
  let page: Page
  let context: BrowserContext

  test.beforeAll(async ({ browser }) => {
    // Create a clean browser context for each test run
    context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
    })
  })

  test.beforeEach(async () => {
    page = await context.newPage()

    // Start with a clean slate - clear all storage
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })

    // Navigate to dashboard with authentication
    await page.goto('/dashboard')

    // Wait for authentication and main components to load
    await page.waitForSelector('[data-testid="main-navbar"]', {
      timeout: 15000,
      state: 'visible',
    })

    // Wait for theme system to initialize
    await page.waitForFunction(
      () => {
        const primaryColor = getComputedStyle(document.documentElement)
          .getPropertyValue('--primary')
          .trim()
        return primaryColor && primaryColor !== ''
      },
      { timeout: 10000 }
    )
  })

  test.afterEach(async () => {
    await page.close()
  })

  test.afterAll(async () => {
    await context.close()
  })

  test.describe('🏠 Default Theme State Verification', () => {
    test('should display purple primary color (#8957e5) as default theme in navigation elements', async () => {
      // BDD: GIVEN a user navigates to the dashboard
      // WHEN the page loads with default theme
      // THEN navigation elements should display purple theme colors

      // Verify primary color is set to purple (current implementation)
      const primaryColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
      })

      expect(primaryColor).toBe('#8957e5')

      // Test breadcrumb highlighting - Dashboard should be highlighted in purple
      const dashboardBreadcrumb = page.locator('nav').getByText('Dashboard').first()
      await expect(dashboardBreadcrumb).toBeVisible()

      // Verify breadcrumb has text-primary class for color application
      const breadcrumbClasses = await dashboardBreadcrumb.getAttribute('class')
      expect(breadcrumbClasses).toContain('text-primary')

      // Test burger menu hover state uses primary color
      const burgerMenuButton = page.locator('[data-testid="navbar-burger-menu"]')
      if (await burgerMenuButton.isVisible()) {
        const burgerIcon = page
          .locator('[data-testid="collapse-icon"], [data-testid="expand-icon"]')
          .first()
        const iconClasses = await burgerIcon.getAttribute('class')
        expect(iconClasses).toContain('group-hover:text-primary')
      }

      // Test search bar focus border uses primary color
      const searchInput = page.locator('input[placeholder*="search"], input[type="search"]').first()
      if (await searchInput.isVisible()) {
        await searchInput.focus()
        const searchClasses = await searchInput.getAttribute('class')
        expect(searchClasses).toContain('focus:border-primary')
      }
    })

    test('should verify all CSS custom properties are correctly defined', async () => {
      // BDD: GIVEN the default theme is loaded
      // WHEN I check CSS custom properties
      // THEN all theme variables should be properly defined

      const cssProperties = await page.evaluate(() => {
        const styles = getComputedStyle(document.documentElement)
        return {
          primary: styles.getPropertyValue('--primary').trim(),
          secondary: styles.getPropertyValue('--secondary').trim(),
          accent: styles.getPropertyValue('--accent').trim(),
          background: styles.getPropertyValue('--background').trim(),
          foreground: styles.getPropertyValue('--foreground').trim(),
        }
      })

      // Verify all essential CSS properties are defined
      expect(cssProperties.primary).toBeTruthy()
      expect(cssProperties.secondary).toBeTruthy()
      expect(cssProperties.accent).toBeTruthy()
      expect(cssProperties.background).toBeTruthy()
      expect(cssProperties.foreground).toBeTruthy()

      // Verify primary color matches expected default
      expect(cssProperties.primary).toBe('#8957e5')
    })
  })

  test.describe('🔄 Theme Switching Behavior', () => {
    for (const theme of THEME_CONFIGS) {
      test(`should apply ${theme.name} theme colors to all navigation elements`, async () => {
        // BDD: GIVEN a user wants to change theme
        // WHEN they select a new theme
        // THEN all navigation elements should update to new colors

        // Navigate to appearance settings
        await page.goto('/settings/platform/appearance')

        // Wait for appearance settings to load
        await page.waitForSelector(theme.selector, { timeout: 10000 })

        // Select the theme
        await page.click(theme.selector)

        // Wait for theme to be applied (allow time for database update and CSS application)
        await page.waitForTimeout(2000)

        // Navigate back to dashboard to test navigation elements
        await page.goto('/dashboard')
        await page.waitForSelector('[data-testid="main-navbar"]', { timeout: 10000 })

        // Wait for theme system to apply changes
        await page.waitForFunction(
          expectedColor => {
            const currentColor = getComputedStyle(document.documentElement)
              .getPropertyValue('--primary')
              .trim()
            return currentColor === expectedColor
          },
          theme.colors.primary,
          { timeout: 15000 }
        )

        // Verify primary color is correctly applied
        const appliedPrimaryColor = await page.evaluate(() => {
          return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
        })

        expect(appliedPrimaryColor).toBe(theme.colors.primary)

        // Test breadcrumb color application
        const dashboardBreadcrumb = page.locator('nav').getByText('Dashboard').first()
        await expect(dashboardBreadcrumb).toBeVisible()

        const breadcrumbClasses = await dashboardBreadcrumb.getAttribute('class')
        expect(breadcrumbClasses).toContain('text-primary')

        // Verify computed style matches theme color
        const breadcrumbComputedColor = await dashboardBreadcrumb.evaluate(el => {
          return getComputedStyle(el).color
        })

        // Convert hex to rgb for comparison (browsers return RGB values)
        const expectedRgb = await page.evaluate(hex => {
          const temp = document.createElement('div')
          temp.style.color = hex
          document.body.appendChild(temp)
          const rgb = getComputedStyle(temp).color
          document.body.removeChild(temp)
          return rgb
        }, theme.colors.primary)

        expect(breadcrumbComputedColor).toBe(expectedRgb)
      })
    }

    test('should persist theme colors after page refresh', async () => {
      // BDD: GIVEN a user has selected a non-default theme
      // WHEN they refresh the page
      // THEN the theme should persist across the reload

      const testTheme = THEME_CONFIGS.find(t => t.name === 'Twilight')!

      // Apply Twilight theme
      await page.goto('/settings/platform/appearance')
      await page.waitForSelector(testTheme.selector)
      await page.click(testTheme.selector)
      await page.waitForTimeout(2000)

      // Navigate to dashboard
      await page.goto('/dashboard')
      await page.waitForSelector('[data-testid="main-navbar"]')

      // Verify theme is applied
      await page.waitForFunction(expectedColor => {
        const currentColor = getComputedStyle(document.documentElement)
          .getPropertyValue('--primary')
          .trim()
        return currentColor === expectedColor
      }, testTheme.colors.primary)

      // Refresh the page
      await page.reload()
      await page.waitForSelector('[data-testid="main-navbar"]')

      // Verify theme persists after refresh
      await page.waitForFunction(
        expectedColor => {
          const currentColor = getComputedStyle(document.documentElement)
            .getPropertyValue('--primary')
            .trim()
          return currentColor === expectedColor
        },
        testTheme.colors.primary,
        { timeout: 15000 }
      )

      const persistedColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
      })

      expect(persistedColor).toBe(testTheme.colors.primary)

      // Verify navigation elements still use correct colors
      const dashboardBreadcrumb = page.locator('nav').getByText('Dashboard').first()
      const breadcrumbClasses = await dashboardBreadcrumb.getAttribute('class')
      expect(breadcrumbClasses).toContain('text-primary')
    })
  })

  test.describe('🧩 Navigation Component Integration', () => {
    test('should apply theme colors to all interactive navigation elements', async () => {
      // BDD: GIVEN the theme system is loaded
      // WHEN I interact with navigation elements
      // THEN they should all respond with correct theme colors

      const testTheme = THEME_CONFIGS.find(t => t.name === 'Mint')!

      // Apply Mint theme for distinct color testing
      await page.goto('/settings/platform/appearance')
      await page.waitForSelector(testTheme.selector)
      await page.click(testTheme.selector)
      await page.waitForTimeout(2000)

      await page.goto('/dashboard')
      await page.waitForSelector('[data-testid="main-navbar"]')

      // Wait for theme to be applied
      await page.waitForFunction(expectedColor => {
        const currentColor = getComputedStyle(document.documentElement)
          .getPropertyValue('--primary')
          .trim()
        return currentColor === expectedColor
      }, testTheme.colors.primary)

      // Test sidebar active states (if sidebar is present)
      const sidebarItems = page.locator('[role="navigation"] a, nav a').filter({
        hasText: /Dashboard|Settings|Explore|Grow|Focus|Vision|Team|Connect|Pulse|Celebrate/,
      })

      if ((await sidebarItems.count()) > 0) {
        for (let i = 0; i < Math.min(3, await sidebarItems.count()); i++) {
          const item = sidebarItems.nth(i)
          if (await item.isVisible()) {
            const itemClasses = await item.getAttribute('class')
            // Check for active state classes or hover classes
            expect(itemClasses).toMatch(/(text-primary|bg-primary\/10|hover:text-primary)/)
          }
        }
      }

      // Test AI search elements (if present)
      const aiElements = page.locator('[data-testid*="ai"], [class*="ai-"], text=AI')
      if ((await aiElements.count()) > 0) {
        const firstAiElement = aiElements.first()
        if (await firstAiElement.isVisible()) {
          const aiClasses = await firstAiElement.getAttribute('class')
          expect(aiClasses).toMatch(/(text-primary|border-primary)/)
        }
      }
    })

    test('should handle rapid theme switching without visual glitches', async () => {
      // BDD: GIVEN a user rapidly switches between themes
      // WHEN multiple theme changes occur quickly
      // THEN the system should handle them gracefully without errors

      await page.goto('/settings/platform/appearance')

      // Rapidly switch between different themes
      const themesToTest = THEME_CONFIGS.slice(0, 3) // Test first 3 themes

      for (const theme of themesToTest) {
        await page.waitForSelector(theme.selector)
        await page.click(theme.selector)
        await page.waitForTimeout(500) // Short wait to simulate rapid switching
      }

      // Wait for final theme to settle
      await page.waitForTimeout(2000)

      // Verify final theme is applied correctly
      const finalTheme = themesToTest[themesToTest.length - 1]

      await page.goto('/dashboard')
      await page.waitForSelector('[data-testid="main-navbar"]')

      // Verify no errors occurred and theme is properly applied
      const finalColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
      })

      expect(finalColor).toBe(finalTheme.colors.primary)

      // Verify navigation elements are still functional
      const dashboardBreadcrumb = page.locator('nav').getByText('Dashboard').first()
      await expect(dashboardBreadcrumb).toBeVisible()

      const breadcrumbClasses = await dashboardBreadcrumb.getAttribute('class')
      expect(breadcrumbClasses).toContain('text-primary')
    })
  })

  test.describe('📱 Responsive Theme Application', () => {
    test('should apply theme colors correctly across different viewport sizes', async () => {
      // BDD: GIVEN a user views the app on different devices
      // WHEN the viewport changes
      // THEN theme colors should remain consistent

      const testTheme = THEME_CONFIGS.find(t => t.name === 'Slate')!

      // Apply Slate theme
      await page.goto('/settings/platform/appearance')
      await page.waitForSelector(testTheme.selector)
      await page.click(testTheme.selector)
      await page.waitForTimeout(2000)

      // Test different viewport sizes
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 375, height: 667, name: 'Mobile' },
      ]

      for (const viewport of viewports) {
        await page.setViewportSize(viewport)
        await page.goto('/dashboard')
        await page.waitForSelector('[data-testid="main-navbar"]', { timeout: 10000 })

        // Wait for theme to be applied
        await page.waitForFunction(
          expectedColor => {
            const currentColor = getComputedStyle(document.documentElement)
              .getPropertyValue('--primary')
              .trim()
            return currentColor === expectedColor
          },
          testTheme.colors.primary,
          { timeout: 10000 }
        )

        const viewportColor = await page.evaluate(() => {
          return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
        })

        expect(viewportColor).toBe(testTheme.colors.primary)

        // Verify navigation elements are visible and themed correctly
        const navElements = page.locator('[data-testid="main-navbar"]')
        await expect(navElements).toBeVisible()
      }
    })
  })

  test.describe('⚠️ Error Handling and Edge Cases', () => {
    test('should gracefully handle theme loading failures', async () => {
      // BDD: GIVEN theme loading might fail
      // WHEN a theme cannot be loaded
      // THEN the system should fallback gracefully

      // Simulate network issues by intercepting theme-related requests
      await page.route('**/api/settings/**', route => {
        // Simulate intermittent failures
        if (Math.random() < 0.3) {
          route.abort()
        } else {
          route.continue()
        }
      })

      await page.goto('/settings/platform/appearance')

      try {
        await page.waitForSelector('text=Emynent Default', { timeout: 5000 })
        await page.click('text=Emynent Default')
      } catch (error) {
        // If theme selection fails, that's expected due to our route interception
        console.log('Theme selection failed as expected due to simulated network issues')
      }

      await page.goto('/dashboard')
      await page.waitForSelector('[data-testid="main-navbar"]')

      // Verify system still functions with fallback theme
      const fallbackColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
      })

      // Should have some color value (fallback)
      expect(fallbackColor).toBeTruthy()
      expect(fallbackColor).toMatch(/^#[0-9a-fA-F]{6}$/) // Valid hex color

      // Navigation should still be functional
      const navBar = page.locator('[data-testid="main-navbar"]')
      await expect(navBar).toBeVisible()
    })

    test('should maintain theme consistency when navigating between pages', async () => {
      // BDD: GIVEN a user has selected a theme
      // WHEN they navigate between different pages
      // THEN the theme should remain consistent

      const testTheme = THEME_CONFIGS.find(t => t.name === 'Twilight')!

      // Apply Twilight theme
      await page.goto('/settings/platform/appearance')
      await page.waitForSelector(testTheme.selector)
      await page.click(testTheme.selector)
      await page.waitForTimeout(2000)

      // Navigate through different pages and verify theme consistency
      const pagesToTest = [
        '/dashboard',
        '/settings',
        '/settings/platform',
        '/settings/platform/appearance',
      ]

      for (const pagePath of pagesToTest) {
        await page.goto(pagePath)

        // Wait for navigation to complete
        await page.waitForLoadState('networkidle')

        // Verify theme color is consistent
        const pageColor = await page.evaluate(() => {
          return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
        })

        expect(pageColor).toBe(testTheme.colors.primary)

        // Verify navigation elements maintain theme
        const navElements = page.locator('nav, [role="navigation"]').first()
        if (await navElements.isVisible()) {
          await expect(navElements).toBeVisible()
        }
      }
    })
  })
})
