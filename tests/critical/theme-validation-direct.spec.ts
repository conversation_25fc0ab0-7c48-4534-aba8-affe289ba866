/**
 * Direct Theme Validation Test - No Authentication Required
 *
 * This test validates theme functionality on public pages or with manual authentication,
 * following TDD/BDD principles with real functionality testing.
 */

import { test, expect, Page } from '@playwright/test'

// Skip global setup for this test
test.use({ storageState: { cookies: [], origins: [] } })

test.describe('🎨 Direct Theme Validation (No Auth)', () => {
  let page: Page

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage
  })

  test('should load CSS custom properties on any public page', async () => {
    // BDD: GIVEN I visit the application
    // WHEN the page loads
    // THEN CSS custom properties should be defined

    // Navigate to root page (might redirect to signin)
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Even on signin page, theme should be loaded
    const cssProperties = await page.evaluate(() => {
      const styles = getComputedStyle(document.documentElement)
      return {
        primary: styles.getPropertyValue('--primary').trim(),
        secondary: styles.getPropertyValue('--secondary').trim(),
        accent: styles.getPropertyValue('--accent').trim(),
        background: styles.getPropertyValue('--background').trim(),
        foreground: styles.getPropertyValue('--foreground').trim(),
      }
    })

    // Verify at least some CSS properties are defined
    const definedProperties = Object.values(cssProperties).filter(prop => prop && prop !== '')
    expect(definedProperties.length).toBeGreaterThan(0)

    console.log('CSS Properties found:', cssProperties)

    // If primary color is defined, it should be our expected purple
    if (cssProperties.primary) {
      expect(cssProperties.primary).toBe('#8957e5')
      console.log(`✅ Primary color verified: ${cssProperties.primary}`)
    }
  })

  test('should load theme stylesheets correctly', async () => {
    // BDD: GIVEN the page loads
    // WHEN I check for theme-related stylesheets
    // THEN they should be present and loaded

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Check for CSS files being loaded
    const stylesheetLinks = await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
      return links.map(link => ({
        href: (link as HTMLLinkElement).href,
        loaded: (link as HTMLLinkElement).sheet !== null,
      }))
    })

    expect(stylesheetLinks.length).toBeGreaterThan(0)
    console.log(`✅ Found ${stylesheetLinks.length} stylesheets`)

    // Verify at least some stylesheets are loaded
    const loadedSheets = stylesheetLinks.filter(sheet => sheet.loaded)
    expect(loadedSheets.length).toBeGreaterThan(0)
    console.log(`✅ ${loadedSheets.length} stylesheets successfully loaded`)
  })

  test('should have Next.js theme provider in the DOM', async () => {
    // BDD: GIVEN the application uses next-themes
    // WHEN the page loads
    // THEN the theme provider should be in the DOM

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Check for theme-related script or data attributes
    const themeElements = await page.evaluate(() => {
      // Look for next-themes related elements
      const themeScript = document.querySelector('script[data-theme]')
      const themeDataAttrs =
        document.documentElement.getAttribute('data-theme') ||
        document.documentElement.getAttribute('class')

      return {
        hasThemeScript: !!themeScript,
        documentClass: document.documentElement.className,
        documentDataTheme: document.documentElement.getAttribute('data-theme'),
        documentStyle: document.documentElement.getAttribute('style'),
      }
    })

    console.log('Theme elements found:', themeElements)

    // At least the document should have some theme-related attributes
    const hasThemeIndicators =
      themeElements.documentClass || themeElements.documentDataTheme || themeElements.documentStyle

    expect(hasThemeIndicators).toBeTruthy()
    console.log('✅ Theme system indicators found')
  })

  test('should load without critical JavaScript errors', async () => {
    // BDD: GIVEN the page loads
    // WHEN JavaScript executes
    // THEN there should be no critical errors

    const jsErrors: string[] = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        jsErrors.push(msg.text())
      }
    })

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Wait for any delayed errors
    await page.waitForTimeout(3000)

    // Filter out non-critical errors
    const criticalErrors = jsErrors.filter(
      error =>
        !error.includes('Failed to fetch') &&
        !error.includes('NetworkError') &&
        !error.includes('ERR_INTERNET_DISCONNECTED') &&
        !error.toLowerCase().includes('websocket') &&
        !error.includes('404') &&
        !error.includes('favicon')
    )

    console.log(`Found ${jsErrors.length} total errors, ${criticalErrors.length} critical`)

    if (criticalErrors.length > 0) {
      console.log('Critical errors:', criticalErrors)
    }

    // Allow some errors in test environment
    expect(criticalErrors.length).toBeLessThanOrEqual(3)
    console.log('✅ JavaScript error check passed')
  })

  test('should render page title and basic HTML structure', async () => {
    // BDD: GIVEN the page loads
    // WHEN I check basic HTML elements
    // THEN they should be present and valid

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Check page title
    const title = await page.title()
    expect(title).toBeTruthy()
    expect(title.length).toBeGreaterThan(0)
    console.log(`✅ Page title: "${title}"`)

    // Check for basic HTML structure
    const htmlStructure = await page.evaluate(() => {
      return {
        hasHead: !!document.head,
        hasBody: !!document.body,
        hasMain: !!document.querySelector('main'),
        hasNav: !!document.querySelector('nav'),
        hasFooter: !!document.querySelector('footer'),
      }
    })

    expect(htmlStructure.hasHead).toBe(true)
    expect(htmlStructure.hasBody).toBe(true)
    console.log('✅ Basic HTML structure verified')
    console.log('HTML elements found:', htmlStructure)
  })

  test('should have responsive meta tags for proper mobile rendering', async () => {
    // BDD: GIVEN the page loads
    // WHEN I check meta tags
    // THEN viewport and responsive tags should be present

    await page.goto('/')
    await page.waitForLoadState('networkidle')

    const metaTags = await page.evaluate(() => {
      const viewport = document.querySelector('meta[name="viewport"]')
      const charset = document.querySelector('meta[charset]')

      return {
        hasViewport: !!viewport,
        viewportContent: viewport?.getAttribute('content'),
        hasCharset: !!charset,
        charsetValue: charset?.getAttribute('charset'),
      }
    })

    expect(metaTags.hasViewport).toBe(true)
    expect(metaTags.hasCharset).toBe(true)

    if (metaTags.viewportContent) {
      expect(metaTags.viewportContent).toContain('width=device-width')
    }

    console.log('✅ Meta tags verified:', metaTags)
  })

  test('should handle authentication redirect gracefully', async () => {
    // BDD: GIVEN I access a protected route
    // WHEN not authenticated
    // THEN I should be redirected appropriately without errors

    // Try to access dashboard directly
    await page.goto('/dashboard')
    await page.waitForLoadState('networkidle')

    const currentUrl = page.url()
    const isOnAuthPage =
      currentUrl.includes('/signin') ||
      currentUrl.includes('/auth') ||
      currentUrl.includes('/login')

    // Should either be on auth page OR have access (if somehow authenticated)
    const isOnDashboard = currentUrl.includes('/dashboard')

    expect(isOnAuthPage || isOnDashboard).toBe(true)
    console.log(`✅ Authentication redirect handled correctly: ${currentUrl}`)

    // If on auth page, verify it loads properly
    if (isOnAuthPage) {
      const hasForm = (await page.locator('form').count()) > 0
      const hasInputs = (await page.locator('input').count()) > 0

      expect(hasForm || hasInputs).toBe(true)
      console.log('✅ Authentication page has form elements')
    }
  })
})
