import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'
import { Role } from '@prisma/client'

// Mock dependencies
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>

// Mock feature flag service
jest.mock('@/lib/services/feature-flag-service', () => ({
  hasFeatureSync: jest.fn(() => false),
}))

// Mock behavioral tracking
jest.mock('@/lib/analytics/behavioral-tracking', () => ({
  trackBehavioralEvent: jest.fn(),
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <div>{children}</div>
}

describe('Navigation Duplicate Removal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    mockUsePathname.mockReturnValue('/dashboard')
    mockUseSession.mockReturnValue({
      data: {
        user: {
          id: '1',
          email: '<EMAIL>',
          role: Role.EMPLOYEE,
          companyId: 'company-1',
        },
      },
      status: 'authenticated',
    })

    // Clear localStorage before each test
    localStorage.clear()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('Top OTHER Zone Removal', () => {
    it('should not render any top OTHER zone section', async () => {
      render(<AIFirstSidebar />)

      // Wait for component to fully render
      await waitFor(() => {
        expect(screen.getByTestId('sidebar-nav')).toBeInTheDocument()
      })

      // Check that there are only 3 main navigation zones (Personal, Team, Organisation)
      const zoneHeaders = screen.getAllByText(/Personal|Team|Organisation/i)
      expect(zoneHeaders).toHaveLength(3)

      // Verify specific zone headers exist
      expect(screen.getByText('Personal')).toBeInTheDocument()
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText('Organisation')).toBeInTheDocument()

      // Verify no "Other" zone header exists in the main navigation
      const otherHeaders = screen.queryAllByText('Other')
      expect(otherHeaders).toHaveLength(0)
    })

    it('should not have Settings in the main navigation zones', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('sidebar-nav')).toBeInTheDocument()
      })

      // Look for Settings links in the main navigation zones
      const mainNavigation = screen.getByTestId('sidebar-nav')
      const settingsInMainNav = mainNavigation.querySelector('[href="/settings"]')

      // Settings should not be in the main navigation zones anymore
      expect(settingsInMainNav).toBeNull()
    })
  })

  describe('Bottom Others Section', () => {
    it('should render the bottom Others section with all expected items including Settings', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-section')).toBeInTheDocument()
      })

      // Verify Others section header
      expect(screen.getByText('Others')).toBeInTheDocument()

      // Verify all expected items are present in the Others section
      const othersSection = screen.getByTestId('others-section')

      // Check for Settings in the Others section
      const settingsLink = othersSection.querySelector('[href="/settings"]')
      expect(settingsLink).toBeInTheDocument()
      expect(settingsLink).toHaveTextContent('Settings')

      // Check for other expected items
      expect(othersSection.querySelector('[href="/help"]')).toBeInTheDocument()
      expect(othersSection.querySelector('[href="/feedback"]')).toBeInTheDocument()
      expect(othersSection.querySelector('[href="/docs"]')).toBeInTheDocument()
      expect(othersSection.querySelector('[href="/support"]')).toBeInTheDocument()
    })

    it('should maintain collapsible functionality for Others section', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-section')).toBeInTheDocument()
      })

      const toggleButton = screen.getByTestId('others-toggle')
      expect(toggleButton).toBeInTheDocument()

      // Initially expanded - all links should be visible
      expect(screen.getByText('Settings')).toBeInTheDocument()
      expect(screen.getByText('Help & Support')).toBeInTheDocument()

      // Click to collapse
      fireEvent.click(toggleButton)

      // Links should be hidden after collapse
      await waitFor(() => {
        expect(screen.queryByText('Settings')).not.toBeInTheDocument()
        expect(screen.queryByText('Help & Support')).not.toBeInTheDocument()
      })

      // Click to expand again
      fireEvent.click(toggleButton)

      // Links should be visible again
      await waitFor(() => {
        expect(screen.getByText('Settings')).toBeInTheDocument()
        expect(screen.getByText('Help & Support')).toBeInTheDocument()
      })
    })

    it('should persist Others section collapse state in localStorage', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-toggle')).toBeInTheDocument()
      })

      const toggleButton = screen.getByTestId('others-toggle')

      // Click to collapse
      fireEvent.click(toggleButton)

      // Verify localStorage was called to save the state
      expect(localStorageMock.setItem).toHaveBeenCalledWith('sidebar-others-collapsed', 'true')
    })
  })

  describe('Settings Link Functionality', () => {
    it('should navigate to settings when Settings link is clicked in Others section', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-section')).toBeInTheDocument()
      })

      const othersSection = screen.getByTestId('others-section')
      const settingsLink = othersSection.querySelector('[href="/settings"]')

      expect(settingsLink).toBeInTheDocument()
      expect(settingsLink).toHaveAttribute('href', '/settings')
      expect(settingsLink).toHaveAttribute('title', 'System settings and preferences')
    })

    it('should highlight Settings link when on settings page', async () => {
      mockUsePathname.mockReturnValue('/settings')

      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-section')).toBeInTheDocument()
      })

      const othersSection = screen.getByTestId('others-section')
      const settingsLink = othersSection.querySelector('[href="/settings"]')

      expect(settingsLink).toHaveClass('bg-primary/10', 'text-primary')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for Others section', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-section')).toBeInTheDocument()
      })

      const othersSection = screen.getByTestId('others-section')
      expect(othersSection).toHaveAttribute('aria-label', 'Additional navigation options')

      const toggleButton = screen.getByTestId('others-toggle')
      expect(toggleButton).toHaveAttribute('aria-label', 'Collapse Others section')
    })

    it('should update ARIA label when Others section is collapsed/expanded', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-toggle')).toBeInTheDocument()
      })

      const toggleButton = screen.getByTestId('others-toggle')

      // Initially expanded
      expect(toggleButton).toHaveAttribute('aria-label', 'Collapse Others section')

      // Click to collapse
      fireEvent.click(toggleButton)

      // Should update to expand label
      await waitFor(() => {
        expect(toggleButton).toHaveAttribute('aria-label', 'Expand Others section')
      })
    })
  })

  describe('Navigation Structure Verification', () => {
    it('should have exactly 3 main navigation zones and 1 Others section', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('sidebar-nav')).toBeInTheDocument()
      })

      // Count main zone headers (Personal, Team, Organisation)
      const mainZoneHeaders = screen.getAllByText(/^(Personal|Team|Organisation)$/)
      expect(mainZoneHeaders).toHaveLength(3)

      // Count Others section (should be exactly 1)
      const othersHeaders = screen.getAllByText('Others')
      expect(othersHeaders).toHaveLength(1)

      // Verify the Others section is at the bottom (after main navigation)
      const othersSection = screen.getByTestId('others-section')
      expect(othersSection).toBeInTheDocument()
    })

    it('should maintain proper navigation item order in Others section', async () => {
      render(<AIFirstSidebar />)

      await waitFor(() => {
        expect(screen.getByTestId('others-section')).toBeInTheDocument()
      })

      const othersSection = screen.getByTestId('others-section')
      const links = othersSection.querySelectorAll('a')

      // Verify we have all 5 expected links
      expect(links).toHaveLength(5)

      // Verify the order: Settings, Help & Support, Feedback, Documentation, Contact Support
      expect(links[0]).toHaveAttribute('href', '/settings')
      expect(links[1]).toHaveAttribute('href', '/help')
      expect(links[2]).toHaveAttribute('href', '/feedback')
      expect(links[3]).toHaveAttribute('href', '/docs')
      expect(links[4]).toHaveAttribute('href', '/support')
    })
  })
})
