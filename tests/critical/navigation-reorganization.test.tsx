import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'
import SettingsSidebar from '@/components/settings/Sidebar'

// Mock dependencies
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

// Mock fetch for settings API
global.fetch = jest.fn()

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

// Test wrapper component
function TestWrapper({ children }: { children: React.ReactNode }) {
  return <div data-testid='test-wrapper'>{children}</div>
}

// Mock session data
const mockSession = {
  user: {
    id: '1',
    email: '<EMAIL>',
    role: 'EMPLOYEE',
    companyId: 'company-1',
  },
  expires: '2024-12-31',
}

describe('Navigation Reorganization (TDD)', () => {
  beforeEach(() => {
    mockUsePathname.mockReturnValue('/dashboard')
    mockUseSession.mockReturnValue({ data: mockSession, status: 'authenticated' })

    // Mock settings sections API response
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => [
        {
          id: 'profile',
          title: 'Profile (Personal)',
          subsections: [
            { id: 'personal-info', title: 'Personal Info' },
            { id: 'security', title: 'Security & Login' },
          ],
        },
        {
          id: 'platform',
          title: 'Platform (Preferences)',
          subsections: [
            { id: 'appearance', title: 'Appearance' },
            { id: 'ai-models', title: 'AI Models', isNew: true }, // NEW: AI model selector
            { id: 'notifications', title: 'Notifications' },
            { id: 'dashboard', title: 'Dashboard Preferences' },
          ],
        },
      ],
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('1. AI Model Selector Relocation (RED PHASE - Should Fail)', () => {
    it('should NOT have AI model selector in main sidebar bottom section', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // AI model selector should NOT be in the main sidebar anymore
      const aiRecommendationsSection = screen.queryByTestId('ai-recommendations')

      if (aiRecommendationsSection) {
        const aiProviderSelector = screen.queryByTestId('ai-provider-selector')
        // This test should FAIL initially - AI selector is currently in main sidebar
        expect(aiRecommendationsSection).not.toContainElement(aiProviderSelector)
      }
    })

    it('should have AI model selector in Settings > Platform section', async () => {
      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Platform (Preferences)')).toBeInTheDocument()
      })

      // Should have AI Models option in Platform section
      // This test should FAIL initially - AI Models is not yet in settings
      expect(screen.getByText('AI Models')).toBeInTheDocument()
    })

    it('should navigate to AI models settings page', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Platform (Preferences)')).toBeInTheDocument()
      })

      // Click on AI Models link
      const aiModelsLink = screen.getByText('AI Models')
      await user.click(aiModelsLink)

      // Should navigate to /settings/platform/ai-models
      // This test should FAIL initially - link doesn't exist yet
      expect(aiModelsLink.closest('a')).toHaveAttribute('href', '/settings/platform/ai-models')
    })
  })

  describe('2. Others Section Relocation (RED PHASE - Should Fail)', () => {
    it('should have Others section in main sidebar bottom area', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Others section should be moved to where AI recommendations used to be
      const othersSection = screen.getByTestId('others-section')
      expect(othersSection).toBeInTheDocument()

      // Should be in the bottom area of the sidebar
      const sidebar = screen.getByTestId('ai-first-sidebar')
      const sidebarRect = sidebar.getBoundingClientRect()
      const othersRect = othersSection.getBoundingClientRect()

      // Others should be in bottom 30% of sidebar
      const bottomThreshold = sidebarRect.top + sidebarRect.height * 0.7
      expect(othersRect.top).toBeGreaterThan(bottomThreshold)
    })

    it('should contain Help, Support, and Feedback in Others section', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const othersSection = screen.getByTestId('others-section')

      // Should contain typical "Others" items
      expect(othersSection).toContainElement(screen.getByText('Help & Support'))
      expect(othersSection).toContainElement(screen.getByText('Feedback'))
      expect(othersSection).toContainElement(screen.getByText('Documentation'))
    })

    it('should NOT have Others section in main navigation zones', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Others items should NOT be in the main navigation zones anymore
      const personalZone = screen.queryByTestId('zone-personalgrowth')
      const teamZone = screen.queryByTestId('zone-teamconnection')
      const orgZone = screen.queryByTestId('zone-organizationimpact')

      if (personalZone) {
        expect(personalZone).not.toContainElement(screen.queryByText('Help & Support'))
      }
      if (teamZone) {
        expect(teamZone).not.toContainElement(screen.queryByText('Feedback'))
      }
      if (orgZone) {
        expect(orgZone).not.toContainElement(screen.queryByText('Documentation'))
      }
    })
  })

  describe('3. User Experience Validation (RED PHASE - Should Fail)', () => {
    it('should maintain consistent navigation patterns', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Main navigation should still have the 9 core AI-first items
      const coreNavItems = [
        'Focus',
        'Grow',
        'Team',
        'Connect',
        'Celebrate',
        'Vision',
        'Explore',
        'Pulse',
        'Settings',
      ]

      coreNavItems.forEach(item => {
        expect(screen.getByText(item)).toBeInTheDocument()
      })

      // Others section should be separate from core navigation
      const othersSection = screen.getByTestId('others-section')
      expect(othersSection).toBeInTheDocument()
    })

    it('should provide clear visual separation between core nav and others', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const othersSection = screen.getByTestId('others-section')

      // Should have visual separator (border-top or similar)
      expect(othersSection).toHaveClass(/border-t|border-top/)

      // Should have appropriate spacing
      expect(othersSection).toHaveClass(/mt-|margin-top/)
    })

    it('should maintain accessibility for reorganized navigation', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Others section should have proper ARIA labels
      const othersSection = screen.getByTestId('others-section')
      expect(othersSection).toHaveAttribute('aria-label', 'Additional navigation options')

      // All links should be accessible
      const helpLink = screen.getByText('Help & Support')
      expect(helpLink.closest('a')).toHaveAttribute('href')
      expect(helpLink.closest('a')).not.toHaveAttribute('aria-disabled', 'true')
    })
  })

  describe('4. Settings Integration Validation (RED PHASE - Should Fail)', () => {
    it('should load AI models settings page successfully', async () => {
      mockUsePathname.mockReturnValue('/settings/platform/ai-models')

      // Mock AI models settings API
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          currentProvider: 'openai',
          availableProviders: ['openai', 'claude', 'gemini', 'deepseek'],
          settings: {
            temperature: 0.7,
            maxTokens: 2000,
          },
        }),
      })

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('AI Models')).toBeInTheDocument()
      })

      // AI Models should be highlighted as active when on that page
      const aiModelsLink = screen.getByText('AI Models')
      expect(aiModelsLink).toHaveClass(/bg-muted|active/)
    })

    it('should show AI model configuration options in settings', async () => {
      // This test validates that the AI model settings page exists and works
      // Will fail initially since the page doesn't exist yet

      const aiSettingsPage = screen.queryByTestId('ai-models-settings-page')
      expect(aiSettingsPage).toBeInTheDocument()

      if (aiSettingsPage) {
        expect(aiSettingsPage).toContainElement(screen.getByText('Current AI Provider'))
        expect(aiSettingsPage).toContainElement(screen.getByText('Model Settings'))
      }
    })
  })

  describe('5. Performance and Responsiveness (GREEN PHASE - Should Pass)', () => {
    it('should maintain fast navigation performance after reorganization', async () => {
      const startTime = performance.now()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('ai-first-sidebar')).toBeInTheDocument()
      })

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render quickly even with reorganized structure
      expect(renderTime).toBeLessThan(100) // 100ms target
    })

    it('should handle responsive design correctly', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const sidebar = screen.getByTestId('ai-first-sidebar')

      // Should maintain responsive classes
      expect(sidebar).toHaveClass(/transition-all/)
      expect(sidebar).toHaveClass(/w-16|w-64/) // Collapsed or expanded width
    })
  })
})
