import { render, screen } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { SuperAdminLayout } from '../../src/components/superadmin/SuperAdminLayout'
import { Role } from '@prisma/client'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn() }),
  usePathname: () => '/superadmin',
}))

// Mock session with SuperAdmin role
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Super Admin',
    role: Role.SUPERADMIN,
    companyId: 'test-company-id',
  },
  expires: '2024-12-31',
}

describe('SuperAdmin Header Padding Alignment', () => {
  const renderWithSession = (component: React.ReactElement) => {
    return render(<SessionProvider session={mockSession}>{component}</SessionProvider>)
  }

  it('should have px-4 padding to match main sidebar header', () => {
    const { container } = renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    // Get the SuperAdmin header
    const superAdminHeader = container.querySelector(
      '[data-testid="superadmin-layout"] > div:last-child > div:first-child'
    )

    expect(superAdminHeader).toBeInTheDocument()

    // Verify the header has the correct structure and padding
    expect(superAdminHeader).toHaveClass('h-16')
    expect(superAdminHeader).toHaveClass('border-b', 'border-border')
    expect(superAdminHeader).toHaveClass('bg-background/80', 'backdrop-blur-lg')

    // The key fix: padding should be px-4 to match main sidebar, not px-6
    expect(superAdminHeader).toHaveClass('px-4')
    expect(superAdminHeader).not.toHaveClass('px-6')
  })

  it('should have consistent header styling for border alignment', () => {
    const { container } = renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    const superAdminHeader = container.querySelector(
      '[data-testid="superadmin-layout"] > div:last-child > div:first-child'
    )

    // Verify all the styling classes that ensure proper border alignment
    expect(superAdminHeader).toHaveClass(
      'flex-shrink-0',
      'h-16',
      'flex',
      'items-center',
      'border-b',
      'border-border',
      'bg-background/80',
      'backdrop-blur-lg',
      'px-4'
    )
  })
})
