import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SessionProvider } from 'next-auth/react'
import { SuperAdminLayout } from '../../src/components/superadmin/SuperAdminLayout'

// Mock the navigation components
vi.mock('../../src/components/navigation/AIFirstSidebar', () => ({
  AIFirstSidebar: () => <div data-testid='ai-first-sidebar'>Main Sidebar</div>,
}))

vi.mock('../../src/components/superadmin/SuperAdminSidebar', () => ({
  SuperAdminSidebar: () => <div data-testid='superadmin-sidebar'>SuperAdmin Sidebar</div>,
}))

vi.mock('../../src/components/superadmin/SuperAdminBreadcrumbs', () => ({
  SuperAdminBreadcrumbs: () => <div data-testid='superadmin-breadcrumbs'>Breadcrumbs</div>,
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    prefetch: vi.fn(),
  }),
}))

describe('SuperAdminLayout - Remove Duplicate Sidebar', () => {
  const mockSession = {
    user: {
      id: 'test-user',
      email: '<EMAIL>',
      role: 'SUPERADMIN' as const,
      companyId: 'test-company',
    },
    expires: '2024-01-01',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should NOT render AIFirstSidebar when used in SuperAdmin layout', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    // The SuperAdminLayout should NOT render the main sidebar
    // because it's already rendered by the parent protected layout
    const mainSidebars = screen.queryAllByTestId('ai-first-sidebar')
    expect(mainSidebars).toHaveLength(0)

    // Should only have the SuperAdmin sidebar
    const superAdminSidebar = screen.getByTestId('superadmin-sidebar')
    expect(superAdminSidebar).toBeInTheDocument()
  })

  it('should have correct layout structure without duplicate main sidebar', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    // Should have layout container
    const layout = screen.getByTestId('superadmin-layout')
    expect(layout).toBeInTheDocument()

    // Should NOT have main sidebar (to avoid duplication)
    const mainSidebars = screen.queryAllByTestId('ai-first-sidebar')
    expect(mainSidebars).toHaveLength(0)

    // Should have SuperAdmin sidebar
    const superAdminSidebar = screen.getByTestId('superadmin-sidebar')
    expect(superAdminSidebar).toBeInTheDocument()

    // Should have breadcrumbs
    const breadcrumbs = screen.getByTestId('superadmin-breadcrumbs')
    expect(breadcrumbs).toBeInTheDocument()

    // Should have content
    const content = screen.getByTestId('test-content')
    expect(content).toBeInTheDocument()
  })

  it('should use flex layout optimized for single SuperAdmin sidebar', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    const layout = screen.getByTestId('superadmin-layout')
    expect(layout).toHaveClass('flex', 'h-screen', 'bg-background')

    // Should have only one sidebar section (SuperAdmin)
    const sidebarSections = layout.querySelectorAll('.flex-shrink-0')
    expect(sidebarSections).toHaveLength(1) // Only SuperAdmin sidebar container
  })
})
