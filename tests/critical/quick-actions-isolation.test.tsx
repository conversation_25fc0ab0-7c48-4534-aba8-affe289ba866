import { QuickActions } from '@/components/shared/QuickActions'
import { useQuickActions } from '@/hooks/useQuickActions'
import { fireEvent, render, screen } from '@testing-library/react'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

// Mock the useQuickActions hook
vi.mock('@/hooks/useQuickActions')

// Mock the navigation context
vi.mock('@/lib/context/NavigationContextProvider', () => ({
  useNavigationContext: () => ({
    isCollapsed: false,
    toggleCollapsed: vi.fn(),
    toggleSidebarMinimize: vi.fn(),
    getSidebarState: () => 'expanded',
    getSidebarWidth: () => 'w-64',
    refreshRecommendations: vi.fn(),
  }),
}))

// Mock the feature flag service
vi.mock('@/lib/services/feature-flag-service', () => ({
  hasFeatureSync: vi.fn(() => true),
}))

// Mock the behavioral tracking
vi.mock('@/lib/analytics/behavioral-tracking', () => ({
  trackBehavioralEvent: vi.fn(),
}))

// Mock the session
vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: '1',
        email: '<EMAIL>',
        role: 'EMPLOYEE',
        companyId: '1',
      },
    },
    status: 'authenticated',
  }),
}))

describe('Quick Actions Functionality - Isolation Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('QuickActions Component', () => {
    it('should render all Quick Actions buttons correctly', () => {
      // Mock the hook return value
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions />)

      // Check that all buttons are rendered
      expect(screen.getByTestId('quick-access-support')).toBeInTheDocument()
      expect(screen.getByTestId('quick-access-collapse-all')).toBeInTheDocument()
      expect(screen.getByTestId('quick-access-refresh-ai')).toBeInTheDocument()
      expect(screen.getByTestId('sidebar-collapse-toggle')).toBeInTheDocument()
    })

    it('should handle support button click', () => {
      const mockHandleSupportToggle = vi.fn()
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: mockHandleSupportToggle,
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions />)

      const supportButton = screen.getByTestId('quick-access-support')
      fireEvent.click(supportButton)

      expect(mockHandleSupportToggle).toHaveBeenCalledTimes(1)
    })

    it('should handle collapse all button click', () => {
      const mockHandleCollapseAll = vi.fn()
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: mockHandleCollapseAll,
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions />)

      const collapseButton = screen.getByTestId('quick-access-collapse-all')
      fireEvent.click(collapseButton)

      expect(mockHandleCollapseAll).toHaveBeenCalledTimes(1)
    })

    it('should handle sidebar toggle button click', () => {
      const mockHandleSidebarToggle = vi.fn()
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: mockHandleSidebarToggle,
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions />)

      const sidebarToggleButton = screen.getByTestId('sidebar-collapse-toggle')
      fireEvent.click(sidebarToggleButton)

      expect(mockHandleSidebarToggle).toHaveBeenCalledTimes(1)
    })

    it('should call onActionTrigger callback when provided', () => {
      const mockOnActionTrigger = vi.fn()
      const mockHandleSupportToggle = vi.fn()
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: mockHandleSupportToggle,
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions onActionTrigger={mockOnActionTrigger} />)

      const supportButton = screen.getByTestId('quick-access-support')
      fireEvent.click(supportButton)

      expect(mockHandleSupportToggle).toHaveBeenCalledTimes(1)
      expect(mockOnActionTrigger).toHaveBeenCalledWith('support')
    })

    it('should not render when show prop is false', () => {
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions show={false} />)

      expect(screen.queryByTestId('quick-access-support')).not.toBeInTheDocument()
      expect(screen.queryByTestId('quick-access-collapse-all')).not.toBeInTheDocument()
      expect(screen.queryByTestId('quick-access-refresh-ai')).not.toBeInTheDocument()
      expect(screen.queryByTestId('sidebar-collapse-toggle')).not.toBeInTheDocument()
    })

    it('should render Refresh AI button when context-aware features are enabled', () => {
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions />)

      expect(screen.getByTestId('quick-access-refresh-ai')).toBeInTheDocument()
    })

    it('should not render Refresh AI button when context-aware features are disabled', () => {
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: false,
          isMinimized: false,
        },
      })

      render(<QuickActions />)

      expect(screen.queryByTestId('quick-access-refresh-ai')).not.toBeInTheDocument()
    })

    it('should have proper ARIA labels for all buttons', () => {
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      })

      render(<QuickActions />)

      expect(screen.getByLabelText('Open support')).toBeInTheDocument()
      expect(screen.getByLabelText('Collapse all dropdowns')).toBeInTheDocument()
      expect(screen.getByLabelText('Refresh AI recommendations')).toBeInTheDocument()
      expect(screen.getByLabelText('Minimize sidebar')).toBeInTheDocument()
    })

    it('should update aria-label based on sidebar state', () => {
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      mockUseQuickActions.mockReturnValue({
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: true,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: true,
        },
      })

      render(<QuickActions />)

      expect(screen.getByLabelText('Expand all dropdowns')).toBeInTheDocument()
      expect(screen.getByLabelText('Expand sidebar')).toBeInTheDocument()
    })
  })

  describe('useQuickActions Hook', () => {
    it('should initialize with correct default state', () => {
      const mockUseQuickActions = useQuickActions as vi.MockedFunction<typeof useQuickActions>
      const mockReturnValue = {
        state: {
          supportModalOpen: false,
          allDropdownsCollapsed: false,
        },
        handlers: {
          handleSupportToggle: vi.fn(),
          handleCollapseAllDropdowns: vi.fn(),
          handleRefreshAI: vi.fn(),
          handleSidebarToggle: vi.fn(),
          setSupportModalOpen: vi.fn(),
        },
        config: {
          isContextAware: true,
          isMinimized: false,
        },
      }
      mockUseQuickActions.mockReturnValue(mockReturnValue)

      render(<QuickActions />)

      expect(mockUseQuickActions).toHaveBeenCalled()
      const result = mockUseQuickActions.mock.results[0]?.value
      expect(result.state.supportModalOpen).toBe(false)
      expect(result.state.allDropdownsCollapsed).toBe(false)
      expect(result.config.isContextAware).toBe(true)
      expect(result.config.isMinimized).toBe(false)
    })
  })
})
