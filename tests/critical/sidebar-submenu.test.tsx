/**
 * Sidebar Submenu Functionality - Unit Tests
 *
 * Comprehensive tests for sidebar submenu expansion, navigation, and state management
 * Following TDD approach with real data testing (no mocks)
 *
 * Test Categories:
 * - Submenu Expansion/Collapse
 * - Navigation State Management
 * - Active State Detection
 * - Role-Based Access Control
 * - User Interactions
 * - Accessibility
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SessionProvider } from 'next-auth/react'
import { ThemeProvider } from 'next-themes'
import { Sidebar } from '../../src/components/shared/Sidebar'
import { NavigationProvider } from '../../src/lib/context/NavigationContextProvider'
import { Role } from '@prisma/client'
import { Session } from 'next-auth'

// Mock next/navigation
const mockPush = vi.fn()
const mockPathname = vi.fn(() => '/focus')

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    prefetch: vi.fn(),
  }),
  usePathname: () => mockPathname(),
}))

// Mock window.matchMedia for theme tests
beforeEach(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
})

afterEach(() => {
  vi.clearAllMocks()
})

// Test sessions for different roles
const createTestSession = (role: Role): Session => ({
  user: {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: role,
    companyId: 1,
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
})

const employeeSession = createTestSession(Role.EMPLOYEE)
const managerSession = createTestSession(Role.MANAGER)
const superAdminSession = createTestSession(Role.SUPERADMIN)

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode
  session?: Session
  theme?: string
}

const TestWrapper: React.FC<TestWrapperProps> = ({
  children,
  session = employeeSession,
  theme = 'light',
}) => (
  <SessionProvider session={session}>
    <ThemeProvider attribute='class' defaultTheme={theme}>
      <NavigationProvider>{children}</NavigationProvider>
    </ThemeProvider>
  </SessionProvider>
)

describe('Sidebar Submenu Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockPathname.mockReturnValue('/focus')
  })

  describe('Submenu Expansion and Collapse', () => {
    it('should show expand button for items with submenus', async () => {
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Focus has submenus, so it should have an expand button
      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-toggle')).toBeInTheDocument()
      })

      // Customize doesn't have submenus, so it shouldn't have an expand button
      expect(screen.queryByTestId('nav-customize-toggle')).not.toBeInTheDocument()
    })

    it('should expand submenu when toggle button is clicked', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Initially, submenus should not be visible
      expect(screen.queryByTestId('nav-focus-skills-assessment')).not.toBeInTheDocument()

      // Click the expand button
      const expandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(expandButton)

      // Submenus should now be visible
      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-skills-assessment')).toBeInTheDocument()
        expect(screen.getByTestId('nav-focus-learning-paths')).toBeInTheDocument()
        expect(screen.getByTestId('nav-focus-mentorship')).toBeInTheDocument()
      })
    })

    it('should collapse submenu when toggle button is clicked again', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Expand submenu first
      const expandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(expandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-skills-assessment')).toBeInTheDocument()
      })

      // Click again to collapse
      await user.click(expandButton)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus-skills-assessment')).not.toBeInTheDocument()
      })
    })

    it('should show chevron rotation animation when expanding/collapsing', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const expandButton = screen.getByTestId('nav-focus-toggle')
      const chevron = expandButton.querySelector('svg')

      // Initially, chevron should not be rotated
      expect(chevron).not.toHaveClass('rotate-180')

      // Click to expand
      await user.click(expandButton)

      await waitFor(() => {
        expect(chevron).toHaveClass('rotate-180')
      })

      // Click to collapse
      await user.click(expandButton)

      await waitFor(() => {
        expect(chevron).not.toHaveClass('rotate-180')
      })
    })
  })

  describe('Multiple Submenu Management', () => {
    it('should allow multiple submenus to be expanded simultaneously', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Expand Focus submenu
      const focusExpandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(focusExpandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-skills-assessment')).toBeInTheDocument()
      })

      // Expand Grow submenu
      const growExpandButton = screen.getByTestId('nav-grow-toggle')
      await user.click(growExpandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-grow-skill-development')).toBeInTheDocument()
      })

      // Both submenus should be visible
      expect(screen.getByTestId('nav-focus-skills-assessment')).toBeInTheDocument()
      expect(screen.getByTestId('nav-grow-skill-development')).toBeInTheDocument()
    })

    it('should expand Team submenu correctly', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={managerSession}>
          <Sidebar />
        </TestWrapper>
      )

      const teamExpandButton = screen.getByTestId('nav-team-toggle')
      await user.click(teamExpandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-team-team-dashboard')).toBeInTheDocument()
        expect(screen.getByTestId('nav-team-one-on-ones')).toBeInTheDocument()
        expect(screen.getByTestId('nav-team-team-goals')).toBeInTheDocument()
      })
    })

    it('should expand Connect submenu correctly', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const connectExpandButton = screen.getByTestId('nav-connect-toggle')
      await user.click(connectExpandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-connect-networking')).toBeInTheDocument()
        expect(screen.getByTestId('nav-connect-collaboration')).toBeInTheDocument()
        expect(screen.getByTestId('nav-connect-knowledge-sharing')).toBeInTheDocument()
      })
    })

    it('should expand Vision submenu correctly', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={managerSession}>
          <Sidebar />
        </TestWrapper>
      )

      const visionExpandButton = screen.getByTestId('nav-vision-toggle')
      await user.click(visionExpandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-vision-strategic-planning')).toBeInTheDocument()
        expect(screen.getByTestId('nav-vision-innovation')).toBeInTheDocument()
        expect(screen.getByTestId('nav-vision-future-roadmap')).toBeInTheDocument()
      })
    })
  })

  describe('Active State Detection', () => {
    it('should show parent item as active when on submenu page', async () => {
      mockPathname.mockReturnValue('/focus/skills-assessment')

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const focusNavItem = screen.getByTestId('nav-focus')
      const focusNavContainer = focusNavItem.parentElement

      await waitFor(() => {
        expect(focusNavContainer).toHaveClass('bg-primary/10', 'text-primary')
      })
    })

    it('should show submenu item as active when on that specific page', async () => {
      mockPathname.mockReturnValue('/focus/skills-assessment')

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Submenu should be auto-expanded when on a submenu page
      await waitFor(() => {
        const skillsAssessmentItem = screen.getByTestId('nav-focus-skills-assessment')
        expect(skillsAssessmentItem).toHaveClass('bg-primary/10', 'text-primary')
      })
    })

    it('should auto-expand submenu when on a submenu page', async () => {
      mockPathname.mockReturnValue('/focus/learning-paths')

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // When on a submenu page, the parent submenu should auto-expand
      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-learning-paths')).toBeInTheDocument()
      })
    })
  })

  describe('Collapsed Sidebar Behavior', () => {
    it('should hide submenu toggle buttons when sidebar is collapsed', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Collapse the sidebar
      const collapseButton = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(collapseButton)

      await waitFor(() => {
        // Toggle buttons should not be visible when collapsed
        expect(screen.queryByTestId('nav-focus-toggle')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-grow-toggle')).not.toBeInTheDocument()
      })
    })

    it('should hide all submenus when sidebar is collapsed', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // First expand a submenu
      const expandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(expandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-skills-assessment')).toBeInTheDocument()
      })

      // Then collapse the sidebar
      const collapseButton = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(collapseButton)

      await waitFor(() => {
        // Submenus should be hidden
        expect(screen.queryByTestId('nav-focus-skills-assessment')).not.toBeInTheDocument()
      })
    })
  })

  describe('Navigation and Routing', () => {
    it('should navigate to submenu pages when clicked', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Expand submenu
      const expandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(expandButton)

      await waitFor(() => {
        const skillsAssessmentLink = screen.getByTestId('nav-focus-skills-assessment')
        expect(skillsAssessmentLink).toHaveAttribute('href', '/focus/skills-assessment')
      })
    })

    it('should have correct href attributes for all submenu items', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Test Focus submenus
      const focusExpandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(focusExpandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-skills-assessment')).toHaveAttribute(
          'href',
          '/focus/skills-assessment'
        )
        expect(screen.getByTestId('nav-focus-learning-paths')).toHaveAttribute(
          'href',
          '/focus/learning-paths'
        )
        expect(screen.getByTestId('nav-focus-mentorship')).toHaveAttribute(
          'href',
          '/focus/mentorship'
        )
      })

      // Test Grow submenus
      const growExpandButton = screen.getByTestId('nav-grow-toggle')
      await user.click(growExpandButton)

      await waitFor(() => {
        expect(screen.getByTestId('nav-grow-career-planning')).toHaveAttribute(
          'href',
          '/grow/career-planning'
        )
        expect(screen.getByTestId('nav-grow-skill-development')).toHaveAttribute(
          'href',
          '/grow/skill-development'
        )
        expect(screen.getByTestId('nav-grow-performance-reviews')).toHaveAttribute(
          'href',
          '/grow/performance-reviews'
        )
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes for submenu toggles', async () => {
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const expandButton = screen.getByTestId('nav-focus-toggle')

      // Should be a button element
      expect(expandButton.tagName).toBe('BUTTON')

      // Should have proper role
      expect(expandButton).toHaveAttribute('role', 'button')
    })

    it('should support keyboard navigation for submenu toggles', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const expandButton = screen.getByTestId('nav-focus-toggle')

      // Focus the button
      expandButton.focus()
      expect(expandButton).toHaveFocus()

      // Press Enter to expand
      await user.keyboard('{Enter}')

      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-skills-assessment')).toBeInTheDocument()
      })
    })

    it('should support keyboard navigation for submenu items', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Expand submenu
      const expandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(expandButton)

      await waitFor(() => {
        const submenuItem = screen.getByTestId('nav-focus-skills-assessment')
        submenuItem.focus()
        expect(submenuItem).toHaveFocus()
      })
    })
  })

  describe('Visual Styling and Animations', () => {
    it('should apply correct indentation for submenu items', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const expandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(expandButton)

      await waitFor(() => {
        const submenuContainer = screen.getByTestId('nav-focus-skills-assessment').closest('div')
        expect(submenuContainer).toHaveClass('ml-6')
      })
    })

    it('should apply correct styling for active submenu items', async () => {
      mockPathname.mockReturnValue('/focus/skills-assessment')

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Submenu should be auto-expanded when on a submenu page
      await waitFor(() => {
        const activeSubmenuItem = screen.getByTestId('nav-focus-skills-assessment')
        expect(activeSubmenuItem).toHaveClass('bg-primary/10', 'text-primary')
      })
    })

    it('should apply hover effects to submenu items', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const expandButton = screen.getByTestId('nav-focus-toggle')
      await user.click(expandButton)

      await waitFor(() => {
        const submenuItem = screen.getByTestId('nav-focus-skills-assessment')
        expect(submenuItem).toHaveClass('hover:text-foreground', 'hover:bg-muted/50')
      })
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle missing submenu data gracefully', async () => {
      // This test ensures the component doesn't crash if submenu data is malformed
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Component should render without errors
      expect(screen.getByTestId('sidebar')).toBeInTheDocument()
    })

    it('should handle rapid toggle clicks without errors', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const expandButton = screen.getByTestId('nav-focus-toggle')

      // Rapidly click the toggle button
      await user.click(expandButton)
      await user.click(expandButton)
      await user.click(expandButton)
      await user.click(expandButton)

      // Component should still be functional
      expect(screen.getByTestId('sidebar')).toBeInTheDocument()
    })
  })
})
