import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { expect, describe, it, vi, beforeEach } from 'vitest'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'

// Mock Next.js modules
vi.mock('next/navigation', () => ({
  usePathname: () => '/employee/dashboard',
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  }),
}))

vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'EMPLOYEE',
        companyId: 'test-company-123',
      },
    },
    status: 'authenticated',
  }),
}))

// Mock context providers
vi.mock('@/lib/context/navigation-context', () => ({
  useNavigationContext: () => ({
    navigationState: {
      currentPath: '/employee/dashboard',
      lastNavigationTime: Date.now(),
    },
    setNavigationState: vi.fn(),
  }),
}))

vi.mock('@/lib/context/user-context', () => ({
  useUserContext: () => ({
    userContext: {
      navigationFrequency: {},
      timeSpent: {},
      recentlyVisited: [],
    },
    updateUserContext: vi.fn(),
  }),
}))

vi.mock('@/lib/hooks/useAIModelProvider', () => ({
  useAIModelProvider: () => ({
    currentProvider: 'openai',
    switchProvider: vi.fn(),
    isProviderReady: true,
    providerError: null,
  }),
}))

// Mock feature flags
vi.mock('@/lib/services/feature-flag-service', () => ({
  hasFeatureSync: vi.fn(() => true),
}))

// Mock analytics
vi.mock('@/lib/analytics/behavioral-tracking', () => ({
  trackBehavioralEvent: vi.fn(),
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid='test-wrapper'>{children}</div>
}

describe('Three-Level Navigation System', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear()
    vi.clearAllMocks()
  })

  describe('Level 1: Zone Management', () => {
    it('should display all four main zones', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Check for zone headers using more specific selectors
      const zoneHeaders = screen.getAllByText(/^(Personal|Team|Organisation|Other)$/)
      const zoneHeaderTexts = zoneHeaders
        .filter(el => el.className.includes('uppercase'))
        .map(el => el.textContent)

      expect(zoneHeaderTexts).toContain('Personal')
      expect(zoneHeaderTexts).toContain('Team')
      expect(zoneHeaderTexts).toContain('Organisation')
      expect(zoneHeaderTexts).toContain('Other')
    })

    it('should allow individual zone minimization', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Test Personal zone minimization
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-grow')).not.toBeInTheDocument()
      })

      // Other zones should remain visible
      expect(screen.getByTestId('nav-team')).toBeVisible()
      expect(screen.getByTestId('nav-vision')).toBeVisible()
    })

    it('should support "minimize all zones" functionality', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Click minimize all zones button
      const minimizeAllButton = screen.getByTestId('toggle-all-zones')
      await user.click(minimizeAllButton)

      await waitFor(() => {
        // All navigation items should be hidden
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument()
      })
    })
  })

  describe('Level 2: Main Navigation Items', () => {
    it('should display all main navigation items when zones are expanded', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Personal zone items
      expect(screen.getByTestId('nav-focus')).toBeVisible()
      expect(screen.getByTestId('nav-grow')).toBeVisible()

      // Team zone items
      expect(screen.getByTestId('nav-team')).toBeVisible()
      expect(screen.getByTestId('nav-connect')).toBeVisible()
      expect(screen.getByTestId('nav-celebrate')).toBeVisible()

      // Organization zone items
      expect(screen.getByTestId('nav-vision')).toBeVisible()
      expect(screen.getByTestId('nav-explore')).toBeVisible()
      expect(screen.getByTestId('nav-pulse')).toBeVisible()

      // Other zone items
      expect(screen.getByTestId('nav-help')).toBeVisible()
      expect(screen.getByTestId('nav-support')).toBeVisible()
      expect(screen.getByTestId('nav-settings')).toBeVisible()
    })

    it('should support individual item expansion for sub-navigation', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Focus item should have expansion capability
      const focusItem = screen.getByTestId('nav-focus')
      expect(focusItem).toBeVisible()

      // Look for expansion indicator (chevron or similar)
      const focusExpansionToggle = screen.getByTestId('nav-focus-expand')
      expect(focusExpansionToggle).toBeVisible()

      await user.click(focusExpansionToggle)

      // Should show sub-items
      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-today')).toBeVisible()
        expect(screen.getByTestId('nav-focus-goals')).toBeVisible()
        expect(screen.getByTestId('nav-focus-skills')).toBeVisible()
      })
    })
  })

  describe('Level 3: Sub-Navigation Items', () => {
    it('should display Focus sub-items when expanded', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Expand Focus item
      const focusExpansionToggle = screen.getByTestId('nav-focus-expand')
      await user.click(focusExpansionToggle)

      await waitFor(() => {
        // Focus sub-items
        expect(screen.getByTestId('nav-focus-today')).toBeVisible()
        expect(screen.getByTestId('nav-focus-goals')).toBeVisible()
        expect(screen.getByTestId('nav-focus-skills')).toBeVisible()
        expect(screen.getByTestId('nav-focus-performance')).toBeVisible()
        expect(screen.getByTestId('nav-focus-insights')).toBeVisible()
      })
    })

    it('should display Grow sub-items when expanded', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Expand Grow item
      const growExpansionToggle = screen.getByTestId('nav-grow-expand')
      await user.click(growExpansionToggle)

      await waitFor(() => {
        // Grow sub-items
        expect(screen.getByTestId('nav-grow-learning')).toBeVisible()
        expect(screen.getByTestId('nav-grow-skills')).toBeVisible()
        expect(screen.getByTestId('nav-grow-career')).toBeVisible()
        expect(screen.getByTestId('nav-grow-development')).toBeVisible()
      })
    })

    it('should navigate to correct sub-item URLs', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Expand Focus and click Today
      const focusExpansionToggle = screen.getByTestId('nav-focus-expand')
      await user.click(focusExpansionToggle)

      await waitFor(() => {
        const todayLink = screen.getByTestId('nav-focus-today')
        expect(todayLink).toHaveAttribute('href', '/focus/today')
      })
    })
  })

  describe('Other Zone Integration', () => {
    it('should display other zone with help, support, and settings items', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Other zone should be visible (check for zone header)
      const otherHeaders = screen.getAllByText('Other')
      const otherZoneHeader = otherHeaders.find(el => el.className.includes('uppercase'))
      expect(otherZoneHeader).toBeVisible()

      // Other zone items should be visible
      expect(screen.getByTestId('nav-help')).toBeVisible()
      expect(screen.getByTestId('nav-support')).toBeVisible()
      expect(screen.getByTestId('nav-settings')).toBeVisible()
    })

    it('should support other zone minimization', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Minimize other zone
      const otherToggle = screen.getByTestId('zone-toggle-other')
      await user.click(otherToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-help')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-support')).not.toBeInTheDocument()
        expect(screen.queryByTestId('nav-settings')).not.toBeInTheDocument()
      })

      // Other zones should remain visible
      expect(screen.getByTestId('nav-focus')).toBeVisible()
      expect(screen.getByTestId('nav-team')).toBeVisible()
    })

    it('should display settings sub-items when settings is expanded', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Expand Settings item
      const settingsExpansionToggle = screen.getByTestId('nav-settings-expand')
      await user.click(settingsExpansionToggle)

      await waitFor(() => {
        // Settings sub-items should be visible
        expect(screen.getByTestId('nav-settings-profile')).toBeVisible()
        expect(screen.getByTestId('nav-settings-appearance')).toBeVisible()
        expect(screen.getByTestId('nav-settings-ai-models')).toBeVisible()
        expect(screen.getByTestId('nav-settings-notifications')).toBeVisible()
        expect(screen.getByTestId('nav-settings-privacy')).toBeVisible()
        expect(screen.getByTestId('nav-settings-advanced')).toBeVisible()
      })
    })
  })

  describe('AI Provider Integration', () => {
    it('should display AI provider selector', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      expect(screen.getByTestId('ai-provider-selector')).toBeVisible()
      expect(screen.getByText('Switch AI Model')).toBeVisible()
    })

    it('should allow AI provider switching', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const switchButton = screen.getByText('Switch AI Model')
      await user.click(switchButton)

      // Should show provider options
      await waitFor(() => {
        expect(screen.getByText('OpenAI')).toBeVisible()
        expect(screen.getByText('Claude')).toBeVisible()
      })
    })
  })

  describe('Context-Aware Features', () => {
    it('should display relevance scores for navigation items', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Items with high relevance should have indicators
      const focusItem = screen.getByTestId('nav-focus')
      expect(focusItem).toBeVisible()

      // Should have relevance indicators or badges
      const relevanceBadge = screen.queryByTestId('nav-focus-relevance')
      if (relevanceBadge) {
        expect(relevanceBadge).toBeVisible()
      }
    })

    it('should track navigation analytics', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const focusLink = screen.getByTestId('nav-focus')
      await user.click(focusLink)

      // Analytics should be tracked (mocked function should be called)
      // This will be verified through the mock
    })
  })

  describe('State Persistence', () => {
    it('should persist zone states in localStorage', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Collapse Personal zone
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      // Check localStorage
      expect(localStorage.getItem('sidebar-zone-personalgrowth-collapsed')).toBe('true')
    })

    it('should restore zone states from localStorage', async () => {
      // Pre-set localStorage
      localStorage.setItem('sidebar-zone-personalgrowth-collapsed', 'true')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Personal zone items should be hidden
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })
    })
  })

  describe('Responsive Behavior', () => {
    it('should support sidebar collapse/expand', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(collapseToggle)

      await waitFor(() => {
        const sidebar = screen.getByTestId('sidebar')
        expect(sidebar).toHaveClass('w-16')
      })
    })

    it('should show tooltips when sidebar is collapsed', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Collapse sidebar
      const collapseToggle = screen.getByTestId('sidebar-collapse-toggle')
      await user.click(collapseToggle)

      // Hover over navigation item
      const focusItem = screen.getByTestId('nav-focus')
      await user.hover(focusItem)

      // Check if tooltip content is accessible via title attribute
      await waitFor(() => {
        expect(focusItem).toHaveAttribute('title', 'Your daily command center')
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Navigation should have proper role
      const navigation = screen.getByRole('navigation')
      expect(navigation).toBeVisible()
      expect(navigation).toHaveAttribute('aria-label', 'Main navigation')

      // Zone toggles should have proper labels
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      expect(personalToggle).toHaveAttribute('aria-label')
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Tab through navigation items
      await user.tab()
      await user.tab()

      // Should be able to activate with Enter/Space
      await user.keyboard('{Enter}')

      // Navigation should work
      expect(document.activeElement).toBeDefined()
    })
  })
})
