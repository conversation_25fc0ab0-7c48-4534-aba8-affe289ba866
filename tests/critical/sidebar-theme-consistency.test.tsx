/**
 * @fileoverview Tests for sidebar theme consistency and navigation behavior
 * This test suite verifies that all sidebar contexts (Main, Settings, SuperAdmin)
 * have consistent theming and navigation behavior.
 */

import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'
import SettingsSidebar from '@/components/settings/Sidebar'
import SuperAdminSidebar from '@/components/superadmin/SuperAdminSidebar'
import { TooltipProvider } from '@/components/ui/tooltip'
import { NavigationContextProvider } from '@/lib/context/NavigationContextProvider'
import { act, fireEvent, render, renderHook, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useSession } from 'next-auth/react'
import { usePathname } from 'next/navigation'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

import { QuickActions } from '@/components/shared/QuickActions'
import { useQuickActions } from '@/hooks/useQuickActions'
import { trackBehavioralEvent } from '@/lib/analytics/behavioral-tracking'
import { useNavigationContext } from '@/lib/context/NavigationContextProvider'
import { hasFeatureSync } from '@/lib/services/feature-flag-service'

// Mock Next.js hooks
vi.mock('next-auth/react')
vi.mock('next/navigation')

// Mock dependencies
vi.mock('@/lib/analytics/behavioral-tracking')
vi.mock('@/lib/context/NavigationContextProvider')
vi.mock('@/lib/services/feature-flag-service')
vi.mock('@/components/navigation/SupportModal', () => ({
  SupportModal: ({
    open,
    onOpenChange,
  }: {
    open: boolean
    onOpenChange: (open: boolean) => void
  }) => (
    <div data-testid='support-modal' data-open={open}>
      <button onClick={() => onOpenChange(false)}>Close</button>
    </div>
  ),
}))

const mockUseSession = vi.mocked(useSession)
const mockUsePathname = vi.mocked(usePathname)
const mockTrackBehavioralEvent = vi.mocked(trackBehavioralEvent)
const mockUseNavigationContext = vi.mocked(useNavigationContext)
const mockHasFeatureSync = vi.mocked(hasFeatureSync)

const mockNavigationContext = {
  toggleSidebarMinimize: vi.fn(),
  refreshRecommendations: vi.fn(),
  getSidebarState: vi.fn(() => ({
    isMinimized: false,
    isCollapsed: false,
    width: 256,
  })),
  toggleCollapsed: vi.fn(),
  isCollapsed: false,
}

const mockSession = {
  user: {
    id: 'user-1',
    email: '<EMAIL>',
    role: 'EMPLOYEE',
    companyId: 'company-1',
  },
  expires: '2024-12-31',
}

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <NavigationContextProvider>
    <TooltipProvider>{children}</TooltipProvider>
  </NavigationContextProvider>
)

describe('Sidebar Theme Consistency & Navigation', () => {
  beforeEach(() => {
    mockUseSession.mockReturnValue({
      data: mockSession,
      status: 'authenticated',
      update: vi.fn(),
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Theme Consistency Across Sidebars', () => {
    it('should have consistent theme-aware hover effects in main navigation', async () => {
      mockUsePathname.mockReturnValue('/dashboard')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Find the title link in main navigation
      const titleLink = screen.getByRole('link', { name: /emynent/i })
      expect(titleLink).toBeInTheDocument()

      // Check that the link has the group class for hover effects
      expect(titleLink).toHaveClass('group')

      // Check for theme-aware CSS custom properties
      const titleIcon = titleLink.querySelector('svg')
      expect(titleIcon).toHaveClass('text-[var(--primary)]')
      expect(titleIcon).toHaveClass('group-hover:text-[var(--primary)]/80')
      expect(titleIcon).toHaveClass('transition-colors')

      // Check title text
      const titleText = titleLink.querySelector('span')
      expect(titleText).toHaveClass('group-hover:text-[var(--primary)]')
      expect(titleText).toHaveClass('transition-colors')
    })

    it('should have consistent theme-aware hover effects in settings sidebar', async () => {
      mockUsePathname.mockReturnValue('/settings')

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      // Find the title link in settings sidebar
      const titleLink = screen.getByRole('link', { name: /emynent/i })
      expect(titleLink).toBeInTheDocument()

      // Verify it has group class and theme-aware properties
      expect(titleLink).toHaveClass('group')

      const titleIcon = titleLink.querySelector('svg')
      expect(titleIcon).toHaveClass('text-[var(--primary)]')
      expect(titleIcon).toHaveClass('group-hover:text-[var(--primary)]/80')
      expect(titleIcon).toHaveClass('transition-colors')
    })

    it('should have consistent theme-aware hover effects in superadmin sidebar', async () => {
      mockUsePathname.mockReturnValue('/superadmin')

      render(
        <TestWrapper>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // Find the title link in superadmin sidebar
      const titleLink = screen.getByRole('link', { name: /emynent/i })
      expect(titleLink).toBeInTheDocument()

      // Verify it has group class and theme-aware properties
      expect(titleLink).toHaveClass('group')

      const titleIcon = titleLink.querySelector('svg')
      expect(titleIcon).toHaveClass('text-[var(--primary)]')
      expect(titleIcon).toHaveClass('group-hover:text-[var(--primary)]/80')
      expect(titleIcon).toHaveClass('transition-colors')
    })
  })

  describe('Back Button Consistency', () => {
    it('should not show back button in settings sidebar', async () => {
      mockUsePathname.mockReturnValue('/settings')

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      // Check that no back button exists
      const backButton = screen.queryByTestId('settings-sidebar-back-button')
      expect(backButton).not.toBeInTheDocument()

      // Also check for any arrow left icon which would indicate a back button
      const arrowLeftIcon = screen.queryByRole('button', { name: /go back/i })
      expect(arrowLeftIcon).not.toBeInTheDocument()
    })

    it('should not show back button in superadmin sidebar', async () => {
      mockUsePathname.mockReturnValue('/superadmin')

      render(
        <TestWrapper>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // Check that no back button exists
      const backButton = screen.queryByTestId('superadmin-sidebar-back-button')
      expect(backButton).not.toBeInTheDocument()

      // Also check for any arrow left icon which would indicate a back button
      const arrowLeftIcon = screen.queryByRole('button', { name: /go back/i })
      expect(arrowLeftIcon).not.toBeInTheDocument()
    })

    it('should not show back button in main navigation sidebar', async () => {
      mockUsePathname.mockReturnValue('/dashboard')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Check that no back button exists (main navigation never had one)
      const backButton = screen.queryByTestId('ai-first-sidebar-back-button')
      expect(backButton).not.toBeInTheDocument()

      const arrowLeftIcon = screen.queryByRole('button', { name: /go back/i })
      expect(arrowLeftIcon).not.toBeInTheDocument()
    })
  })

  describe('Header Structure Consistency', () => {
    const testCases = [
      {
        name: 'Main Navigation',
        component: AIFirstSidebar,
        pathname: '/dashboard',
        testId: 'ai-first-sidebar',
      },
      {
        name: 'Settings Sidebar',
        component: SettingsSidebar,
        pathname: '/settings',
        testId: 'settings-sidebar',
      },
      {
        name: 'SuperAdmin Sidebar',
        component: SuperAdminSidebar,
        pathname: '/superadmin',
        testId: 'superadmin-sidebar',
      },
    ]

    testCases.forEach(({ name, component: Component, pathname, testId }) => {
      it(`should have consistent header structure in ${name}`, async () => {
        mockUsePathname.mockReturnValue(pathname)

        render(
          <TestWrapper>
            <Component />
          </TestWrapper>
        )

        // Check for sidebar container
        const sidebar = screen.getByTestId(testId)
        expect(sidebar).toBeInTheDocument()

        // Check for header section with consistent height (h-14)
        const headerElement = sidebar.querySelector('.h-14')
        expect(headerElement).toBeInTheDocument()
        expect(headerElement).toHaveClass('border-b', 'border-border')

        // Check for title link
        const titleLink = screen.getByRole('link', { name: /emynent/i })
        expect(titleLink).toBeInTheDocument()
        expect(titleLink).toHaveAttribute('href', '/dashboard')
      })
    })
  })

  describe('Interactive Behavior', () => {
    it('should track behavior events when title is clicked in main navigation', async () => {
      mockUsePathname.mockReturnValue('/dashboard')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const titleLink = screen.getByRole('link', { name: /emynent/i })

      // Simulate click
      fireEvent.click(titleLink)

      // The navigation should work (we can't easily test the tracking without mocking the function)
      expect(titleLink).toHaveAttribute('href', '/dashboard')
    })

    it('should navigate correctly when title is clicked in all sidebars', async () => {
      const components = [
        { Component: AIFirstSidebar, pathname: '/dashboard' },
        { Component: SettingsSidebar, pathname: '/settings' },
        { Component: SuperAdminSidebar, pathname: '/superadmin' },
      ]

      for (const { Component, pathname } of components) {
        mockUsePathname.mockReturnValue(pathname)

        const { unmount } = render(
          <TestWrapper>
            <Component />
          </TestWrapper>
        )

        const titleLink = screen.getByRole('link', { name: /emynent/i })
        expect(titleLink).toHaveAttribute('href', '/dashboard')

        unmount()
      }
    })
  })

  describe('Visual Consistency', () => {
    it('should use consistent icon and text sizing across all sidebars', async () => {
      const components = [
        { Component: AIFirstSidebar, pathname: '/dashboard', testId: 'ai-first-sidebar' },
        { Component: SettingsSidebar, pathname: '/settings', testId: 'settings-sidebar' },
        { Component: SuperAdminSidebar, pathname: '/superadmin', testId: 'superadmin-sidebar' },
      ]

      for (const { Component, pathname, testId } of components) {
        mockUsePathname.mockReturnValue(pathname)

        const { unmount } = render(
          <TestWrapper>
            <Component />
          </TestWrapper>
        )

        const sidebar = screen.getByTestId(testId)
        const titleIcon = sidebar.querySelector('svg')

        // Check icon sizing consistency
        if (titleIcon) {
          // Main navigation uses h-6 w-6, UnifiedSidebar uses h-7 w-7
          // We should standardize this in a follow-up if needed
          expect(titleIcon).toHaveClass('transition-colors')
        }

        unmount()
      }
    })
  })

  describe('Navigation Behavior', () => {
    it('should not show collapse/minimize button in Settings sidebar', () => {
      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      const collapseButton = screen.queryByTestId('settings-sidebar-collapse-toggle')
      expect(collapseButton).not.toBeInTheDocument()
    })

    it('should not show collapse/minimize button in SuperAdmin sidebar', () => {
      render(
        <TestWrapper>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const collapseButton = screen.queryByTestId('superadmin-sidebar-collapse-toggle')
      expect(collapseButton).not.toBeInTheDocument()
    })
  })

  describe('Interactive Elements', () => {
    it('should have working hover states for logo and icon in Settings sidebar', async () => {
      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      const logoLink = screen.getByRole('link', { name: /emynent/i })
      expect(logoLink).toBeInTheDocument()

      // Test hover interaction
      fireEvent.mouseEnter(logoLink)
      await waitFor(() => {
        expect(logoLink).toHaveClass('group')
      })
    })

    it('should have working hover states for logo and icon in SuperAdmin sidebar', async () => {
      render(
        <TestWrapper>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const logoLink = screen.getByRole('link', { name: /emynent/i })
      expect(logoLink).toBeInTheDocument()

      // Test hover interaction
      fireEvent.mouseEnter(logoLink)
      await waitFor(() => {
        expect(logoLink).toHaveClass('group')
      })
    })
  })
})

describe('Quick Actions Consistency Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    mockUseSession.mockReturnValue({
      data: mockSession,
      status: 'authenticated',
      update: vi.fn(),
    })

    mockUsePathname.mockReturnValue('/dashboard')
    mockUseNavigationContext.mockReturnValue(mockNavigationContext)
    mockHasFeatureSync.mockReturnValue(false)
    mockTrackBehavioralEvent.mockResolvedValue(undefined)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('useQuickActions Hook', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => useQuickActions())

      expect(result.current.state.supportModalOpen).toBe(false)
      expect(result.current.state.allDropdownsCollapsed).toBe(false)
      expect(result.current.config.isContextAware).toBe(false)
      expect(result.current.config.isMinimized).toBe(false)
      expect(result.current.config.isCollapsed).toBe(false)
      expect(result.current.config.userRole).toBe('EMPLOYEE')
      expect(result.current.config.companyId).toBe('company-1')
    })

    it('should handle support toggle correctly', async () => {
      const { result } = renderHook(() => useQuickActions())

      act(() => {
        result.current.handlers.handleSupportToggle()
      })

      expect(result.current.state.supportModalOpen).toBe(true)
      expect(mockTrackBehavioralEvent).toHaveBeenCalledWith(
        'quick_action_support',
        'quick_actions',
        expect.objectContaining({
          action: 'toggle',
          newState: true,
        })
      )
    })

    it('should handle collapse all dropdowns correctly', async () => {
      const { result } = renderHook(() => useQuickActions())

      act(() => {
        result.current.handlers.handleCollapseAllDropdowns()
      })

      expect(result.current.state.allDropdownsCollapsed).toBe(true)
      expect(mockTrackBehavioralEvent).toHaveBeenCalledWith(
        'quick_action_collapse_all',
        'quick_actions',
        expect.objectContaining({
          collapsed: true,
          action: 'collapse',
        })
      )
    })

    it('should handle refresh AI correctly', async () => {
      const { result } = renderHook(() => useQuickActions())

      act(() => {
        result.current.handlers.handleRefreshAI()
      })

      expect(mockNavigationContext.refreshRecommendations).toHaveBeenCalled()
      expect(mockTrackBehavioralEvent).toHaveBeenCalledWith(
        'quick_action_refresh_ai',
        'quick_actions',
        expect.objectContaining({
          action: 'refresh',
          contextAware: false,
          hasRefreshFunction: true,
        })
      )
    })

    it('should handle sidebar toggle correctly', async () => {
      const { result } = renderHook(() => useQuickActions())

      act(() => {
        result.current.handlers.handleSidebarToggle()
      })

      expect(mockNavigationContext.toggleSidebarMinimize).toHaveBeenCalled()
      expect(mockTrackBehavioralEvent).toHaveBeenCalledWith(
        'quick_action_sidebar_toggle',
        'quick_actions',
        expect.objectContaining({
          action: 'toggle',
          previousState: { isMinimized: false, isCollapsed: false },
        })
      )
    })

    it('should detect context-aware features correctly', () => {
      mockHasFeatureSync.mockReturnValue(true)

      const { result } = renderHook(() => useQuickActions())

      expect(result.current.config.isContextAware).toBe(true)
      expect(mockHasFeatureSync).toHaveBeenCalledWith('company-1', 'contextAwareness')
    })

    it('should handle missing session gracefully', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: vi.fn(),
      })

      const { result } = renderHook(() => useQuickActions())

      expect(result.current.config.userRole).toBe(null)
      expect(result.current.config.companyId).toBe(null)
      expect(result.current.config.isContextAware).toBe(false)
    })

    it('should handle behavioral tracking errors gracefully', async () => {
      mockTrackBehavioralEvent.mockRejectedValue(new Error('Tracking failed'))
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const { result } = renderHook(() => useQuickActions())

      act(() => {
        result.current.handlers.handleSupportToggle()
      })

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          'Failed to track behavioral event:',
          expect.any(Error)
        )
      })

      consoleSpy.mockRestore()
    })
  })

  describe('QuickActions Component', () => {
    it('should render all Quick Actions buttons', () => {
      render(<QuickActions />)

      expect(screen.getByTestId('quick-access-support')).toBeInTheDocument()
      expect(screen.getByTestId('quick-access-collapse-all')).toBeInTheDocument()
      expect(screen.getByTestId('sidebar-collapse-toggle')).toBeInTheDocument()
      expect(screen.getByTestId('quick-actions-section')).toBeInTheDocument()
    })

    it('should not render when show prop is false', () => {
      render(<QuickActions show={false} />)

      expect(screen.queryByTestId('quick-actions-section')).not.toBeInTheDocument()
    })

    it('should render Refresh AI button when context-aware features are enabled', () => {
      mockHasFeatureSync.mockReturnValue(true)

      render(<QuickActions />)

      expect(screen.getByTestId('quick-access-refresh-ai')).toBeInTheDocument()
    })

    it('should not render Refresh AI button when context-aware features are disabled', () => {
      mockHasFeatureSync.mockReturnValue(false)

      render(<QuickActions />)

      expect(screen.queryByTestId('quick-access-refresh-ai')).not.toBeInTheDocument()
    })

    it('should handle support button click', async () => {
      const user = userEvent.setup()
      render(<QuickActions />)

      await user.click(screen.getByTestId('quick-access-support'))

      expect(screen.getByTestId('support-modal')).toBeInTheDocument()
      expect(screen.getByTestId('support-modal')).toHaveAttribute('data-open', 'true')
    })

    it('should handle collapse all button click', async () => {
      const user = userEvent.setup()
      render(<QuickActions />)

      const collapseButton = screen.getByTestId('quick-access-collapse-all')

      // Initially should show FoldVertical (collapse)
      expect(collapseButton).toHaveAttribute('aria-label', 'Collapse all dropdowns')

      await user.click(collapseButton)

      // After click should show UnfoldVertical (expand)
      await waitFor(() => {
        expect(collapseButton).toHaveAttribute('aria-label', 'Expand all dropdowns')
      })
    })

    it('should handle sidebar toggle button click', async () => {
      const user = userEvent.setup()
      render(<QuickActions />)

      await user.click(screen.getByTestId('sidebar-collapse-toggle'))

      expect(mockNavigationContext.toggleSidebarMinimize).toHaveBeenCalled()
    })

    it('should call onActionTrigger callback when provided', async () => {
      const onActionTrigger = vi.fn()
      const user = userEvent.setup()

      render(<QuickActions onActionTrigger={onActionTrigger} />)

      await user.click(screen.getByTestId('quick-access-support'))

      expect(onActionTrigger).toHaveBeenCalledWith('support')
    })

    it('should render with custom className and testId', () => {
      render(<QuickActions className='custom-class' testId='custom-test-id' />)

      const quickActions = screen.getByTestId('custom-test-id')
      expect(quickActions).toBeInTheDocument()
      expect(quickActions).toHaveClass('custom-class')
    })

    it('should support different button sizes', () => {
      render(<QuickActions buttonSize='sm' />)

      const buttons = screen.getAllByRole('button')
      buttons.forEach(button => {
        expect(button).toHaveClass('h-6', 'w-6')
      })
    })

    it('should support disabling tooltips', () => {
      render(<QuickActions showTooltips={false} />)

      // Tooltips should not be rendered
      expect(screen.queryByRole('tooltip')).not.toBeInTheDocument()
    })

    it('should update aria-label based on sidebar state', () => {
      mockNavigationContext.getSidebarState.mockReturnValue({
        isMinimized: true,
        isCollapsed: false,
        width: 64,
      })

      render(<QuickActions />)

      const sidebarToggle = screen.getByTestId('sidebar-collapse-toggle')
      expect(sidebarToggle).toHaveAttribute('aria-label', 'Expand sidebar')
    })

    it('should handle vertical orientation', () => {
      render(<QuickActions orientation='vertical' />)

      const buttonContainer = screen.getByTestId('quick-actions-section').firstChild
      expect(buttonContainer).toHaveClass('flex-col', 'space-y-1')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for all buttons', () => {
      render(<QuickActions />)

      expect(screen.getByLabelText('Open support')).toBeInTheDocument()
      expect(screen.getByLabelText('Collapse all dropdowns')).toBeInTheDocument()
      expect(screen.getByLabelText('Minimize sidebar')).toBeInTheDocument()
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()
      render(<QuickActions />)

      const supportButton = screen.getByTestId('quick-access-support')

      // Tab to the button
      await user.tab()
      expect(supportButton).toHaveFocus()

      // Activate with keyboard
      await user.keyboard('{Enter}')

      expect(screen.getByTestId('support-modal')).toHaveAttribute('data-open', 'true')
    })

    it('should update ARIA labels dynamically', async () => {
      const user = userEvent.setup()
      render(<QuickActions />)

      const collapseButton = screen.getByTestId('quick-access-collapse-all')

      expect(collapseButton).toHaveAttribute('aria-label', 'Collapse all dropdowns')

      await user.click(collapseButton)

      await waitFor(() => {
        expect(collapseButton).toHaveAttribute('aria-label', 'Expand all dropdowns')
      })
    })
  })

  describe('Theme Integration', () => {
    it('should apply consistent styling classes', () => {
      render(<QuickActions />)

      const buttons = screen
        .getAllByRole('button')
        .filter(
          btn =>
            btn.getAttribute('data-testid')?.startsWith('quick-access') ||
            btn.getAttribute('data-testid') === 'sidebar-collapse-toggle'
        )

      buttons.forEach(button => {
        expect(button).toHaveClass('transition-colors', 'hover:bg-muted')
      })
    })

    it('should maintain consistent icon sizes', () => {
      render(<QuickActions />)

      // All icons should have consistent h-4 w-4 classes
      const icons = screen.getByTestId('quick-actions-section').querySelectorAll('svg')
      icons.forEach(icon => {
        expect(icon).toHaveClass('h-4', 'w-4')
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle missing navigation context gracefully', () => {
      mockUseNavigationContext.mockReturnValue({
        ...mockNavigationContext,
        toggleSidebarMinimize: undefined as any,
        refreshRecommendations: undefined as any,
        getSidebarState: () => null as any,
      })

      const { result } = renderHook(() => useQuickActions())

      expect(result.current.config.isMinimized).toBe(false)
      expect(result.current.config.isCollapsed).toBe(false)

      // Should not throw when calling handlers
      expect(() => {
        result.current.handlers.handleRefreshAI()
        result.current.handlers.handleSidebarToggle()
      }).not.toThrow()
    })

    it('should handle feature flag service errors gracefully', () => {
      mockHasFeatureSync.mockImplementation(() => {
        throw new Error('Feature flag service error')
      })

      const { result } = renderHook(() => useQuickActions())

      expect(result.current.config.isContextAware).toBe(false)
    })
  })
})
