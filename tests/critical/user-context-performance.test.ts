import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest'
import { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { prisma } from '@/lib/prisma'
import { RedisService } from '@/services/redis-service'
import { GET, POST } from '@/app/api/user/context/route'
import { performance } from 'perf_hooks'

// Mock dependencies
vi.mock('next-auth/jwt')
vi.mock('@/lib/prisma', () => ({
  prisma: {
    userContext: {
      findUnique: vi.fn(),
      upsert: vi.fn(),
    },
  },
}))

const mockGetToken = vi.mocked(getToken)
const mockPrisma = vi.mocked(prisma)

describe('User Context API Performance (Task 14)', () => {
  let redisService: RedisService

  beforeAll(async () => {
    // Initialize Redis service for testing
    redisService = new RedisService()
  })

  beforeEach(() => {
    vi.clearAllMocks()
    // Clear Redis cache before each test
    redisService.clearCache('user:context:*')
  })

  afterAll(async () => {
    redisService.disconnect()
  })

  describe('Performance Requirements', () => {
    it('should respond within 25-50ms for cached requests', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      const mockUserContext = {
        id: 'ctx123',
        userId: 'user123',
        role: 'EMPLOYEE',
        companyId: 'company123',
        preferences: { aiRecommendations: true },
        recentActions: [],
        historicalData: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(mockUserContext)

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // First call to populate cache
      await GET(request)

      // Act - Measure performance of cached request
      const startTime = performance.now()
      const response = await GET(request)
      const endTime = performance.now()
      const duration = endTime - startTime

      // Assert
      expect(response.status).toBe(200)
      expect(duration).toBeLessThan(50) // 50ms performance target
      expect(duration).toBeGreaterThan(1) // Ensure it's not mocked timing
    })

    it('should respond within 50ms for database requests (99th percentile)', async () => {
      // Arrange
      const mockToken = {
        sub: 'user456',
        companyId: 'company123',
        role: 'MANAGER',
      }

      const mockUserContext = {
        id: 'ctx456',
        userId: 'user456',
        role: 'MANAGER',
        companyId: 'company123',
        preferences: { aiRecommendations: true, behavioralTracking: true },
        recentActions: [],
        historicalData: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(mockUserContext)

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act - Measure database request performance
      const startTime = performance.now()
      const response = await GET(request)
      const endTime = performance.now()
      const duration = endTime - startTime

      // Assert
      expect(response.status).toBe(200)
      expect(duration).toBeLessThan(100) // 100ms for database requests
    })

    it('should handle 100 concurrent requests efficiently', async () => {
      // Arrange
      const mockToken = {
        sub: 'user789',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      const mockUserContext = {
        id: 'ctx789',
        userId: 'user789',
        role: 'EMPLOYEE',
        companyId: 'company123',
        preferences: {},
        recentActions: [],
        historicalData: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(mockUserContext)

      // Act - Create 100 concurrent requests
      const requests = Array.from({ length: 100 }, () => {
        const request = new NextRequest('http://localhost:3000/api/user/context')
        return GET(request)
      })

      const startTime = performance.now()
      const responses = await Promise.all(requests)
      const endTime = performance.now()
      const totalDuration = endTime - startTime

      // Assert
      expect(responses).toHaveLength(100)
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      // Average response time should be reasonable
      const avgResponseTime = totalDuration / 100
      expect(avgResponseTime).toBeLessThan(50)

      // Database should only be called once due to request deduplication
      expect(mockPrisma.userContext.findUnique).toHaveBeenCalledOnce()
    })
  })

  describe('Redis Caching Behavior', () => {
    it('should cache user context data with 1-hour TTL', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      const mockUserContext = {
        id: 'ctx123',
        userId: 'user123',
        role: 'EMPLOYEE',
        companyId: 'company123',
        preferences: { aiRecommendations: true },
        recentActions: [],
        historicalData: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(mockUserContext)

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act
      await GET(request)

      // Assert - Check if data is cached
      const cacheKey = 'user:context:user123'
      const cachedData = await redisService.get(cacheKey)
      expect(cachedData).not.toBeNull()

      const parsedData = JSON.parse(cachedData!)
      expect(parsedData.userId).toBe('user123')
      expect(parsedData.companyId).toBe('company123')

      // Check TTL is approximately 1 hour (3600 seconds)
      const ttl = await redisService.ttl(cacheKey)
      expect(ttl).toBeGreaterThan(3500) // Allow some variance
      expect(ttl).toBeLessThanOrEqual(3600)
    })

    it('should use cached data on subsequent requests', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      mockGetToken.mockResolvedValue(mockToken)

      // Pre-populate cache
      const cacheKey = 'user:context:user123'
      const cachedContext = {
        id: 'ctx123',
        userId: 'user123',
        companyId: 'company123',
        preferences: { cached: true },
      }
      await redisService.set(cacheKey, JSON.stringify(cachedContext), 3600)

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act
      const response = await GET(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.context.preferences.cached).toBe(true)

      // Database should not be called when using cache
      expect(mockPrisma.userContext.findUnique).not.toHaveBeenCalled()
    })

    it('should invalidate cache when user context is updated', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      const mockUserContext = {
        id: 'ctx123',
        userId: 'user123',
        role: 'EMPLOYEE',
        companyId: 'company123',
        preferences: { updated: true },
        recentActions: [],
        historicalData: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.upsert.mockResolvedValue(mockUserContext)

      // Pre-populate cache
      const cacheKey = 'user:context:user123'
      await redisService.set(cacheKey, JSON.stringify({ old: 'data' }), 3600)

      const request = new NextRequest('http://localhost:3000/api/user/context', {
        method: 'POST',
        body: JSON.stringify({
          context: {
            preferences: { updated: true },
            recentActions: ['new_action'],
          },
        }),
      })

      // Act
      await POST(request)

      // Assert - Cache should be invalidated
      const cachedData = await redisService.get(cacheKey)
      expect(cachedData).toBeNull()
    })
  })

  describe('Error Handling & Reliability', () => {
    it('should fallback to database when Redis is unavailable', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      const mockUserContext = {
        id: 'ctx123',
        userId: 'user123',
        role: 'EMPLOYEE',
        companyId: 'company123',
        preferences: { fallback: true },
        recentActions: [],
        historicalData: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(mockUserContext)

      // Simulate Redis error by disconnecting
      redisService.disconnect()

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act
      const response = await GET(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.context.preferences.fallback).toBe(true)
      expect(mockPrisma.userContext.findUnique).toHaveBeenCalled()
    })

    it('should return 401 for unauthenticated requests', async () => {
      // Arrange
      mockGetToken.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act
      const response = await GET(request)

      // Assert
      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.error).toBe('Unauthorized')
    })

    it('should return 404 when user context not found', async () => {
      // Arrange
      const mockToken = {
        sub: 'nonexistent_user',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act
      const response = await GET(request)

      // Assert
      expect(response.status).toBe(404)
      const data = await response.json()
      expect(data.error).toBe('User context not found')
    })

    it('should handle database errors gracefully', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockRejectedValue(new Error('Database connection failed'))

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act
      const response = await GET(request)

      // Assert
      expect(response.status).toBe(500)
      const data = await response.json()
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('Request Deduplication', () => {
    it('should prevent multiple database calls for same user ID', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      const mockUserContext = {
        id: 'ctx123',
        userId: 'user123',
        role: 'EMPLOYEE',
        companyId: 'company123',
        preferences: {},
        recentActions: [],
        historicalData: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(mockUserContext)

      // Act - Fire multiple concurrent requests for same user
      const requests = Array.from({ length: 10 }, () => {
        const request = new NextRequest('http://localhost:3000/api/user/context')
        return GET(request)
      })

      const responses = await Promise.all(requests)

      // Assert
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      // Should only call database once due to deduplication
      expect(mockPrisma.userContext.findUnique).toHaveBeenCalledOnce()
    })
  })

  describe('Data Consistency & Type Safety', () => {
    it('should return properly typed UserContext data', async () => {
      // Arrange
      const mockToken = {
        sub: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
      }

      const mockUserContext = {
        id: 'ctx123',
        userId: 'user123',
        role: 'EMPLOYEE',
        companyId: 'company123',
        preferences: {
          aiRecommendations: true,
          behavioralTracking: true,
          contextualHelp: false,
          adaptiveUI: true,
        },
        recentActions: [{ action: 'page_view', timestamp: Date.now(), section: 'dashboard' }],
        historicalData: {
          totalSessions: 45,
          averageSessionDuration: 1800,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockGetToken.mockResolvedValue(mockToken)
      mockPrisma.userContext.findUnique.mockResolvedValue(mockUserContext)

      const request = new NextRequest('http://localhost:3000/api/user/context')

      // Act
      const response = await GET(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.context).toMatchObject({
        userId: 'user123',
        companyId: 'company123',
        role: 'EMPLOYEE',
        preferences: expect.objectContaining({
          aiRecommendations: expect.any(Boolean),
          behavioralTracking: expect.any(Boolean),
          contextualHelp: expect.any(Boolean),
          adaptiveUI: expect.any(Boolean),
        }),
        recentActions: expect.any(Array),
        historicalData: expect.any(Object),
      })
    })
  })
})
