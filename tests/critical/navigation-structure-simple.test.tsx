import React from 'react'
import { render, screen } from '@testing-library/react'
import { expect, describe, it, vi } from 'vitest'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'

// Mock Next.js modules
vi.mock('next/navigation', () => ({
  usePathname: () => '/employee/dashboard',
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  }),
}))

vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'EMPLOYEE',
        companyId: 'test-company-123',
      },
    },
    status: 'authenticated',
  }),
}))

// Mock all the context providers
vi.mock('@/lib/context/navigation-context', () => ({
  useNavigationContext: () => ({
    navigationState: {
      currentPath: '/employee/dashboard',
      lastNavigationTime: Date.now(),
    },
    setNavigationState: vi.fn(),
  }),
}))

vi.mock('@/lib/context/user-context', () => ({
  useUserContext: () => ({
    userContext: {
      navigationFrequency: {},
      timeSpent: {},
      recentlyVisited: [],
    },
    updateUserContext: vi.fn(),
  }),
}))

vi.mock('@/lib/hooks/useAIModelProvider', () => ({
  useAIModelProvider: () => ({
    currentProvider: 'openai',
    switchProvider: vi.fn(),
    isProviderReady: true,
    providerError: null,
  }),
}))

vi.mock('@/lib/services/feature-flag-service', () => ({
  hasFeatureSync: vi.fn(() => true),
}))

vi.mock('@/lib/analytics/behavioral-tracking', () => ({
  trackBehavioralEvent: vi.fn(),
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid='test-wrapper'>{children}</div>
}

describe('Navigation Structure - Simple Test', () => {
  it('should render without crashing', () => {
    // Simple smoke test to verify our changes don't break basic rendering
    expect(() => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )
    }).not.toThrow()
  })

  it('should have basic navigation elements', () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    // Check that the sidebar renders
    const sidebar = screen.getByTestId('ai-first-sidebar')
    expect(sidebar).toBeInTheDocument()
  })

  it('should have platform section for AI provider selector', () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    // Check that platform section exists
    const platformSection = screen.getByTestId('platform-section')
    expect(platformSection).toBeInTheDocument()

    // Check that AI provider selector is in platform section
    const aiSelector = screen.getByTestId('ai-provider-selector')
    expect(platformSection).toContainElement(aiSelector)
  })

  it('should have settings in other zone', () => {
    render(
      <TestWrapper>
        <AIFirstSidebar />
      </TestWrapper>
    )

    // Check that Other zone exists
    const otherZone = screen.getByTestId('zone-other')
    expect(otherZone).toBeInTheDocument()

    // Check that settings is in Other zone
    const settingsItem = screen.getByTestId('nav-settings')
    expect(otherZone).toContainElement(settingsItem)
  })
})
