/**
 * AI-First Navigation System Tests
 * Tests the next-generation AI-native navigation system using REAL data
 * Following TDD principle: when tests pass, the navigation works correctly
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SessionProvider } from 'next-auth/react'
import { Session } from 'next-auth'
import { Role } from '@prisma/client'

// Import real test utilities (no mocking)
import { setupTestDatabase, cleanupTestDatabase } from '../utils/database-helpers'
import { createTestCompany, createTestUser } from '../utils/test-data'

// Import real components
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'
import { NavigationProvider } from '@/lib/context/NavigationContextProvider'
import { UserContextProvider } from '@/lib/context/UserContextProvider'
import { AIModelProvider } from '@/lib/context/AIModelProvider'
import { ThemeProvider } from 'next-themes'

// Import real services (no mocking)
import { hasFeature, hasFeatureSync } from '@/lib/services/feature-flag-service'

// Test wrapper component with all necessary providers
function TestWrapper({ children, session }: { children: React.ReactNode; session: Session }) {
  return (
    <SessionProvider session={session}>
      <ThemeProvider attribute='class' defaultTheme='system' enableSystem>
        {children}
      </ThemeProvider>
    </SessionProvider>
  )
}

describe('AI-First Navigation System', () => {
  let realEmployeeSession: Session
  let realManagerSession: Session
  let realSuperAdminSession: Session

  beforeEach(async () => {
    await setupTestDatabase()

    // Create real company and users in database
    await createTestCompany('company-1', 'Test Company')
    const employeeUser = await createTestUser('emp-user-1', 'company-1', Role.EMPLOYEE)
    const managerUser = await createTestUser('mgr-user-2', 'company-1', Role.MANAGER)
    const superAdminUser = await createTestUser('sa-user-3', 'company-1', Role.SUPERADMIN)

    // Create real sessions from actual users
    realEmployeeSession = {
      user: {
        id: employeeUser.id,
        email: employeeUser.email,
        name: employeeUser.name,
        role: employeeUser.role,
        companyId: employeeUser.companyId,
      },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    }

    realManagerSession = {
      user: {
        id: managerUser.id,
        email: managerUser.email,
        name: managerUser.name,
        role: managerUser.role,
        companyId: managerUser.companyId,
      },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    }

    realSuperAdminSession = {
      user: {
        id: superAdminUser.id,
        email: superAdminUser.email,
        name: superAdminUser.name,
        role: superAdminUser.role,
        companyId: superAdminUser.companyId,
      },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    }
  })

  afterEach(async () => {
    await cleanupTestDatabase()
  })

  describe('AI-Powered Navigation Features', () => {
    test('adapts navigation based on user behavior patterns', async () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Should render with AI-powered adaptations
      expect(screen.getByTestId('sidebar')).toBeInTheDocument()

      // Wait for AI features to initialize (if feature flag enabled)
      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument()
      })
    })

    test('provides contextual recommendations based on user activity', async () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Check for contextual features (when AI features are enabled)
      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument()
      })
    })

    test('learns from user interactions for future optimizations', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Interact with navigation elements
      const sidebar = screen.getByTestId('sidebar')
      expect(sidebar).toBeInTheDocument()

      // Test navigation clicks (when elements are available)
      await waitFor(() => {
        expect(sidebar).toBeInTheDocument()
      })
    })
  })

  describe('Role-Based Access Control', () => {
    test('shows appropriate features for employee role', async () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Employee should see all navigation items but with limited access
      expect(screen.getByText('Focus')).toBeInTheDocument()
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText('Vision')).toBeInTheDocument()

      // Should NOT see SuperAdmin section
      expect(screen.queryByText('Super Admin')).not.toBeInTheDocument()
    })

    test('shows manager-specific features for manager role', async () => {
      render(
        <TestWrapper session={realManagerSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Manager should see all navigation items with enhanced features
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText('Pulse')).toBeInTheDocument()

      // Should NOT see SuperAdmin section
      expect(screen.queryByText('Super Admin')).not.toBeInTheDocument()
    })

    test('shows superadmin panel for superadmin role', async () => {
      render(
        <TestWrapper session={realSuperAdminSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // SuperAdmin should see all navigation items plus SuperAdmin section
      expect(screen.getByText('Focus')).toBeInTheDocument()
      expect(screen.getByText('Super Admin')).toBeInTheDocument()
    })
  })

  describe('Behavioral Tracking and Personalization', () => {
    test('tracks navigation clicks for personalization', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Click on Focus navigation item
      const focusItem = screen.getByTestId('nav-focus')
      await user.click(focusItem)

      // Verify tracking event was fired
      await waitFor(() => {
        expect(focusItem).toHaveAttribute('data-clicked', 'true')
      })

      // Check that behavioral data is being collected
      // (This would integrate with actual behavioral tracking service)
    })

    test('provides personalized suggestions based on usage patterns', async () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Should render with personalized content (when available)
      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument()
      })
    })
  })

  describe('Context-Aware Intelligence', () => {
    test('adapts interface based on user context and preferences', async () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Should show context-aware features (when enabled)
      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument()
      })
    })

    test('provides intelligent recommendations for next actions', async () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      // Should provide intelligent suggestions (when AI features are active)
      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument()
      })
    })
  })

  describe('Performance and Responsiveness', () => {
    test('maintains responsive design across device sizes', async () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      const sidebar = screen.getByTestId('sidebar')
      expect(sidebar).toHaveClass('transition-all')
    })

    test('loads quickly with AI enhancements', async () => {
      const startTime = performance.now()

      render(
        <TestWrapper session={realEmployeeSession}>
          <NavigationProvider>
            <UserContextProvider>
              <AIModelProvider>
                <AIFirstSidebar />
              </AIModelProvider>
            </UserContextProvider>
          </NavigationProvider>
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument()
      })

      const endTime = performance.now()
      const loadTime = endTime - startTime

      // Should load within performance targets
      expect(loadTime).toBeLessThan(500) // 500ms target
    })
  })
})
