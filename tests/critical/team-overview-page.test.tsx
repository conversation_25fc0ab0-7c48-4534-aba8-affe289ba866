import { describe, it, expect, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import TeamOverviewPage from '@/app/(protected)/team/overview/page'
import { render, withDatabaseIsolation, createTestData } from '../utils/test-wrapper'

describe('Team Overview Page - BDD Test Suite', () => {
  describe('Page Structure & Navigation', () => {
    it('renders page title and navigation correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('Team Overview')).toBeInTheDocument()
        expect(
          screen.getByText('Monitor team health, engagement, and performance metrics')
        ).toBeInTheDocument()
      })
    })

    it('displays team health dashboard with key metrics', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        // Team Health Metrics
        expect(screen.getByText('Team Health Score')).toBeInTheDocument()
        expect(screen.getByText('Engagement Level')).toBeInTheDocument()
        expect(screen.getByText('Performance Rating')).toBeInTheDocument()
        expect(screen.getByText('Collaboration Index')).toBeInTheDocument()
      })
    })
  })

  describe('Team Health Monitoring', () => {
    it('shows team health score with visual indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('8.4')).toBeInTheDocument()
        expect(screen.getByText('Out of 10')).toBeInTheDocument()
        expect(screen.getByText('Excellent')).toBeInTheDocument()
      })
    })

    it('displays engagement metrics with trend analysis', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('92%')).toBeInTheDocument()
        expect(screen.getByText('High engagement')).toBeInTheDocument()
        expect(screen.getByText('+5% vs last month')).toBeInTheDocument()
      })
    })

    it('shows performance rating with team comparison', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('4.6')).toBeInTheDocument()
        expect(screen.getByText('Above average')).toBeInTheDocument()
        expect(screen.getByText('Top 15% in org')).toBeInTheDocument()
      })
    })

    it('displays collaboration index with team dynamics', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('87%')).toBeInTheDocument()
        expect(screen.getByText('Strong collaboration')).toBeInTheDocument()
        expect(screen.getByText('Cross-team projects')).toBeInTheDocument()
      })
    })
  })

  describe('Team Member Overview', () => {
    it('displays team member cards with key information', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('Team Members')).toBeInTheDocument()
        expect(screen.getByText('Sarah Chen')).toBeInTheDocument()
        expect(screen.getByText('Senior Frontend Developer')).toBeInTheDocument()
        expect(screen.getByText('Mike Johnson')).toBeInTheDocument()
        expect(screen.getByText('Backend Developer')).toBeInTheDocument()
      })
    })

    it('shows individual performance indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('4.8/5.0')).toBeInTheDocument()
        expect(screen.getByText('95% goal completion')).toBeInTheDocument()
        expect(screen.getByText('4.2/5.0')).toBeInTheDocument()
        expect(screen.getByText('88% goal completion')).toBeInTheDocument()
      })
    })

    it('displays current focus and development areas', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('React Performance')).toBeInTheDocument()
        expect(screen.getByText('API Architecture')).toBeInTheDocument()
        expect(screen.getByText('Leadership Skills')).toBeInTheDocument()
        expect(screen.getByText('System Design')).toBeInTheDocument()
      })
    })
  })

  describe('Recent Activity & Updates', () => {
    it('shows recent team activities and achievements', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('Recent Activity')).toBeInTheDocument()
        expect(screen.getByText('Sarah completed React Advanced certification')).toBeInTheDocument()
        expect(screen.getByText('Team achieved 95% sprint completion rate')).toBeInTheDocument()
        expect(screen.getByText('Mike started mentoring junior developers')).toBeInTheDocument()
      })
    })

    it('displays upcoming deadlines and milestones', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('Upcoming Milestones')).toBeInTheDocument()
        expect(screen.getByText('Q1 Performance Reviews')).toBeInTheDocument()
        expect(screen.getByText('Due in 2 weeks')).toBeInTheDocument()
        expect(screen.getByText('Team Building Workshop')).toBeInTheDocument()
        expect(screen.getByText('Next Friday')).toBeInTheDocument()
      })
    })
  })

  describe('AI-Powered Insights', () => {
    it('shows AI-generated team insights and recommendations', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('AI Insights')).toBeInTheDocument()
        expect(screen.getByText('Team Productivity Trend')).toBeInTheDocument()
        expect(
          screen.getByText('Your team shows 15% higher productivity during morning hours')
        ).toBeInTheDocument()
        expect(screen.getByText('Skill Development Opportunity')).toBeInTheDocument()
        expect(
          screen.getByText('Consider cross-training in cloud technologies')
        ).toBeInTheDocument()
      })
    })

    it('displays predictive analytics for team performance', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('Performance Forecast')).toBeInTheDocument()
        expect(screen.getByText('Projected to exceed Q1 goals by 12%')).toBeInTheDocument()
        expect(screen.getByText('Risk Assessment')).toBeInTheDocument()
        expect(screen.getByText('Low risk of burnout detected')).toBeInTheDocument()
      })
    })
  })

  describe('Quick Actions & Navigation', () => {
    it('provides quick action buttons for common tasks', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('Schedule 1:1s')).toBeInTheDocument()
        expect(screen.getByText('Team Check-in')).toBeInTheDocument()
        expect(screen.getByText('Performance Review')).toBeInTheDocument()
        expect(screen.getByText('Goal Setting')).toBeInTheDocument()
      })
    })

    it('allows navigation to detailed team sections', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByText('View All Members')).toBeInTheDocument()
        expect(screen.getByText('Team Analytics')).toBeInTheDocument()
        expect(screen.getByText('Development Plans')).toBeInTheDocument()
        expect(screen.getByText('Feedback History')).toBeInTheDocument()
      })
    })
  })

  describe('Responsive Design & Accessibility', () => {
    it('maintains responsive layout across screen sizes', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        const container = screen.getByTestId('team-overview-container')
        expect(container).toHaveClass('container', 'mx-auto', 'px-4', 'py-8')
      })
    })

    it('provides proper accessibility attributes', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        expect(screen.getByRole('main')).toBeInTheDocument()
        expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()
        expect(screen.getByLabelText('Team health metrics')).toBeInTheDocument()
      })
    })
  })

  describe('User Interactions', () => {
    it('handles team member card clicks for detailed view', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        const memberCard = screen.getByTestId('member-card-sarah')
        fireEvent.click(memberCard)

        // Should show detailed member information
        expect(screen.getByText('Member Details')).toBeInTheDocument()
      })
    })

    it('allows filtering and sorting of team data', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createManagerWithTeam()

        render(<TeamOverviewPage />)

        const filterButton = screen.getByText('Filter by Performance')
        fireEvent.click(filterButton)

        expect(screen.getByText('High Performers')).toBeInTheDocument()
        expect(screen.getByText('Needs Support')).toBeInTheDocument()
      })
    })
  })
})
