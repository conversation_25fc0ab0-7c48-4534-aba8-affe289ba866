/**
 * Behavioral Analytics Complete Test Suite - Task 15
 * BDD-style comprehensive testing for unified analytics + optimization approach
 *
 * Focus: AI-first, performance-optimized behavioral analytics database
 * Requirements: 10,000+ events/minute, 25-50ms write latency, API optimization
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { AnalyticsService } from '@/services/analytics-service'
import { BehavioralAnalyticsService } from '@/services/behavioral-analytics-service'
import { NavigationAnalyticsService } from '@/services/navigation-analytics-service'
import { UserPreferencesService } from '@/services/user-preferences-service'
import { AIInsightsService } from '@/services/ai-insights-service'

// Test database clients
const mainDb = new PrismaClient({
  datasources: { db: { url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL } },
})

const analyticsDb = new PrismaClient({
  datasources: {
    db: {
      url:
        process.env.TEST_ANALYTICS_DATABASE_URL ||
        'postgresql://localhost:5432/emynent_analytics_test',
    },
  },
})

describe('Task 15: Unified Analytics + API Optimization', () => {
  let testUserId: string
  let testCompanyId: string
  let testSessionId: string

  beforeAll(async () => {
    testCompanyId = 'test-company-unified'
    testUserId = 'unified-test-user'
    testSessionId = 'unified-test-session'

    // Create test user
    await mainDb.user.create({
      data: {
        id: testUserId,
        email: '<EMAIL>',
        name: 'Unified Test User',
        companyId: testCompanyId,
        role: 'EMPLOYEE',
      },
    })

    // Enable analytics feature flag
    await mainDb.featureFlag.upsert({
      where: { companyId_key: { companyId: testCompanyId, key: 'behavioralAnalytics' } },
      create: {
        companyId: testCompanyId,
        key: 'behavioralAnalytics',
        enabled: true,
      },
      update: { enabled: true },
    })
  })

  afterAll(async () => {
    // Cleanup
    await analyticsDb.eventStream.deleteMany({ where: { userId: testUserId } })
    await analyticsDb.userSession.deleteMany({ where: { userId: testUserId } })
    await analyticsDb.performanceMetric.deleteMany({ where: { userId: testUserId } })
    await mainDb.user.delete({ where: { id: testUserId } })
    await mainDb.featureFlag.deleteMany({ where: { companyId: testCompanyId } })
    await mainDb.$disconnect()
    await analyticsDb.$disconnect()
  })

  describe('Feature 1: High-Performance Event Collection', () => {
    describe('Scenario: Single Event Tracking Performance', () => {
      it('Given a user tracks an event, When written to analytics DB, Then response time should be under 25ms', async () => {
        // Given
        const eventData = {
          userId: testUserId,
          companyId: testCompanyId,
          sessionId: testSessionId,
          eventType: 'interaction',
          action: 'click',
          target: 'submit-button',
          context: { page: '/dashboard', component: 'form' },
          payload: { buttonId: 'submit', formValid: true },
        }

        // When
        const startTime = Date.now()
        await analyticsDb.eventStream.create({ data: eventData })
        const responseTime = Date.now() - startTime

        // Then
        expect(responseTime).toBeLessThan(25)

        const storedEvent = await analyticsDb.eventStream.findFirst({
          where: { userId: testUserId },
        })
        expect(storedEvent).toBeDefined()
        expect(storedEvent?.eventType).toBe('interaction')
      })
    })

    describe('Scenario: Bulk Event Processing', () => {
      it('Given 1000 events need processing, When batch inserted, Then throughput should exceed 10,000/minute', async () => {
        // Given
        const events = Array.from({ length: 1000 }, (_, i) => ({
          userId: testUserId,
          companyId: testCompanyId,
          sessionId: testSessionId,
          eventType: 'bulk_test',
          action: 'performance_test',
          target: `item-${i}`,
          context: { batchIndex: i },
          payload: { testData: `bulk-${i}` },
        }))

        // When
        const startTime = Date.now()
        await analyticsDb.eventStream.createMany({ data: events })
        const duration = Date.now() - startTime

        // Then
        const eventsPerMinute = (1000 / duration) * 60000
        expect(eventsPerMinute).toBeGreaterThan(10000)

        const insertedCount = await analyticsDb.eventStream.count({
          where: { eventType: 'bulk_test' },
        })
        expect(insertedCount).toBe(1000)
      })
    })
  })

  describe('Feature 2: API Performance Optimization', () => {
    describe('Scenario: Performance Monitoring Integration', () => {
      it('Given API calls are made, When performance is tracked, Then Sentry integration should capture metrics', async () => {
        // Given
        const performanceData = {
          endpoint: '/api/user/preferences',
          method: 'GET',
          userId: testUserId,
          companyId: testCompanyId,
          responseTime: 45,
          statusCode: 200,
          cacheHit: true,
        }

        // When
        await analyticsDb.performanceMetric.create({ data: performanceData })

        // Then
        const metrics = await analyticsDb.performanceMetric.findMany({
          where: { endpoint: '/api/user/preferences' },
        })
        expect(metrics).toHaveLength(1)
        expect(metrics[0].responseTime).toBe(45)
        expect(metrics[0].cacheHit).toBe(true)
      })

      it('Given slow API responses occur, When thresholds exceeded, Then alerts should be tracked', async () => {
        // Given - Simulate slow response
        const slowPerformanceData = {
          endpoint: '/api/user/preferences',
          method: 'GET',
          userId: testUserId,
          companyId: testCompanyId,
          responseTime: 285, // Above 50ms threshold
          statusCode: 200,
          cacheHit: false,
        }

        // When
        await analyticsDb.performanceMetric.create({ data: slowPerformanceData })

        // Then
        const slowMetrics = await analyticsDb.performanceMetric.findMany({
          where: {
            endpoint: '/api/user/preferences',
            responseTime: { gt: 50 },
          },
        })
        expect(slowMetrics).toHaveLength(1)
        expect(slowMetrics[0].responseTime).toBeGreaterThan(50)
      })
    })
  })

  describe('Feature 3: AI Training Pipeline Data Structure', () => {
    describe('Scenario: ML Feature Vector Generation', () => {
      it('Given user behavior data exists, When feature vectors are generated, Then AI training data should be structured correctly', async () => {
        // Given - Create behavior data
        await analyticsDb.eventStream.createMany({
          data: [
            {
              userId: testUserId,
              companyId: testCompanyId,
              sessionId: testSessionId,
              eventType: 'page_visit',
              action: 'navigate',
              target: '/dashboard',
              context: { referrer: '/login' },
              payload: { duration: 1500 },
            },
            {
              userId: testUserId,
              companyId: testCompanyId,
              sessionId: testSessionId,
              eventType: 'feature_usage',
              action: 'click',
              target: 'export_button',
              context: { feature: 'data_export' },
              payload: { success: true },
            },
          ],
        })

        // When - Generate ML feature vector
        const featureVector = {
          userId: testUserId,
          companyId: testCompanyId,
          featureSet: 'user_behavior',
          features: {
            page_visits_per_session: 2.5,
            feature_usage_rate: 0.8,
            avg_session_duration: 1500,
            error_rate: 0.1,
          },
          labels: {
            user_segment: 'power_user',
            engagement_level: 'high',
          },
        }

        await analyticsDb.mLFeatureVector.create({ data: featureVector })

        // Then
        const storedVector = await analyticsDb.mLFeatureVector.findFirst({
          where: { userId: testUserId },
        })
        expect(storedVector).toBeDefined()
        expect(storedVector?.featureSet).toBe('user_behavior')
        expect(storedVector?.features).toMatchObject({
          page_visits_per_session: 2.5,
          feature_usage_rate: 0.8,
        })
      })
    })

    describe('Scenario: Behavioral Pattern Detection', () => {
      it('Given user events exist, When patterns are analyzed, Then insights should be generated for AI training', async () => {
        // Given - Pattern data
        const patternData = {
          userId: testUserId,
          companyId: testCompanyId,
          patternType: 'navigation',
          pattern: {
            sequence: ['/dashboard', '/settings', '/profile'],
            frequency: 5,
            timePattern: 'morning_routine',
          },
          confidence: 0.85,
          frequency: 5,
          impact: 'medium',
          insights: {
            description: 'User follows consistent morning routine',
            recommendations: ['Optimize dashboard loading', 'Preload settings data'],
          },
        }

        // When
        await analyticsDb.behaviorPattern.create({ data: patternData })

        // Then
        const patterns = await analyticsDb.behaviorPattern.findMany({
          where: { userId: testUserId },
        })
        expect(patterns).toHaveLength(1)
        expect(patterns[0].confidence).toBeGreaterThan(0.8)
        expect(patterns[0].insights).toMatchObject({
          description: expect.stringContaining('morning routine'),
        })
      })
    })
  })

  describe('Feature 4: Real-time Performance Monitoring', () => {
    describe('Scenario: Session Tracking with Performance Correlation', () => {
      it('Given a user session is active, When performance metrics are collected, Then session data should correlate with performance', async () => {
        // Given - Start session
        const sessionData = {
          userId: testUserId,
          companyId: testCompanyId,
          sessionId: testSessionId,
          deviceInfo: {
            browser: 'Chrome',
            os: 'macOS',
            screenSize: '1920x1080',
          },
          pageCount: 3,
          eventCount: 10,
          engagementScore: 0.75,
        }

        await analyticsDb.userSession.create({ data: sessionData })

        // When - Track performance during session
        await analyticsDb.performanceMetric.createMany({
          data: [
            {
              endpoint: '/api/user/context',
              method: 'GET',
              userId: testUserId,
              companyId: testCompanyId,
              responseTime: 32,
              statusCode: 200,
              cacheHit: true,
            },
            {
              endpoint: '/api/user/preferences',
              method: 'GET',
              userId: testUserId,
              companyId: testCompanyId,
              responseTime: 45,
              statusCode: 200,
              cacheHit: true,
            },
          ],
        })

        // Then
        const session = await analyticsDb.userSession.findFirst({
          where: { sessionId: testSessionId },
        })
        const sessionMetrics = await analyticsDb.performanceMetric.findMany({
          where: { userId: testUserId },
        })

        expect(session).toBeDefined()
        expect(session?.engagementScore).toBe(0.75)
        expect(sessionMetrics).toHaveLength(2)

        const avgResponseTime =
          sessionMetrics.reduce((sum, m) => sum + m.responseTime, 0) / sessionMetrics.length
        expect(avgResponseTime).toBeLessThan(50)
      })
    })
  })

  describe('Feature 5: Error Tracking and AI Insights', () => {
    describe('Scenario: Error Pattern Detection for AI Training', () => {
      it('Given errors occur in the system, When tracked in analytics DB, Then AI insights should be generated', async () => {
        // Given - Error events
        await analyticsDb.errorEvent.createMany({
          data: [
            {
              userId: testUserId,
              companyId: testCompanyId,
              sessionId: testSessionId,
              errorType: 'api_timeout',
              errorCode: '504',
              message: 'Gateway timeout on user preferences API',
              context: { endpoint: '/api/user/preferences', duration: 5000 },
              severity: 'high',
            },
            {
              userId: testUserId,
              companyId: testCompanyId,
              sessionId: testSessionId,
              errorType: 'validation_error',
              errorCode: '400',
              message: 'Invalid form data submitted',
              context: { form: 'user_profile', field: 'email' },
              severity: 'medium',
            },
          ],
        })

        // When - Generate AI insight
        const insight = {
          userId: testUserId,
          companyId: testCompanyId,
          insightType: 'performance',
          title: 'API Timeout Pattern Detected',
          description: 'User preferences API showing consistent timeout issues',
          recommendation: 'Implement request timeout optimization and caching strategy',
          confidence: 0.9,
          impact: 'high',
          category: 'performance_optimization',
          metadata: {
            affected_endpoint: '/api/user/preferences',
            error_frequency: 2,
            suggested_actions: ['add_caching', 'optimize_query', 'add_timeout_handling'],
          },
        }

        await analyticsDb.aIInsight.create({ data: insight })

        // Then
        const insights = await analyticsDb.aIInsight.findMany({
          where: { companyId: testCompanyId },
        })
        const errors = await analyticsDb.errorEvent.findMany({
          where: { userId: testUserId },
        })

        expect(insights).toHaveLength(1)
        expect(insights[0].confidence).toBeGreaterThan(0.8)
        expect(errors).toHaveLength(2)

        const highSeverityErrors = errors.filter(e => e.severity === 'high')
        expect(highSeverityErrors).toHaveLength(1)
      })
    })
  })

  describe('Feature 6: Data Aggregation for AI Training', () => {
    describe('Scenario: Daily Aggregates for ML Pipeline', () => {
      it('Given daily analytics data exists, When aggregated, Then ML training data should be prepared', async () => {
        // Given - Multiple events throughout day
        const today = new Date()
        today.setHours(0, 0, 0, 0)

        await analyticsDb.eventStream.createMany({
          data: Array.from({ length: 50 }, (_, i) => ({
            userId: testUserId,
            companyId: testCompanyId,
            sessionId: testSessionId,
            eventType: 'page_visit',
            action: 'navigate',
            target: `/page-${i % 5}`,
            context: { timeOfDay: i % 24 },
            payload: { pageIndex: i },
          })),
        })

        // When - Create daily aggregates
        const aggregates = [
          {
            date: today,
            companyId: testCompanyId,
            metricType: 'user_engagement',
            value: 0.75,
            additionalData: { user_count: 10, session_count: 25 },
          },
          {
            date: today,
            companyId: testCompanyId,
            metricType: 'api_performance',
            value: 42.5, // Average response time
            additionalData: { total_requests: 100, cache_hit_rate: 0.8 },
          },
        ]

        await analyticsDb.dailyAggregate.createMany({ data: aggregates })

        // Then
        const storedAggregates = await analyticsDb.dailyAggregate.findMany({
          where: { companyId: testCompanyId },
        })
        const eventCount = await analyticsDb.eventStream.count({
          where: { userId: testUserId, eventType: 'page_visit' },
        })

        expect(storedAggregates).toHaveLength(2)
        expect(eventCount).toBe(50)

        const performanceAggregate = storedAggregates.find(a => a.metricType === 'api_performance')
        expect(performanceAggregate?.value).toBe(42.5)
      })
    })
  })
})

/**
 * Integration Tests: Analytics + Main App
 */
describe('Analytics Integration with Main Application', () => {
  let testUserId: string
  let testCompanyId: string

  beforeAll(async () => {
    testUserId = 'integration-user'
    testCompanyId = 'integration-company'
  })

  describe('Cross-Database Data Consistency', () => {
    it('Given user data in main DB, When analytics events are created, Then data should be consistent', async () => {
      // Given
      await mainDb.user.create({
        data: {
          id: testUserId,
          email: '<EMAIL>',
          name: 'Integration User',
          companyId: testCompanyId,
          role: 'EMPLOYEE',
        },
      })

      // When
      await analyticsDb.eventStream.create({
        data: {
          userId: testUserId,
          companyId: testCompanyId,
          sessionId: 'integration-session',
          eventType: 'integration_test',
          action: 'cross_db_test',
          target: 'data_consistency',
          context: { test: 'integration' },
          payload: { success: true },
        },
      })

      // Then
      const mainUser = await mainDb.user.findUnique({ where: { id: testUserId } })
      const analyticsEvent = await analyticsDb.eventStream.findFirst({
        where: { userId: testUserId },
      })

      expect(mainUser).toBeDefined()
      expect(analyticsEvent).toBeDefined()
      expect(analyticsEvent?.userId).toBe(mainUser?.id)
      expect(analyticsEvent?.companyId).toBe(mainUser?.companyId)

      // Cleanup
      await analyticsDb.eventStream.deleteMany({ where: { userId: testUserId } })
      await mainDb.user.delete({ where: { id: testUserId } })
    })
  })
})

/**
 * Performance Benchmarks
 */
describe('Performance Benchmarks - Task 15 Requirements', () => {
  it('BENCHMARK: 25ms write latency target', async () => {
    const measurements: number[] = []
    const iterations = 50

    for (let i = 0; i < iterations; i++) {
      const start = Date.now()
      await analyticsDb.eventStream.create({
        data: {
          userId: 'benchmark-user',
          companyId: 'benchmark-company',
          sessionId: 'benchmark-session',
          eventType: 'benchmark',
          action: 'performance_test',
          target: 'write_latency',
          context: { iteration: i },
          payload: { benchmark: true },
        },
      })
      measurements.push(Date.now() - start)
    }

    const averageLatency = measurements.reduce((a, b) => a + b, 0) / measurements.length
    const p95Latency = measurements.sort((a, b) => a - b)[Math.floor(measurements.length * 0.95)]

    expect(averageLatency).toBeLessThan(25)
    expect(p95Latency).toBeLessThan(50)

    // Cleanup
    await analyticsDb.eventStream.deleteMany({ where: { userId: 'benchmark-user' } })
  })

  it('BENCHMARK: 10,000+ events/minute throughput', async () => {
    const eventCount = 1000 // Scaled for test speed
    const timeLimit = 6000 // 6 seconds

    const events = Array.from({ length: eventCount }, (_, i) => ({
      userId: 'throughput-user',
      companyId: 'throughput-company',
      sessionId: 'throughput-session',
      eventType: 'throughput_test',
      action: 'bulk_insert',
      target: 'performance',
      context: { batchIndex: Math.floor(i / 100) },
      payload: { eventIndex: i },
    }))

    const startTime = Date.now()
    await analyticsDb.eventStream.createMany({ data: events })
    const actualTime = Date.now() - startTime

    const eventsPerMinute = (eventCount / actualTime) * 60000
    expect(eventsPerMinute).toBeGreaterThan(10000)
    expect(actualTime).toBeLessThan(timeLimit)

    // Cleanup
    await analyticsDb.eventStream.deleteMany({ where: { userId: 'throughput-user' } })
  })
})
