import { describe, it, expect } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import ExternalPage from '@/app/(protected)/explore/external/page'
import { render, withDatabaseIsolation, createTestData } from '../utils/test-wrapper'

describe('Explore External Page', () => {
  describe('Page Structure & Navigation', () => {
    it('renders page title and navigation correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('External Learning')).toBeInTheDocument()
        expect(
          screen.getByText(
            'Discover external learning opportunities, conferences, and industry resources'
          )
        ).toBeInTheDocument()
      })
    })

    it('displays external learning categories section', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getAllByText('Categories')[0]).toBeInTheDocument()
        expect(screen.getByText('Conferences')).toBeInTheDocument()
        expect(screen.getByText('Online Courses')).toBeInTheDocument()
        expect(screen.getAllByText('Certifications')[0]).toBeInTheDocument()
        expect(screen.getByText('Workshops')).toBeInTheDocument()
      })
    })

    it('shows growth indicators for external learning categories', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('+35% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+28% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+42% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+22% this quarter')).toBeInTheDocument()
      })
    })
  })

  describe('AI Insights Section', () => {
    it('displays AI insights section with recommendations', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getAllByText('AI Insights')[0]).toBeInTheDocument()
        expect(screen.getByText('Learning Recommendation')).toBeInTheDocument()
        expect(screen.getByText('Industry Trend')).toBeInTheDocument()
      })
    })

    it('shows industry trend analysis', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getAllByText('Industry Trend')[0]).toBeInTheDocument()
        expect(
          screen.getByText('AI and machine learning skills are in highest demand across industries')
        ).toBeInTheDocument()
      })
    })

    it('displays skill development insights', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('Skill Development')).toBeInTheDocument()
        expect(
          screen.getByText('External certifications increase career advancement by 60%')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Learning Overview Section', () => {
    it('shows learning overview information', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('Learning Overview')).toBeInTheDocument()
        expect(screen.getAllByText('Available Courses')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Completion Rate')[0]).toBeInTheDocument()
      })
    })

    it('displays learning metrics and indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('1,247 available courses')).toBeInTheDocument()
        expect(screen.getAllByText('84%')[0]).toBeInTheDocument()
      })
    })
  })

  describe('External Opportunities Section', () => {
    it('displays external opportunities with proper structure', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('External Opportunities')).toBeInTheDocument()
        expect(screen.getByText('AI & Machine Learning Summit 2024')).toBeInTheDocument()
        expect(screen.getByText('Advanced React Development Course')).toBeInTheDocument()
      })
    })

    it('shows opportunity details and status', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('Conference')).toBeInTheDocument()
        expect(screen.getByText('Online Course')).toBeInTheDocument()
        expect(screen.getAllByText('Available')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Enrolling')[0]).toBeInTheDocument()
      })
    })

    it('displays opportunity value and metrics', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getAllByText('High Value')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Medium Value')[0]).toBeInTheDocument()
        expect(screen.getByText('3 days duration')).toBeInTheDocument()
        expect(screen.getByText('8 weeks duration')).toBeInTheDocument()
      })
    })

    it('shows opportunity cost and investment', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('$1,200 investment')).toBeInTheDocument()
        expect(screen.getByText('$299 investment')).toBeInTheDocument()
        expect(screen.getByText('San Francisco, CA')).toBeInTheDocument()
        expect(screen.getByText('Online Platform')).toBeInTheDocument()
      })
    })
  })

  describe('Quick Actions Section', () => {
    it('displays quick action buttons', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getAllByText('Quick Actions')[0]).toBeInTheDocument()
        expect(screen.getByTestId('browse-courses-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('find-conferences-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('certification-paths-sidebar')).toBeInTheDocument()
      })
    })

    it('handles quick action button clicks', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        const coursesButton = screen.getByTestId('browse-courses-sidebar')
        const conferencesButton = screen.getByTestId('find-conferences-sidebar')
        const certificationButton = screen.getByTestId('certification-paths-sidebar')

        fireEvent.click(coursesButton)
        fireEvent.click(conferencesButton)
        fireEvent.click(certificationButton)

        // Buttons should be clickable (no errors thrown)
        expect(coursesButton).toBeInTheDocument()
        expect(conferencesButton).toBeInTheDocument()
        expect(certificationButton).toBeInTheDocument()
      })
    })
  })

  describe('Learning Resources Section', () => {
    it('displays learning resources with platforms', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getAllByText('Learning Resources')[0]).toBeInTheDocument()
        expect(screen.getByText('Popular Platforms')).toBeInTheDocument()
        expect(screen.getByText('Upcoming Events')).toBeInTheDocument()
      })
    })

    it('shows learning metrics and progress indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getByText('12 popular platforms')).toBeInTheDocument()
        expect(screen.getByText('8 upcoming events')).toBeInTheDocument()
      })
    })
  })

  describe('External Growth Display', () => {
    it('displays various growth percentages correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        expect(screen.getAllByText('60% growth')[0]).toBeInTheDocument()
        expect(screen.getByText('+35% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+28% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+42% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+22% this quarter')).toBeInTheDocument()
      })
    })
  })

  describe('User Interaction & Accessibility', () => {
    it('supports keyboard navigation for interactive elements', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        const exploreButton = screen.getByTestId('explore-opportunities-header')

        exploreButton.focus()
        expect(document.activeElement).toBe(exploreButton)
      })
    })

    it('has proper ARIA labels and accessibility features', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        const buttons = screen.getAllByRole('button')
        expect(buttons.length).toBeGreaterThan(0)

        buttons.forEach(button => {
          expect(button).toBeInTheDocument()
        })
      })
    })
  })

  describe('Responsive Design Elements', () => {
    it('renders properly with responsive layout', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ExternalPage />)

        // Check that main container exists
        const container = screen.getByText('External Learning').closest('div')
        expect(container).toBeInTheDocument()
      })
    })
  })
})
