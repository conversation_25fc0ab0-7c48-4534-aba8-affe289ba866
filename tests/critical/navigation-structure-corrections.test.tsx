import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { expect, describe, it, vi, beforeEach } from 'vitest'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'

// Mock Next.js modules
vi.mock('next/navigation', () => ({
  usePathname: () => '/employee/dashboard',
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  }),
}))

vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'EMPLOYEE',
        companyId: 'test-company-123',
      },
    },
    status: 'authenticated',
  }),
}))

// Mock context providers
vi.mock('@/lib/context/navigation-context', () => ({
  useNavigationContext: () => ({
    navigationState: {
      currentPath: '/employee/dashboard',
      lastNavigationTime: Date.now(),
    },
    setNavigationState: vi.fn(),
  }),
}))

vi.mock('@/lib/context/user-context', () => ({
  useUserContext: () => ({
    userContext: {
      navigationFrequency: {},
      timeSpent: {},
      recentlyVisited: [],
    },
    updateUserContext: vi.fn(),
  }),
}))

vi.mock('@/lib/hooks/useAIModelProvider', () => ({
  useAIModelProvider: () => ({
    currentProvider: 'openai',
    switchProvider: vi.fn(),
    isProviderReady: true,
    providerError: null,
  }),
}))

// Mock feature flags
vi.mock('@/lib/services/feature-flag-service', () => ({
  hasFeatureSync: vi.fn(() => true),
}))

// Mock analytics
vi.mock('@/lib/analytics/behavioral-tracking', () => ({
  trackBehavioralEvent: vi.fn(),
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid='test-wrapper'>{children}</div>
}

describe('Navigation Structure Corrections', () => {
  beforeEach(() => {
    localStorage.clear()
    vi.clearAllMocks()
  })

  describe('Zone Order Requirements', () => {
    it('should display zones in correct order: Personal, Team, Organisation, Other (at bottom)', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Get all zone headers in document order
      const allHeaders = screen.getAllByText(/^(Personal|Team|Organisation|Other)$/)
      const zoneHeaders = allHeaders
        .filter(el => el.className.includes('uppercase'))
        .map(el => el.textContent)

      // Verify order: Other should be last (at bottom)
      expect(zoneHeaders).toEqual(['Personal', 'Team', 'Organisation', 'Other'])

      // Verify Other zone is positioned at the bottom
      const otherZoneIndex = zoneHeaders.indexOf('Other')
      expect(otherZoneIndex).toBe(zoneHeaders.length - 1)
    })

    it('should have "Other" zone at bottom left of sidebar', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const otherZoneElement = screen.getByText('Other').closest('[data-testid*="zone"]')
      const sidebar = screen.getByTestId('ai-first-sidebar')

      // Verify Other zone is within the sidebar and positioned at bottom
      expect(sidebar).toContainElement(otherZoneElement)

      // Check that Other zone comes after all other zones in DOM order
      const allZones = sidebar.querySelectorAll('[data-testid*="zone"]')
      const otherZonePosition = Array.from(allZones).indexOf(otherZoneElement as Element)
      expect(otherZonePosition).toBe(allZones.length - 1)
    })
  })

  describe('Settings Integration Requirements', () => {
    it('should show Settings as submenu under "Other" zone, not as standalone', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Settings should be within Other zone
      const otherZone = screen.getByTestId('zone-other')
      const settingsItem = screen.getByTestId('nav-settings')

      expect(otherZone).toContainElement(settingsItem)

      // Settings should link to existing /settings route
      const settingsLink = settingsItem.querySelector('a[href="/settings"]')
      expect(settingsLink).toBeInTheDocument()
    })

    it('should NOT have duplicate settings pages or components', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Should only have ONE settings item that links to /settings
      const settingsLinks = screen.getAllByTestId('nav-settings')
      expect(settingsLinks).toHaveLength(1)

      // Should not have separate setting sub-items that duplicate existing settings structure
      expect(screen.queryByTestId('nav-settings-profile')).not.toBeInTheDocument()
      expect(screen.queryByTestId('nav-settings-appearance')).not.toBeInTheDocument()
      expect(screen.queryByTestId('nav-settings-ai-models')).not.toBeInTheDocument()
    })

    it('should use existing high-quality settings at /settings route', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const settingsItem = screen.getByTestId('nav-settings')
      const settingsLink = settingsItem.querySelector('a')

      expect(settingsLink).toHaveAttribute('href', '/settings')

      // Verify it's not creating new routes or components
      expect(settingsLink).not.toHaveAttribute('href', '/settings/profile')
      expect(settingsLink).not.toHaveAttribute('href', '/settings/appearance')
    })
  })

  describe('AI Model Switch Requirements', () => {
    it('should have "Switch AI Model" in Platform section, NOT in Other zone', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // AI Model selector should NOT be in Other zone
      const otherZone = screen.getByTestId('zone-other')
      const aiModelSelector = screen.getByTestId('ai-provider-selector')

      expect(otherZone).not.toContainElement(aiModelSelector)

      // Should be in Platform section (could be settings/platform or dedicated platform area)
      // For now, let's verify it's not in Other zone as that's the main issue
      expect(screen.getByTestId('ai-provider-selector')).toBeInTheDocument()
    })

    it('should move AI provider selector to appropriate platform location', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const aiSelector = screen.getByTestId('ai-provider-selector')

      // Should not be in the main navigation zones
      const personalZone = screen.getByTestId('zone-personalgrowth')
      const teamZone = screen.getByTestId('zone-teamconnection')
      const orgZone = screen.getByTestId('zone-organizationimpact')
      const otherZone = screen.getByTestId('zone-other')

      expect(personalZone).not.toContainElement(aiSelector)
      expect(teamZone).not.toContainElement(aiSelector)
      expect(orgZone).not.toContainElement(aiSelector)
      expect(otherZone).not.toContainElement(aiSelector)

      // Should be in a platform/system section or bottom controls area
      // This allows for flexible implementation while ensuring it's not in wrong zones
    })
  })

  describe('Navigation Anti-Patterns Prevention', () => {
    it('should NOT have recreated settings pages that duplicate existing functionality', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Should not have navigation to recreated settings routes
      const allLinks = screen.getAllByRole('link')
      const settingsLinks = allLinks.filter(
        link =>
          link.getAttribute('href')?.includes('/settings') &&
          link.getAttribute('href') !== '/settings'
      )

      // Should only have main /settings link, no sub-routes in navigation
      expect(settingsLinks).toHaveLength(0)
    })

    it('should maintain clean folder structure without duplicate settings components', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // This test verifies the structure doesn't create duplicate navigation paths
      // All settings functionality should route through /settings
      const settingsItem = screen.getByTestId('nav-settings')
      const href = settingsItem.querySelector('a')?.getAttribute('href')

      expect(href).toBe('/settings')
    })
  })

  describe('User Experience Requirements', () => {
    it('should provide intuitive navigation flow: zones → main items → existing settings', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Navigate to Other zone → Settings
      const otherZone = screen.getByTestId('zone-other')
      expect(otherZone).toBeVisible()

      const settingsItem = screen.getByTestId('nav-settings')
      expect(settingsItem).toBeVisible()

      // Should have clear visual hierarchy
      expect(otherZone).toContainElement(settingsItem)
    })

    it('should reduce friction by eliminating duplicate settings access paths', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Should have exactly one clear path to settings
      const settingsAccessPoints = screen
        .getAllByText(/settings/i)
        .filter(el => el.closest('[role="link"]') || el.closest('button'))

      // Only one main settings access point in navigation
      expect(
        settingsAccessPoints.filter(el => el.textContent?.toLowerCase() === 'settings')
      ).toHaveLength(1)
    })
  })

  describe('AI-First Principles Compliance', () => {
    it('should support behavioral tracking for corrected navigation structure', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      const settingsItem = screen.getByTestId('nav-settings')
      await user.click(settingsItem)

      // Behavioral tracking should work for the corrected structure
      expect(settingsItem).toBeInTheDocument()
    })

    it('should maintain context-awareness with proper navigation hierarchy', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Context-aware features should work with corrected structure
      const sidebar = screen.getByTestId('ai-first-sidebar')
      expect(sidebar).toBeInTheDocument()

      // AI provider selector should be properly positioned for context-aware recommendations
      const aiSelector = screen.getByTestId('ai-provider-selector')
      expect(aiSelector).toBeInTheDocument()
    })
  })
})
