/**
 * Navigation Theme Integration Tests - Manual Authentication
 *
 * Comprehensive tests for theme color implementation in navigation components
 * following TDD/BDD principles with manual authentication handling.
 */

import { test, expect, Page } from '@playwright/test'

// Skip global setup for this test
test.use({ storageState: { cookies: [], origins: [] } })

test.describe('🎨 Navigation Theme Integration (Manual Auth)', () => {
  let page: Page

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage
  })

  async function authenticateUser() {
    // Manual authentication process
    await page.goto('/signin')
    await page.waitForLoadState('networkidle')

    try {
      // Try to find and fill email input
      const emailInput = page.locator('input[type="email"]').first()
      if (await emailInput.isVisible({ timeout: 3000 })) {
        await emailInput.fill('<EMAIL>')
      }

      // Try to find and fill password input
      const passwordInput = page.locator('input[type="password"]').first()
      if (await passwordInput.isVisible({ timeout: 3000 })) {
        await passwordInput.fill('password')
      }

      // Try to submit form
      const submitButton = page.locator('button[type="submit"]').first()
      if (await submitButton.isVisible({ timeout: 3000 })) {
        await submitButton.click()

        // Wait for potential redirect
        await page.waitForLoadState('networkidle', { timeout: 10000 })

        // Check if we're now on dashboard or still on auth page
        const currentUrl = page.url()
        if (currentUrl.includes('/dashboard')) {
          return true // Successfully authenticated
        }
      }
    } catch (error) {
      console.log('Authentication failed, will continue with public testing')
    }

    return false // Authentication failed or not needed
  }

  test('should verify default theme colors in navigation elements', async () => {
    // BDD: GIVEN the application loads
    // WHEN I examine navigation elements
    // THEN they should use the correct default theme colors

    const isAuthenticated = await authenticateUser()

    if (isAuthenticated) {
      // We're on dashboard - test authenticated navigation
      console.log('✅ Authenticated - testing dashboard navigation')

      // Wait for navigation to load
      await page.waitForSelector('nav', { timeout: 10000 })

      // Verify primary color is applied
      const primaryColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
      })

      expect(primaryColor).toBe('#8957e5')
      console.log(`✅ Primary color verified: ${primaryColor}`)

      // Test breadcrumb highlighting
      const dashboardBreadcrumb = page
        .locator('nav')
        .getByText('Dashboard', { exact: false })
        .first()
      if (await dashboardBreadcrumb.isVisible({ timeout: 5000 })) {
        const breadcrumbClasses = await dashboardBreadcrumb.getAttribute('class')
        console.log(`Breadcrumb classes: ${breadcrumbClasses}`)

        // Should have text-primary class or similar
        expect(breadcrumbClasses).toMatch(/(text-primary|text-\[.*\]|current)/)
        console.log('✅ Breadcrumb has theme-aware classes')
      }

      // Test burger menu (if present)
      const burgerButton = page.locator('[data-testid="navbar-burger-menu"]').first()
      if (await burgerButton.isVisible({ timeout: 2000 })) {
        const burgerIcon = page
          .locator('[data-testid="collapse-icon"], [data-testid="expand-icon"]')
          .first()
        if (await burgerIcon.isVisible()) {
          const iconClasses = await burgerIcon.getAttribute('class')
          expect(iconClasses).toMatch(/(group-hover:text-primary|hover:text-primary)/)
          console.log('✅ Burger menu has theme-aware hover classes')
        }
      }

      // Test search elements (if present)
      const searchInput = page.locator('input[placeholder*="search"], input[type="search"]').first()
      if (await searchInput.isVisible({ timeout: 2000 })) {
        const searchClasses = await searchInput.getAttribute('class')
        expect(searchClasses).toMatch(/(focus:border-primary|border-primary)/)
        console.log('✅ Search input has theme-aware focus classes')
      }

      // Test sidebar navigation items (if present)
      const navItems = page.locator('nav a, [role="navigation"] a').filter({
        hasText: /Dashboard|Settings|Profile|Admin/,
      })

      if ((await navItems.count()) > 0) {
        const firstNavItem = navItems.first()
        const navClasses = await firstNavItem.getAttribute('class')

        // Should have some theme-related classes
        expect(navClasses).toMatch(/(text-primary|bg-primary|hover:text-primary|hover:bg-primary)/)
        console.log('✅ Navigation items have theme-aware classes')
      }
    } else {
      // Test theme on sign-in page
      console.log('✅ Testing theme on authentication page')

      // Verify theme is still loaded on auth pages
      const primaryColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
      })

      expect(primaryColor).toBe('#8957e5')
      console.log(`✅ Primary color verified on auth page: ${primaryColor}`)

      // Test form elements use theme colors
      const formInputs = page.locator('input[type="email"], input[type="password"]')
      if ((await formInputs.count()) > 0) {
        const firstInput = formInputs.first()
        const inputClasses = await firstInput.getAttribute('class')

        // Should have focus states that use theme colors
        expect(inputClasses).toMatch(/(focus:border-primary|focus:ring-primary|border-\[.*\])/)
        console.log('✅ Form inputs have theme-aware focus states')
      }

      const submitButton = page.locator('button[type="submit"]').first()
      if (await submitButton.isVisible()) {
        const buttonClasses = await submitButton.getAttribute('class')

        // Should use theme colors for background or text
        expect(buttonClasses).toMatch(/(bg-primary|text-primary|bg-\[.*\])/)
        console.log('✅ Submit button uses theme colors')
      }
    }
  })

  test('should verify theme colors are consistent across page elements', async () => {
    // BDD: GIVEN the page has loaded with theme colors
    // WHEN I examine all elements using primary colors
    // THEN they should be visually consistent

    const isAuthenticated = await authenticateUser()
    const pageName = isAuthenticated ? 'dashboard' : 'authentication'

    console.log(`✅ Testing theme consistency on ${pageName} page`)

    // Get all theme colors
    const themeColors = await page.evaluate(() => {
      const styles = getComputedStyle(document.documentElement)
      return {
        primary: styles.getPropertyValue('--primary').trim(),
        secondary: styles.getPropertyValue('--secondary').trim(),
        accent: styles.getPropertyValue('--accent').trim(),
        background: styles.getPropertyValue('--background').trim(),
        foreground: styles.getPropertyValue('--foreground').trim(),
      }
    })

    console.log('Theme colors:', themeColors)

    // Verify all theme colors are defined
    Object.entries(themeColors).forEach(([name, color]) => {
      expect(color).toBeTruthy()
      expect(color).toMatch(/^#[0-9a-fA-F]{6}$/)
      console.log(`✅ ${name}: ${color}`)
    })

    // Find all elements that should use primary color
    const primaryElements = await page.locator('.text-primary, [class*="text-primary"]').count()
    console.log(`Found ${primaryElements} elements using primary color class`)

    // Find elements with computed primary color
    const elementsWithPrimaryColor = await page.evaluate(expectedPrimary => {
      const elements = Array.from(document.querySelectorAll('*'))
      let count = 0

      elements.forEach(el => {
        const computedColor = getComputedStyle(el).color
        const computedBg = getComputedStyle(el).backgroundColor
        const computedBorder = getComputedStyle(el).borderColor

        // Convert hex to rgb for comparison
        const temp = document.createElement('div')
        temp.style.color = expectedPrimary
        document.body.appendChild(temp)
        const expectedRgb = getComputedStyle(temp).color
        document.body.removeChild(temp)

        if (
          computedColor === expectedRgb ||
          computedBg === expectedRgb ||
          computedBorder === expectedRgb
        ) {
          count++
        }
      })

      return count
    }, themeColors.primary)

    console.log(`Found ${elementsWithPrimaryColor} elements with computed primary color`)
    expect(elementsWithPrimaryColor).toBeGreaterThan(0)
  })

  test('should handle responsive design with theme colors', async () => {
    // BDD: GIVEN the page loads on different screen sizes
    // WHEN I check theme application
    // THEN colors should remain consistent across viewports

    const isAuthenticated = await authenticateUser()

    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' },
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      console.log(`Testing ${viewport.name} (${viewport.width}x${viewport.height})`)

      // Wait for responsive changes to settle
      await page.waitForTimeout(500)

      // Verify theme colors persist across viewport changes
      const primaryColor = await page.evaluate(() => {
        return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
      })

      expect(primaryColor).toBe('#8957e5')
      console.log(`✅ ${viewport.name}: Primary color consistent`)

      // Check if navigation is still visible and themed
      const navElement = page.locator('nav').first()
      if (await navElement.isVisible()) {
        console.log(`✅ ${viewport.name}: Navigation visible`)
      }
    }

    // Reset to default viewport
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('should verify accessibility of themed elements', async () => {
    // BDD: GIVEN themed elements are rendered
    // WHEN I check accessibility properties
    // THEN they should meet basic accessibility standards

    const isAuthenticated = await authenticateUser()

    // Check color contrast (simplified)
    const contrastCheck = await page.evaluate(() => {
      const primary = getComputedStyle(document.documentElement)
        .getPropertyValue('--primary')
        .trim()
      const background = getComputedStyle(document.documentElement)
        .getPropertyValue('--background')
        .trim()

      // Basic contrast check - in real implementation, use proper WCAG formula
      const isValid = primary !== background && primary.length > 0 && background.length > 0

      return {
        primary,
        background,
        hasContrast: isValid,
      }
    })

    expect(contrastCheck.hasContrast).toBe(true)
    console.log(
      `✅ Color contrast check: Primary ${contrastCheck.primary} vs Background ${contrastCheck.background}`
    )

    // Check for ARIA labels on interactive elements
    const interactiveElements = page.locator('button, a, input[type="submit"], [role="button"]')
    const elementCount = await interactiveElements.count()

    if (elementCount > 0) {
      console.log(`Found ${elementCount} interactive elements`)

      // Check at least one element for accessibility attributes
      const firstElement = interactiveElements.first()
      const hasAriaLabel = await firstElement.getAttribute('aria-label')
      const hasTitle = await firstElement.getAttribute('title')
      const hasText = await firstElement.textContent()

      const hasAccessibilityInfo =
        hasAriaLabel || hasTitle || (hasText && hasText.trim().length > 0)
      expect(hasAccessibilityInfo).toBe(true)
      console.log('✅ Interactive elements have accessibility information')
    }
  })

  test('should maintain theme during page transitions', async () => {
    // BDD: GIVEN I navigate between different pages
    // WHEN the pages load
    // THEN theme colors should remain consistent

    const isAuthenticated = await authenticateUser()

    if (isAuthenticated) {
      // Test navigation between authenticated pages
      const pages = ['/dashboard', '/settings']

      for (const pagePath of pages) {
        await page.goto(pagePath)
        await page.waitForLoadState('networkidle')

        const primaryColor = await page.evaluate(() => {
          return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
        })

        expect(primaryColor).toBe('#8957e5')
        console.log(`✅ Theme consistent on ${pagePath}`)
      }
    } else {
      // Test navigation between public pages
      const pages = ['/', '/signin']

      for (const pagePath of pages) {
        await page.goto(pagePath)
        await page.waitForLoadState('networkidle')

        const primaryColor = await page.evaluate(() => {
          return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
        })

        expect(primaryColor).toBe('#8957e5')
        console.log(`✅ Theme consistent on ${pagePath}`)
      }
    }
  })
})
