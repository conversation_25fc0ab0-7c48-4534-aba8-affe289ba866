/**
 * TDD Test: NextAuth Vendor Chunk Generation
 *
 * This test ensures NextAuth vendor chunks are properly generated during build process
 * Following TDD methodology - test first, then verify fix
 *
 * CRITICAL: Tests NextAuth v5 beta integration with Next.js 15+ build system
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs'
import path from 'path'

const execAsync = promisify(exec)

describe('NextAuth Vendor Chunk Build System', () => {
  const nextDir = path.join(process.cwd(), '.next')
  const staticChunksDir = path.join(nextDir, 'static', 'chunks')
  const serverChunksDir = path.join(nextDir, 'server', 'chunks')

  beforeAll(async () => {
    // Clean previous build artifacts
    if (fs.existsSync(nextDir)) {
      fs.rmSync(nextDir, { recursive: true, force: true })
    }
  }, 120000) // 2 min timeout for cleanup

  afterAll(async () => {
    // Keep build artifacts for debugging if test fails
    if (process.env.NODE_ENV !== 'test') {
      console.log('Build artifacts preserved in .next directory for debugging')
    }
  })

  it('should generate NextAuth vendor chunks during build', async () => {
    // GREEN PHASE: This test should now pass with proper webpack config
    // Build the application
    console.log('🔨 Building Next.js application...')

    try {
      const { stdout, stderr } = await execAsync('npm run build', {
        timeout: 180000, // 3 minutes timeout
        env: { ...process.env, NODE_ENV: 'production' },
      })

      console.log('Build stdout:', stdout)
      if (stderr) console.log('Build stderr:', stderr)

      // Verify build completed successfully
      expect(fs.existsSync(nextDir)).toBe(true)

      // Next.js 15+ uses static/chunks for client-side chunks
      expect(fs.existsSync(staticChunksDir)).toBe(true)

      // Check for NextAuth-related vendor chunks in static/chunks
      const staticChunkFiles = fs.readdirSync(staticChunksDir)
      console.log('Generated static chunks:', staticChunkFiles)

      // NextAuth should generate vendor chunks (our webpack config creates next-auth-vendor)
      const nextAuthChunks = staticChunkFiles.filter(
        file => file.includes('next-auth-vendor') || file.includes('auth') || file.includes('@auth')
      )

      expect(nextAuthChunks.length).toBeGreaterThan(0)

      // Verify specific NextAuth vendor chunk exists
      const nextAuthVendorChunk = staticChunkFiles.find(file => file.startsWith('next-auth-vendor'))
      expect(nextAuthVendorChunk).toBeDefined()

      // Verify chunk content is valid
      if (nextAuthVendorChunk) {
        const chunkPath = path.join(staticChunksDir, nextAuthVendorChunk)
        const chunkContent = fs.readFileSync(chunkPath, 'utf8')
        expect(chunkContent.length).toBeGreaterThan(0)
        expect(chunkContent).toContain('next-auth') // Should contain NextAuth code
      }
    } catch (error) {
      console.error('Build failed:', error)

      // Provide detailed failure information for debugging
      if (fs.existsSync(nextDir)) {
        console.log('Build directory exists but build failed')

        if (fs.existsSync(staticChunksDir)) {
          const existingChunks = fs.readdirSync(staticChunksDir)
          console.log('Existing static chunks:', existingChunks)
        } else {
          console.log('Static chunks directory does not exist')
        }
      } else {
        console.log('.next directory does not exist - build completely failed')
      }

      throw error
    }
  }, 240000) // 4 minutes timeout for full build

  it('should generate proper webpack module chunks', async () => {
    // Verify specific webpack chunks that were failing
    expect(fs.existsSync(serverChunksDir)).toBe(true)

    // Look for numbered chunks that were failing (7719.js, 8548.js)
    const serverFiles = fs.readdirSync(serverChunksDir, { recursive: true }) as string[]

    // Should have JavaScript chunks
    const jsChunks = serverFiles.filter(file => file.endsWith('.js'))
    expect(jsChunks.length).toBeGreaterThan(0)

    console.log('Generated server JS chunks:', jsChunks.slice(0, 10)) // Show first 10

    // Should include the specific chunks that were missing (7719.js)
    const has7719 = jsChunks.some(file => file.includes('7719'))
    if (has7719) {
      console.log('✅ Critical chunk 7719.js found')
    }
  })

  it('should have static assets properly generated', async () => {
    // Verify static assets that were returning 404s
    const staticDir = path.join(nextDir, 'static')
    if (fs.existsSync(staticDir)) {
      const staticFiles = fs.readdirSync(staticDir, { recursive: true })
      console.log('Generated static files:', staticFiles.slice(0, 10))
    }

    // Check for critical CSS and JS files
    const appBuildManifest = path.join(nextDir, 'app-build-manifest.json')
    if (fs.existsSync(appBuildManifest)) {
      const manifest = JSON.parse(fs.readFileSync(appBuildManifest, 'utf8'))
      expect(manifest).toBeDefined()
      console.log('App build manifest keys:', Object.keys(manifest))
    }
  })
})
