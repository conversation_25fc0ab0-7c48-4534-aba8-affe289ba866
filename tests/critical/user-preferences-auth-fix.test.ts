import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { testApiHandler } from 'next-test-api-route-handler'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth/config'
import * as handler from '@/app/api/user/preferences/route'
import bcrypt from 'bcryptjs'

// TDD Test Suite for Task 13: Fix User Preferences API Authentication
// Target: 100% authentication success rate, 25-50ms response time
// Addresses: 401 authentication errors, session validation, multi-tenant support

describe('User Preferences API Authentication Fixes - TDD Implementation', () => {
  let testCompany: any
  let testUser: any
  let testUserSession: any

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.user.deleteMany({
      where: {
        email: {
          startsWith: 'test-user-prefs-',
        },
      },
    })

    await prisma.company.deleteMany({
      where: {
        name: {
          startsWith: 'Test Company Prefs',
        },
      },
    })

    // Create test company
    testCompany = await prisma.company.create({
      data: {
        name: 'Test Company Prefs Auth',
        domain: 'testcompanyprefs.com',
        subscriptionStatus: 'ACTIVE',
        plan: 'ENTERPRISE',
        maxUsers: 100,
      },
    })

    // Create test user with proper multi-tenant structure
    const hashedPassword = await bcrypt.hash('testpassword123', 12)
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User Preferences',
        password: hashedPassword,
        role: 'EMPLOYEE',
        companyId: testCompany.id,
        themeMode: 'light',
        colorScheme: 'blue',
        isEmailVerified: true,
      },
    })

    // Mock session structure that should include companyId
    testUserSession = {
      user: {
        id: testUser.id,
        email: testUser.email,
        name: testUser.name,
        role: testUser.role,
        companyId: testUser.companyId, // CRITICAL: This must be present
      },
      expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    }
  })

  afterEach(async () => {
    // Clean up test data
    if (testUser?.id) {
      await prisma.user.delete({
        where: { id: testUser.id },
      })
    }
    if (testCompany?.id) {
      await prisma.company.delete({
        where: { id: testCompany.id },
      })
    }
  })

  // TDD Test 1: Authentication Success with Valid Session
  it('should successfully authenticate GET request with valid session including companyId', async () => {
    // Mock auth to return valid session with companyId
    vi.spyOn(auth, 'auth').mockResolvedValue(testUserSession)

    const startTime = Date.now()

    await testApiHandler({
      handler: handler.GET,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'GET',
        })

        const responseTime = Date.now() - startTime
        const data = await response.json()

        // Performance requirement: 25-50ms response time
        expect(responseTime).toBeLessThanOrEqual(50)

        // Authentication success requirement: 100% success rate
        expect(response.status).toBe(200)
        expect(data.user.email).toBe(testUser.email)
        expect(data.user.preferences.themeMode).toBe('light')
        expect(data.user.preferences.colorScheme).toBe('blue')
        expect(data.company.id).toBe(testCompany.id)
      },
    })
  })

  // TDD Test 2: Authentication Failure Without Session
  it('should return 401 when session is missing', async () => {
    // Mock auth to return null session
    vi.spyOn(auth, 'auth').mockResolvedValue(null)

    await testApiHandler({
      handler: handler.GET,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'GET',
        })

        const data = await response.json()

        expect(response.status).toBe(401)
        expect(data.error).toContain('Unauthorized: Please sign in')
      },
    })
  })

  // TDD Test 3: Authentication Failure Without CompanyId in Session
  it('should return 401 when session lacks companyId', async () => {
    // Mock auth to return session without companyId
    const sessionWithoutCompanyId = {
      ...testUserSession,
      user: {
        ...testUserSession.user,
        companyId: undefined, // Missing companyId
      },
    }

    vi.spyOn(auth, 'auth').mockResolvedValue(sessionWithoutCompanyId)

    await testApiHandler({
      handler: handler.GET,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'GET',
        })

        const data = await response.json()

        expect(response.status).toBe(401)
        expect(data.error).toContain('Unauthorized: Please sign in')
      },
    })
  })

  // TDD Test 4: Multi-tenant Company Context Validation
  it('should return 403 when user exists but with wrong company context', async () => {
    // Create another company
    const otherCompany = await prisma.company.create({
      data: {
        name: 'Other Test Company',
        domain: 'othertestcompany.com',
        subscriptionStatus: 'ACTIVE',
        plan: 'STARTER',
        maxUsers: 10,
      },
    })

    // Mock session with wrong companyId
    const wrongCompanySession = {
      ...testUserSession,
      user: {
        ...testUserSession.user,
        companyId: otherCompany.id, // Wrong company
      },
    }

    vi.spyOn(auth, 'auth').mockResolvedValue(wrongCompanySession)

    await testApiHandler({
      handler: handler.GET,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'GET',
        })

        const data = await response.json()

        expect(response.status).toBe(403)
        expect(data.error).toContain('Invalid company context')
      },
    })

    // Clean up
    await prisma.company.delete({
      where: { id: otherCompany.id },
    })
  })

  // TDD Test 5: PUT Request Authentication Success with Fixed Variable Bug
  it('should successfully authenticate PUT request and update preferences', async () => {
    vi.spyOn(auth, 'auth').mockResolvedValue(testUserSession)

    const startTime = Date.now()

    await testApiHandler({
      handler: handler.PUT,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            themeMode: 'dark', // This should work now (was _themeMode bug)
            colorScheme: 'green',
          }),
        })

        const responseTime = Date.now() - startTime
        const data = await response.json()

        // Performance requirement: 25-50ms response time
        expect(responseTime).toBeLessThanOrEqual(50)

        // Authentication success requirement: 100% success rate
        expect(response.status).toBe(200)
        expect(data.message).toContain('updated successfully')
        expect(data.user.preferences.themeMode).toBe('dark')
        expect(data.user.preferences.colorScheme).toBe('green')

        // Verify database was updated
        const updatedUser = await prisma.user.findUnique({
          where: { id: testUser.id },
          select: { themeMode: true, colorScheme: true },
        })

        expect(updatedUser?.themeMode).toBe('dark')
        expect(updatedUser?.colorScheme).toBe('green')
      },
    })
  })

  // TDD Test 6: Session Validation Performance Test
  it('should meet performance requirements for session validation', async () => {
    vi.spyOn(auth, 'auth').mockResolvedValue(testUserSession)

    // Test multiple requests to ensure consistent performance
    const performanceTests = Array.from({ length: 5 }, async () => {
      const startTime = Date.now()

      return testApiHandler({
        handler: handler.GET,
        test: async ({ fetch }) => {
          const response = await fetch({
            method: 'GET',
          })

          const responseTime = Date.now() - startTime

          expect(response.status).toBe(200)
          expect(responseTime).toBeLessThanOrEqual(50) // 25-50ms target
          expect(responseTime).toBeGreaterThanOrEqual(0)

          return responseTime
        },
      })
    })

    const responseTimes = await Promise.all(performanceTests)
    const averageTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length

    // Average should be well within target range
    expect(averageTime).toBeLessThanOrEqual(45)
  })

  // TDD Test 7: Expired Company Subscription Handling
  it('should handle expired company subscription correctly', async () => {
    // Update company to expired status
    await prisma.company.update({
      where: { id: testCompany.id },
      data: { subscriptionStatus: 'EXPIRED' },
    })

    vi.spyOn(auth, 'auth').mockResolvedValue(testUserSession)

    await testApiHandler({
      handler: handler.GET,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'GET',
        })

        const data = await response.json()

        expect(response.status).toBe(403)
        expect(data.error).toContain('subscription has expired')
      },
    })
  })

  // TDD Test 8: Error Handling and Logging
  it('should handle database errors gracefully with proper logging', async () => {
    vi.spyOn(auth, 'auth').mockResolvedValue(testUserSession)

    // Mock prisma to throw an error
    vi.spyOn(prisma.user, 'findUnique').mockRejectedValue(new Error('Database connection failed'))

    await testApiHandler({
      handler: handler.GET,
      test: async ({ fetch }) => {
        const response = await fetch({
          method: 'GET',
        })

        const data = await response.json()

        expect(response.status).toBe(500)
        expect(data.error).toContain('Internal Server Error')
        expect(data.error).toContain('try again later')
      },
    })
  })
})
