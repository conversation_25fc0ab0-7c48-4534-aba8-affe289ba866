import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'
import SettingsSidebar from '@/components/settings/Sidebar'

// Mock dependencies
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>

// Test wrapper component
function TestWrapper({ children }: { children: React.ReactNode }) {
  return <div data-testid='test-wrapper'>{children}</div>
}

describe('Settings Navigation Consolidation (TDD)', () => {
  beforeEach(() => {
    mockUseSession.mockReturnValue({
      data: {
        user: {
          id: '1',
          email: '<EMAIL>',
          role: 'EMPLOYEE',
        },
      },
      status: 'authenticated',
    })
    mockUsePathname.mockReturnValue('/settings')
  })

  describe('BDD: As a user, I want a single, unified settings navigation', () => {
    it('GIVEN I am on any settings page, WHEN I view the navigation, THEN I should see only one settings entry point from the main sidebar', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Should have only ONE settings entry in main sidebar
      const settingsItems = screen.getAllByText('Settings')
      expect(settingsItems).toHaveLength(1)

      // Settings should be in the "Other" zone
      const settingsLink = screen.getByRole('link', { name: /settings/i })
      expect(settingsLink).toHaveAttribute('href', '/settings')
    })

    it('GIVEN I click on Settings in the main sidebar, WHEN the page loads, THEN I should see a comprehensive settings page with internal navigation', async () => {
      mockUsePathname.mockReturnValue('/settings')

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      // Should see settings sections in the internal sidebar
      expect(screen.getByText('Settings')).toBeInTheDocument()

      // Should see profile and platform sections (based on API response)
      await screen.findByText('Profile')
      await screen.findByText('Platform')
    })

    it('GIVEN I am on a settings subpage, WHEN I view navigation, THEN the main sidebar should NOT duplicate the settings internal navigation', async () => {
      mockUsePathname.mockReturnValue('/settings/profile')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Main sidebar should NOT have individual settings sub-items
      expect(screen.queryByText('Profile')).not.toBeInTheDocument()
      expect(screen.queryByText('Appearance')).not.toBeInTheDocument()
      expect(screen.queryByText('AI Models')).not.toBeInTheDocument()
      expect(screen.queryByText('Notifications')).not.toBeInTheDocument()
      expect(screen.queryByText('Privacy')).not.toBeInTheDocument()
      expect(screen.queryByText('Advanced')).not.toBeInTheDocument()

      // Should only have the main Settings link
      const settingsItems = screen.getAllByText('Settings')
      expect(settingsItems).toHaveLength(1)
    })
  })

  describe('BDD: As a user, I want clear navigation hierarchy', () => {
    it('GIVEN I navigate to settings, WHEN I look at the page structure, THEN I should see a clear hierarchy with no duplicate navigation', async () => {
      mockUsePathname.mockReturnValue('/settings/appearance')

      // Render both sidebars to test they don't conflict
      render(
        <TestWrapper>
          <div data-testid='main-sidebar'>
            <AIFirstSidebar />
          </div>
          <div data-testid='settings-sidebar'>
            <SettingsSidebar />
          </div>
        </TestWrapper>
      )

      const mainSidebar = screen.getByTestId('main-sidebar')
      const settingsSidebar = screen.getByTestId('settings-sidebar')

      // Main sidebar should only have "Settings" link
      expect(mainSidebar).toHaveTextContent('Settings')
      expect(mainSidebar).not.toHaveTextContent('Appearance')

      // Settings sidebar should have the detailed navigation
      expect(settingsSidebar).toHaveTextContent('Settings')
      await screen.findByText('Appearance')
    })

    it('GIVEN I am using the settings page, WHEN I navigate between settings sections, THEN the main sidebar should remain simple and the settings sidebar should handle detailed navigation', async () => {
      const user = userEvent.setup()
      mockUsePathname.mockReturnValue('/settings/profile')

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      // Should see profile section active
      const profileSection = await screen.findByText('Profile')
      expect(profileSection).toBeInTheDocument()

      // Should be able to navigate to other settings sections
      // (This would be tested with actual navigation in E2E tests)
    })
  })

  describe('BDD: As a product owner, I want clean architecture without duplication', () => {
    it('GIVEN the settings navigation exists, WHEN I audit the component structure, THEN there should be no duplicate settings components', () => {
      // This test ensures we don't have multiple settings sidebar components
      // We should only have:
      // 1. AIFirstSidebar with single "Settings" entry
      // 2. SettingsSidebar for internal settings navigation

      const settingsComponentPaths = [
        'src/components/settings/Sidebar.tsx',
        'domains/settings/client/settings/Sidebar.tsx',
      ]

      // In a real implementation, we'd check file system
      // For now, we test that components don't render duplicate content
      expect(true).toBe(true) // Placeholder for file system check
    })

    it('GIVEN the navigation structure, WHEN I check for settings routes, THEN main sidebar should not create routes that duplicate existing settings functionality', () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Main sidebar should not have individual settings sub-routes
      const allLinks = screen.getAllByRole('link')
      const settingsSubRoutes = allLinks.filter(link => {
        const href = link.getAttribute('href')
        return href && href.includes('/settings/') && href !== '/settings'
      })

      expect(settingsSubRoutes).toHaveLength(0)
    })
  })

  describe('BDD: As a user, I want consistent UX patterns', () => {
    it('GIVEN I use other navigation sections, WHEN I compare to settings, THEN settings should follow the same pattern of simple main nav + detailed internal nav', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Main navigation should have simple, single-word entries
      expect(screen.getByText('Focus')).toBeInTheDocument()
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText('Settings')).toBeInTheDocument()

      // Should not have sub-navigation visible in collapsed state
      expect(screen.queryByText('Profile')).not.toBeInTheDocument()
      expect(screen.queryByText('Today')).not.toBeInTheDocument() // Focus sub-item
      expect(screen.queryByText('Members')).not.toBeInTheDocument() // Team sub-item
    })
  })
})
