import { describe, it, expect, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import ProjectsPage from '@/app/(protected)/explore/projects/page'
import { render, withDatabaseIsolation, createTestData } from '../utils/test-wrapper'

describe('Explore Projects Page', () => {
  describe('Page Structure & Navigation', () => {
    it('renders page title and navigation correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getByText('Projects')).toBeInTheDocument()
        expect(
          screen.getByText('Discover and collaborate on exciting projects across your organization')
        ).toBeInTheDocument()
      })
    })

    it('displays project categories section', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('Categories')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Engineering')[0]).toBeInTheDocument()
        expect(screen.getByText('Design')).toBeInTheDocument()
        expect(screen.getByText('Product')).toBeInTheDocument()
        expect(screen.getAllByText('Data Science')[0]).toBeInTheDocument()
      })
    })

    it('shows growth indicators for project categories', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        const growthIndicators = screen.getAllByText(/\+\d+% this quarter/)
        expect(growthIndicators.length).toBeGreaterThan(0)
      })
    })
  })

  describe('AI Insights Section', () => {
    it('displays AI insights section with recommendations', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('AI Insights')[0]).toBeInTheDocument()
        expect(screen.getByText('Smart Recommendations')).toBeInTheDocument()
        expect(screen.getByText('Project Match Score')).toBeInTheDocument()
      })
    })

    it('shows market trend analysis', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('Market Trend')[0]).toBeInTheDocument()
        expect(
          screen.getByText('AI/ML projects show 45% growth in participation')
        ).toBeInTheDocument()
      })
    })

    it('displays participation insights', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getByText('High Engagement')).toBeInTheDocument()
        expect(
          screen.getByText('Cross-functional projects have 85% completion rates')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Team Capacity Section', () => {
    it('shows team capacity information', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getByText('Team Capacity')).toBeInTheDocument()
        expect(screen.getByText('Available Slots')).toBeInTheDocument()
        expect(screen.getByText('Current Load')).toBeInTheDocument()
      })
    })

    it('displays capacity metrics and indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getByText('12 open positions')).toBeInTheDocument()
        expect(screen.getByText('78% utilized')).toBeInTheDocument()
      })
    })
  })

  describe('Recommended Projects Section', () => {
    it('displays recommended projects with proper structure', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getByText('Recommended for You')).toBeInTheDocument()
        expect(screen.getByText('AI-Powered Customer Analytics Platform')).toBeInTheDocument()
        expect(screen.getByText('Cross-Platform Mobile Experience')).toBeInTheDocument()
      })
    })

    it('shows project participants and status', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getByText('3/5 participants')).toBeInTheDocument()
        expect(screen.getByText('4/6 participants')).toBeInTheDocument()
        expect(screen.getAllByText('Recruiting')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Active')[0]).toBeInTheDocument()
      })
    })

    it('displays project skills and technologies', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('Python')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Machine Learning')[0]).toBeInTheDocument()
        expect(screen.getByText('React Native')).toBeInTheDocument()
        expect(screen.getByText('TypeScript')).toBeInTheDocument()
      })
    })

    it('shows allocation percentages', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('6 months')[0]).toBeInTheDocument()
        expect(screen.getAllByText('50% allocation')[0]).toBeInTheDocument()
      })
    })
  })

  describe('Quick Actions Section', () => {
    it('displays quick action buttons', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('Quick Actions')[0]).toBeInTheDocument()
        expect(screen.getByTestId('browse-all-projects-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('my-applications-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('create-project-sidebar')).toBeInTheDocument()
      })
    })

    it('handles quick action button clicks', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        const browseButton = screen.getByTestId('browse-all-projects-sidebar')
        const applicationsButton = screen.getByTestId('my-applications-sidebar')
        const createButton = screen.getByTestId('create-project-sidebar')

        fireEvent.click(browseButton)
        fireEvent.click(applicationsButton)
        fireEvent.click(createButton)

        // Buttons should be clickable (no errors thrown)
        expect(browseButton).toBeInTheDocument()
        expect(applicationsButton).toBeInTheDocument()
        expect(createButton).toBeInTheDocument()
      })
    })
  })

  describe('Learning Opportunities Section', () => {
    it('displays learning opportunities with skills', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('Learning Opportunities')[0]).toBeInTheDocument()
        expect(screen.getByText('Skill Development Focus')).toBeInTheDocument()
        expect(screen.getByText('Project-Based Learning')).toBeInTheDocument()
      })
    })

    it('shows learning metrics and progress indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getByText('12 new skills available')).toBeInTheDocument()
        expect(screen.getByText('8 hands-on projects')).toBeInTheDocument()
      })
    })
  })

  describe('Growth Metrics Display', () => {
    it('displays various growth percentages correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        expect(screen.getAllByText('45% growth')[0]).toBeInTheDocument()
        expect(screen.getByText('+23% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+18% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+31% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+27% this quarter')).toBeInTheDocument()
      })
    })
  })

  describe('User Interaction & Accessibility', () => {
    it('supports keyboard navigation for interactive elements', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        const browseButton = screen.getByTestId('browse-all-projects-header')

        browseButton.focus()
        expect(document.activeElement).toBe(browseButton)
      })
    })

    it('has proper ARIA labels and accessibility features', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        const buttons = screen.getAllByRole('button')
        expect(buttons.length).toBeGreaterThan(0)

        buttons.forEach(button => {
          expect(button).toBeInTheDocument()
        })
      })
    })
  })

  describe('Responsive Design Elements', () => {
    it('renders properly with responsive layout', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<ProjectsPage />)

        // Check that main container exists
        const container = screen.getByText('Projects').closest('div')
        expect(container).toBeInTheDocument()
      })
    })
  })
})
