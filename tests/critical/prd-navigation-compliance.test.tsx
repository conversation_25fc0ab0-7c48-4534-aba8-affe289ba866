import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Sidebar } from '@/components/shared/Sidebar'
import { Role } from '@prisma/client'
import { vi } from 'vitest'
import { NavigationProvider } from '@/lib/context/NavigationContextProvider'

// Mock dependencies
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
}))

vi.mock('next-auth/react', () => ({
  useSession: vi.fn(),
}))

const mockPathname = usePathname as ReturnType<typeof vi.fn>
const mockUseSession = useSession as ReturnType<typeof vi.fn>

// Test wrapper component
function TestWrapper({ children, session }: { children: React.ReactNode; session: any }) {
  mockUseSession.mockReturnValue({ data: session })
  return <NavigationProvider>{children}</NavigationProvider>
}

// Test sessions for different roles
const employeeSession = {
  user: { id: '1', name: 'Employee User', email: '<EMAIL>', role: Role.EMPLOYEE },
}

const managerSession = {
  user: { id: '2', name: 'Manager User', email: '<EMAIL>', role: Role.MANAGER },
}

const superadminSession = {
  user: { id: '3', name: 'Superadmin User', email: '<EMAIL>', role: Role.SUPERADMIN },
}

describe('PRD Navigation Compliance Tests', () => {
  beforeEach(() => {
    mockPathname.mockReturnValue('/focus')
    vi.clearAllMocks()
  })

  describe('Main Navigation Structure', () => {
    it('should have exactly 9 main navigation items as specified in PRD', async () => {
      await act(async () => {
        render(
          <TestWrapper session={employeeSession}>
            <Sidebar />
          </TestWrapper>
        )
      })

      // Verify all 9 main navigation items exist
      expect(screen.getByTestId('nav-focus')).toBeInTheDocument()
      expect(screen.getByTestId('nav-team')).toBeInTheDocument()
      expect(screen.getByTestId('nav-vision')).toBeInTheDocument()
      expect(screen.getByTestId('nav-grow')).toBeInTheDocument()
      expect(screen.getByTestId('nav-connect')).toBeInTheDocument()
      expect(screen.getByTestId('nav-celebrate')).toBeInTheDocument()
      expect(screen.getByTestId('nav-explore')).toBeInTheDocument()
      expect(screen.getByTestId('nav-pulse')).toBeInTheDocument()
      expect(screen.getByTestId('nav-customize')).toBeInTheDocument()
    })

    it('should group navigation items into correct zones as per PRD', async () => {
      await act(async () => {
        render(
          <TestWrapper session={employeeSession}>
            <Sidebar />
          </TestWrapper>
        )
      })

      // Verify zone groupings
      expect(screen.getByText('Personal Growth Zone')).toBeInTheDocument()
      expect(screen.getByText('Team Connection Zone')).toBeInTheDocument()
      expect(screen.getByText('Organization Impact Zone')).toBeInTheDocument()
    })
  })

  describe('Focus Section - PRD Compliance', () => {
    it('should have exactly 5 Focus subitems as specified in PRD', async () => {
      const user = userEvent.setup()

      await act(async () => {
        render(
          <TestWrapper session={employeeSession}>
            <Sidebar />
          </TestWrapper>
        )
      })

      // Expand Focus submenu
      const focusToggle = screen.getByTestId('nav-focus-toggle')

      await act(async () => {
        await user.click(focusToggle)
      })

      await waitFor(() => {
        // PRD specifies: Today, Goals, Skills, Performance, Insights
        expect(screen.getByTestId('nav-focus-today')).toBeInTheDocument()
        expect(screen.getByTestId('nav-focus-goals')).toBeInTheDocument()
        expect(screen.getByTestId('nav-focus-skills')).toBeInTheDocument()
        expect(screen.getByTestId('nav-focus-performance')).toBeInTheDocument()
        expect(screen.getByTestId('nav-focus-insights')).toBeInTheDocument()
      })
    })

    it('should have correct Focus submenu routes as per PRD', async () => {
      const user = userEvent.setup()

      await act(async () => {
        render(
          <TestWrapper session={employeeSession}>
            <Sidebar />
          </TestWrapper>
        )
      })

      const focusToggle = screen.getByTestId('nav-focus-toggle')

      await act(async () => {
        await user.click(focusToggle)
      })

      await waitFor(() => {
        expect(screen.getByTestId('nav-focus-today')).toHaveAttribute('href', '/focus/today')
        expect(screen.getByTestId('nav-focus-goals')).toHaveAttribute('href', '/focus/goals')
        expect(screen.getByTestId('nav-focus-skills')).toHaveAttribute('href', '/focus/skills')
        expect(screen.getByTestId('nav-focus-performance')).toHaveAttribute(
          'href',
          '/focus/performance'
        )
        expect(screen.getByTestId('nav-focus-insights')).toHaveAttribute('href', '/focus/insights')
      })
    })
  })

  describe('Team Section - PRD Compliance', () => {
    it('should have exactly 6 Team subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={managerSession}>
          <Sidebar />
        </TestWrapper>
      )

      const teamToggle = screen.getByTestId('nav-team-toggle')
      await user.click(teamToggle)

      await waitFor(() => {
        // PRD specifies: Overview, Members, Goals, Development, Feedback, Analytics
        expect(screen.getByTestId('nav-team-overview')).toBeInTheDocument()
        expect(screen.getByTestId('nav-team-members')).toBeInTheDocument()
        expect(screen.getByTestId('nav-team-goals')).toBeInTheDocument()
        expect(screen.getByTestId('nav-team-development')).toBeInTheDocument()
        expect(screen.getByTestId('nav-team-feedback')).toBeInTheDocument()
        expect(screen.getByTestId('nav-team-analytics')).toBeInTheDocument()
      })
    })
  })

  describe('Vision Section - PRD Compliance', () => {
    it('should have exactly 5 Vision subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={managerSession}>
          <Sidebar />
        </TestWrapper>
      )

      const visionToggle = screen.getByTestId('nav-vision-toggle')
      await user.click(visionToggle)

      await waitFor(() => {
        // PRD specifies: Strategy, Planning, Alignment, Progress, Intelligence
        expect(screen.getByTestId('nav-vision-strategy')).toBeInTheDocument()
        expect(screen.getByTestId('nav-vision-planning')).toBeInTheDocument()
        expect(screen.getByTestId('nav-vision-alignment')).toBeInTheDocument()
        expect(screen.getByTestId('nav-vision-progress')).toBeInTheDocument()
        expect(screen.getByTestId('nav-vision-intelligence')).toBeInTheDocument()
      })
    })
  })

  describe('Grow Section - PRD Compliance', () => {
    it('should have exactly 5 Grow subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const growToggle = screen.getByTestId('nav-grow-toggle')
      await user.click(growToggle)

      await waitFor(() => {
        // PRD specifies: Learning, Mentorship, Certifications, Career Path, Resources
        expect(screen.getByTestId('nav-grow-learning')).toBeInTheDocument()
        expect(screen.getByTestId('nav-grow-mentorship')).toBeInTheDocument()
        expect(screen.getByTestId('nav-grow-certifications')).toBeInTheDocument()
        expect(screen.getByTestId('nav-grow-career-path')).toBeInTheDocument()
        expect(screen.getByTestId('nav-grow-resources')).toBeInTheDocument()
      })
    })
  })

  describe('Connect Section - PRD Compliance', () => {
    it('should have exactly 5 Connect subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const connectToggle = screen.getByTestId('nav-connect-toggle')
      await user.click(connectToggle)

      await waitFor(() => {
        // PRD specifies: Feedback, Recognition, Communication, Collaboration, Network
        expect(screen.getByTestId('nav-connect-feedback')).toBeInTheDocument()
        expect(screen.getByTestId('nav-connect-recognition')).toBeInTheDocument()
        expect(screen.getByTestId('nav-connect-communication')).toBeInTheDocument()
        expect(screen.getByTestId('nav-connect-collaboration')).toBeInTheDocument()
        expect(screen.getByTestId('nav-connect-network')).toBeInTheDocument()
      })
    })
  })

  describe('Celebrate Section - PRD Compliance', () => {
    it('should have exactly 5 Celebrate subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const celebrateToggle = screen.getByTestId('nav-celebrate-toggle')
      await user.click(celebrateToggle)

      await waitFor(() => {
        // PRD specifies: Achievements, Recognition, Milestones, Team Wins, Company
        expect(screen.getByTestId('nav-celebrate-achievements')).toBeInTheDocument()
        expect(screen.getByTestId('nav-celebrate-recognition')).toBeInTheDocument()
        expect(screen.getByTestId('nav-celebrate-milestones')).toBeInTheDocument()
        expect(screen.getByTestId('nav-celebrate-team-wins')).toBeInTheDocument()
        expect(screen.getByTestId('nav-celebrate-company')).toBeInTheDocument()
      })
    })
  })

  describe('Explore Section - PRD Compliance', () => {
    it('should have exactly 5 Explore subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      const exploreToggle = screen.getByTestId('nav-explore-toggle')
      await user.click(exploreToggle)

      await waitFor(() => {
        // PRD specifies: Opportunities, Projects, Networking, Innovation, External
        expect(screen.getByTestId('nav-explore-opportunities')).toBeInTheDocument()
        expect(screen.getByTestId('nav-explore-projects')).toBeInTheDocument()
        expect(screen.getByTestId('nav-explore-networking')).toBeInTheDocument()
        expect(screen.getByTestId('nav-explore-innovation')).toBeInTheDocument()
        expect(screen.getByTestId('nav-explore-external')).toBeInTheDocument()
      })
    })
  })

  describe('Pulse Section - PRD Compliance', () => {
    it('should have exactly 6 Pulse subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={managerSession}>
          <Sidebar />
        </TestWrapper>
      )

      const pulseToggle = screen.getByTestId('nav-pulse-toggle')
      await user.click(pulseToggle)

      await waitFor(() => {
        // PRD specifies: Dashboard, Engagement, Performance, Skills, Culture, Predictive
        expect(screen.getByTestId('nav-pulse-dashboard')).toBeInTheDocument()
        expect(screen.getByTestId('nav-pulse-engagement')).toBeInTheDocument()
        expect(screen.getByTestId('nav-pulse-performance')).toBeInTheDocument()
        expect(screen.getByTestId('nav-pulse-skills')).toBeInTheDocument()
        expect(screen.getByTestId('nav-pulse-culture')).toBeInTheDocument()
        expect(screen.getByTestId('nav-pulse-predictive')).toBeInTheDocument()
      })
    })
  })

  describe('Customize Section - PRD Compliance', () => {
    it('should have exactly 6 Customize subitems as specified in PRD', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper session={superadminSession}>
          <Sidebar />
        </TestWrapper>
      )

      const customizeToggle = screen.getByTestId('nav-customize-toggle')
      await user.click(customizeToggle)

      await waitFor(() => {
        // PRD specifies: Profile, Platform, User & Access Control, Content Management, Advanced, SuperAdmin Panel
        expect(screen.getByTestId('nav-customize-profile')).toBeInTheDocument()
        expect(screen.getByTestId('nav-customize-platform')).toBeInTheDocument()
        expect(screen.getByTestId('nav-customize-user-access-control')).toBeInTheDocument()
        expect(screen.getByTestId('nav-customize-content-management')).toBeInTheDocument()
        expect(screen.getByTestId('nav-customize-advanced')).toBeInTheDocument()
        expect(screen.getByTestId('nav-customize-superadmin-panel')).toBeInTheDocument()
      })
    })
  })

  describe('Role-Based Visibility - PRD Compliance', () => {
    it('should show appropriate sections for Employee role', async () => {
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Employee should see Focus, Grow, Connect, Celebrate, Explore, Customize
      expect(screen.getByTestId('nav-focus')).toBeInTheDocument()
      expect(screen.getByTestId('nav-grow')).toBeInTheDocument()
      expect(screen.getByTestId('nav-connect')).toBeInTheDocument()
      expect(screen.getByTestId('nav-celebrate')).toBeInTheDocument()
      expect(screen.getByTestId('nav-explore')).toBeInTheDocument()
      expect(screen.getByTestId('nav-customize')).toBeInTheDocument()

      // Employee should have limited access to Team, Vision, Pulse
      expect(screen.getByTestId('nav-team')).toBeInTheDocument() // View only
      expect(screen.getByTestId('nav-vision')).toBeInTheDocument() // Read only
      expect(screen.getByTestId('nav-pulse')).toBeInTheDocument() // Personal only
    })

    it('should show appropriate sections for Manager role', async () => {
      render(
        <TestWrapper session={managerSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Manager should see all sections with appropriate permissions
      expect(screen.getByTestId('nav-focus')).toBeInTheDocument()
      expect(screen.getByTestId('nav-team')).toBeInTheDocument()
      expect(screen.getByTestId('nav-vision')).toBeInTheDocument()
      expect(screen.getByTestId('nav-grow')).toBeInTheDocument()
      expect(screen.getByTestId('nav-connect')).toBeInTheDocument()
      expect(screen.getByTestId('nav-celebrate')).toBeInTheDocument()
      expect(screen.getByTestId('nav-explore')).toBeInTheDocument()
      expect(screen.getByTestId('nav-pulse')).toBeInTheDocument()
      expect(screen.getByTestId('nav-customize')).toBeInTheDocument()
    })

    it('should show SuperAdmin Panel for Superadmin role only', async () => {
      render(
        <TestWrapper session={superadminSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Superadmin should see all sections including special SuperAdmin features
      expect(screen.getByTestId('nav-customize')).toBeInTheDocument()

      // Expand customize to see SuperAdmin Panel
      const customizeToggle = screen.getByTestId('nav-customize-toggle')
      await userEvent.setup().click(customizeToggle)

      await waitFor(() => {
        expect(screen.getByTestId('nav-customize-superadmin-panel')).toBeInTheDocument()
      })
    })
  })

  describe('Navigation Accessibility - PRD Compliance', () => {
    it('should have proper ARIA attributes for all navigation items', async () => {
      render(
        <TestWrapper session={employeeSession}>
          <Sidebar />
        </TestWrapper>
      )

      // Check main navigation items have proper accessibility
      const focusNav = screen.getByTestId('nav-focus')
      expect(focusNav).toHaveAttribute('href', '/focus')

      // Check toggle buttons have proper ARIA attributes
      const focusToggle = screen.getByTestId('nav-focus-toggle')
      expect(focusToggle).toHaveAttribute('aria-expanded', 'false')
      expect(focusToggle).toHaveAttribute('role', 'button')
    })
  })
})
