import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SessionProvider } from 'next-auth/react'
import { SuperAdminLayout } from '../../src/components/superadmin/SuperAdminLayout'

// Mock the navigation components
vi.mock('../../src/components/superadmin/SuperAdminSidebar', () => ({
  SuperAdminSidebar: () => (
    <div data-testid='superadmin-sidebar' className='w-64 h-screen border-r border-border'>
      SuperAdmin Sidebar
    </div>
  ),
}))

vi.mock('../../src/components/superadmin/SuperAdminBreadcrumbs', () => ({
  SuperAdminBreadcrumbs: () => (
    <div data-testid='superadmin-breadcrumbs' className='flex items-center'>
      SuperAdmin Panel
    </div>
  ),
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    prefetch: vi.fn(),
  }),
}))

describe('SuperAdminLayout - Layout Alignment', () => {
  const mockSession = {
    user: {
      id: 'test-user',
      email: '<EMAIL>',
      role: 'SUPERADMIN' as const,
      companyId: 'test-company',
    },
    expires: '2024-01-01',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should have proper flex layout structure with aligned borders', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    const layout = screen.getByTestId('superadmin-layout')

    // Should use flex layout for proper alignment
    expect(layout).toHaveClass('flex', 'h-screen')

    // Should not have any margin or padding that breaks alignment
    expect(layout).not.toHaveClass('p-6', 'm-6', 'px-6', 'py-6')
  })

  it('should have consistent border alignment between sidebar and content', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    const sidebar = screen.getByTestId('superadmin-sidebar')
    const layout = screen.getByTestId('superadmin-layout')

    // Sidebar should have consistent border styling
    expect(sidebar).toHaveClass('border-r', 'border-border')

    // Layout should not have conflicting border styles
    expect(layout).not.toHaveClass('border', 'border-l', 'border-r')
  })

  it('should have header aligned with sidebar borders', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    const breadcrumbs = screen.getByTestId('superadmin-breadcrumbs')

    // Header should be properly aligned
    expect(breadcrumbs).toBeInTheDocument()
    expect(breadcrumbs).toHaveClass('flex', 'items-center')
  })

  it('should not contain home button or unnecessary navigation elements', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    // Should not have home button
    expect(screen.queryByText(/home/<USER>
    expect(screen.queryByTestId('home-button')).not.toBeInTheDocument()

    // Should not have unnecessary navigation duplicates
    expect(screen.queryByText(/dashboard/i)).not.toBeInTheDocument()
  })

  it('should have proper content area without layout-breaking padding', async () => {
    render(
      <SessionProvider session={mockSession}>
        <SuperAdminLayout>
          <div data-testid='test-content'>Test Content</div>
        </SuperAdminLayout>
      </SessionProvider>
    )

    const content = screen.getByTestId('test-content')
    expect(content).toBeInTheDocument()

    // Content should be in a properly structured main element
    const mainElement = content.closest('main')
    expect(mainElement).toBeInTheDocument()
    expect(mainElement).toHaveClass('flex-1', 'overflow-auto')
  })
})
