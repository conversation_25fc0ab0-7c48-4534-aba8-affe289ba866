import { describe, it, expect } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import InnovationPage from '@/app/(protected)/explore/innovation/page'
import { render, withDatabaseIsolation, createTestData } from '../utils/test-wrapper'

describe('Explore Innovation Page', () => {
  describe('Page Structure & Navigation', () => {
    it('renders page title and navigation correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('Innovation')).toBeInTheDocument()
        expect(
          screen.getByText(
            'Explore innovation initiatives, submit ideas, and drive organizational change'
          )
        ).toBeInTheDocument()
      })
    })

    it('displays innovation categories section', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getAllByText('Categories')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Process')[0]).toBeInTheDocument()
        expect(screen.getByText('Technology')).toBeInTheDocument()
        expect(screen.getByText('Product')).toBeInTheDocument()
        expect(screen.getByText('Culture')).toBeInTheDocument()
      })
    })

    it('shows growth indicators for innovation categories', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('+25% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+18% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+32% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+15% this quarter')).toBeInTheDocument()
      })
    })
  })

  describe('AI Insights Section', () => {
    it('displays AI insights section with recommendations', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getAllByText('AI Insights')[0]).toBeInTheDocument()
        expect(screen.getByText('Innovation Opportunity')).toBeInTheDocument()
        expect(screen.getByText('Trend Analysis')).toBeInTheDocument()
      })
    })

    it('shows innovation trend analysis', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getAllByText('Innovation Trend')[0]).toBeInTheDocument()
        expect(
          screen.getByText('AI-driven process improvements show 40% efficiency gains')
        ).toBeInTheDocument()
      })
    })

    it('displays implementation insights', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('Implementation Success')).toBeInTheDocument()
        expect(
          screen.getByText('Cross-functional innovation teams achieve 75% higher success rates')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Innovation Overview Section', () => {
    it('shows innovation overview information', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('Innovation Overview')).toBeInTheDocument()
        expect(screen.getByText('Active Ideas')).toBeInTheDocument()
        expect(screen.getByText('Implementation Rate')).toBeInTheDocument()
      })
    })

    it('displays innovation metrics and indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('47 active ideas')).toBeInTheDocument()
        expect(screen.getAllByText('68%')[0]).toBeInTheDocument()
      })
    })
  })

  describe('Innovation Initiatives Section', () => {
    it('displays innovation initiatives with proper structure', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('Innovation Initiatives')).toBeInTheDocument()
        expect(screen.getByText('AI-Powered Customer Insights')).toBeInTheDocument()
        expect(screen.getByText('Automated Workflow Optimization')).toBeInTheDocument()
      })
    })

    it('shows initiative details and status', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('Product Innovation')).toBeInTheDocument()
        expect(screen.getByText('Process Innovation')).toBeInTheDocument()
        expect(screen.getAllByText('In Progress')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Planning')[0]).toBeInTheDocument()
      })
    })

    it('displays initiative impact and metrics', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getAllByText('High Impact')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Medium Impact')[0]).toBeInTheDocument()
        expect(screen.getByText('6 months timeline')).toBeInTheDocument()
        expect(screen.getByText('4 months timeline')).toBeInTheDocument()
      })
    })

    it('shows initiative team and participation', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('8 team members')).toBeInTheDocument()
        expect(screen.getByText('5 team members')).toBeInTheDocument()
        expect(screen.getByText('Led by Sarah Chen')).toBeInTheDocument()
        expect(screen.getByText('Led by Marcus Rodriguez')).toBeInTheDocument()
      })
    })
  })

  describe('Quick Actions Section', () => {
    it('displays quick action buttons', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getAllByText('Quick Actions')[0]).toBeInTheDocument()
        expect(screen.getByTestId('submit-idea-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('browse-initiatives-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('innovation-challenges-sidebar')).toBeInTheDocument()
      })
    })

    it('handles quick action button clicks', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        const submitButton = screen.getByTestId('submit-idea-sidebar')
        const browseButton = screen.getByTestId('browse-initiatives-sidebar')
        const challengesButton = screen.getByTestId('innovation-challenges-sidebar')

        fireEvent.click(submitButton)
        fireEvent.click(browseButton)
        fireEvent.click(challengesButton)

        // Buttons should be clickable (no errors thrown)
        expect(submitButton).toBeInTheDocument()
        expect(browseButton).toBeInTheDocument()
        expect(challengesButton).toBeInTheDocument()
      })
    })
  })

  describe('Innovation Opportunities Section', () => {
    it('displays innovation opportunities with challenges', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getAllByText('Innovation Opportunities')[0]).toBeInTheDocument()
        expect(screen.getByText('Active Challenges')).toBeInTheDocument()
        expect(screen.getByText('Idea Submissions')).toBeInTheDocument()
      })
    })

    it('shows innovation metrics and progress indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getByText('3 active challenges')).toBeInTheDocument()
        expect(screen.getByText('24 idea submissions')).toBeInTheDocument()
      })
    })
  })

  describe('Innovation Growth Display', () => {
    it('displays various growth percentages correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        expect(screen.getAllByText('40% growth')[0]).toBeInTheDocument()
        expect(screen.getByText('+25% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+18% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+32% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+15% this quarter')).toBeInTheDocument()
      })
    })
  })

  describe('User Interaction & Accessibility', () => {
    it('supports keyboard navigation for interactive elements', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        const submitButton = screen.getByTestId('submit-idea-header')

        submitButton.focus()
        expect(document.activeElement).toBe(submitButton)
      })
    })

    it('has proper ARIA labels and accessibility features', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        const buttons = screen.getAllByRole('button')
        expect(buttons.length).toBeGreaterThan(0)

        buttons.forEach(button => {
          expect(button).toBeInTheDocument()
        })
      })
    })
  })

  describe('Responsive Design Elements', () => {
    it('renders properly with responsive layout', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<InnovationPage />)

        // Check that main container exists
        const container = screen.getByText('Innovation').closest('div')
        expect(container).toBeInTheDocument()
      })
    })
  })
})
