import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { SuperAdminSidebar } from '../../src/components/superadmin/SuperAdminSidebar'
import { AIFirstSidebar } from '../../src/components/navigation/AIFirstSidebar'
import { Role } from '@prisma/client'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn() }),
  usePathname: () => '/superadmin',
}))

// Mock session with SuperAdmin role
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Super Admin',
    role: Role.SUPERADMIN,
    companyId: 'test-company-id',
  },
  expires: '2024-12-31',
}

describe('SuperAdmin Exact Spacing Alignment Tests', () => {
  const renderWithSession = (component: React.ReactElement) => {
    return render(<SessionProvider session={mockSession}>{component}</SessionProvider>)
  }

  describe('Header Alignment', () => {
    it('should have identical header height and padding as main sidebar', () => {
      const { container: superAdminContainer } = renderWithSession(<SuperAdminSidebar />)

      // Find SuperAdmin header
      const superAdminHeader = superAdminContainer.querySelector(
        '[data-testid="superadmin-sidebar"] > div:first-child'
      )
      expect(superAdminHeader).toBeInTheDocument()

      // Check header structure and classes
      expect(superAdminHeader).toHaveClass(
        'flex',
        'items-center',
        'justify-between',
        'p-4',
        'border-b',
        'border-border'
      )
    })

    it('should have matching header typography', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Check header text styling
      const headerText = screen.getByText('Super Admin Panel')
      expect(headerText).toHaveClass('text-lg', 'font-semibold', 'text-foreground')

      // Check icon sizing
      const headerIcon = headerText.parentElement?.querySelector('svg')
      expect(headerIcon).toHaveClass('h-6', 'w-6', 'text-primary')
    })

    it('should have matching toggle button styling', () => {
      renderWithSession(<SuperAdminSidebar />)

      const toggleButton = screen.getByTestId('superadmin-sidebar-toggle')
      expect(toggleButton).toHaveClass('h-8', 'w-8', 'p-0')

      // Check icon inside toggle button
      const toggleIcon = toggleButton.querySelector('svg')
      expect(toggleIcon).toHaveClass('h-4', 'w-4')
    })
  })

  describe('Expand All Section Alignment', () => {
    it('should have exact same container styling as main sidebar "Expand All Zones"', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Find the expand all container
      const expandAllContainer = screen.getByTestId('toggle-all-groups').parentElement
      expect(expandAllContainer).toHaveClass('px-4', 'py-2', 'border-b', 'border-border')
    })

    it('should have identical button styling as main sidebar expand all button', () => {
      renderWithSession(<SuperAdminSidebar />)

      const expandAllButton = screen.getByTestId('toggle-all-groups')
      expect(expandAllButton).toHaveClass(
        'w-full',
        'justify-start',
        'h-8',
        'text-xs',
        'font-medium',
        'text-muted-foreground',
        'hover:text-foreground',
        'transition-colors'
      )
    })

    it('should have matching icon sizing in expand all button', () => {
      renderWithSession(<SuperAdminSidebar />)

      const expandAllButton = screen.getByTestId('toggle-all-groups')
      const buttonIcon = expandAllButton.querySelector('svg')
      expect(buttonIcon).toHaveClass('h-3', 'w-3', 'mr-2')
    })

    it('should toggle between expand and minimize states correctly', async () => {
      renderWithSession(<SuperAdminSidebar />)

      const expandAllButton = screen.getByTestId('toggle-all-groups')

      // Initially should show "Expand All Groups" (since groups start expanded by default)
      expect(expandAllButton).toHaveTextContent('Minimize All Groups')

      // Click to collapse all
      fireEvent.click(expandAllButton)
      await waitFor(() => {
        expect(expandAllButton).toHaveTextContent('Expand All Groups')
      })

      // Click to expand all
      fireEvent.click(expandAllButton)
      await waitFor(() => {
        expect(expandAllButton).toHaveTextContent('Minimize All Groups')
      })
    })
  })

  describe('Group Header Alignment', () => {
    it('should have identical group header container styling as main sidebar zone headers', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Find a group header
      const groupHeader = screen.getByText('SYSTEM MANAGEMENT').parentElement
      expect(groupHeader).toHaveClass(
        'flex',
        'items-center',
        'justify-between',
        'px-3',
        'py-2',
        'group'
      )
    })

    it('should have matching group header text styling', () => {
      renderWithSession(<SuperAdminSidebar />)

      const groupHeaderText = screen.getByText('SYSTEM MANAGEMENT')
      expect(groupHeaderText).toHaveClass(
        'text-xs',
        'font-semibold',
        'text-muted-foreground',
        'uppercase',
        'tracking-wider'
      )
    })

    it('should have matching group toggle button styling', () => {
      renderWithSession(<SuperAdminSidebar />)

      const groupToggle = screen.getByTestId('group-toggle-system-management')
      expect(groupToggle).toHaveClass(
        'h-4',
        'w-4',
        'opacity-70',
        'hover:opacity-100',
        'transition-opacity'
      )

      // Check icon sizing
      const toggleIcon = groupToggle.querySelector('svg')
      expect(toggleIcon).toHaveAttribute('size', '12')
    })
  })

  describe('Navigation Item Alignment', () => {
    it('should have identical navigation item container styling as main sidebar', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Find a navigation item
      const navItem = screen.getByTestId('nav-companies')
      expect(navItem).toHaveClass(
        'flex',
        'items-center',
        'px-3',
        'py-2',
        'rounded-md',
        'text-sm',
        'font-medium',
        'transition-colors',
        'group',
        'relative'
      )
    })

    it('should have matching navigation item icon sizing', () => {
      renderWithSession(<SuperAdminSidebar />)

      const navItem = screen.getByTestId('nav-companies')
      const navIcon = navItem.querySelector('svg')
      expect(navIcon).toHaveClass('h-5', 'w-5', 'flex-shrink-0', 'mr-3')
    })

    it('should have proper spacing between icon and text', () => {
      renderWithSession(<SuperAdminSidebar />)

      const navItem = screen.getByTestId('nav-companies')
      const navIcon = navItem.querySelector('svg')
      expect(navIcon).toHaveClass('mr-3') // 12px margin-right, same as main sidebar
    })

    it('should have matching hover and active states', () => {
      renderWithSession(<SuperAdminSidebar />)

      const navItem = screen.getByTestId('nav-companies')
      expect(navItem).toHaveClass('text-foreground/60', 'hover:text-foreground', 'hover:bg-muted')
    })
  })

  describe('Navigation Container Alignment', () => {
    it('should have identical navigation container styling as main sidebar', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Find the navigation container
      const navContainer = screen.getByRole('navigation')
      expect(navContainer).toHaveClass(
        'flex-1',
        'flex',
        'flex-col',
        'px-2',
        'py-4',
        'overflow-y-auto'
      )
    })

    it('should have matching space-y classes for item spacing', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Find group containers
      const groupContainer = screen.getByTestId('system-management-group')
      expect(groupContainer).toHaveClass('space-y-1')
    })
  })

  describe('Collapsed State Alignment', () => {
    it('should maintain proper spacing when collapsed', async () => {
      renderWithSession(<SuperAdminSidebar />)

      // Click collapse button
      const collapseButton = screen.getByTestId('superadmin-sidebar-toggle')
      fireEvent.click(collapseButton)

      await waitFor(() => {
        const sidebar = screen.getByTestId('superadmin-sidebar')
        expect(sidebar).toHaveClass('w-16')
      })

      // Check that navigation items are centered
      const navItem = screen.getByTestId('nav-companies')
      expect(navItem).toHaveClass('justify-center')

      // Check icon has no margin when collapsed
      const navIcon = navItem.querySelector('svg')
      expect(navIcon).toHaveClass('mx-0')
    })

    it('should hide expand all section when collapsed', async () => {
      renderWithSession(<SuperAdminSidebar />)

      // Click collapse button
      const collapseButton = screen.getByTestId('superadmin-sidebar-toggle')
      fireEvent.click(collapseButton)

      await waitFor(() => {
        const expandAllButton = screen.queryByTestId('toggle-all-groups')
        expect(expandAllButton).not.toBeInTheDocument()
      })
    })
  })

  describe('Pixel-Perfect Measurements', () => {
    it('should have exact measurements matching main sidebar', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Test specific pixel measurements
      const sidebar = screen.getByTestId('superadmin-sidebar')

      // Check computed styles would match
      const styles = window.getComputedStyle(sidebar)

      // Width should be 256px (w-64 = 16rem = 256px)
      expect(sidebar).toHaveClass('w-64')

      // Check header height (h-16 = 4rem = 64px)
      const header = sidebar.querySelector('div:first-child')
      expect(header).toHaveClass('p-4') // 16px padding

      // Check expand all button height (h-8 = 2rem = 32px)
      const expandAllButton = screen.getByTestId('toggle-all-groups')
      expect(expandAllButton).toHaveClass('h-8')

      // Check navigation item padding (px-3 py-2 = 12px horizontal, 8px vertical)
      const navItem = screen.getByTestId('nav-companies')
      expect(navItem).toHaveClass('px-3', 'py-2')
    })

    it('should have matching border and spacing measurements', () => {
      renderWithSession(<SuperAdminSidebar />)

      // Check border-b classes
      const expandAllContainer = screen.getByTestId('toggle-all-groups').parentElement
      expect(expandAllContainer).toHaveClass('border-b', 'border-border')

      // Check padding classes
      expect(expandAllContainer).toHaveClass('px-4', 'py-2') // 16px horizontal, 8px vertical

      // Check navigation container padding
      const navContainer = screen.getByRole('navigation')
      expect(navContainer).toHaveClass('px-2', 'py-4') // 8px horizontal, 16px vertical
    })
  })
})
