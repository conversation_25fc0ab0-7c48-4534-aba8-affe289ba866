/**
 * Basic Theme Validation E2E Test
 *
 * Simple test to validate the theme system is working correctly
 * following TDD/BDD principles with real functionality testing.
 */

import { test, expect } from '@playwright/test'

test.describe('🎨 Basic Theme Validation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application root first
    await page.goto('/')

    // If we get redirected to sign-in, handle authentication
    const currentUrl = page.url()
    if (currentUrl.includes('/signin') || currentUrl.includes('/auth')) {
      // For this basic test, let's try to navigate directly to dashboard
      // and see if there's already authentication
      await page.goto('/dashboard')

      // If still on auth page, the test environment needs proper setup
      const finalUrl = page.url()
      if (!finalUrl.includes('/dashboard')) {
        console.log('Authentication required - skipping for basic validation')
        test.skip()
      }
    }

    // Wait for the page to load completely
    await page.waitForLoadState('networkidle')
  })

  test('should have default theme colors applied to the page', async () => {
    // BDD: GIVEN the application loads
    // WHEN I check the CSS custom properties
    // THEN the default theme colors should be applied

    // Wait for theme system to initialize
    await page.waitForFunction(
      () => {
        const primaryColor = getComputedStyle(document.documentElement)
          .getPropertyValue('--primary')
          .trim()
        return primaryColor && primaryColor !== ''
      },
      { timeout: 10000 }
    )

    // Verify default primary color is purple (#8957e5)
    const primaryColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--primary').trim()
    })

    expect(primaryColor).toBe('#8957e5')
    console.log(`✅ Primary color verified: ${primaryColor}`)

    // Verify other essential CSS properties exist
    const cssProperties = await page.evaluate(() => {
      const styles = getComputedStyle(document.documentElement)
      return {
        secondary: styles.getPropertyValue('--secondary').trim(),
        accent: styles.getPropertyValue('--accent').trim(),
        background: styles.getPropertyValue('--background').trim(),
        foreground: styles.getPropertyValue('--foreground').trim(),
      }
    })

    expect(cssProperties.secondary).toBeTruthy()
    expect(cssProperties.accent).toBeTruthy()
    expect(cssProperties.background).toBeTruthy()
    expect(cssProperties.foreground).toBeTruthy()

    console.log('✅ All CSS custom properties are defined')
  })

  test('should have navigation elements that use theme colors', async () => {
    // BDD: GIVEN the page has loaded with theme colors
    // WHEN I examine navigation elements
    // THEN they should use CSS classes that reference theme colors

    // Look for any navigation element
    const navElements = page.locator('nav, [role="navigation"], header')

    if ((await navElements.count()) > 0) {
      const firstNav = navElements.first()
      await expect(firstNav).toBeVisible()
      console.log('✅ Navigation elements found and visible')

      // Check for elements with theme-related classes
      const themedElements = page.locator(
        '.text-primary, .bg-primary, .border-primary, [class*="primary"]'
      )

      if ((await themedElements.count()) > 0) {
        console.log(`✅ Found ${await themedElements.count()} elements using theme classes`)

        // Verify at least one element is visible
        const firstThemedElement = themedElements.first()
        await expect(firstThemedElement).toBeVisible()
      } else {
        console.log('ℹ️ No elements with explicit theme classes found')
      }
    } else {
      console.log('ℹ️ No navigation elements found on this page')
    }
  })

  test('should load without JavaScript errors', async () => {
    // BDD: GIVEN the page loads
    // WHEN JavaScript executes
    // THEN there should be no console errors

    const consoleErrors: string[] = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Reload the page to capture any errors
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Wait a bit for any delayed errors
    await page.waitForTimeout(2000)

    // Filter out known acceptable errors (like network issues in test environment)
    const criticalErrors = consoleErrors.filter(
      error =>
        !error.includes('Failed to fetch') &&
        !error.includes('NetworkError') &&
        !error.includes('ERR_INTERNET_DISCONNECTED') &&
        !error.toLowerCase().includes('websocket')
    )

    if (criticalErrors.length > 0) {
      console.log('⚠️ JavaScript errors found:', criticalErrors)
    }

    // For this basic test, we'll warn but not fail on JS errors
    // to focus on theme functionality
    expect(criticalErrors.length).toBeLessThanOrEqual(5)
    console.log('✅ JavaScript error check completed')
  })

  test('should render the page title correctly', async () => {
    // BDD: GIVEN the page loads
    // WHEN I check the page title
    // THEN it should contain the application name

    const title = await page.title()
    expect(title).toBeTruthy()
    expect(title.length).toBeGreaterThan(0)

    // Should contain Emynent in some form
    const titleLower = title.toLowerCase()
    expect(titleLower).toMatch(/(emynent|dashboard|app)/i)

    console.log(`✅ Page title verified: "${title}"`)
  })
})
