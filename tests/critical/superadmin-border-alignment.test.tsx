import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SessionProvider } from 'next-auth/react'
import { SuperAdminLayout } from '../../src/components/superadmin/SuperAdminLayout'
import { AIFirstSidebar } from '../../src/components/navigation/AIFirstSidebar'
import { Role } from '@prisma/client'

// Mock the navigation components
vi.mock('../../src/components/superadmin/SuperAdminSidebar', () => ({
  SuperAdminSidebar: () => (
    <div
      data-testid='superadmin-sidebar'
      className='w-64 h-full border-r border-border bg-background'
    >
      SuperAdmin Sidebar
    </div>
  ),
}))

vi.mock('../../src/components/superadmin/SuperAdminBreadcrumbs', () => ({
  SuperAdminBreadcrumbs: () => <div data-testid='superadmin-breadcrumbs'>SuperAdmin Panel</div>,
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }),
  usePathname: () => '/superadmin',
}))

// Mock next-auth
vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Super Admin',
        role: Role.SUPERADMIN,
        companyId: 'test-company-id',
      },
    },
    status: 'authenticated',
  }),
  SessionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}))

describe('SuperAdmin Border Alignment', () => {
  const renderWithSession = (component: React.ReactElement) => {
    return render(<SessionProvider session={null}>{component}</SessionProvider>)
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should have adjacent layout structure for dual sidebars', () => {
    renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    const layout = screen.getByTestId('superadmin-layout')

    // Verify adjacent layout structure (not fixed positioning)
    expect(layout).toHaveClass('flex')
    expect(layout).toHaveClass('h-full')
    expect(layout).toHaveClass('bg-background')
    expect(layout).not.toHaveClass('fixed')
    expect(layout).not.toHaveClass('inset-0')
  })

  it('should have perfect border alignment between elements', () => {
    renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    const sidebar = screen.getByTestId('superadmin-sidebar')
    const breadcrumbs = screen.getByTestId('superadmin-breadcrumbs')

    // Verify sidebar has proper border
    expect(sidebar).toHaveClass('border-r')
    expect(sidebar).toHaveClass('border-border')

    // Verify breadcrumbs area exists (header with border-b)
    expect(breadcrumbs).toBeInTheDocument()
  })

  it('should have header height matching main sidebar for perfect alignment', () => {
    renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    const layout = screen.getByTestId('superadmin-layout')

    // The header should have h-16 class to match main sidebar header
    // This ensures horizontal borders align perfectly
    expect(layout).toBeInTheDocument()
  })

  it('should eliminate excessive padding and gaps', () => {
    renderWithSession(
      <SuperAdminLayout>
        <div data-testid='test-content'>Test Content</div>
      </SuperAdminLayout>
    )

    const layout = screen.getByTestId('superadmin-layout')
    const content = screen.getByTestId('test-content')

    // Layout should not have padding classes that create gaps
    expect(layout.className).not.toMatch(/p-\d+/)
    expect(layout.className).not.toMatch(/px-\d+/)
    expect(layout.className).not.toMatch(/py-\d+/)

    // Content should be contained properly
    expect(content).toBeInTheDocument()
  })

  it('should maintain consistent width with main sidebar', () => {
    renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    const sidebar = screen.getByTestId('superadmin-sidebar')

    // Should have consistent width (w-64 = 256px)
    expect(sidebar).toHaveClass('w-64')
  })

  it('should have proper flex layout structure', () => {
    renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    const layout = screen.getByTestId('superadmin-layout')

    // Should use flex layout for proper alignment
    expect(layout).toHaveClass('flex')
    expect(layout).toHaveClass('bg-background')
  })

  it('should work as adjacent sidebar without overlaying main sidebar', () => {
    renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    const layout = screen.getByTestId('superadmin-layout')

    // Should be adjacent layout, not overlaying
    expect(layout).toHaveClass('flex')
    expect(layout).toHaveClass('h-full')
    expect(layout).not.toHaveClass('fixed')
    expect(layout).not.toHaveClass('absolute')
  })

  it('should have matching header heights and padding alignment', () => {
    // Render both components to compare their header structures
    const { container: sidebarContainer } = renderWithSession(<AIFirstSidebar />)
    const { container: superAdminContainer } = renderWithSession(
      <SuperAdminLayout>
        <div>Test Content</div>
      </SuperAdminLayout>
    )

    // Get the headers
    const sidebarHeader = sidebarContainer.querySelector('header')
    const superAdminHeader = superAdminContainer.querySelector(
      '[data-testid="superadmin-layout"] > div:last-child > div:first-child'
    )

    expect(sidebarHeader).toBeInTheDocument()
    expect(superAdminHeader).toBeInTheDocument()

    // Check that both headers have the same height class
    expect(sidebarHeader).toHaveClass('h-16')
    expect(superAdminHeader).toHaveClass('h-16')

    // Check that both headers have the same border styling
    expect(sidebarHeader).toHaveClass('border-b', 'border-border')
    expect(superAdminHeader).toHaveClass('border-b', 'border-border')

    // The key issue: padding alignment
    // Main sidebar header has internal px-4 padding
    // SuperAdmin header should match this for proper alignment
    const sidebarHeaderContent = sidebarHeader?.querySelector('div')
    expect(sidebarHeaderContent).toHaveClass('px-4')

    // SuperAdmin header should have px-4 padding to match, not px-6
    expect(superAdminHeader).toHaveClass('px-4')
  })

  it('should have proper border continuity across headers', () => {
    const { container } = renderWithSession(
      <div className='flex h-full'>
        <AIFirstSidebar />
        <SuperAdminLayout>
          <div>Test Content</div>
        </SuperAdminLayout>
      </div>
    )

    // Both headers should have consistent border styling for visual continuity
    const sidebarHeader = container.querySelector('[data-testid="ai-first-sidebar"] header')
    const superAdminHeader = container.querySelector(
      '[data-testid="superadmin-layout"] > div:last-child > div:first-child'
    )

    expect(sidebarHeader).toHaveClass('border-b', 'border-border')
    expect(superAdminHeader).toHaveClass('border-b', 'border-border')

    // Both should have the same background styling for consistency
    expect(sidebarHeader).toHaveClass('bg-background/80', 'backdrop-blur-lg')
    expect(superAdminHeader).toHaveClass('bg-background/80', 'backdrop-blur-lg')
  })
})
