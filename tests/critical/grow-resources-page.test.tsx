import { describe, it, expect, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import GrowResourcesPage from '@/app/(protected)/grow/resources/page'
import { render, withDatabaseIsolation, createTestData } from '../utils/test-wrapper'

describe('Grow Resources Page - BDD Test Suite', () => {
  describe('Page Structure & Navigation', () => {
    it('renders page title and navigation correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Learning Resources')).toBeInTheDocument()
        expect(
          screen.getByText(
            'Discover tools, courses, and resources to accelerate your professional growth'
          )
        ).toBeInTheDocument()
      })
    })

    it('displays resource categories section', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Resource Categories')).toBeInTheDocument()
        expect(screen.getByText('Technical Skills')).toBeInTheDocument()
        expect(screen.getByText('Leadership')).toBeInTheDocument()
        expect(screen.getByText('Communication')).toBeInTheDocument()
        expect(screen.getByText('Industry Knowledge')).toBeInTheDocument()
      })
    })

    it('shows AI-powered recommendations section', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('AI Recommendations')).toBeInTheDocument()
        expect(
          screen.getByText('Personalized learning paths based on your role and goals')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Resource Discovery Features', () => {
    it('displays featured resources with ratings', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Featured Resources')).toBeInTheDocument()
        expect(screen.getByText('Advanced React Patterns')).toBeInTheDocument()
        expect(screen.getByText('Leadership Fundamentals')).toBeInTheDocument()
        expect(screen.getByText('System Design Mastery')).toBeInTheDocument()

        // Check for ratings
        const ratingElements = screen.getAllByText('4.8')
        expect(ratingElements.length).toBeGreaterThan(0)
      })
    })

    it('shows resource types and formats', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getAllByText('Course')[0]).toBeInTheDocument()
        expect(screen.getByText('Book')).toBeInTheDocument()
        expect(screen.getAllByText('Video')[0]).toBeInTheDocument()
        expect(screen.getByText('Article')).toBeInTheDocument()
      })
    })

    it('displays resource providers and platforms', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getAllByText('Coursera')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Udemy')[0]).toBeInTheDocument()
        expect(screen.getAllByText('LinkedIn Learning')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Pluralsight')[0]).toBeInTheDocument()
      })
    })
  })

  describe('AI-Powered Personalization', () => {
    it('shows personalized learning paths', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Your Learning Path')).toBeInTheDocument()
        expect(screen.getByText('Frontend Development Track')).toBeInTheDocument()
        expect(screen.getByText('Based on your role as Frontend Developer')).toBeInTheDocument()
        expect(screen.getByText('85% match')).toBeInTheDocument()
      })
    })

    it('displays skill gap analysis', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Skill Gap Analysis')).toBeInTheDocument()
        expect(screen.getByText('React Advanced Patterns')).toBeInTheDocument()
        expect(screen.getByText('TypeScript Mastery')).toBeInTheDocument()
        expect(screen.getByText('System Architecture')).toBeInTheDocument()
      })
    })

    it('shows trending resources in your field', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Trending in Your Field')).toBeInTheDocument()
        expect(screen.getByText('AI-Powered Development')).toBeInTheDocument()
        expect(screen.getByText('Modern Web Architecture')).toBeInTheDocument()
      })
    })
  })

  describe('Resource Management Features', () => {
    it('allows bookmarking resources', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        const bookmarkButtons = screen.getAllByLabelText('Bookmark resource')
        expect(bookmarkButtons.length).toBeGreaterThan(0)

        fireEvent.click(bookmarkButtons[0])
        // Should show bookmarked state
      })
    })

    it('displays my bookmarks section', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('My Bookmarks')).toBeInTheDocument()
        expect(screen.getByText('3 saved resources')).toBeInTheDocument()
      })
    })

    it('shows progress tracking for enrolled resources', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getAllByText('In Progress')[0]).toBeInTheDocument()
        expect(screen.getByText('65% complete')).toBeInTheDocument()
        expect(screen.getByText('Continue Learning')).toBeInTheDocument()
      })
    })

    it('shows resource statistics', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('247')).toBeInTheDocument()
        expect(screen.getByText('Total Resources')).toBeInTheDocument()
        expect(screen.getByText('12')).toBeInTheDocument()
        expect(screen.getAllByText('In Progress')[0]).toBeInTheDocument()
        expect(screen.getByText('8')).toBeInTheDocument()
        expect(screen.getByText('Completed')).toBeInTheDocument()
      })
    })
  })

  describe('Search and Filtering', () => {
    it('provides search functionality', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        const searchInput = screen.getByPlaceholderText('Search resources...')
        expect(searchInput).toBeInTheDocument()

        fireEvent.change(searchInput, { target: { value: 'React' } })
        expect(searchInput).toHaveValue('React')
      })
    })

    it('offers filtering options', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Filter by Type')).toBeInTheDocument()
        expect(screen.getByText('Filter by Level')).toBeInTheDocument()
        expect(screen.getByText('Filter by Duration')).toBeInTheDocument()
      })
    })

    it('shows filter results count', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('247 resources found')).toBeInTheDocument()
      })
    })
  })

  describe('Quick Actions', () => {
    it('displays quick action buttons', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByText('Browse All Resources')).toBeInTheDocument()
        expect(screen.getByText('My Learning Plan')).toBeInTheDocument()
        expect(screen.getByText('Request Resource')).toBeInTheDocument()
      })
    })
  })

  describe('Accessibility & Responsive Design', () => {
    it('has proper ARIA labels and roles', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        expect(screen.getByRole('main')).toBeInTheDocument()
        expect(screen.getByRole('search')).toBeInTheDocument()
        expect(screen.getAllByRole('button').length).toBeGreaterThan(0)
      })
    })

    it('supports keyboard navigation', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        const { user } = await testData.createEmployeeWithCompany()

        render(<GrowResourcesPage />)

        const searchInput = screen.getByPlaceholderText('Search resources...')
        searchInput.focus()
        expect(document.activeElement).toBe(searchInput)
      })
    })
  })
})
