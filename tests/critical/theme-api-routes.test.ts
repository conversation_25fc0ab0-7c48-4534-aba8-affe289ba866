/**
 * 🧪 Theme API Routes Tests (TDD/BDD)
 *
 * Write tests FIRST, then implement API routes to make them pass.
 * Ensures 100% client-server compliance with proper controller-service pattern.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import {
  testPrisma,
  testRedis,
  TEST_USERS,
  cleanTestData,
  seedTestData,
} from '../setup/test-database'
// Import controller and service directly for pure TDD testing
import { AIThemeController } from '@/controllers/ai-theme-controller'
import { AIThemeService } from '@/services/ai-theme-service'

describe('🌐 Theme API Routes (Client-Server Compliance)', () => {
  let themeController: AIThemeController
  let themeService: AIThemeService

  beforeEach(async () => {
    await cleanTestData()
    await seedTestData()

    // Initialize controller and service for direct testing
    themeService = new AIThemeService(testPrisma, testRedis)
    themeController = new AIThemeController(themeService)
  })

  afterEach(async () => {
    await cleanTestData()
  })

  // Helper function to create mock request/response
  const createMockRequest = (user: any, method: string, body?: any) => ({
    user,
    headers: { 'Content-Type': 'application/json' },
    method,
    url: '/api/theme/preferences',
    body,
  })

  const createMockResponse = () => {
    let responseData: any = null
    let statusCode = 200
    const mockResponse = {
      status: (code: number) => {
        statusCode = code
        return mockResponse
      },
      json: (data: any) => {
        responseData = data
        return mockResponse
      },
    }
    return { mockResponse, getResponse: () => ({ statusCode, responseData }) }
  }

  describe('🎨 GET /api/theme/preferences', () => {
    it('should retrieve user theme preferences with full metadata', async () => {
      // Given: Authenticated user request
      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'GET'
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Getting theme preferences via controller
      await themeController.getThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should return complete theme data
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(200)
      expect(responseData).toMatchObject({
        success: true,
        data: expect.objectContaining({
          userId: TEST_USERS.employee.id,
          currentTheme: expect.objectContaining({
            mode: expect.stringMatching(/light|dark|system/),
            colorScheme: expect.any(String),
            appliedAt: expect.any(Date),
            source: expect.any(String),
          }),
          aiContext: expect.objectContaining({
            preferredColorSchemes: expect.any(Array),
            satisfactionScores: expect.any(Object),
            usagePatterns: expect.any(Object),
            behaviorTriggers: expect.any(Array),
            learningMetadata: expect.objectContaining({
              confidenceLevel: expect.any(Number),
              dataPoints: expect.any(Number),
              lastUpdated: expect.any(Date),
            }),
          }),
          customizations: expect.objectContaining({
            colors: expect.any(Array),
            gradients: expect.any(Object),
          }),
          metadata: expect.objectContaining({
            version: expect.any(String),
            createdAt: expect.any(Date),
            updatedAt: expect.any(Date),
          }),
        }),
        metadata: expect.objectContaining({
          version: '1.0.0',
        }),
      })

      // Architecture validation successful!
      expect(responseData.success).toBe(true)
    })

    it('should handle unauthenticated requests', async () => {
      // Given: Unauthenticated request (no user)
      const mockRequest = createMockRequest(null, 'GET')
      const { mockResponse, getResponse } = createMockResponse()

      // When: Accessing protected endpoint via controller
      await themeController.getThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should reject with proper error
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(401)
      expect(responseData).toMatchObject({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Valid authentication required',
        },
      })
    })

    it('should use Redis caching for performance', async () => {
      // Given: User with cached preferences
      const cacheKey = `theme:preferences:${TEST_USERS.employee.id}`
      await testRedis.setex(
        cacheKey,
        3600,
        JSON.stringify({
          userId: TEST_USERS.employee.id,
          currentTheme: { mode: 'dark', colorScheme: 'emynent-dark' },
        })
      )

      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'GET'
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Requesting preferences via controller
      await themeController.getThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should return cached data faster
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(200)
      expect(responseData.metadata.cached).toBe(true)
      expect(responseData.metadata.responseTime).toBeLessThan(50) // Cached should be faster
    })
  })

  describe('🎨 PUT /api/theme/preferences', () => {
    it('should update theme preferences with validation', async () => {
      // Given: Valid theme update data
      const updateData = {
        mode: 'dark',
        colorScheme: 'emynent-dark',
        customColors: [],
      }

      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'PUT',
        updateData
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Updating theme preferences via controller
      await themeController.updateThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should update successfully
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(200)
      expect(responseData).toMatchObject({
        success: true,
        data: expect.objectContaining({
          currentTheme: expect.objectContaining({
            mode: 'dark',
            colorScheme: 'emynent-dark',
            source: 'user_selection',
          }),
        }),
        metadata: expect.objectContaining({
          responseTime: expect.any(Number),
          aiLearningApplied: true,
        }),
      })

      // Performance requirement
      expect(responseData.metadata.responseTime).toBeLessThan(100)
    })

    it('should validate theme data and reject invalid inputs', async () => {
      // Given: Invalid theme data
      const invalidData = {
        mode: 'invalid-mode',
        colorScheme: 'non-existent-scheme',
      }

      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'PUT',
        invalidData
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Sending invalid data via controller
      await themeController.updateThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should reject with validation errors
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(400)
      expect(responseData).toMatchObject({
        success: false,
        error: {
          code: expect.stringMatching(/VALIDATION_ERROR|SECURITY_VIOLATION/),
          message: expect.any(String),
          details: expect.any(Array),
        },
      })
    })

    it('should persist changes to database and update cache', async () => {
      // Given: Theme update
      const updateData = {
        mode: 'system',
        colorScheme: 'draculapro',
      }

      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'PUT',
        updateData
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Updating preferences via controller
      await themeController.updateThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should persist to database
      const user = await testPrisma.user.findUnique({
        where: { id: TEST_USERS.employee.id },
        select: { themeMode: true, colorScheme: true },
      })

      expect(user).toMatchObject({
        themeMode: 'system',
        colorScheme: 'draculapro',
      })

      // And: Should invalidate cache (cache should be cleared after update)
      const cacheKey = `theme:preferences:${TEST_USERS.employee.id}`
      const cached = await testRedis.get(cacheKey)
      // Cache should be cleared after update for fresh data
      expect(cached).toBeFalsy()
    })
  })

  describe('🤖 GET /api/theme/recommendations', () => {
    it('should provide AI-powered theme recommendations', async () => {
      // Given: User with behavioral data
      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'GET'
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Requesting AI recommendations via controller
      await themeController.getAIRecommendations(mockRequest as any, mockResponse as any)

      // Then: Should return AI recommendations
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(200)
      expect(responseData).toMatchObject({
        success: true,
        data: expect.objectContaining({
          userId: expect.any(String),
          recommendations: expect.any(Array),
          insights: expect.any(Object),
          generatedAt: expect.any(String),
        }),
        metadata: expect.objectContaining({
          responseTime: expect.any(Number),
          algorithm: expect.any(String),
        }),
      })

      // Each recommendation should have required fields
      if (responseData.data.recommendations.length > 0) {
        responseData.data.recommendations.forEach((rec: any) => {
          expect(rec).toMatchObject({
            type: expect.any(String),
            suggestion: expect.any(String),
            confidence: expect.any(Number),
            reasoning: expect.any(String),
            expectedBenefit: expect.any(String),
            metadata: expect.any(Object),
          })
        })
      }
    })

    it('should implement rate limiting for AI endpoints', async () => {
      // Given: Multiple rapid requests (exceeding the 10 request limit)
      const requests = Array(15)
        .fill(null)
        .map(() => {
          const mockRequest = createMockRequest(
            { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
            'GET'
          )
          const { mockResponse, getResponse } = createMockResponse()
          return { mockRequest, mockResponse, getResponse }
        })

      // When: Making rapid requests sequentially to trigger rate limiting
      const results = []
      for (const { mockRequest, mockResponse, getResponse } of requests) {
        await themeController.getAIRecommendations(mockRequest as any, mockResponse as any)
        results.push(getResponse())
      }

      // Then: Should rate limit after threshold (10 requests per minute)
      const successCount = results.filter(r => r.statusCode === 200).length
      const rateLimitedCount = results.filter(r => r.statusCode === 429).length

      // Should allow 10 requests but rate limit the rest
      expect(successCount).toBe(10)
      expect(rateLimitedCount).toBe(5)
    })
  })

  describe('🔒 Security & Performance Requirements', () => {
    it('should implement proper CSRF protection', async () => {
      // Given: Request without proper headers
      const mockRequest = {
        user: { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        headers: {}, // Missing Content-Type
        method: 'PUT',
        url: '/api/theme/preferences',
        body: { mode: 'dark' },
      }
      const { mockResponse, getResponse } = createMockResponse()

      // When: Making request without proper headers
      await themeController.updateThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should still process but with security validation
      const { statusCode } = getResponse()
      // The controller should handle this gracefully
      expect([200, 400, 403]).toContain(statusCode)
    })

    it('should sanitize all user inputs', async () => {
      // Given: Malicious input data
      const maliciousData = {
        mode: '<script>alert("xss")</script>',
        colorScheme: 'javascript:alert(1)',
        customColors: ['<img src=x onerror=alert(1)>'],
      }

      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'PUT',
        maliciousData
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Sending malicious data
      await themeController.updateThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should reject with security violation
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(400)
      expect(responseData).toMatchObject({
        success: false,
        error: {
          code: expect.stringMatching(/VALIDATION_ERROR|SECURITY_VIOLATION/),
          message: expect.any(String),
        },
      })
    })

    it('should meet performance requirements consistently', async () => {
      // Given: Multiple concurrent requests
      const startTime = performance.now()
      const requests = Array(5)
        .fill(null)
        .map(() => {
          const mockRequest = createMockRequest(
            { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
            'GET'
          )
          const { mockResponse, getResponse } = createMockResponse()
          return { mockRequest, mockResponse, getResponse }
        })

      // When: Processing concurrent requests
      const results = await Promise.all(
        requests.map(async ({ mockRequest, mockResponse, getResponse }) => {
          await themeController.getThemePreferences(mockRequest as any, mockResponse as any)
          return getResponse()
        })
      )
      const endTime = performance.now()

      // Then: Should meet performance requirements
      const totalTime = endTime - startTime
      expect(totalTime).toBeLessThan(500) // 5 requests in under 500ms

      results.forEach(({ statusCode, responseData }) => {
        expect(statusCode).toBe(200)
        expect(responseData.metadata.responseTime).toBeLessThan(100)
      })
    })
  })

  describe('🔄 Real-time Features', () => {
    it('should broadcast theme updates via WebSocket', async () => {
      // Given: WebSocket-enabled theme update
      const updateData = { mode: 'dark', colorScheme: 'emynent-dark' }
      const mockRequest = createMockRequest(
        { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
        'PUT',
        updateData
      )
      const { mockResponse, getResponse } = createMockResponse()

      // When: Updating theme with WebSocket support
      await themeController.updateThemePreferences(mockRequest as any, mockResponse as any)

      // Then: Should update successfully (WebSocket broadcasting is internal)
      const { statusCode, responseData } = getResponse()
      expect(statusCode).toBe(200)
      expect(responseData.success).toBe(true)

      // WebSocket functionality is tested separately
      // This ensures the API structure supports real-time features
    })
  })
})
