import { render, screen } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { SuperAdminLayout } from '../../src/components/superadmin/SuperAdminLayout'
import { Role } from '@prisma/client'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn() }),
  usePathname: () => '/superadmin',
}))

// Mock session with SuperAdmin role
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Super Admin',
    role: Role.SUPERADMIN,
    companyId: 'test-company-id',
  },
  expires: '2024-12-31',
}

describe('SuperAdmin Responsive Padding Fix', () => {
  const renderWithSession = (component: React.ReactElement) => {
    return render(<SessionProvider session={mockSession as any}>{component}</SessionProvider>)
  }

  it('should have responsive padding in SuperAdmin header matching MainNavbar', () => {
    const TestContent = () => <div data-testid='test-content'>Test Content</div>

    renderWithSession(
      <SuperAdminLayout>
        <TestContent />
      </SuperAdminLayout>
    )

    // Get the SuperAdmin layout
    const layout = screen.getByTestId('superadmin-layout')
    expect(layout).toBeInTheDocument()

    // Find the header element (should be the first child with h-16 class)
    const header = layout.querySelector('.h-16.border-b')
    expect(header).toBeInTheDocument()

    // Verify responsive padding classes are present
    expect(header).toHaveClass('px-4') // Base padding
    expect(header).toHaveClass('md:px-6') // Medium screen padding
    expect(header).toHaveClass('lg:px-8') // Large screen padding

    // Verify other alignment properties
    expect(header).toHaveClass('flex')
    expect(header).toHaveClass('items-center')
    expect(header).toHaveClass('border-b')
    expect(header).toHaveClass('border-border')
    expect(header).toHaveClass('bg-background/80')
    expect(header).toHaveClass('backdrop-blur-lg')
  })

  it('should maintain consistent height with main sidebar', () => {
    const TestContent = () => <div data-testid='test-content'>Test Content</div>

    renderWithSession(
      <SuperAdminLayout>
        <TestContent />
      </SuperAdminLayout>
    )

    const layout = screen.getByTestId('superadmin-layout')
    const header = layout.querySelector('.h-16.border-b')

    // Verify height consistency
    expect(header).toHaveClass('h-16') // 64px height matching main sidebar
    expect(header).toHaveClass('flex-shrink-0') // Prevents height collapse
  })

  it('should have proper visual alignment properties', () => {
    const TestContent = () => <div data-testid='test-content'>Test Content</div>

    renderWithSession(
      <SuperAdminLayout>
        <TestContent />
      </SuperAdminLayout>
    )

    const layout = screen.getByTestId('superadmin-layout')
    const header = layout.querySelector('.h-16.border-b')

    // Verify visual properties for alignment
    expect(header).toHaveClass('bg-background/80') // Same background as main sidebar
    expect(header).toHaveClass('backdrop-blur-lg') // Same backdrop blur
    expect(header).toHaveClass('border-border') // Same border color variable
  })
})
