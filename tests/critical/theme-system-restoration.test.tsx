/**
 * 🧪 Theme System Restoration Test
 *
 * Verifies that the sophisticated theme system with useEnhancedTheme is working correctly
 * after restoring from commit 401302a83e285d334ce58eb5bd566570e9360032.
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach } from 'vitest'
import { EnhancedThemeProvider, useEnhancedTheme } from '@/lib/ThemeContext'
import { ThemeProvider } from 'next-themes'

// Test component that uses the sophisticated theme system
function TestThemeComponent() {
  const {
    colorScheme,
    setColorScheme,
    customColors,
    addCustomColor,
    removeCustomColor,
    gradientSettings,
    updateGradientSettings,
    exportTheme,
    resetTheme,
  } = useEnhancedTheme()

  return (
    <div data-testid='theme-component'>
      <div data-testid='current-scheme'>{colorScheme}</div>
      <div data-testid='custom-colors-count'>{customColors.length}</div>
      <div data-testid='gradient-enabled'>{gradientSettings.enabled.toString()}</div>

      <button data-testid='change-scheme' onClick={() => setColorScheme('emynent-dark')}>
        Change to Dark
      </button>

      <button
        data-testid='add-color'
        onClick={() =>
          addCustomColor({
            id: 'test-color',
            name: 'Test Color',
            value: '#ff0000',
            category: 'primary',
          })
        }
      >
        Add Color
      </button>

      <button data-testid='reset-theme' onClick={resetTheme}>
        Reset Theme
      </button>

      <button
        data-testid='export-theme'
        onClick={() => {
          const exported = exportTheme()
          console.log('Exported theme:', exported)
        }}
      >
        Export Theme
      </button>
    </div>
  )
}

// Wrapper component with providers
function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute='class'
      defaultTheme='light'
      enableSystem={true}
      disableTransitionOnChange={false}
    >
      <EnhancedThemeProvider>{children}</EnhancedThemeProvider>
    </ThemeProvider>
  )
}

describe('🎨 Theme System Restoration', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear()
  })

  it('should render the sophisticated theme system correctly', () => {
    render(
      <TestWrapper>
        <TestThemeComponent />
      </TestWrapper>
    )

    expect(screen.getByTestId('theme-component')).toBeInTheDocument()
    expect(screen.getByTestId('current-scheme')).toBeInTheDocument()
    expect(screen.getByTestId('custom-colors-count')).toBeInTheDocument()
    expect(screen.getByTestId('gradient-enabled')).toBeInTheDocument()
  })

  it('should have default color scheme set', () => {
    render(
      <TestWrapper>
        <TestThemeComponent />
      </TestWrapper>
    )

    const currentScheme = screen.getByTestId('current-scheme')
    expect(currentScheme.textContent).toBe('emynent-light')
  })

  it('should allow changing color schemes', async () => {
    render(
      <TestWrapper>
        <TestThemeComponent />
      </TestWrapper>
    )

    const changeButton = screen.getByTestId('change-scheme')
    fireEvent.click(changeButton)

    await waitFor(() => {
      const currentScheme = screen.getByTestId('current-scheme')
      expect(currentScheme.textContent).toBe('emynent-dark')
    })
  })

  it('should allow adding custom colors', async () => {
    render(
      <TestWrapper>
        <TestThemeComponent />
      </TestWrapper>
    )

    const addButton = screen.getByTestId('add-color')
    const customColorsCount = screen.getByTestId('custom-colors-count')

    // Initial count should be 0
    expect(customColorsCount.textContent).toBe('0')

    fireEvent.click(addButton)

    await waitFor(() => {
      expect(customColorsCount.textContent).toBe('1')
    })
  })

  it('should have gradient settings available', () => {
    render(
      <TestWrapper>
        <TestThemeComponent />
      </TestWrapper>
    )

    const gradientEnabled = screen.getByTestId('gradient-enabled')
    expect(gradientEnabled.textContent).toBe('false') // Default should be false
  })

  it('should allow theme export functionality', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

    render(
      <TestWrapper>
        <TestThemeComponent />
      </TestWrapper>
    )

    const exportButton = screen.getByTestId('export-theme')
    fireEvent.click(exportButton)

    expect(consoleSpy).toHaveBeenCalledWith(
      'Exported theme:',
      expect.objectContaining({
        colorScheme: expect.any(String),
        customColors: expect.any(Array),
        gradientSettings: expect.any(Object),
      })
    )

    consoleSpy.mockRestore()
  })

  it('should allow theme reset functionality', async () => {
    render(
      <TestWrapper>
        <TestThemeComponent />
      </TestWrapper>
    )

    // First add a custom color and change scheme
    const addButton = screen.getByTestId('add-color')
    const changeButton = screen.getByTestId('change-scheme')

    fireEvent.click(addButton)
    fireEvent.click(changeButton)

    await waitFor(() => {
      expect(screen.getByTestId('custom-colors-count').textContent).toBe('1')
      expect(screen.getByTestId('current-scheme').textContent).toBe('emynent-dark')
    })

    // Now reset
    const resetButton = screen.getByTestId('reset-theme')
    fireEvent.click(resetButton)

    await waitFor(() => {
      expect(screen.getByTestId('custom-colors-count').textContent).toBe('0')
      expect(screen.getByTestId('current-scheme').textContent).toBe('emynent-light')
    })
  })
})

describe('🎨 Appearance Page Integration', () => {
  it('should load the appearance page without errors', async () => {
    // This test verifies that the appearance page can be imported and rendered
    const { default: AppearancePage } = await import(
      '@/app/(protected)/settings/platform/appearance/page'
    )

    expect(AppearancePage).toBeDefined()
    expect(typeof AppearancePage).toBe('function')
  })
})
