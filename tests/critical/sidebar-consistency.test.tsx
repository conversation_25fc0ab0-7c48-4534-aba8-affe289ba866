import { test, expect } from '@playwright/test'

test.describe('Sidebar Consistency and Layout', () => {
  test('Main sidebar should have a footer collapse button and no header button', async ({
    page,
  }) => {
    await page.goto('/dashboard')

    const sidebar = page.getByTestId('ai-first-sidebar')

    // Assert the collapse button is in the footer of the sidebar
    const footerCollapseButton = sidebar.locator('footer [data-testid="sidebar-collapse-toggle"]')
    await expect(footerCollapseButton).toBeVisible()

    // Assert there is no collapse button in the sidebar's header
    const headerCollapseButton = sidebar.locator('header [data-testid="sidebar-collapse-toggle"]')
    await expect(headerCollapseButton).not.toBeVisible()
  })

  test('SuperAdmin sidebar should have a footer collapse button and no main content header button', async ({
    page,
  }) => {
    await page.goto('/superadmin')

    const superAdminSidebar = page.getByTestId('superadmin-sidebar')

    // Assert the collapse button is in the footer of the SuperAdmin sidebar
    const footerCollapseButton = superAdminSidebar.locator(
      'footer [data-testid="sidebar-collapse-toggle"]'
    )
    await expect(footerCollapseButton).toBeVisible()

    // Assert there is no collapse button in the main content's header
    const mainContentHeader = page.locator('.flex-1.flex.flex-col.overflow-hidden > .flex-shrink-0')
    const headerCollapseButton = mainContentHeader.getByRole('button', { name: /sidebar/i })
    await expect(headerCollapseButton).not.toBeVisible()
  })

  test('Settings sidebar should have a footer collapse button', async ({ page }) => {
    await page.goto('/settings/profile')

    // Look for the Settings sidebar by its structure - it should contain settings navigation
    const settingsSidebar = page.locator('aside').first()

    // Assert the collapse button is in the footer of the Settings sidebar
    const footerCollapseButton = settingsSidebar.locator(
      'footer [data-testid="sidebar-collapse-toggle"]'
    )
    await expect(footerCollapseButton).toBeVisible()
  })

  test('Sidebar collapse state should be controlled internally and persisted', async ({ page }) => {
    await page.goto('/dashboard')
    const sidebar = page.getByTestId('ai-first-sidebar')
    const collapseButton = sidebar.locator('footer [data-testid="sidebar-collapse-toggle"]')

    // Check initial width (assuming default is expanded)
    const initialWidth = await sidebar.evaluate(el => el.getBoundingClientRect().width)
    expect(initialWidth).toBeGreaterThan(100)

    // Click to collapse
    await collapseButton.click()

    // Wait for animation to complete
    await page.waitForTimeout(500)

    const collapsedWidth = await sidebar.evaluate(el => el.getBoundingClientRect().width)
    expect(collapsedWidth).toBeLessThan(100)

    // Reload the page and check if the state is persisted
    await page.reload()
    await page.waitForURL('**/dashboard')

    // Wait for sidebar to load
    const persistedSidebar = page.getByTestId('ai-first-sidebar')
    await expect(persistedSidebar).toBeVisible()
    await page.waitForTimeout(500)

    const persistedWidth = await persistedSidebar.evaluate(el => el.getBoundingClientRect().width)
    expect(persistedWidth).toBeLessThan(100)
  })
})
