import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SessionProvider } from 'next-auth/react'
import { Role } from '@prisma/client'
import React from 'react'
import { SuperAdminSidebar } from '@/components/superadmin/SuperAdminSidebar'
import { SuperAdminLayout } from '@/components/superadmin/SuperAdminLayout'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  usePathname: () => '/superadmin',
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock session data
const mockSuperAdminSession = {
  user: {
    id: 'superadmin-123',
    email: '<EMAIL>',
    name: 'Super Admin',
    role: Role.SUPERADMIN,
    companyId: 'company-123',
  },
  expires: '2024-12-31',
}

const mockAdminSession = {
  user: {
    id: 'admin-123',
    email: '<EMAIL>',
    name: 'Admin User',
    role: Role.ADMIN,
    companyId: 'company-123',
  },
  expires: '2024-12-31',
}

const mockEmployeeSession = {
  user: {
    id: 'employee-123',
    email: '<EMAIL>',
    name: 'Employee User',
    role: Role.EMPLOYEE,
    companyId: 'company-123',
  },
  expires: '2024-12-31',
}

// Test wrapper component
function TestWrapper({
  children,
  session = mockSuperAdminSession,
}: {
  children: React.ReactNode
  session?: any
}) {
  return <SessionProvider session={session}>{children}</SessionProvider>
}

describe('SuperAdmin Hierarchical Sidebar Navigation System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('1. SuperAdmin Sidebar Visibility & Access Control', () => {
    it('should show SuperAdmin sidebar ONLY to users with SUPERADMIN role', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // SuperAdmin sidebar should be visible
      expect(screen.getByTestId('superadmin-sidebar')).toBeInTheDocument()
      expect(screen.getByText('Super Admin Panel')).toBeInTheDocument()
    })

    it('should NOT show SuperAdmin sidebar to ADMIN users', async () => {
      render(
        <TestWrapper session={mockAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // Should show access denied or nothing
      expect(screen.queryByTestId('superadmin-sidebar')).toBeNull()
      expect(screen.getByText('Access Denied')).toBeInTheDocument()
    })

    it('should NOT show SuperAdmin sidebar to EMPLOYEE users', async () => {
      render(
        <TestWrapper session={mockEmployeeSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // Should show access denied or nothing
      expect(screen.queryByTestId('superadmin-sidebar')).toBeNull()
      expect(screen.getByText('Access Denied')).toBeInTheDocument()
    })

    it('should redirect non-SuperAdmin users attempting to access SuperAdmin routes', async () => {
      const mockRouter = { push: vi.fn() }
      vi.doMock('next/navigation', () => ({
        useRouter: () => mockRouter,
        usePathname: () => '/superadmin',
      }))

      render(
        <TestWrapper session={mockAdminSession}>
          <SuperAdminLayout>
            <div>Protected Content</div>
          </SuperAdminLayout>
        </TestWrapper>
      )

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/unauthorized')
      })
    })
  })

  describe('2. Hierarchical Navigation Structure', () => {
    it('should display correct hierarchical navigation items', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // System Management section
      expect(screen.getByText('System Management')).toBeInTheDocument()
      expect(screen.getByTestId('nav-companies')).toBeInTheDocument()
      expect(screen.getByTestId('nav-users')).toBeInTheDocument()
      expect(screen.getByTestId('nav-audit-logs')).toBeInTheDocument()

      // Design System section
      expect(screen.getByText('Design System')).toBeInTheDocument()
      expect(screen.getByTestId('nav-design-editor')).toBeInTheDocument()
      expect(screen.getByTestId('nav-component-library')).toBeInTheDocument()
      expect(screen.getByTestId('nav-theme-manager')).toBeInTheDocument()

      // AI & Intelligence section
      expect(screen.getByText('AI & Intelligence')).toBeInTheDocument()
      expect(screen.getByTestId('nav-ai-models')).toBeInTheDocument()
      expect(screen.getByTestId('nav-ai-performance')).toBeInTheDocument()
      expect(screen.getByTestId('nav-ai-training')).toBeInTheDocument()

      // System Health section
      expect(screen.getByText('System Health')).toBeInTheDocument()
      expect(screen.getByTestId('nav-system-metrics')).toBeInTheDocument()
      expect(screen.getByTestId('nav-performance-monitoring')).toBeInTheDocument()
      expect(screen.getByTestId('nav-error-tracking')).toBeInTheDocument()
    })

    it('should support expandable/collapsible navigation groups', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const systemManagementToggle = screen.getByTestId('nav-system-management-toggle')

      // Should be expanded by default
      expect(screen.getByTestId('nav-companies')).toBeInTheDocument()

      // Collapse the section
      await userEvent.click(systemManagementToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-companies')).not.toBeInTheDocument()
      })

      // Expand again
      await userEvent.click(systemManagementToggle)

      await waitFor(() => {
        expect(screen.getByTestId('nav-companies')).toBeInTheDocument()
      })
    })

    it('should maintain proper navigation state when switching between sections', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // Click on Company Management
      const companyLink = screen.getByTestId('nav-companies')
      await userEvent.click(companyLink)

      // Should highlight active navigation item
      expect(companyLink).toHaveClass('bg-primary/10', 'text-primary')

      // Click on Design System Editor
      const designEditorLink = screen.getByTestId('nav-design-editor')
      await userEvent.click(designEditorLink)

      // Should update active state
      expect(designEditorLink).toHaveClass('bg-primary/10', 'text-primary')
      expect(companyLink).not.toHaveClass('bg-primary/10', 'text-primary')
    })
  })

  describe('3. SuperAdmin Sidebar Independence from Settings Sidebar', () => {
    it('should render SuperAdmin sidebar independently of Settings sidebar', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminLayout>
            <div>SuperAdmin Content</div>
          </SuperAdminLayout>
        </TestWrapper>
      )

      // SuperAdmin layout should NOT include Settings sidebar
      expect(screen.queryByTestId('settings-sidebar')).toBeNull()
      expect(screen.getByTestId('superadmin-sidebar')).toBeInTheDocument()
      expect(screen.getByTestId('superadmin-layout')).toBeInTheDocument()
    })

    it('should have completely separate navigation routing from Settings', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // All navigation links should point to /superadmin/* routes, not /settings/*
      const companyLink = screen.getByTestId('nav-companies')
      expect(companyLink).toHaveAttribute('href', '/superadmin/companies')

      const designEditorLink = screen.getByTestId('nav-design-editor')
      expect(designEditorLink).toHaveAttribute('href', '/superadmin/design-system')

      const aiModelsLink = screen.getByTestId('nav-ai-models')
      expect(aiModelsLink).toHaveAttribute('href', '/superadmin/ai-models')

      // Should NOT have any /settings/* routes
      const allLinks = screen.getAllByRole('link')
      allLinks.forEach(link => {
        const href = link.getAttribute('href')
        if (href) {
          expect(href).not.toMatch(/^\/settings/)
        }
      })
    })

    it('should support SuperAdmin access to BOTH Settings sidebar AND SuperAdmin sidebar', async () => {
      // This test verifies that SuperAdmin users can access both sidebars
      // but they are architecturally independent

      // First, test that SuperAdmin can access regular Settings
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <div data-testid='settings-page'>
            {/* Mock Settings sidebar component */}
            <div data-testid='settings-sidebar'>Settings Navigation</div>
          </div>
        </TestWrapper>
      )

      expect(screen.getByTestId('settings-sidebar')).toBeInTheDocument()

      // Now test SuperAdmin sidebar access
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      expect(screen.getByTestId('superadmin-sidebar')).toBeInTheDocument()
    })
  })

  describe('4. Navigation Breadcrumbs & Context', () => {
    it('should display proper breadcrumb navigation', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminLayout>
            <div>Content</div>
          </SuperAdminLayout>
        </TestWrapper>
      )

      const breadcrumbs = screen.getByTestId('superadmin-breadcrumbs')
      expect(breadcrumbs).toBeInTheDocument()
      expect(screen.getByText('Super Admin')).toBeInTheDocument()
    })

    it('should update breadcrumbs based on current navigation context', async () => {
      // Mock being on /superadmin/companies page
      vi.doMock('next/navigation', () => ({
        usePathname: () => '/superadmin/companies',
        useRouter: () => ({ push: vi.fn() }),
      }))

      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminLayout>
            <div>Company Management</div>
          </SuperAdminLayout>
        </TestWrapper>
      )

      const breadcrumbs = screen.getByTestId('superadmin-breadcrumbs')
      expect(breadcrumbs).toContainElement(screen.getByText('Super Admin'))
      expect(breadcrumbs).toContainElement(screen.getByText('Companies'))
    })
  })

  describe('5. Collapsible Sidebar Functionality', () => {
    it('should support collapsible sidebar with toggle button', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const toggleButton = screen.getByTestId('superadmin-sidebar-toggle')
      const sidebar = screen.getByTestId('superadmin-sidebar')

      // Should be expanded by default
      expect(sidebar).not.toHaveClass('collapsed')

      // Collapse sidebar
      await userEvent.click(toggleButton)

      await waitFor(() => {
        expect(sidebar).toHaveClass('collapsed')
      })

      // Expand sidebar
      await userEvent.click(toggleButton)

      await waitFor(() => {
        expect(sidebar).not.toHaveClass('collapsed')
      })
    })

    it('should show tooltip hints when sidebar is collapsed', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const toggleButton = screen.getByTestId('superadmin-sidebar-toggle')
      await userEvent.click(toggleButton)

      // Hover over a collapsed navigation item
      const companyLink = screen.getByTestId('nav-companies')
      await userEvent.hover(companyLink)

      await waitFor(() => {
        expect(screen.getByText('Company Management')).toBeInTheDocument()
      })
    })
  })

  describe('6. Responsive Design & Mobile Support', () => {
    it('should adapt to mobile viewport', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      })

      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const sidebar = screen.getByTestId('superadmin-sidebar')

      // Should have mobile-specific classes
      expect(sidebar).toHaveClass('mobile-sidebar')
    })

    it('should support touch gestures for mobile navigation', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const sidebar = screen.getByTestId('superadmin-sidebar')

      // Simulate touch events
      fireEvent.touchStart(sidebar, { touches: [{ clientX: 0, clientY: 0 }] })
      fireEvent.touchEnd(sidebar, { changedTouches: [{ clientX: 100, clientY: 0 }] })

      // Should handle touch gestures appropriately
      expect(sidebar).toBeInTheDocument()
    })
  })

  describe('7. Performance & Accessibility', () => {
    it('should have proper ARIA attributes for accessibility', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const sidebar = screen.getByTestId('superadmin-sidebar')
      expect(sidebar).toHaveAttribute('role', 'navigation')
      expect(sidebar).toHaveAttribute('aria-label', 'Super Admin Navigation')

      const companyLink = screen.getByTestId('nav-companies')
      expect(companyLink).toHaveAttribute('aria-label')
      expect(companyLink).toHaveAttribute('href')
    })

    it('should support keyboard navigation', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const firstNavItem = screen.getByTestId('nav-companies')
      firstNavItem.focus()

      // Tab through navigation items
      await userEvent.keyboard('{Tab}')

      const secondNavItem = screen.getByTestId('nav-users')
      expect(secondNavItem).toHaveFocus()

      // Enter should activate navigation
      await userEvent.keyboard('{Enter}')

      // Should navigate to the route
      expect(secondNavItem).toHaveAttribute('href', '/superadmin/users')
    })

    it('should lazy load heavy navigation components', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // Heavy components should be lazy loaded
      const designSystemSection = screen.getByText('Design System')
      await userEvent.click(designSystemSection)

      // Should load design system navigation items
      await waitFor(() => {
        expect(screen.getByTestId('nav-design-editor')).toBeInTheDocument()
      })
    })
  })

  describe('8. Error Handling & Fallbacks', () => {
    it('should handle navigation errors gracefully', async () => {
      // Mock navigation error
      const mockRouter = { push: vi.fn().mockRejectedValue(new Error('Navigation failed')) }
      vi.doMock('next/navigation', () => ({
        useRouter: () => mockRouter,
        usePathname: () => '/superadmin',
      }))

      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      const companyLink = screen.getByTestId('nav-companies')
      await userEvent.click(companyLink)

      // Should show error message or fallback
      await waitFor(() => {
        expect(screen.getByText(/navigation error/i)).toBeInTheDocument()
      })
    })

    it('should show fallback UI when sidebar fails to load', async () => {
      // Mock component error
      const originalError = console.error
      console.error = vi.fn()

      const ThrowError = () => {
        throw new Error('Sidebar failed to load')
      }

      render(
        <TestWrapper session={mockSuperAdminSession}>
          <ThrowError />
        </TestWrapper>
      )

      console.error = originalError
    })
  })

  describe('9. Integration with Existing SuperAdmin Functionality', () => {
    it('should integrate seamlessly with existing SuperAdmin pages', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminLayout>
            <div data-testid='superadmin-page-content'>Existing SuperAdmin Dashboard Content</div>
          </SuperAdminLayout>
        </TestWrapper>
      )

      // Should have both sidebar and existing content
      expect(screen.getByTestId('superadmin-sidebar')).toBeInTheDocument()
      expect(screen.getByTestId('superadmin-page-content')).toBeInTheDocument()
      expect(screen.getByTestId('superadmin-layout')).toBeInTheDocument()
    })

    it('should maintain all existing SuperAdmin route functionality', async () => {
      render(
        <TestWrapper session={mockSuperAdminSession}>
          <SuperAdminSidebar />
        </TestWrapper>
      )

      // Verify navigation links point to existing routes
      const routes = [
        { testId: 'nav-companies', href: '/superadmin/companies' },
        { testId: 'nav-design-editor', href: '/superadmin/design-system' },
        { testId: 'nav-ai-models', href: '/superadmin/ai-models' },
        { testId: 'nav-audit-logs', href: '/superadmin/audit-logs' },
      ]

      routes.forEach(route => {
        const link = screen.getByTestId(route.testId)
        expect(link).toHaveAttribute('href', route.href)
      })
    })
  })
})
