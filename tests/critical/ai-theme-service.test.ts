/**
 * 🧪 AI-First Theme Service Tests
 *
 * Behavior-Driven Development tests for the sophisticated theme service.
 * Tests written BEFORE implementation to drive design.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  testPrisma,
  testRedis,
  TEST_USERS,
  TEST_THEME_CONTEXTS,
  cleanTestData,
  seedTestData,
} from '../setup/test-database'

// Import the service we're about to create
import { AIThemeService } from '../../src/services/ai-theme-service'
import type {
  ThemePreferences,
  AIThemeRecommendation,
  UserThemeContext,
} from '../../src/types/ai-theme'

describe('🤖 AI-First Theme Service', () => {
  let themeService: AIThemeService

  beforeEach(async () => {
    await cleanTestData()
    await seedTestData()
    themeService = new AIThemeService(testPrisma, testRedis)
  })

  afterEach(async () => {
    await cleanTestData()
  })

  describe('🎨 Theme Preference Management', () => {
    it('should retrieve user theme preferences with AI context', async () => {
      // Given: A user with theme preferences
      const userId = TEST_USERS.employee.id

      // When: Getting theme preferences
      const preferences = await themeService.getUserThemePreferences(userId)

      // Then: Should return comprehensive theme data
      expect(preferences).toMatchObject({
        userId,
        currentTheme: {
          mode: 'light',
          colorScheme: 'emynent-light',
        },
        aiContext: {
          preferredColorSchemes: ['emynent-light', 'syntaxlight'],
          satisfactionScores: expect.any(Object),
          usagePatterns: expect.any(Object),
        },
        customizations: {
          colors: [
            {
              id: 'custom-1',
              name: 'Brand Blue',
              value: '#1E40AF',
              element: 'primary',
            },
          ],
          gradients: expect.objectContaining({
            enabled: true,
            type: 'linear',
          }),
        },
      })

      expect(preferences.metadata.version).toBe('1.0.0')
    })

    it('should update theme preferences with AI learning', async () => {
      // Given: A user and new theme preferences
      const userId = TEST_USERS.employee.id
      const updates = {
        mode: 'dark' as const,
        colorScheme: 'nightowl' as const,
      }

      // When: Updating preferences
      const result = await themeService.updateThemePreferences(userId, updates)

      // Then: Should update successfully
      expect(result.success).toBe(true)
      expect(result.preferences?.currentTheme.mode).toBe('dark')
      expect(result.preferences?.currentTheme.colorScheme).toBe('nightowl')

      // And: Should record the change for AI learning
      const context = await themeService.getUserThemeContext(userId)
      expect(context.recentActions).toContainEqual(
        expect.objectContaining({
          action: 'theme_switch',
          data: expect.objectContaining({
            from: expect.any(String),
            to: 'dark/nightowl',
          }),
        })
      )
    })
  })

  describe('🧠 AI-Powered Recommendations', () => {
    it('should generate personalized theme recommendations', async () => {
      // Given: A user with theme usage history
      const userId = TEST_USERS.employee.id

      // When: Getting AI recommendations
      const recommendations = await themeService.getAIRecommendations(userId)

      // Then: Should provide personalized suggestions
      expect(recommendations.userId).toBe(userId)
      expect(recommendations.recommendations).toBeInstanceOf(Array)
      expect(recommendations.insights).toHaveProperty('usagePatterns')
      expect(recommendations.insights).toHaveProperty('satisfactionMetrics')
      expect(recommendations.generatedAt).toEqual(expect.any(String))

      // And: Should include high-confidence recommendations
      const highConfidenceRecs = recommendations.recommendations.filter(r => r.confidence > 0.7)
      expect(highConfidenceRecs.length).toBeGreaterThan(0)
    })

    it('should adapt recommendations based on time of day', async () => {
      // Given: Evening time (18:00)
      const userId = TEST_USERS.employee.id
      const mockDate = new Date()
      mockDate.setHours(18, 0, 0, 0)
      vi.setSystemTime(mockDate)

      // When: Getting contextual recommendations
      const recommendations = await themeService.getContextualRecommendations(userId, {
        time: { hour: 18, timezone: 'UTC' },
      })

      // Then: Should suggest dark theme for evening
      const darkThemeRec = recommendations.recommendations.find(
        r => r.type === 'time_based' && r.suggestion.includes('dark')
      )
      expect(darkThemeRec).toBeDefined()
      expect(darkThemeRec?.reasoning).toContain('evening')

      vi.useRealTimers()
    })
  })

  describe('🎯 Behavioral Analytics', () => {
    it('should track theme usage patterns', async () => {
      // Given: A user and theme interactions
      const userId = TEST_USERS.employee.id
      const interactions = [
        {
          action: 'theme_switch' as const,
          from: 'light/emynent-light',
          to: 'dark/emynent-dark',
          timestamp: Date.now(),
        },
        {
          action: 'color_customize' as const,
          color: '#FF5733',
          element: 'accent',
          timestamp: Date.now(),
        },
      ]

      // When: Tracking interactions
      for (const interaction of interactions) {
        await themeService.trackThemeInteraction(userId, interaction)
      }

      // Then: Should update usage patterns
      const analytics = await themeService.getThemeAnalytics(userId)
      expect(analytics.interactions.length).toBeGreaterThanOrEqual(interactions.length)
      expect(analytics.patterns.switchFrequency).toBeGreaterThan(0)
    })

    it('should calculate satisfaction scores', async () => {
      // Given: A user and satisfaction feedback
      const userId = TEST_USERS.employee.id
      const feedback = {
        colorScheme: 'emynent-light' as const,
        satisfaction: 9,
        aspects: {
          readability: 9,
          aesthetics: 8,
          productivity: 9,
        },
        timestamp: new Date(),
      }

      // When: Recording satisfaction
      await themeService.recordThemeSatisfaction(userId, feedback)

      // Then: Should update satisfaction metrics
      const analytics = await themeService.getThemeAnalytics(userId)
      expect(analytics.satisfaction['emynent-light']).toBeDefined()
      expect(analytics.satisfaction['emynent-light'].overall).toBe(9)
      expect(analytics.satisfaction['emynent-light'].aspects.readability).toBe(9)
    })
  })

  describe('🚀 Performance & Caching', () => {
    it('should cache theme preferences efficiently', async () => {
      // Given: A user
      const userId = TEST_USERS.employee.id

      // When: Getting preferences multiple times
      const start1 = performance.now()
      await themeService.getUserThemePreferences(userId)
      const time1 = performance.now() - start1

      const start2 = performance.now()
      await themeService.getUserThemePreferences(userId)
      const time2 = performance.now() - start2

      // Then: Second call should be faster (cached)
      expect(time2).toBeLessThan(time1)
      expect(time2).toBeLessThan(50) // Should be very fast from cache
    })

    it('should invalidate cache on preference updates', async () => {
      // Given: Cached preferences
      const userId = TEST_USERS.employee.id
      await themeService.getUserThemePreferences(userId) // Cache it

      // When: Updating preferences
      await themeService.updateThemePreferences(userId, { mode: 'dark' })

      // Then: Cache should be invalidated
      const cacheKey = `theme:preferences:${userId}`
      const cached = await testRedis.get(cacheKey)
      expect(cached).toBeFalsy()
    })

    it('should meet performance targets', async () => {
      // Given: A user
      const userId = TEST_USERS.employee.id

      // When: Performing operations
      const start = performance.now()
      await themeService.getUserThemePreferences(userId)
      const getTime = performance.now() - start

      const updateStart = performance.now()
      await themeService.updateThemePreferences(userId, { mode: 'dark' })
      const updateTime = performance.now() - updateStart

      // Then: Should meet performance targets
      expect(getTime).toBeLessThan(100) // <100ms for retrieval
      expect(updateTime).toBeLessThan(200) // <200ms for updates
    })
  })

  describe('🔄 Real-time Updates', () => {
    it('should broadcast theme changes to connected clients', async () => {
      // Given: A mock WebSocket handler
      const broadcasts: any[] = []
      const mockWsHandler = {
        broadcast: (userId: string, message: any) => broadcasts.push({ userId, message }),
        emit: vi.fn(),
      }
      themeService.setWebSocketHandler(mockWsHandler)

      // When: Updating theme preferences
      const userId = TEST_USERS.employee.id
      await themeService.updateThemePreferences(userId, { mode: 'dark' })

      // Then: Should broadcast the change
      expect(broadcasts).toHaveLength(1)
      expect(broadcasts[0].userId).toBe(userId)
      expect(broadcasts[0].message.type).toBe('theme_updated')
      expect(broadcasts[0].message.data.mode).toBe('dark')
    })
  })

  describe('🛡️ Security & Validation', () => {
    it('should validate theme customizations against security rules', async () => {
      // Given: Unsafe theme customizations
      const userId = TEST_USERS.employee.id
      const unsafeUpdates = {
        customCSS: 'body { background: url(javascript:alert("xss")); }',
        colors: [{ value: 'javascript:alert("xss")', name: 'Evil Color' }],
      }

      // When: Attempting to update with unsafe content
      const result = await themeService.updateThemePreferences(userId, unsafeUpdates)

      // Then: Should reject unsafe content
      expect(result.success).toBe(false)
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'customCSS',
          message: expect.stringContaining('unsafe'),
        })
      )
    })

    it('should enforce company theme policies', async () => {
      // Given: A restricted theme
      const userId = TEST_USERS.employee.id
      const restrictedUpdate = {
        colorScheme: 'custom-competitor-theme' as const,
      }

      // When: Attempting to use restricted theme
      const result = await themeService.updateThemePreferences(userId, restrictedUpdate)

      // Then: Should reject based on policy
      expect(result.success).toBe(false)
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'colorScheme',
          message: expect.stringContaining('restricted'),
        })
      )
    })
  })
})
