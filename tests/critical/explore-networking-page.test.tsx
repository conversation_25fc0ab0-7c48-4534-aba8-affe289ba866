import { describe, it, expect } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import NetworkingPage from '@/app/(protected)/explore/networking/page'
import { render, withDatabaseIsolation, createTestData } from '../utils/test-wrapper'

describe('Explore Networking Page', () => {
  describe('Page Structure & Navigation', () => {
    it('renders page title and navigation correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('Networking')).toBeInTheDocument()
        expect(
          screen.getByText(
            'Connect with colleagues, mentors, and industry professionals to expand your network'
          )
        ).toBeInTheDocument()
      })
    })

    it('displays networking categories section', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('Categories')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Internal')[0]).toBeInTheDocument()
        expect(screen.getByText('External')).toBeInTheDocument()
        expect(screen.getByText('Mentorship')).toBeInTheDocument()
        expect(screen.getByText('Industry')).toBeInTheDocument()
      })
    })

    it('shows growth indicators for networking categories', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('+15%')).toBeInTheDocument()
        expect(screen.getByText('+8%')).toBeInTheDocument()
        expect(screen.getByText('+22%')).toBeInTheDocument()
        expect(screen.getByText('+12%')).toBeInTheDocument()
      })
    })
  })

  describe('AI Insights Section', () => {
    it('displays AI insights section with recommendations', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('AI Insights')[0]).toBeInTheDocument()
        expect(screen.getByText('Smart Connections')).toBeInTheDocument()
        expect(screen.getByText('Network Analysis')).toBeInTheDocument()
      })
    })

    it('shows networking trend analysis', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('Network Trend')[0]).toBeInTheDocument()
        expect(
          screen.getByText('Cross-functional connections show 35% higher career growth')
        ).toBeInTheDocument()
      })
    })

    it('displays connection insights', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('Quality Connections')).toBeInTheDocument()
        expect(
          screen.getByText('Strong professional relationships lead to 60% better opportunities')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Network Overview Section', () => {
    it('shows network overview information', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('Network Overview')).toBeInTheDocument()
        expect(screen.getByText('Connection Strength')).toBeInTheDocument()
        expect(screen.getByText('Network Diversity')).toBeInTheDocument()
      })
    })

    it('displays network metrics and indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('127 connections')).toBeInTheDocument()
        expect(screen.getAllByText('85%')[0]).toBeInTheDocument()
        expect(screen.getByText('Strong')).toBeInTheDocument()
      })
    })
  })

  describe('Recommended Connections Section', () => {
    it('displays recommended connections with proper structure', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('Recommended Connections')).toBeInTheDocument()
        expect(screen.getByText('Sarah Chen')).toBeInTheDocument()
        expect(screen.getByText('Marcus Rodriguez')).toBeInTheDocument()
      })
    })

    it('shows connection details and mutual connections', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('Senior Product Manager')).toBeInTheDocument()
        expect(screen.getByText('Engineering Director')).toBeInTheDocument()
        expect(screen.getAllByText('3 mutual connections')[0]).toBeInTheDocument()
        expect(screen.getAllByText('5 mutual connections')[0]).toBeInTheDocument()
      })
    })

    it('displays connection skills and expertise', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('Product Strategy')[0]).toBeInTheDocument()
        expect(screen.getAllByText('User Research')[0]).toBeInTheDocument()
        expect(screen.getByText('System Architecture')).toBeInTheDocument()
        expect(screen.getByText('Team Leadership')).toBeInTheDocument()
      })
    })

    it('shows connection match scores', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('92% match')[0]).toBeInTheDocument()
        expect(screen.getAllByText('87% match')[0]).toBeInTheDocument()
      })
    })
  })

  describe('Quick Actions Section', () => {
    it('displays quick action buttons', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('Quick Actions')[0]).toBeInTheDocument()
        expect(screen.getByTestId('find-colleagues-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('my-connections-sidebar')).toBeInTheDocument()
        expect(screen.getByTestId('mentorship-program-sidebar')).toBeInTheDocument()
      })
    })

    it('handles quick action button clicks', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        const findButton = screen.getByTestId('find-colleagues-sidebar')
        const connectionsButton = screen.getByTestId('my-connections-sidebar')
        const mentorshipButton = screen.getByTestId('mentorship-program-sidebar')

        fireEvent.click(findButton)
        fireEvent.click(connectionsButton)
        fireEvent.click(mentorshipButton)

        // Buttons should be clickable (no errors thrown)
        expect(findButton).toBeInTheDocument()
        expect(connectionsButton).toBeInTheDocument()
        expect(mentorshipButton).toBeInTheDocument()
      })
    })
  })

  describe('Networking Opportunities Section', () => {
    it('displays networking opportunities with events', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('Networking Opportunities')[0]).toBeInTheDocument()
        expect(screen.getAllByText('Upcoming Events')[0]).toBeInTheDocument()
        expect(screen.getByText('Coffee Chats')).toBeInTheDocument()
      })
    })

    it('shows networking metrics and progress indicators', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getByText('8 upcoming events')).toBeInTheDocument()
        expect(screen.getByText('12 coffee chat requests')).toBeInTheDocument()
      })
    })
  })

  describe('Network Growth Display', () => {
    it('displays various growth percentages correctly', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        expect(screen.getAllByText('35% growth')[0]).toBeInTheDocument()
        expect(screen.getByText('+15% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+8% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+22% this quarter')).toBeInTheDocument()
        expect(screen.getByText('+12% this quarter')).toBeInTheDocument()
      })
    })
  })

  describe('User Interaction & Accessibility', () => {
    it('supports keyboard navigation for interactive elements', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        const findButton = screen.getByTestId('find-colleagues-header')

        findButton.focus()
        expect(document.activeElement).toBe(findButton)
      })
    })

    it('has proper ARIA labels and accessibility features', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        const buttons = screen.getAllByRole('button')
        expect(buttons.length).toBeGreaterThan(0)

        buttons.forEach(button => {
          expect(button).toBeInTheDocument()
        })
      })
    })
  })

  describe('Responsive Design Elements', () => {
    it('renders properly with responsive layout', async () => {
      await withDatabaseIsolation(async testId => {
        const testData = await createTestData(testId)
        await testData.createEmployeeWithCompany()

        render(<NetworkingPage />)

        // Check that main container exists
        const container = screen.getByText('Networking').closest('div')
        expect(container).toBeInTheDocument()
      })
    })
  })
})
