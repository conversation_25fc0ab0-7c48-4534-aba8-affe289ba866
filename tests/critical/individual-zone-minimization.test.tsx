import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { expect, describe, it, vi, beforeEach } from 'vitest'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'

// Mock Next.js modules
vi.mock('next/navigation', () => ({
  usePathname: () => '/employee/dashboard',
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  }),
}))

vi.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'EMPLOYEE',
        companyId: 'test-company-123',
      },
    },
    status: 'authenticated',
  }),
}))

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    },
  }
})()

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock fetch for AI endpoints
global.fetch = vi.fn()

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <div>{children}</div>
}

describe('Individual Zone Minimization Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.clear()

    // Mock successful fetch responses
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        recommendations: ['Test recommendation'],
      }),
    })
  })

  describe('Personal Growth Zone', () => {
    it('should be expanded by default', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Check if Personal Growth zone is expanded
      const personalZone = screen.getByTestId('personal-growth-zone')
      expect(personalZone).toBeInTheDocument()

      // Check if Personal Growth items are visible (Focus should be visible when expanded)
      expect(screen.getByTestId('nav-focus')).toBeVisible()
    })

    it('should collapse when zone toggle is clicked', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Find and click the Personal Growth zone toggle
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      expect(personalToggle).toBeInTheDocument()

      await user.click(personalToggle)

      // Verify the zone collapsed
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })

      // Verify localStorage was updated
      expect(localStorageMock.getItem('sidebar-zone-personalgrowth-collapsed')).toBe('true')
    })

    it('should expand when zone toggle is clicked again', async () => {
      const user = userEvent.setup()

      // Start with zone collapsed
      localStorageMock.setItem('sidebar-zone-personalgrowth-collapsed', 'true')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Verify zone starts collapsed
      expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()

      // Click toggle to expand
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      // Verify zone expanded
      await waitFor(() => {
        expect(screen.getByTestId('nav-focus')).toBeVisible()
      })

      // Verify localStorage was updated
      expect(localStorageMock.getItem('sidebar-zone-personalgrowth-collapsed')).toBe('false')
    })
  })

  describe('Team Connection Zone', () => {
    it('should be expanded by default', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Check if Team Connection zone is expanded
      const teamZone = screen.getByTestId('team-connection-zone')
      expect(teamZone).toBeInTheDocument()

      // Check if Team items are visible
      expect(screen.getByTestId('nav-team')).toBeVisible()
    })

    it('should collapse when zone toggle is clicked', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Find and click the Team Connection zone toggle
      const teamToggle = screen.getByTestId('zone-toggle-teamconnection')
      expect(teamToggle).toBeInTheDocument()

      await user.click(teamToggle)

      // Verify the zone collapsed
      await waitFor(() => {
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
      })

      // Verify localStorage was updated
      expect(localStorageMock.getItem('sidebar-zone-teamconnection-collapsed')).toBe('true')
    })
  })

  describe('Organization Impact Zone', () => {
    it('should be expanded by default', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Check if Organization Impact zone is expanded
      const orgZone = screen.getByTestId('organization-impact-zone')
      expect(orgZone).toBeInTheDocument()

      // Check if Organization items are visible
      expect(screen.getByTestId('nav-vision')).toBeVisible()
    })

    it('should collapse when zone toggle is clicked', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Find and click the Organization Impact zone toggle
      const orgToggle = screen.getByTestId('zone-toggle-organizationimpact')
      expect(orgToggle).toBeInTheDocument()

      await user.click(orgToggle)

      // Verify the zone collapsed
      await waitFor(() => {
        expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument()
      })

      // Verify localStorage was updated
      expect(localStorageMock.getItem('sidebar-zone-organizationimpact-collapsed')).toBe('true')
    })
  })

  describe('Zone Toggle Icons', () => {
    it('should show correct chevron icons based on zone state', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Check initial state - should show ChevronUp (expanded)
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')

      // Click to collapse
      await user.click(personalToggle)

      // Should now show ChevronDown (collapsed)
      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })
    })
  })

  describe('Mixed Zone States', () => {
    it('should handle different zones being in different states', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Collapse Personal zone
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })

      // Verify Team and Organization zones are still expanded
      expect(screen.getByTestId('nav-team')).toBeVisible()
      expect(screen.getByTestId('nav-connect')).toBeVisible()

      // Collapse Team zone
      const teamToggle = screen.getByTestId('zone-toggle-teamconnection')
      await user.click(teamToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-team')).not.toBeInTheDocument()
      })

      // Verify Organization zone is still expanded
      expect(screen.getByTestId('nav-vision')).toBeVisible()
    })
  })

  describe('localStorage Persistence', () => {
    it('should restore zone states from localStorage on mount', async () => {
      // Set initial localStorage state
      localStorageMock.setItem('sidebar-zone-personalgrowth-collapsed', 'true')
      localStorageMock.setItem('sidebar-zone-teamconnection-collapsed', 'false')
      localStorageMock.setItem('sidebar-zone-organizationimpact-collapsed', 'true')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Verify states match localStorage
      expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument() // Personal collapsed
      expect(screen.getByTestId('nav-team')).toBeVisible() // Team expanded
      expect(screen.queryByTestId('nav-vision')).not.toBeInTheDocument() // Organization collapsed
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels for zone toggles', async () => {
      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      // Check ARIA labels
      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')
      expect(personalToggle).toHaveAttribute('aria-label')

      const teamToggle = screen.getByTestId('zone-toggle-teamconnection')
      expect(teamToggle).toHaveAttribute('aria-label')

      const orgToggle = screen.getByTestId('zone-toggle-organizationimpact')
      expect(orgToggle).toHaveAttribute('aria-label')
    })
  })

  describe('Performance', () => {
    it('should handle zone toggle interactions within performance targets', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('navigation-context')).toBeInTheDocument()
      })

      const personalToggle = screen.getByTestId('zone-toggle-personalgrowth')

      // Measure interaction time
      const startTime = performance.now()
      await user.click(personalToggle)

      await waitFor(() => {
        expect(screen.queryByTestId('nav-focus')).not.toBeInTheDocument()
      })

      const endTime = performance.now()
      const interactionTime = endTime - startTime

      // Should complete within 300ms
      expect(interactionTime).toBeLessThan(300)
    })
  })
})
