/**
 * Analytics Service Unit Tests
 *
 * Unit tests for the analytics service implementation
 * Focus: Basic functionality validation without complex setup
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { AnalyticsService } from '@/services/analytics-service'

// Mock the analytics client to avoid database dependencies
vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn(() => ({
    eventStream: {
      create: vi.fn(),
      createMany: vi.fn(),
      findMany: vi.fn(),
    },
    performanceMetrics: {
      create: vi.fn(),
      findMany: vi.fn(),
    },
    sessionData: {
      create: vi.fn(),
      update: vi.fn(),
      findUnique: vi.fn(),
    },
    mlFeatureVectors: {
      create: vi.fn(),
      findMany: vi.fn(),
    },
    $disconnect: vi.fn(),
  })),
}))

// Mock Redis
vi.mock('ioredis', () => ({
  Redis: vi.fn().mockImplementation(() => ({
    lpush: vi.fn().mockResolvedValue(1),
    ltrim: vi.fn().mockResolvedValue('OK'),
    expire: vi.fn().mockResolvedValue(1),
    disconnect: vi.fn(),
  })),
}))

// Mock Sentry
vi.mock('@sentry/nextjs', () => ({
  captureException: vi.fn(),
}))

describe('Analytics Service Unit Tests', () => {
  // Import after mocks are set up
  let AnalyticsService: any
  let analyticsService: any

  beforeEach(async () => {
    // Clear all mocks
    vi.clearAllMocks()

    // Import the service after mocks are set up
    const module = await import('@/services/analytics-service')
    AnalyticsService = module.AnalyticsService
    analyticsService = new AnalyticsService()
  })

  afterEach(async () => {
    if (analyticsService) {
      await analyticsService.disconnect()
    }
  })

  describe('Event Tracking', () => {
    it('should validate required fields for analytics events', async () => {
      const invalidEvent = {
        userId: '',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'test',
        action: 'test',
        target: 'test',
      }

      await expect(analyticsService.trackEvent(invalidEvent)).rejects.toThrow(
        'Missing required field: userId'
      )
    })

    it('should track a valid analytics event', async () => {
      const validEvent = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'page_visit',
        action: 'navigate',
        target: '/dashboard',
        context: { page: 'dashboard' },
        payload: { timestamp: Date.now() },
      }

      await expect(analyticsService.trackEvent(validEvent)).resolves.not.toThrow()
    })

    it('should identify critical events correctly', async () => {
      const criticalEvent = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'error',
        action: 'api_error',
        target: '/api/test',
        context: {},
        payload: {},
      }

      // This should process in real-time (not batched)
      await analyticsService.trackEvent(criticalEvent)

      // Verify it was processed immediately
      expect(analyticsService.client.eventStream.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          eventType: 'error',
          action: 'api_error',
        }),
      })
    })

    it('should batch non-critical events', async () => {
      const regularEvent = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'feature_usage',
        action: 'click',
        target: 'button',
        context: {},
        payload: {},
      }

      await analyticsService.trackEvent(regularEvent)

      // Should be added to batch, not processed immediately
      expect(analyticsService.batchQueue.length).toBe(1)
    })
  })

  describe('Performance Tracking', () => {
    it('should track performance metrics', async () => {
      const performanceMetric = {
        endpoint: '/api/test',
        method: 'GET',
        responseTime: 150,
        statusCode: 200,
        cacheHit: false,
      }

      await analyticsService.trackPerformanceMetric(performanceMetric)

      expect(analyticsService.client.performanceMetrics.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          endpoint: '/api/test',
          method: 'GET',
          responseTime: 150,
        }),
      })
    })

    it('should handle performance tracking errors gracefully', async () => {
      // Mock a database error
      analyticsService.client.performanceMetrics.create.mockRejectedValueOnce(
        new Error('Database connection failed')
      )

      const performanceMetric = {
        endpoint: '/api/test',
        method: 'GET',
        responseTime: 150,
        statusCode: 200,
        cacheHit: false,
      }

      // Should not throw, but handle gracefully
      await expect(
        analyticsService.trackPerformanceMetric(performanceMetric)
      ).resolves.not.toThrow()
    })
  })

  describe('Session Management', () => {
    it('should update user sessions', async () => {
      const sessionData = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        deviceInfo: { browser: 'Chrome', os: 'MacOS' },
        pageCount: 5,
        eventCount: 20,
        engagementScore: 0.8,
      }

      await analyticsService.updateSession(sessionData)

      expect(analyticsService.client.sessionData.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'test-user',
          companyId: 'test-company',
          isActive: true,
        }),
      })
    })

    it('should end user sessions with duration calculation', async () => {
      const sessionId = 'test-session'

      await analyticsService.endSession(sessionId)

      expect(analyticsService.client.sessionData.findUnique).toHaveBeenCalledWith({
        where: { sessionId },
      })

      expect(analyticsService.client.sessionData.update).toHaveBeenCalledWith({
        where: { sessionId },
        data: expect.objectContaining({
          endTime: expect.any(Date),
          duration: expect.any(Number),
          isActive: false,
        }),
      })
    })
  })

  describe('ML Feature Generation', () => {
    it('should generate ML feature vectors', async () => {
      const featureData = {
        userId: 'test-user',
        companyId: 'test-company',
        featureSet: 'user_behavior',
        features: {
          pageViewsPerSession: 5.2,
          avgSessionDuration: 300,
          featureUsageRate: 0.7,
        },
        labels: {
          userEngagement: 'high',
          churnRisk: 'low',
        },
      }

      await analyticsService.generateFeatureVector(featureData)

      expect(analyticsService.client.mlFeatureVectors.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'test-user',
          companyId: 'test-company',
          featureSet: 'user_behavior',
          features: expect.objectContaining({
            pageViewsPerSession: 5.2,
            avgSessionDuration: 300,
          }),
        }),
      })
    })
  })

  describe('Batch Processing', () => {
    it('should process events in batches', async () => {
      const events = Array.from({ length: 5 }, (_, i) => ({
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'feature_usage',
        action: 'click',
        target: `button-${i}`,
        context: {},
        payload: {},
      }))

      await analyticsService.trackEventsBatch(events)

      expect(analyticsService.client.eventStream.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            userId: 'test-user',
            eventType: 'feature_usage',
          }),
        ]),
      })
    })

    it('should handle large batches efficiently', async () => {
      const largeEventSet = Array.from({ length: 2500 }, (_, i) => ({
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'feature_usage',
        action: 'click',
        target: `button-${i}`,
        context: {},
        payload: {},
      }))

      await analyticsService.trackEventsBatch(largeEventSet)

      // Should call createMany multiple times for large batches
      expect(analyticsService.client.eventStream.createMany).toHaveBeenCalledTimes(3) // 2500 / 1000 = 3 batches
    })
  })

  describe('Performance Analysis', () => {
    it('should analyze performance metrics', async () => {
      const mockMetrics = [
        { endpoint: '/api/test', responseTime: 100, statusCode: 200, cacheHit: true },
        { endpoint: '/api/test', responseTime: 150, statusCode: 200, cacheHit: false },
        { endpoint: '/api/test', responseTime: 200, statusCode: 500, cacheHit: false },
      ]

      analyticsService.client.performanceMetrics.findMany.mockResolvedValue(mockMetrics)

      const insights = await analyticsService.getPerformanceInsights('test-company', 'hour')

      expect(insights).toHaveProperty('/api/test')
      expect(insights['/api/test']).toMatchObject({
        avgResponseTime: 150, // (100 + 150 + 200) / 3
        requestCount: 3,
        errorRate: expect.any(Number),
        cacheHitRate: expect.any(Number),
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      analyticsService.client.eventStream.create.mockRejectedValueOnce(
        new Error('Connection timeout')
      )

      const event = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'error',
        action: 'test',
        target: 'test',
        context: {},
        payload: {},
      }

      await expect(analyticsService.trackEvent(event)).rejects.toThrow('Connection timeout')
    })
  })
})

describe('Analytics Service Performance Tests', () => {
  let analyticsService: any

  beforeEach(async () => {
    const module = await import('@/services/analytics-service')
    analyticsService = new module.AnalyticsService()
  })

  afterEach(async () => {
    if (analyticsService) {
      await analyticsService.disconnect()
    }
  })

  it('should track events within 25ms target', async () => {
    const event = {
      userId: 'test-user',
      companyId: 'test-company',
      sessionId: 'test-session',
      eventType: 'page_visit',
      action: 'navigate',
      target: '/dashboard',
      context: {},
      payload: {},
    }

    const startTime = Date.now()
    await analyticsService.trackEvent(event)
    const duration = Date.now() - startTime

    // Should complete within 25ms target (allowing some overhead for mocking)
    expect(duration).toBeLessThan(100) // Generous for unit test environment
  })

  it('should handle high-volume batch processing', async () => {
    const events = Array.from({ length: 1000 }, (_, i) => ({
      userId: 'test-user',
      companyId: 'test-company',
      sessionId: 'test-session',
      eventType: 'feature_usage',
      action: 'click',
      target: `button-${i}`,
      context: {},
      payload: {},
    }))

    const startTime = Date.now()
    await analyticsService.trackEventsBatch(events)
    const duration = Date.now() - startTime

    // Should process 1000 events quickly
    expect(duration).toBeLessThan(1000) // 1 second for 1000 events
  })
})
