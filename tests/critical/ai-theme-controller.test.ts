/**
 * 🧪 AI-First Theme Controller Tests
 *
 * Behavior-Driven Development tests for the sophisticated theme controller.
 * Tests written BEFORE implementation to drive API design.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  testPrisma,
  testRedis,
  TEST_USERS,
  cleanTestData,
  seedTestData,
} from '../setup/test-database'

// Import the controller we're about to create
import { AIThemeController } from '@/controllers/ai-theme-controller'
import { AIThemeService } from '@/services/ai-theme-service'

// Mock request/response objects
const mockRequest = (data: any = {}) => ({
  json: vi.fn().mockResolvedValue(data),
  body: data,
  method: 'GET',
  url: '/api/theme/preferences',
  headers: { 'content-type': 'application/json' },
  user: { id: TEST_USERS.employee.id, email: TEST_USERS.employee.email },
})

const mockResponse = () => {
  const res: any = {}
  res.status = vi.fn().mockReturnValue(res)
  res.json = vi.fn().mockReturnValue(res)
  res.send = vi.fn().mockReturnValue(res)
  return res
}

describe('🤖 AI-First Theme Controller', () => {
  let themeController: AIThemeController
  let themeService: AIThemeService

  beforeEach(async () => {
    // Setup fresh data for each test
    await cleanTestData()
    await seedTestData()

    themeService = new AIThemeService(testPrisma, testRedis)
    themeController = new AIThemeController(themeService)
  })

  afterEach(async () => {
    // Clean up after each test
    await cleanTestData()
  })

  describe('🎨 Theme Preferences API', () => {
    it('should GET user theme preferences with AI context', async () => {
      // Given: A user requesting their theme preferences
      const req = mockRequest()
      const res = mockResponse()

      // When: Getting theme preferences
      await themeController.getThemePreferences(req, res)

      // Then: Should return comprehensive theme data
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            userId: TEST_USERS.employee.id,
            currentTheme: expect.objectContaining({
              mode: 'light',
              colorScheme: 'emynent-light',
            }),
            aiContext: expect.objectContaining({
              preferredColorSchemes: expect.any(Array),
              satisfactionScores: expect.any(Object),
            }),
            customizations: expect.objectContaining({
              colors: expect.any(Array),
              gradients: expect.any(Object),
            }),
          }),
          metadata: expect.objectContaining({
            responseTime: expect.any(Number),
            cached: expect.any(Boolean),
            version: '1.0.0',
          }),
        })
      )
    })

    it('should PUT theme preferences with validation', async () => {
      // Given: Valid theme preference updates
      const updates = {
        mode: 'dark',
        colorScheme: 'nightowl',
        customColors: [{ id: 'accent-purple', name: 'Accent Purple', value: '#8B5CF6' }],
      }
      const req = mockRequest(updates)
      const res = mockResponse()

      // When: Updating theme preferences
      await themeController.updateThemePreferences(req, res)

      // Then: Should update successfully
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            currentTheme: expect.objectContaining({
              mode: 'dark',
              colorScheme: 'nightowl',
            }),
          }),
          metadata: expect.objectContaining({
            responseTime: expect.any(Number),
            aiLearningApplied: true,
          }),
        })
      )
    })

    it('should validate request data and return 400 for invalid input', async () => {
      // Given: Invalid theme data
      const invalidUpdates = {
        mode: 'invalid-mode',
        colorScheme: 'non-existent-scheme',
        customCSS: '<script>alert("xss")</script>',
      }
      const req = mockRequest(invalidUpdates)
      const res = mockResponse()

      // When: Attempting to update with invalid data
      await themeController.updateThemePreferences(req, res)

      // Then: Should return validation error
      expect(res.status).toHaveBeenCalledWith(400)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            code: 'VALIDATION_ERROR',
            message: expect.stringContaining('Invalid'),
            details: expect.any(Array),
          }),
        })
      )
    })
  })

  describe('🧠 AI Recommendations API', () => {
    it('should GET personalized AI recommendations', async () => {
      // Given: A user requesting AI recommendations
      const req = mockRequest()
      const res = mockResponse()

      // When: Getting AI recommendations
      await themeController.getAIRecommendations(req, res)

      // Then: Should return personalized suggestions
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            userId: TEST_USERS.employee.id,
            recommendations: expect.arrayContaining([
              expect.objectContaining({
                type: expect.any(String),
                suggestion: expect.any(String),
                confidence: expect.any(Number),
                reasoning: expect.any(String),
              }),
            ]),
            insights: expect.objectContaining({
              usagePatterns: expect.any(Object),
              satisfactionMetrics: expect.any(Object),
            }),
          }),
          metadata: expect.objectContaining({
            generatedAt: expect.any(String),
            algorithm: 'ai_personalization_v1',
          }),
        })
      )
    })

    it('should GET contextual recommendations with query parameters', async () => {
      // Given: A request with contextual parameters
      const req = {
        ...mockRequest(),
        query: {
          timeOfDay: '18',
          environment: 'office',
          activity: 'focused_work',
        },
      }
      const res = mockResponse()

      // When: Getting contextual recommendations
      await themeController.getContextualRecommendations(req, res)

      // Then: Should return context-aware suggestions
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            contextFactors: expect.arrayContaining([
              expect.objectContaining({
                factor: 'time_of_day',
                influence: expect.any(Number),
              }),
            ]),
          }),
        })
      )
    })

    it('should POST feedback to improve AI recommendations', async () => {
      // Given: User feedback on a recommendation
      const feedback = {
        recommendationId: 'rec-123',
        accepted: true,
        satisfaction: 8,
        feedback: 'Great suggestion for evening work!',
      }
      const req = mockRequest(feedback)
      const res = mockResponse()

      // When: Submitting feedback
      await themeController.submitRecommendationFeedback(req, res)

      // Then: Should process feedback successfully
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            feedbackProcessed: true,
            aiModelUpdated: true,
          }),
          metadata: expect.objectContaining({
            learningImpact: expect.any(Number),
          }),
        })
      )
    })
  })

  describe('📊 Analytics & Insights API', () => {
    it('should GET comprehensive theme analytics', async () => {
      // Given: A user requesting their analytics
      const req = {
        ...mockRequest(),
        query: { period: '30d', includeInsights: 'true' },
      }
      const res = mockResponse()

      // When: Getting analytics
      await themeController.getThemeAnalytics(req, res)

      // Then: Should return detailed analytics
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            userId: TEST_USERS.employee.id,
            interactions: expect.any(Array),
            patterns: expect.objectContaining({
              switchFrequency: expect.any(Number),
              mostUsedSchemes: expect.any(Array),
            }),
            satisfaction: expect.any(Object),
            aiInsights: expect.objectContaining({
              productivityCorrelation: expect.any(Number),
              optimalThemes: expect.any(Array),
              improvementSuggestions: expect.any(Array),
            }),
          }),
        })
      )
    })

    it('should POST theme interaction tracking', async () => {
      // Given: A theme interaction to track
      const interaction = {
        action: 'theme_switch',
        from: 'light/emynent-light',
        to: 'dark/emynent-dark',
        context: {
          timeOfDay: 18,
          deviceType: 'desktop',
        },
      }
      const req = mockRequest(interaction)
      const res = mockResponse()

      // When: Tracking the interaction
      await themeController.trackThemeInteraction(req, res)

      // Then: Should track successfully
      expect(res.status).toHaveBeenCalledWith(200)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            tracked: true,
            interactionId: expect.any(String),
          }),
        })
      )
    })
  })

  describe('🚀 Performance & Caching', () => {
    it('should implement response caching for expensive operations', async () => {
      // Given: Clear cache to ensure clean state
      await testRedis.flushdb()

      // Multiple requests for the same data
      const req = mockRequest()
      const res1 = mockResponse()
      const res2 = mockResponse()

      // When: Making multiple requests
      await themeController.getAIRecommendations(req, res1)
      await themeController.getAIRecommendations(req, res2)

      // Then: First request should be uncached, second should be cached
      expect(res1.json).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            cached: false,
          }),
        })
      )

      expect(res2.json).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            cached: true,
          }),
        })
      )
    })

    it('should meet API response time requirements', async () => {
      // Given: Performance requirements
      const req = mockRequest()
      const res = mockResponse()

      // When: Making API calls
      const start = performance.now()
      await themeController.getThemePreferences(req, res)
      const responseTime = performance.now() - start

      // Then: Should meet performance targets
      expect(responseTime).toBeLessThan(100) // <100ms target
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            responseTime: expect.any(Number),
          }),
        })
      )
    })
  })

  describe('🛡️ Security & Validation', () => {
    it('should validate user authentication', async () => {
      // Given: A request without valid user
      const req = { ...mockRequest(), user: null }
      const res = mockResponse()

      // When: Attempting to access protected endpoint
      await themeController.getThemePreferences(req, res)

      // Then: Should return authentication error
      expect(res.status).toHaveBeenCalledWith(401)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            code: 'AUTHENTICATION_REQUIRED',
            message: 'Valid authentication required',
          }),
        })
      )
    })

    it('should implement rate limiting for AI endpoints', async () => {
      // Given: Multiple rapid requests from same user
      const req = mockRequest()
      const responses = Array(20)
        .fill(null)
        .map(() => mockResponse())

      // When: Making many rapid requests
      const promises = responses.map(res => themeController.getAIRecommendations(req, res))
      await Promise.all(promises)

      // Then: Should implement rate limiting
      const rateLimitedResponses = responses.filter(res =>
        res.status.mock.calls.some(call => call[0] === 429)
      )
      expect(rateLimitedResponses.length).toBeGreaterThan(0)
    })

    it('should sanitize and validate all input data', async () => {
      // Given: Potentially malicious input
      const maliciousInput = {
        mode: 'dark',
        customColors: [
          {
            name: '<script>alert("xss")</script>',
            value: 'javascript:alert("xss")',
            description: '${process.env.SECRET}',
          },
        ],
      }
      const req = mockRequest(maliciousInput)
      const res = mockResponse()

      // When: Processing the input
      await themeController.updateThemePreferences(req, res)

      // Then: Should sanitize and reject unsafe content
      expect(res.status).toHaveBeenCalledWith(400)
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            code: 'SECURITY_VIOLATION',
            message: expect.stringContaining('unsafe'),
          }),
        })
      )
    })
  })

  describe('🔄 Real-time Features', () => {
    it('should handle WebSocket connections for real-time updates', async () => {
      // Given: A WebSocket connection
      const mockWs = {
        send: vi.fn(),
        close: vi.fn(),
        readyState: 1,
      }

      // When: Registering WebSocket and updating theme
      themeController.registerWebSocket(TEST_USERS.employee.id, mockWs)

      const req = mockRequest({ mode: 'dark' })
      const res = mockResponse()
      await themeController.updateThemePreferences(req, res)

      // Then: Should broadcast update via WebSocket
      expect(mockWs.send).toHaveBeenCalledWith(expect.stringContaining('"type":"theme_updated"'))
      expect(mockWs.send).toHaveBeenCalledWith(expect.stringContaining('"mode":"dark"'))
    })
  })

  describe('🌐 API Versioning & Backwards Compatibility', () => {
    it('should support API versioning in headers', async () => {
      // Given: Request with API version header
      const req = {
        ...mockRequest(),
        headers: {
          'api-version': '2.0',
          'content-type': 'application/json',
        },
      }
      const res = mockResponse()

      // When: Processing versioned request
      await themeController.getThemePreferences(req, res)

      // Then: Should handle version appropriately
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            apiVersion: '2.0',
            compatibility: 'full',
          }),
        })
      )
    })
  })
})
