import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useEnhancedTheme, EnhancedThemeProvider } from '@/lib/ThemeContext'
import { ThemeProvider } from '@/app/providers/theme-provider'
import { SessionProvider } from 'next-auth/react'

// Test component that uses the enhanced theme hook
function TestComponent() {
  const enhancedTheme = useEnhancedTheme()

  return (
    <div data-testid='test-component'>
      <span data-testid='color-scheme'>{enhancedTheme.colorScheme}</span>
      <span data-testid='gradient-enabled'>{enhancedTheme.gradientEnabled.toString()}</span>
      <span data-testid='system-theme'>{enhancedTheme.systemTheme}</span>
    </div>
  )
}

// Test wrapper with proper provider hierarchy
function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <ThemeProvider
        attribute='class'
        defaultTheme='system'
        enableSystem
        disableTransitionOnChange
        storageKey='theme'
        forcedTheme={undefined}
      >
        <EnhancedThemeProvider>{children}</EnhancedThemeProvider>
      </ThemeProvider>
    </SessionProvider>
  )
}

describe('EnhancedThemeProvider Integration', () => {
  beforeEach(() => {
    // Clear any existing theme data
    localStorage.clear()
    // Reset document classes
    document.documentElement.className = ''
  })

  describe('Provider Chain Integration', () => {
    it('should provide enhanced theme context without errors', () => {
      expect(() => {
        render(
          <TestWrapper>
            <TestComponent />
          </TestWrapper>
        )
      }).not.toThrow()
    })

    it('should render component with default enhanced theme values', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      )

      expect(screen.getByTestId('test-component')).toBeInTheDocument()
      expect(screen.getByTestId('color-scheme')).toHaveTextContent('emynent-light')
      expect(screen.getByTestId('gradient-enabled')).toHaveTextContent('false')
      expect(screen.getByTestId('system-theme')).toHaveTextContent('system')
    })

    it('should throw error when useEnhancedTheme is used without provider', () => {
      // Capture console.error to avoid test noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        render(<TestComponent />)
      }).toThrow('useEnhancedTheme must be used within an EnhancedThemeProvider')

      consoleSpy.mockRestore()
    })
  })

  describe('Theme State Management', () => {
    it('should allow color scheme changes', async () => {
      const user = userEvent.setup()

      function InteractiveTestComponent() {
        const enhancedTheme = useEnhancedTheme()

        return (
          <div>
            <span data-testid='current-scheme'>{enhancedTheme.colorScheme}</span>
            <button
              data-testid='change-scheme'
              onClick={() => enhancedTheme.setColorScheme('nightowl')}
            >
              Change to Night Owl
            </button>
          </div>
        )
      }

      render(
        <TestWrapper>
          <InteractiveTestComponent />
        </TestWrapper>
      )

      expect(screen.getByTestId('current-scheme')).toHaveTextContent('emynent-light')

      await user.click(screen.getByTestId('change-scheme'))

      expect(screen.getByTestId('current-scheme')).toHaveTextContent('nightowl')
    })

    it('should manage gradient settings', async () => {
      const user = userEvent.setup()

      function GradientTestComponent() {
        const enhancedTheme = useEnhancedTheme()

        return (
          <div>
            <span data-testid='gradient-enabled'>{enhancedTheme.gradientEnabled.toString()}</span>
            <button
              data-testid='toggle-gradient'
              onClick={() => enhancedTheme.setGradientEnabled(!enhancedTheme.gradientEnabled)}
            >
              Toggle Gradient
            </button>
          </div>
        )
      }

      render(
        <TestWrapper>
          <GradientTestComponent />
        </TestWrapper>
      )

      expect(screen.getByTestId('gradient-enabled')).toHaveTextContent('false')

      await user.click(screen.getByTestId('toggle-gradient'))

      expect(screen.getByTestId('gradient-enabled')).toHaveTextContent('true')
    })
  })

  describe('Custom Colors Management', () => {
    it('should manage custom colors array', async () => {
      const user = userEvent.setup()

      function CustomColorsTestComponent() {
        const enhancedTheme = useEnhancedTheme()

        return (
          <div>
            <span data-testid='colors-count'>{enhancedTheme.customColors.length}</span>
            <button
              data-testid='add-color'
              onClick={() =>
                enhancedTheme.addCustomColor({
                  id: 'test-color',
                  name: 'Test Color',
                  value: '#ff0000',
                })
              }
            >
              Add Color
            </button>
          </div>
        )
      }

      render(
        <TestWrapper>
          <CustomColorsTestComponent />
        </TestWrapper>
      )

      const initialCount = parseInt(screen.getByTestId('colors-count').textContent || '0')

      await user.click(screen.getByTestId('add-color'))

      expect(screen.getByTestId('colors-count')).toHaveTextContent((initialCount + 1).toString())
    })
  })

  describe('Theme Persistence', () => {
    it('should export and import theme data', () => {
      function ThemePersistenceTestComponent() {
        const enhancedTheme = useEnhancedTheme()

        return (
          <div>
            <button
              data-testid='export-theme'
              onClick={() => {
                const exported = enhancedTheme.exportTheme()
                // Store in a data attribute for testing
                document.body.setAttribute('data-exported-theme', exported)
              }}
            >
              Export Theme
            </button>
          </div>
        )
      }

      render(
        <TestWrapper>
          <ThemePersistenceTestComponent />
        </TestWrapper>
      )

      userEvent.click(screen.getByTestId('export-theme'))

      const exportedTheme = document.body.getAttribute('data-exported-theme')
      expect(exportedTheme).toBeTruthy()
      expect(() => JSON.parse(exportedTheme!)).not.toThrow()
    })
  })

  describe('Error Boundaries', () => {
    it('should handle theme context errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

      function ErrorTestComponent() {
        try {
          const enhancedTheme = useEnhancedTheme()
          return <div data-testid='success'>Theme loaded</div>
        } catch (error) {
          return <div data-testid='error'>Theme error: {(error as Error).message}</div>
        }
      }

      render(<ErrorTestComponent />)

      expect(screen.getByTestId('error')).toHaveTextContent(
        'useEnhancedTheme must be used within an EnhancedThemeProvider'
      )

      consoleSpy.mockRestore()
    })
  })
})

describe('Component Integration Tests', () => {
  describe('Page Components', () => {
    it('should render homepage without enhanced theme errors', () => {
      // Mock the page component structure
      function MockHomePage() {
        const enhancedTheme = useEnhancedTheme()
        return (
          <div data-testid='homepage'>
            <span data-testid='theme-scheme'>{enhancedTheme.colorScheme}</span>
          </div>
        )
      }

      expect(() => {
        render(
          <TestWrapper>
            <MockHomePage />
          </TestWrapper>
        )
      }).not.toThrow()

      expect(screen.getByTestId('homepage')).toBeInTheDocument()
      expect(screen.getByTestId('theme-scheme')).toHaveTextContent('emynent-light')
    })
  })

  describe('Navigation Components', () => {
    it('should render navbar without enhanced theme errors', () => {
      function MockNavbar() {
        const enhancedTheme = useEnhancedTheme()
        return (
          <nav data-testid='navbar'>
            <span data-testid='nav-theme'>{enhancedTheme.colorScheme}</span>
          </nav>
        )
      }

      expect(() => {
        render(
          <TestWrapper>
            <MockNavbar />
          </TestWrapper>
        )
      }).not.toThrow()

      expect(screen.getByTestId('navbar')).toBeInTheDocument()
    })
  })

  describe('Settings Components', () => {
    it('should render appearance settings without enhanced theme errors', () => {
      function MockAppearanceSettings() {
        const enhancedTheme = useEnhancedTheme()
        return (
          <div data-testid='appearance-settings'>
            <span data-testid='settings-theme'>{enhancedTheme.colorScheme}</span>
            <span data-testid='gradient-state'>{enhancedTheme.gradientEnabled.toString()}</span>
          </div>
        )
      }

      expect(() => {
        render(
          <TestWrapper>
            <MockAppearanceSettings />
          </TestWrapper>
        )
      }).not.toThrow()

      expect(screen.getByTestId('appearance-settings')).toBeInTheDocument()
    })
  })
})
