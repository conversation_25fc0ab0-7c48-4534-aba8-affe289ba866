import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ContextProcessor } from '../../src/lib/intelligence/context-processor'
import { ContextAggregator } from '../../src/lib/intelligence/context-aggregator'
import { ContextAnalyzer } from '../../src/lib/intelligence/context-analyzer'
import { ContextCache } from '../../src/lib/intelligence/context-cache'

/**
 * PHASE 3 - CONTEXT PROCESSING ENGINE TESTS
 * These tests define the requirements for the Intelligence Layer's context processing
 * Following TDD Red-Green-Refactor cycle
 */

describe('🧠 Phase 3: Context Processing Engine', () => {
  let contextProcessor: ContextProcessor
  let contextAggregator: ContextAggregator
  let contextAnalyzer: ContextAnalyzer
  let contextCache: ContextCache

  beforeEach(() => {
    // Reset all mocks and create fresh instances
    vi.clearAllMocks()
    contextProcessor = new ContextProcessor()
    contextAggregator = new ContextAggregator()
    contextAnalyzer = new ContextAnalyzer()
    contextCache = new ContextCache()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Context Aggregation', () => {
    it('should aggregate user context from multiple sources', async () => {
      // Test that context aggregator can collect data from:
      // - User profile data
      // - Recent activity logs
      // - Behavioral patterns
      // - Performance reviews
      // - Skill assessments

      const userId = 'test-user-123'
      const companyId = 'test-company-456'

      const expectedContext = {
        userId,
        companyId,
        role: 'EMPLOYEE',
        preferences: {
          theme: 'dark',
          language: 'en',
          notifications: true,
        },
        recentActions: [
          {
            action: 'page_visit',
            timestamp: expect.any(Date),
            entityType: 'dashboard',
            entityId: 'main',
          },
        ],
        skillGaps: ['leadership', 'public-speaking'],
        performanceReview: 'Excellent technical skills, needs leadership development',
        historicalData: {
          avgSessionTime: 1800, // 30 minutes
          mostUsedFeatures: ['dashboard', 'settings', 'reports'],
          loginFrequency: 'daily',
        },
      }

      const result = await contextAggregator.aggregateUserContext(userId, companyId)

      expect(result).toEqual(expectedContext)
      expect(result.userId).toBe(userId)
      expect(result.companyId).toBe(companyId)
      expect(result.recentActions).toBeInstanceOf(Array)
      expect(result.skillGaps).toBeInstanceOf(Array)
    })

    it('should handle missing or incomplete user data gracefully', async () => {
      const userId = 'incomplete-user-789'
      const companyId = 'test-company-456'

      const result = await contextAggregator.aggregateUserContext(userId, companyId)

      // Should return partial context with defaults
      expect(result.userId).toBe(userId)
      expect(result.companyId).toBe(companyId)
      expect(result.preferences).toEqual({})
      expect(result.recentActions).toEqual([])
      expect(result.skillGaps).toEqual([])
    })

    it('should aggregate context data within performance targets', async () => {
      const startTime = Date.now()

      await contextAggregator.aggregateUserContext('test-user', 'test-company')

      const endTime = Date.now()
      const duration = endTime - startTime

      // Context aggregation should be < 200ms
      expect(duration).toBeLessThan(200)
    })
  })

  describe('Context Processing', () => {
    it('should process context data for AI consumption', async () => {
      const rawContext = {
        userId: 'test-user-123',
        companyId: 'test-company-456',
        role: 'EMPLOYEE',
        preferences: { theme: 'dark' },
        recentActions: [{ action: 'page_visit', timestamp: new Date(), entityType: 'dashboard' }],
        skillGaps: ['leadership'],
        performanceReview: 'Good performance, needs leadership skills',
      }

      const processedContext = await contextProcessor.processContextForAI(rawContext)

      expect(processedContext).toHaveProperty('userId')
      expect(processedContext).toHaveProperty('contextVector')
      expect(processedContext).toHaveProperty('insights')
      expect(processedContext).toHaveProperty('recommendations')
      expect(processedContext.contextVector).toBeInstanceOf(Array)
      expect(processedContext.insights).toHaveProperty('skillGapAnalysis')
      expect(processedContext.insights).toHaveProperty('behaviorPatterns')
    })

    it('should generate context insights and patterns', async () => {
      const context = {
        userId: 'test-user-123',
        recentActions: [
          { action: 'page_visit', entityType: 'dashboard', timestamp: new Date() },
          { action: 'feature_use', entityType: 'reports', timestamp: new Date() },
          { action: 'page_visit', entityType: 'settings', timestamp: new Date() },
        ],
        historicalData: {
          mostUsedFeatures: ['dashboard', 'reports'],
          avgSessionTime: 1200,
        },
      }

      const insights = await contextAnalyzer.generateInsights(context)

      expect(insights).toHaveProperty('usagePatterns')
      expect(insights).toHaveProperty('engagementScore')
      expect(insights).toHaveProperty('preferredFeatures')
      expect(insights.usagePatterns).toContain('dashboard-focused')
      expect(insights.engagementScore).toBeGreaterThan(0)
      expect(insights.engagementScore).toBeLessThanOrEqual(1)
    })

    it('should process context within performance targets', async () => {
      const context = { userId: 'test', companyId: 'test' }
      const startTime = Date.now()

      await contextProcessor.processContextForAI(context)

      const endTime = Date.now()
      const duration = endTime - startTime

      // Context processing should be < 300ms
      expect(duration).toBeLessThan(300)
    })
  })

  describe('Context Caching', () => {
    it('should cache processed context efficiently', async () => {
      const userId = 'test-user-123'
      const processedContext = {
        userId,
        contextVector: [0.1, 0.2, 0.3],
        insights: { engagementScore: 0.8 },
        lastProcessed: new Date(),
      }

      await contextCache.cacheContext(userId, processedContext)

      const cachedContext = await contextCache.getContext(userId)

      expect(cachedContext).toEqual(processedContext)
      expect(cachedContext.userId).toBe(userId)
    })

    it('should handle cache misses gracefully', async () => {
      const nonExistentUserId = 'non-existent-user'

      const result = await contextCache.getContext(nonExistentUserId)

      expect(result).toBeNull()
    })

    it('should respect cache TTL settings', async () => {
      const userId = 'test-user-ttl'
      const context = { userId, data: 'test' }
      const ttl = 100 // 100ms for testing

      await contextCache.cacheContext(userId, context, ttl)

      // Should be available immediately
      let cached = await contextCache.getContext(userId)
      expect(cached).toEqual(context)

      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150))

      // Should be expired
      cached = await contextCache.getContext(userId)
      expect(cached).toBeNull()
    })

    it('should retrieve cached context within performance targets', async () => {
      const userId = 'test-user-perf'
      const context = { userId, data: 'performance-test' }

      await contextCache.cacheContext(userId, context)

      const startTime = Date.now()
      await contextCache.getContext(userId)
      const endTime = Date.now()

      const duration = endTime - startTime

      // Cache retrieval should be < 50ms
      expect(duration).toBeLessThan(50)
    })
  })

  describe('Real-time Context Updates', () => {
    it('should handle context updates in real-time', async () => {
      const userId = 'test-user-realtime'
      const initialContext = { userId, version: 1 }

      // Use fresh instances to avoid any state issues
      const testCache = new ContextCache()
      const testProcessor = new ContextProcessor()

      // Cache initial context
      await testCache.cacheContext(userId, initialContext)

      // Update context using the same cache instance
      testProcessor['cache'] = testCache
      await testProcessor.updateContext(userId, { version: 2 })

      // Should reflect the update
      const currentContext = await testCache.getContext(userId)
      expect(currentContext.version).toBe(2)
    })

    it('should broadcast context changes to subscribers', async () => {
      const userId = 'test-user-broadcast'
      const mockSubscriber = vi.fn()

      // Subscribe to context changes
      contextProcessor.subscribe(userId, mockSubscriber)

      // Update context
      await contextProcessor.updateContext(userId, { newData: 'test' })

      // Subscriber should be notified
      expect(mockSubscriber).toHaveBeenCalledWith({
        userId,
        type: 'context_update',
        data: { newData: 'test' },
        timestamp: expect.any(Date),
      })
    })

    it('should handle real-time updates within performance targets', async () => {
      const userId = 'test-user-realtime-perf'
      const updateData = { timestamp: new Date() }

      const startTime = Date.now()
      await contextProcessor.updateContext(userId, updateData)
      const endTime = Date.now()

      const duration = endTime - startTime

      // Real-time updates should be < 100ms
      expect(duration).toBeLessThan(100)
    })
  })

  describe('Context Data Validation', () => {
    it('should validate context data structure', async () => {
      const validContext = {
        userId: 'test-user',
        companyId: 'test-company',
        role: 'EMPLOYEE',
        preferences: {},
        recentActions: [],
        skillGaps: [],
      }

      const isValid = await contextProcessor.validateContext(validContext)
      expect(isValid).toBe(true)
    })

    it('should reject invalid context data', async () => {
      const invalidContext = {
        // Missing required fields
        preferences: {},
        recentActions: [],
      }

      const isValid = await contextProcessor.validateContext(invalidContext)
      expect(isValid).toBe(false)
    })

    it('should sanitize sensitive data before processing', async () => {
      const contextWithSensitiveData = {
        userId: 'test-user',
        companyId: 'test-company',
        role: 'EMPLOYEE',
        preferences: {},
        recentActions: [],
        skillGaps: [],
        sensitiveField: 'should-be-removed',
        personalInfo: {
          ssn: '***********',
          creditCard: '4111-1111-1111-1111',
        },
      }

      const sanitizedContext = await contextProcessor.sanitizeContext(contextWithSensitiveData)

      expect(sanitizedContext).not.toHaveProperty('sensitiveField')
      expect(sanitizedContext).not.toHaveProperty('personalInfo')
      expect(sanitizedContext.userId).toBe('test-user')
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Create a new processor instance to avoid cached results
      const testProcessor = new ContextProcessor()

      // Mock database error on the aggregator
      vi.spyOn(testProcessor['aggregator'], 'aggregateUserContext').mockRejectedValue(
        new Error('Database connection failed')
      )

      const result = await testProcessor.processUserContext('test-user-error', 'test-company')

      expect(result).toBeNull()
      // Should log error but not throw
    })

    it('should handle malformed context data', async () => {
      const malformedContext = {
        userId: null,
        companyId: undefined,
        recentActions: 'not-an-array',
      }

      // This should fail validation and return null
      const isValid = await contextProcessor.validateContext(malformedContext)
      expect(isValid).toBe(false)

      const result = await contextProcessor.processContextForAI(malformedContext)

      expect(result).toBeNull()
    })

    it('should handle cache service unavailability', async () => {
      // Mock cache error
      vi.spyOn(contextCache, 'cacheContext').mockRejectedValue(
        new Error('Cache service unavailable')
      )

      // Should not throw, should continue processing
      const context = { userId: 'test', data: 'test' }
      await expect(contextProcessor.processContextForAI(context)).resolves.not.toThrow()
    })
  })
})

/**
 * INTEGRATION TESTS
 * Test the complete context processing pipeline
 */
describe('🔄 Context Processing Integration', () => {
  it('should process complete user context pipeline', async () => {
    const userId = 'integration-test-user'
    const companyId = 'integration-test-company'

    // Full pipeline: Aggregate -> Process -> Cache -> Retrieve
    const processor = new ContextProcessor()

    const result = await processor.processUserContext(userId, companyId)

    expect(result).toBeTruthy()
    expect(result.userId).toBe(userId)
    expect(result.companyId).toBe(companyId)
    expect(result).toHaveProperty('insights')
    expect(result).toHaveProperty('recommendations')
  })

  it('should maintain data consistency across operations', async () => {
    const userId = 'consistency-test-user'
    const processor = new ContextProcessor()

    // Process context twice
    const result1 = await processor.processUserContext(userId, 'test-company')
    const result2 = await processor.processUserContext(userId, 'test-company')

    // Should be consistent (assuming no data changes)
    expect(result1.userId).toBe(result2.userId)
    expect(result1.companyId).toBe(result2.companyId)
  })
})
