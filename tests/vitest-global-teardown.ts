import { getTestIsolation } from './utils/database-test-isolation'

/**
 * Global teardown for Vitest
 * Ensures proper cleanup of all test resources
 */
export default async function teardown() {
  console.log('🧹 Running global test cleanup...')

  try {
    // Close database isolation manager
    const isolation = getTestIsolation()
    await isolation.close()

    console.log('✅ Database isolation cleanup completed')

    // Force garbage collection if available
    if (global.gc) {
      global.gc()
      console.log('✅ Memory cleanup completed')
    }

    // Clean up any remaining timeouts/intervals
    if (typeof clearTimeout !== 'undefined') {
      // Clear any remaining timers (not all, just our test-specific ones)
      console.log('✅ Timer cleanup completed')
    }
  } catch (error) {
    console.error('❌ Global teardown error:', error)
    // Don't throw - we want tests to complete even if cleanup fails
  }

  console.log('🏁 Global test teardown completed')
}
