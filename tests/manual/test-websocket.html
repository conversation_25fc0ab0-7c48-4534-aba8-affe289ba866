<!doctype html>
<html>
  <head>
    <title>WebSocket Debug Test</title>
  </head>
  <body>
    <h1>WebSocket Connection Test</h1>
    <div id="log"></div>

    <script>
      const log = document.getElementById('log')

      function logMessage(message) {
        const div = document.createElement('div')
        div.textContent = `[${new Date().toISOString()}] ${message}`
        log.appendChild(div)
        console.log(message)
      }

      async function testWebSocket() {
        logMessage('🔍 Starting WebSocket debug test...')

        // First, get a token
        try {
          logMessage('🎫 Fetching authentication token...')
          const tokenResponse = await fetch('/api/auth/websocket-token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              email: '<EMAIL>',
              role: 'SUPERADMIN',
              companyId: null,
            }),
            credentials: 'include',
          })

          if (!tokenResponse.ok) {
            throw new Error(`Token request failed: ${tokenResponse.status}`)
          }

          const tokenData = await tokenResponse.json()
          logMessage('✅ Token received: ' + tokenData.token.substring(0, 50) + '...')

          // Now test WebSocket connection
          logMessage('🔌 Testing WebSocket connection...')
          const wsUrl = `ws://localhost:8080?token=${encodeURIComponent(tokenData.token)}`
          logMessage('📡 WebSocket URL: ' + wsUrl.replace(/token=[^&]+/, 'token=***'))

          const ws = new WebSocket(wsUrl)

          ws.onopen = () => {
            logMessage('✅ WebSocket connection successful!')
            ws.close()
          }

          ws.onerror = error => {
            logMessage(
              '❌ WebSocket error: ' +
                JSON.stringify({
                  type: error.type,
                  message: error.message || 'Unknown error',
                  readyState: ws.readyState,
                })
            )
          }

          ws.onclose = event => {
            logMessage(`🔌 Connection closed: ${event.code} - ${event.reason}`)
          }
        } catch (error) {
          logMessage('❌ Test failed: ' + error.message)
        }
      }

      // Test basic connectivity first
      logMessage('🌐 Testing basic network connectivity...')
      fetch('/api/websocket/port')
        .then(r => r.json())
        .then(data => {
          logMessage('📊 Port discovery result: ' + JSON.stringify(data))
          return testWebSocket()
        })
        .catch(err => {
          logMessage('❌ Basic connectivity failed: ' + err.message)
        })
    </script>
  </body>
</html>
