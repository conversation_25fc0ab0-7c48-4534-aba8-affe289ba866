const WebSocket = require('ws')
async function test() {
  try {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      role: 'SUPERADMIN',
      companyId: null,
    })
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/websocket-token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
      },
    }
    const req = require('http').request(options, res => {
      let data = ''
      res.on('data', chunk => (data += chunk))
      res.on('end', () => {
        const tokenData = JSON.parse(data)
        if (tokenData.token) {
          console.log('✅ Token received')
          const wsUrl = `ws://localhost:8080?token=${encodeURIComponent(tokenData.token)}`
          const ws = new WebSocket(wsUrl)
          ws.on('open', () => {
            console.log('✅ WebSocket connection successful!')
            ws.close()
            process.exit(0)
          })
          ws.on('error', error => {
            console.log('❌ WebSocket error:', error.message)
            process.exit(1)
          })
          setTimeout(() => {
            console.log('⏰ Connection timeout')
            ws.terminate()
            process.exit(1)
          }, 5000)
        }
      })
    })
    req.on('error', e => {
      console.log('❌ Token request error:', e.message)
      process.exit(1)
    })
    req.write(postData)
    req.end()
  } catch (error) {
    console.log('❌ Test error:', error.message)
    process.exit(1)
  }
}
test()
