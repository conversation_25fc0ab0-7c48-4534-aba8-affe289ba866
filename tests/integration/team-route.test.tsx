/**
 * Team Route Integration Tests
 * Tests the actual team route using REAL data
 * Following TDD principle: when tests pass, team route works correctly
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Session } from 'next-auth'
import { Role } from '@prisma/client'

// Import real test utilities (no mocking)
import { setupTestDatabase, cleanupTestDatabase } from '../utils/database-helpers'
import { createTestCompany, createTestUser } from '../utils/test-data'

// Import real component
import TeamPage from '@/app/(protected)/team/page'

// Import real providers for test wrapper
import { SessionProvider } from 'next-auth/react'

// Test wrapper with real providers
function TestWrapper({ children, session }: { children: React.ReactNode; session: Session }) {
  return <SessionProvider session={session}>{children}</SessionProvider>
}

describe('Team Route', () => {
  let realEmployeeSession: Session
  let realManagerSession: Session

  beforeEach(async () => {
    await setupTestDatabase()

    // Create real company and users in database
    await createTestCompany('company-1', 'Test Company')
    const employeeUser = await createTestUser('user-1', 'company-1', Role.EMPLOYEE, {
      name: 'Test Employee',
      email: '<EMAIL>',
    })
    const managerUser = await createTestUser('user-2', 'company-1', Role.MANAGER, {
      name: 'Test Manager',
      email: '<EMAIL>',
    })

    // Create real sessions from actual users
    realEmployeeSession = {
      user: {
        id: employeeUser.id,
        email: employeeUser.email,
        name: employeeUser.name,
        role: employeeUser.role,
        companyId: employeeUser.companyId,
      },
      expires: '2024-12-31',
    }

    realManagerSession = {
      user: {
        id: managerUser.id,
        email: managerUser.email,
        name: managerUser.name,
        role: managerUser.role,
        companyId: managerUser.companyId,
      },
      expires: '2024-12-31',
    }
  })

  afterEach(async () => {
    await cleanupTestDatabase()
  })

  describe('Page Structure', () => {
    it('renders team page with correct title and description', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Team')
      expect(screen.getByText(/Your squad management/)).toBeInTheDocument()
    })

    it('displays team overview section', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      expect(screen.getByText(/Team Overview/)).toBeInTheDocument()
      expect(screen.getByText(/View your team structure and members/)).toBeInTheDocument()
    })

    it('displays team communication section', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      expect(screen.getByText(/Team Communication/)).toBeInTheDocument()
      expect(
        screen.getByText(/Direct communication channels with team members/)
      ).toBeInTheDocument()
    })

    it('displays appropriate sections based on role', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      // Employee should see basic team overview and communication
      expect(screen.getByText(/Team Overview/)).toBeInTheDocument()
      expect(screen.getByText(/Team Communication/)).toBeInTheDocument()

      // Employee should NOT see delegation section
      expect(screen.queryByText(/Task Delegation/)).not.toBeInTheDocument()
    })
  })

  describe('Role-based Content', () => {
    it('shows employee-appropriate content for employee role', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      // Should show content appropriate for employees
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText(/Your squad management/)).toBeInTheDocument()
    })

    it('shows manager-specific content for manager role', () => {
      render(
        <TestWrapper session={realManagerSession}>
          <TeamPage />
        </TestWrapper>
      )

      // Should show content that managers can access
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText(/Your squad management/)).toBeInTheDocument()

      // Manager should see delegation section
      expect(screen.getByText(/Task Delegation/)).toBeInTheDocument()
      expect(screen.getByText(/Delegate tasks and track team progress/)).toBeInTheDocument()
    })
  })

  describe('Responsive Layout', () => {
    it('renders properly on different screen sizes', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      const pageContainer = screen.getByRole('heading', { level: 1 }).closest('div')
      expect(pageContainer).toBeInTheDocument()

      // Should have responsive container classes
      expect(screen.getByText('Team')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      // Should have proper heading hierarchy
      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Team')
    })

    it('provides descriptive content', () => {
      render(
        <TestWrapper session={realEmployeeSession}>
          <TeamPage />
        </TestWrapper>
      )

      // Should have descriptive content for screen readers
      expect(screen.getByText(/Your squad management/)).toBeInTheDocument()
      expect(screen.getByText(/View your team structure and members/)).toBeInTheDocument()
    })
  })
})
