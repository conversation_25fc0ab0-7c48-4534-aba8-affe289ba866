import { EnhancedThemeProvider } from '@/lib/ThemeContext'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider } from 'next-themes'
import React from 'react'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

// Mock next-themes
vi.mock('next-themes', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useTheme: () => ({
    theme: 'dark',
    setTheme: vi.fn(),
    resolvedTheme: 'dark',
  }),
}))

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

// Test wrapper
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>
    <EnhancedThemeProvider>{children}</EnhancedThemeProvider>
  </ThemeProvider>
)

// Simple test component to verify theme application
const TestComponent = () => {
  const applyTheme = () => {
    // Apply purple theme
    document.documentElement.style.setProperty('--primary', '#8b5cf6')
    document.documentElement.style.setProperty('--secondary', '#a78bfa')
    document.documentElement.style.setProperty('--accent', '#c4b5fd')
  }

  return (
    <div>
      <button onClick={applyTheme} data-testid='apply-theme'>
        Apply Purple Theme
      </button>
      <div
        data-testid='themed-element'
        style={{
          backgroundColor: 'var(--primary, #8b5cf6)',
          color: 'var(--accent, #c4b5fd)',
        }}
      >
        Themed Element
      </div>
    </div>
  )
}

describe('Theme Application Integration', () => {
  beforeEach(() => {
    // Reset CSS custom properties
    document.documentElement.style.removeProperty('--primary')
    document.documentElement.style.removeProperty('--secondary')
    document.documentElement.style.removeProperty('--accent')
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('GIVEN a user wants to apply themes', () => {
    describe('WHEN they apply a theme', () => {
      it('THEN should update CSS custom properties globally', async () => {
        const user = userEvent.setup()

        render(
          <TestWrapper>
            <TestComponent />
          </TestWrapper>
        )

        const applyButton = screen.getByTestId('apply-theme')
        await user.click(applyButton)

        // Verify CSS custom properties are set
        await waitFor(() => {
          const rootStyles = getComputedStyle(document.documentElement)
          expect(rootStyles.getPropertyValue('--primary')).toBe('#8b5cf6')
          expect(rootStyles.getPropertyValue('--secondary')).toBe('#a78bfa')
          expect(rootStyles.getPropertyValue('--accent')).toBe('#c4b5fd')
        })
      })

      it('THEN should apply theme to elements using CSS variables', async () => {
        const user = userEvent.setup()

        render(
          <TestWrapper>
            <TestComponent />
          </TestWrapper>
        )

        const themedElement = screen.getByTestId('themed-element')
        const applyButton = screen.getByTestId('apply-theme')

        await user.click(applyButton)

        // Verify element styles use the CSS variables
        await waitFor(() => {
          const elementStyles = getComputedStyle(themedElement)
          expect(elementStyles.backgroundColor).toBeTruthy()
          expect(elementStyles.color).toBeTruthy()
        })
      })
    })

    describe('WHEN theme persists across components', () => {
      it('THEN should maintain theme consistency', () => {
        // Apply theme
        document.documentElement.style.setProperty('--primary', '#8b5cf6')

        const Component1 = () => (
          <div data-testid='component-1' style={{ backgroundColor: 'var(--primary)' }}>
            Component 1
          </div>
        )

        const Component2 = () => (
          <div data-testid='component-2' style={{ borderColor: 'var(--primary)' }}>
            Component 2
          </div>
        )

        render(
          <TestWrapper>
            <Component1 />
            <Component2 />
          </TestWrapper>
        )

        // Both components should access the same CSS variable
        const comp1 = screen.getByTestId('component-1')
        const comp2 = screen.getByTestId('component-2')

        const comp1Styles = getComputedStyle(comp1)
        const comp2Styles = getComputedStyle(comp2)

        expect(comp1Styles.backgroundColor).toBeTruthy()
        expect(comp2Styles.borderColor).toBeTruthy()
      })
    })
  })

  describe('GIVEN theme system accessibility', () => {
    describe('WHEN themes are applied', () => {
      it('THEN should maintain contrast ratios', () => {
        // Apply high contrast theme
        document.documentElement.style.setProperty('--primary', '#000000')
        document.documentElement.style.setProperty('--background', '#ffffff')

        const element = document.createElement('div')
        element.style.backgroundColor = 'var(--background)'
        element.style.color = 'var(--primary)'
        document.body.appendChild(element)

        const styles = getComputedStyle(element)

        // Basic contrast check (black on white should pass)
        expect(styles.backgroundColor).toBeTruthy()
        expect(styles.color).toBeTruthy()

        document.body.removeChild(element)
      })
    })
  })
})
