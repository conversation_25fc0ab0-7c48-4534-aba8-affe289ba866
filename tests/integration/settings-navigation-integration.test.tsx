import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { AIFirstSidebar } from '@/components/navigation/AIFirstSidebar'
import SettingsSidebar from '@/components/settings/Sidebar'

// Mock dependencies
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

// Mock fetch for settings API
global.fetch = jest.fn()

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

// Test wrapper component
function TestWrapper({ children }: { children: React.ReactNode }) {
  return <div data-testid='test-wrapper'>{children}</div>
}

describe('Settings Navigation Integration (Post-Consolidation)', () => {
  beforeEach(() => {
    mockUseSession.mockReturnValue({
      data: {
        user: {
          id: '1',
          email: '<EMAIL>',
          role: 'EMPLOYEE',
        },
      },
      status: 'authenticated',
    })

    // Mock settings API response
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => [
        {
          id: 'profile',
          title: 'Profile',
          subsections: [
            { id: 'personal-info', title: 'Personal Info', isDisabled: false },
            { id: 'privacy', title: 'Privacy', isDisabled: false },
          ],
        },
        {
          id: 'platform',
          title: 'Platform',
          subsections: [
            { id: 'appearance', title: 'Appearance', isDisabled: false },
            { id: 'notifications', title: 'Notifications', isDisabled: false },
          ],
        },
      ],
    } as Response)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('✅ Consolidated Navigation Behavior', () => {
    it('should show only one Settings entry in main sidebar', async () => {
      mockUsePathname.mockReturnValue('/dashboard')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Should have exactly one Settings link
      const settingsLinks = screen.getAllByText('Settings')
      expect(settingsLinks).toHaveLength(1)

      // Settings should link to /settings (not sub-routes)
      const settingsLink = screen.getByRole('link', { name: /settings/i })
      expect(settingsLink).toHaveAttribute('href', '/settings')
    })

    it('should NOT show settings sub-items in main sidebar', async () => {
      mockUsePathname.mockReturnValue('/dashboard')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Main sidebar should NOT have settings sub-navigation
      expect(screen.queryByText('Profile')).not.toBeInTheDocument()
      expect(screen.queryByText('Appearance')).not.toBeInTheDocument()
      expect(screen.queryByText('Notifications')).not.toBeInTheDocument()
      expect(screen.queryByText('Privacy')).not.toBeInTheDocument()
      expect(screen.queryByText('Advanced')).not.toBeInTheDocument()
    })

    it('should show detailed navigation in settings internal sidebar', async () => {
      mockUsePathname.mockReturnValue('/settings')

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      // Wait for API call and rendering
      await waitFor(() => {
        expect(screen.getByText('Settings')).toBeInTheDocument()
      })

      // Should show sections from API
      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument()
        expect(screen.getByText('Platform')).toBeInTheDocument()
      })

      // Should show subsections
      await waitFor(() => {
        expect(screen.getByText('Personal Info')).toBeInTheDocument()
        expect(screen.getByText('Privacy')).toBeInTheDocument()
        expect(screen.getByText('Appearance')).toBeInTheDocument()
        expect(screen.getByText('Notifications')).toBeInTheDocument()
      })
    })

    it('should maintain clear separation between main and settings navigation', async () => {
      mockUsePathname.mockReturnValue('/settings/profile')

      // Render both sidebars to test they work together
      render(
        <TestWrapper>
          <div data-testid='main-navigation'>
            <AIFirstSidebar />
          </div>
          <div data-testid='settings-navigation'>
            <SettingsSidebar />
          </div>
        </TestWrapper>
      )

      const mainNav = screen.getByTestId('main-navigation')
      const settingsNav = screen.getByTestId('settings-navigation')

      // Main navigation: simple, high-level
      expect(mainNav).toHaveTextContent('Settings')
      expect(mainNav).not.toHaveTextContent('Personal Info')

      // Settings navigation: detailed, specific
      await waitFor(() => {
        expect(settingsNav).toHaveTextContent('Settings')
        expect(settingsNav).toHaveTextContent('Profile')
        expect(settingsNav).toHaveTextContent('Platform')
      })
    })
  })

  describe('✅ User Experience Validation', () => {
    it('should provide consistent navigation patterns', async () => {
      mockUsePathname.mockReturnValue('/dashboard')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // All main navigation items should be simple, single-word entries
      expect(screen.getByText('Focus')).toBeInTheDocument()
      expect(screen.getByText('Team')).toBeInTheDocument()
      expect(screen.getByText('Vision')).toBeInTheDocument()
      expect(screen.getByText('Settings')).toBeInTheDocument()

      // Should not show sub-navigation in main sidebar
      expect(screen.queryByText('Today')).not.toBeInTheDocument() // Focus sub-item
      expect(screen.queryByText('Members')).not.toBeInTheDocument() // Team sub-item
      expect(screen.queryByText('Strategy')).not.toBeInTheDocument() // Vision sub-item
      expect(screen.queryByText('Profile')).not.toBeInTheDocument() // Settings sub-item
    })

    it('should handle settings API errors gracefully', async () => {
      mockUsePathname.mockReturnValue('/settings')

      // Mock API error
      mockFetch.mockRejectedValue(new Error('API Error'))

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      // Should still render the settings header
      await waitFor(() => {
        expect(screen.getByText('Settings')).toBeInTheDocument()
      })

      // Should not crash or show error to user
      expect(screen.queryByText('API Error')).not.toBeInTheDocument()
    })
  })

  describe('✅ Architecture Validation', () => {
    it('should not create duplicate routes in main navigation', async () => {
      mockUsePathname.mockReturnValue('/dashboard')

      render(
        <TestWrapper>
          <AIFirstSidebar />
        </TestWrapper>
      )

      // Get all links in main navigation
      const allLinks = screen.getAllByRole('link')
      const settingsSubRoutes = allLinks.filter(link => {
        const href = link.getAttribute('href')
        return href && href.includes('/settings/') && href !== '/settings'
      })

      // Should have NO settings sub-routes in main navigation
      expect(settingsSubRoutes).toHaveLength(0)
    })

    it('should maintain single source of truth for settings navigation', async () => {
      mockUsePathname.mockReturnValue('/settings')

      render(
        <TestWrapper>
          <SettingsSidebar />
        </TestWrapper>
      )

      // Verify API is called for settings sections
      expect(mockFetch).toHaveBeenCalledWith('/api/settings/sections')

      // Should use API data, not hardcoded navigation
      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument()
        expect(screen.getByText('Platform')).toBeInTheDocument()
      })
    })
  })
})
