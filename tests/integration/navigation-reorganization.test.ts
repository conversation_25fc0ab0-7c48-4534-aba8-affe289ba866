import { describe, it, expect } from 'vitest'

describe('Navigation Reorganization Integration', () => {
  it('should have AI Models settings page accessible', async () => {
    const response = await fetch('http://localhost:3000/settings/platform/ai-models')
    expect(response.status).toBe(200)

    const html = await response.text()
    expect(html).toContain('AI Models')
    expect(html).toContain('Platform Settings')
  })

  it('should have settings sections API include AI Models', async () => {
    // This would require authentication in a real test
    // For now, we'll just verify the endpoint exists
    const response = await fetch('http://localhost:3000/api/settings/sections')
    // Expect either 200 (if authenticated) or 401/403 (if not authenticated)
    expect([200, 401, 403]).toContain(response.status)
  })

  it('should have main pages loading correctly', async () => {
    const pages = ['/', '/settings']

    for (const page of pages) {
      const response = await fetch(`http://localhost:3000${page}`)
      expect(response.status).toBe(200)
    }
  })
})
