import { expect, test } from '@playwright/test'

test.describe('Comprehensive Theme System E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to settings appearance page
    await page.goto('/settings/platform/appearance')

    // Wait for the page to load
    await page.waitForLoadState('networkidle')
  })

  test('should display comprehensive theme system with ColorPaletteSelector', async ({ page }) => {
    // Verify the main ColorPaletteSelector component is loaded
    await expect(page.locator('[data-testid="color-palette-selector"]')).toBeVisible()

    // Check for theme mode selection
    await expect(page.getByText('Theme Mode')).toBeVisible()
    await expect(page.getByText('Light')).toBeVisible()
    await expect(page.getByText('Dark')).toBeVisible()
    await expect(page.getByText('System')).toBeVisible()

    // Check for color scheme tabs
    await expect(page.getByText('Color Palette')).toBeVisible()
    await expect(page.getByText('Gradient Generator')).toBeVisible()
    await expect(page.getByText('Theme Preview')).toBeVisible()
  })

  test('should allow switching between theme modes', async ({ page }) => {
    // Click on Light mode
    const lightModeButton = page.getByRole('button', { name: /light/i }).first()
    await lightModeButton.click()

    // Verify theme changes (check for light theme class or CSS variable)
    await page.waitForTimeout(500) // Allow theme to apply

    // Click on Dark mode
    const darkModeButton = page.getByRole('button', { name: /dark/i }).first()
    await darkModeButton.click()

    // Verify theme changes
    await page.waitForTimeout(500)

    // The theme should persist across page navigation
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Theme should still be applied
    await expect(page.locator('[data-testid="color-palette-selector"]')).toBeVisible()
  })

  test('should display gradient generator with preview', async ({ page }) => {
    // Navigate to gradient generator tab
    const gradientTab = page.getByRole('tab', { name: /gradient generator/i })
    if (await gradientTab.isVisible()) {
      await gradientTab.click()

      // Check for gradient preview
      await expect(page.locator('[data-testid="gradient-preview"]')).toBeVisible()

      // Check for gradient controls
      await expect(page.getByText('Enable Gradients')).toBeVisible()
    }
  })

  test('should apply theme changes across the application', async ({ page }) => {
    // Apply a theme change
    const purpleScheme = page.getByText('Purple').first()
    if (await purpleScheme.isVisible()) {
      await purpleScheme.click()
      await page.waitForTimeout(1000) // Allow theme to apply
    }

    // Navigate to dashboard to verify theme persists
    await page.goto('/dashboard')
    await page.waitForLoadState('networkidle')

    // Verify the theme is applied across the app
    // Check for CSS custom properties or theme-specific classes
    const rootElement = page.locator('html')
    const computedStyle = await rootElement.evaluate(el => {
      const styles = window.getComputedStyle(el)
      return {
        primary: styles.getPropertyValue('--primary'),
        background: styles.getPropertyValue('--background'),
      }
    })

    // Theme variables should be set
    expect(computedStyle.primary || computedStyle.background).toBeTruthy()

    // Navigate back to settings to verify persistence
    await page.goto('/settings/platform/appearance')
    await page.waitForLoadState('networkidle')

    // The theme selection should still be active
    await expect(page.locator('[data-testid="color-palette-selector"]')).toBeVisible()
  })

  test('should handle theme accessibility requirements', async ({ page }) => {
    // Check for proper ARIA labels and keyboard navigation
    const themeButtons = page.getByRole('button')
    const firstThemeButton = themeButtons.first()

    // Verify button is focusable
    await firstThemeButton.focus()
    await expect(firstThemeButton).toBeFocused()

    // Check for proper contrast (basic check)
    const backgroundColor = await page.evaluate(() => {
      const body = document.body
      return window.getComputedStyle(body).backgroundColor
    })

    const textColor = await page.evaluate(() => {
      const body = document.body
      return window.getComputedStyle(body).color
    })

    // Basic contrast check - colors should be defined
    expect(backgroundColor).toBeTruthy()
    expect(textColor).toBeTruthy()
  })

  test('should not have console errors when using theme system', async ({ page }) => {
    const consoleErrors: string[] = []

    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Interact with theme system
    const darkModeButton = page.getByRole('button', { name: /dark/i }).first()
    await darkModeButton.click()
    await page.waitForTimeout(1000)

    const lightModeButton = page.getByRole('button', { name: /light/i }).first()
    await lightModeButton.click()
    await page.waitForTimeout(1000)

    // Navigate to other pages to test theme persistence
    await page.goto('/dashboard')
    await page.waitForLoadState('networkidle')

    // Should not have critical console errors
    const criticalErrors = consoleErrors.filter(
      error =>
        !error.includes('Warning') && !error.includes('deprecated') && !error.includes('favicon')
    )

    expect(criticalErrors).toHaveLength(0)
  })
})
