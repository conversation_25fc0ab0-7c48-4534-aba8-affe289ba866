/**
 * Analytics Service Unit Tests
 * Testing the core analytics service functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { analyticsService, AnalyticsEvent, PerformanceMetric } from '@/services/analytics-service'

// Mock the analytics database client
vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn(() => ({
    eventStream: {
      create: vi.fn(),
      createMany: vi.fn(),
    },
    performanceMetrics: {
      create: vi.fn(),
    },
    sessionData: {
      upsert: vi.fn(),
    },
    mlFeatureVectors: {
      create: vi.fn(),
    },
    $disconnect: vi.fn(),
  })),
}))

// Mock Sentry
vi.mock('@sentry/nextjs', () => ({
  captureException: vi.fn(),
  addBreadcrumb: vi.fn(),
}))

describe('Analytics Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('trackEvent', () => {
    it('should track a single event successfully', async () => {
      // Given
      const event: AnalyticsEvent = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'interaction',
        action: 'click',
        target: 'button',
        context: { page: '/dashboard' },
        payload: { buttonId: 'submit' },
      }

      // When
      const result = await analyticsService.trackEvent(event)

      // Then
      expect(result).toBe(true)
    })

    it('should handle tracking errors gracefully', async () => {
      // Given
      const event: AnalyticsEvent = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'interaction',
        action: 'click',
        target: 'button',
        context: {},
        payload: {},
      }

      // Mock database error
      const mockCreate = vi.fn().mockRejectedValue(new Error('Database error'))
      vi.mocked(analyticsService).trackEvent = vi.fn().mockImplementation(async () => {
        try {
          await mockCreate()
          return true
        } catch (error) {
          console.error('Analytics tracking failed:', error)
          return false
        }
      })

      // When
      const result = await analyticsService.trackEvent(event)

      // Then
      expect(result).toBe(false)
    })
  })

  describe('trackPerformanceMetric', () => {
    it('should track performance metrics successfully', async () => {
      // Given
      const metric: PerformanceMetric = {
        endpoint: '/api/test',
        method: 'GET',
        userId: 'test-user',
        companyId: 'test-company',
        responseTime: 50,
        statusCode: 200,
        cacheHit: true,
      }

      // When
      const result = await analyticsService.trackPerformanceMetric(metric)

      // Then
      expect(result).toBe(true)
    })
  })

  describe('trackEventsBatch', () => {
    it('should handle batch processing efficiently', async () => {
      // Given
      const events: AnalyticsEvent[] = Array.from({ length: 100 }, (_, i) => ({
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        eventType: 'bulk_test',
        action: 'test',
        target: `item-${i}`,
        context: { index: i },
        payload: {},
      }))

      // When
      const result = await analyticsService.trackEventsBatch(events)

      // Then
      expect(result).toBe(true)
    })
  })

  describe('updateSession', () => {
    it('should update session data successfully', async () => {
      // Given
      const sessionData = {
        userId: 'test-user',
        companyId: 'test-company',
        sessionId: 'test-session',
        deviceInfo: { browser: 'Chrome', os: 'macOS' },
        startTime: new Date().toISOString(),
        pageCount: 5,
        eventCount: 20,
        engagementScore: 0.8,
      }

      // When
      const result = await analyticsService.updateSession(sessionData)

      // Then
      expect(result).toBe(true)
    })
  })
})
