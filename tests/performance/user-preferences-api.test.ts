/**
 * 🚀 User Preferences API Performance Tests (Task 4.4)
 *
 * Tests for optimizing /api/user/preferences endpoint:
 * - Target: 25-50ms response times
 * - Zero 401 authentication errors
 * - Redis caching optimization
 * - Database query optimization
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import {
  testPrisma,
  testRedis,
  TEST_USERS,
  cleanTestData,
  seedTestData,
} from '../setup/test-database'
import { RedisService } from '@/services/redis-service'

describe('🚀 User Preferences API Performance (Task 4.4)', () => {
  let redisService: RedisService

  beforeEach(async () => {
    await cleanTestData()
    await seedTestData()
    redisService = new RedisService()
  })

  afterEach(async () => {
    await cleanTestData()
  })

  describe('🎯 Response Time Performance (25-50ms target)', () => {
    it('should meet 25-50ms response time target for GET requests', async () => {
      // Given: User with preferences in database
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
      })

      expect(user).toBeTruthy()

      // When: Making GET request to preferences API
      const startTime = performance.now()

      // Simulate optimized API call
      const preferences = await testPrisma.user.findFirst({
        where: {
          email: TEST_USERS.employee.email,
          companyId: user!.companyId,
        },
        select: {
          id: true,
          email: true,
          themeMode: true,
          colorScheme: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              subscriptionStatus: true,
            },
          },
        },
      })

      const responseTime = performance.now() - startTime

      // Then: Should meet performance target
      expect(responseTime).toBeLessThan(50) // 50ms max target
      expect(responseTime).toBeGreaterThan(0) // Sanity check
      expect(preferences).toBeTruthy()

      console.log(`✅ GET response time: ${responseTime.toFixed(2)}ms (target: <50ms)`)
    })

    it('should achieve sub-25ms response times with Redis caching', async () => {
      // Given: User preferences cached in Redis
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
      })

      const cacheKey = `user_preferences:${user!.email}:${user!.companyId}`
      const cacheData = {
        user: {
          email: user!.email,
          preferences: {
            themeMode: user!.themeMode,
            colorScheme: user!.colorScheme,
          },
        },
        company: {
          id: user!.companyId,
          name: 'Test Company',
        },
      }

      await testRedis.setex(cacheKey, 300, JSON.stringify(cacheData))

      // When: Making request with cache hit
      const startTime = performance.now()
      const cachedResult = await testRedis.get(cacheKey)
      const responseTime = performance.now() - startTime

      // Then: Should achieve sub-25ms with cache
      expect(responseTime).toBeLessThan(25) // 25ms cache target
      expect(cachedResult).toBeTruthy()
      expect(JSON.parse(cachedResult!)).toMatchObject(cacheData)

      console.log(`⚡ Cached response time: ${responseTime.toFixed(2)}ms (target: <25ms)`)
    })

    it('should handle concurrent requests efficiently', async () => {
      // Given: Multiple simultaneous requests
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
      })

      const concurrentRequests = 10
      const requests = Array(concurrentRequests)
        .fill(null)
        .map(async () => {
          const startTime = performance.now()

          const result = await testPrisma.user.findFirst({
            where: {
              email: user!.email,
              companyId: user!.companyId,
            },
            select: {
              id: true,
              email: true,
              themeMode: true,
              colorScheme: true,
            },
          })

          const responseTime = performance.now() - startTime
          return { result, responseTime }
        })

      // When: Executing concurrent requests
      const startTime = performance.now()
      const results = await Promise.all(requests)
      const totalTime = performance.now() - startTime

      // Then: Should handle concurrency efficiently
      expect(results).toHaveLength(concurrentRequests)
      results.forEach(({ result, responseTime }) => {
        expect(result).toBeTruthy()
        expect(responseTime).toBeLessThan(100) // Individual request should be fast
      })

      const avgResponseTime =
        results.reduce((sum, { responseTime }) => sum + responseTime, 0) / concurrentRequests
      expect(avgResponseTime).toBeLessThan(75) // Average should be reasonable
      expect(totalTime).toBeLessThan(500) // Total time for all requests

      console.log(
        `🔄 Concurrent requests: ${concurrentRequests}, avg time: ${avgResponseTime.toFixed(2)}ms`
      )
    })
  })

  describe('🔐 Authentication Optimization', () => {
    it('should handle valid authentication without 401 errors', async () => {
      // Given: Valid user session data
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
      })

      const mockSession = {
        user: {
          id: user!.id,
          email: user!.email,
          companyId: user!.companyId,
        },
      }

      // When: Validating authentication
      const isAuthenticated = !!(mockSession?.user?.email && mockSession?.user?.companyId)

      // Then: Should authenticate successfully
      expect(isAuthenticated).toBe(true)
      expect(mockSession.user.email).toBe(TEST_USERS.employee.email)
      expect(mockSession.user.companyId).toBeTruthy()
    })

    it('should handle missing session gracefully', async () => {
      // Given: Missing or invalid session
      const invalidSessions = [
        null,
        {},
        { user: null },
        { user: {} },
        { user: { email: null } },
        { user: { email: '<EMAIL>' } }, // Missing companyId
      ]

      // When: Checking each invalid session
      invalidSessions.forEach((session, index) => {
        const isAuthenticated = !!(session?.user?.email && session?.user?.companyId)

        // Then: Should properly reject invalid sessions
        expect(isAuthenticated).toBe(false)
        console.log(`❌ Invalid session ${index + 1}: correctly rejected`)
      })
    })

    it('should validate company context properly', async () => {
      // Given: User with specific company context
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
      })

      // When: Checking company context validation
      const userCheck = await testPrisma.user.findFirst({
        where: { email: user!.email },
        select: { companyId: true },
      })

      const isValidCompanyContext = userCheck?.companyId === user!.companyId

      // Then: Should validate company context correctly
      expect(isValidCompanyContext).toBe(true)
      expect(userCheck?.companyId).toBe(user!.companyId)
    })
  })

  describe('💾 Redis Caching Optimization', () => {
    it('should implement efficient cache operations', async () => {
      // Given: User data for caching
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
      })

      const cacheKey = `user_preferences:${user!.email}:${user!.companyId}`
      const testData = { test: 'data', timestamp: Date.now() }

      // When: Setting and getting cache data
      const setStartTime = performance.now()
      await testRedis.setex(cacheKey, 300, JSON.stringify(testData))
      const setTime = performance.now() - setStartTime

      const getStartTime = performance.now()
      const cachedData = await testRedis.get(cacheKey)
      const getTime = performance.now() - getStartTime

      // Then: Cache operations should be fast
      expect(setTime).toBeLessThan(10) // Cache write should be very fast
      expect(getTime).toBeLessThan(5) // Cache read should be extremely fast
      expect(cachedData).toBeTruthy()
      expect(JSON.parse(cachedData!)).toMatchObject(testData)

      console.log(`💾 Cache write: ${setTime.toFixed(2)}ms, read: ${getTime.toFixed(2)}ms`)
    })

    it('should handle cache invalidation properly', async () => {
      // Given: Cached user preferences
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
      })

      const cacheKey = `user_preferences:${user!.email}:${user!.companyId}`
      await testRedis.setex(cacheKey, 300, JSON.stringify({ test: 'data' }))

      // Verify cache exists
      const beforeInvalidation = await testRedis.get(cacheKey)
      expect(beforeInvalidation).toBeTruthy()

      // When: Invalidating cache
      const invalidationStartTime = performance.now()
      await testRedis.del(cacheKey)
      const invalidationTime = performance.now() - invalidationStartTime

      // Then: Cache should be invalidated quickly
      expect(invalidationTime).toBeLessThan(10) // Invalidation should be fast

      const afterInvalidation = await testRedis.get(cacheKey)
      expect(afterInvalidation).toBeNull()

      console.log(`🗑️ Cache invalidation: ${invalidationTime.toFixed(2)}ms`)
    })

    it('should implement cache warming strategy', async () => {
      // Given: User data to warm cache
      const user = await testPrisma.user.findFirst({
        where: { email: TEST_USERS.employee.email },
        include: { company: true },
      })

      const cacheKey = `user_preferences:${user!.email}:${user!.companyId}`

      // When: Warming cache with user data
      const warmingStartTime = performance.now()

      const warmData = {
        user: {
          email: user!.email,
          preferences: {
            themeMode: user!.themeMode,
            colorScheme: user!.colorScheme,
          },
        },
        company: {
          id: user!.company.id,
          name: user!.company.name,
        },
      }

      await testRedis.setex(cacheKey, 300, JSON.stringify(warmData))
      const warmingTime = performance.now() - warmingStartTime

      // Then: Cache warming should be efficient
      expect(warmingTime).toBeLessThan(15) // Warming should be fast

      const warmedData = await testRedis.get(cacheKey)
      expect(warmedData).toBeTruthy()
      expect(JSON.parse(warmedData!)).toMatchObject(warmData)

      console.log(`🔥 Cache warming: ${warmingTime.toFixed(2)}ms`)
    })
  })

  describe('🗄️ Database Query Optimization', () => {
    it('should use optimized single-query approach', async () => {
      // Given: User email and company ID
      const testEmail = TEST_USERS.employee.email
      const user = await testPrisma.user.findFirst({
        where: { email: testEmail },
      })

      // When: Using optimized single query
      const queryStartTime = performance.now()

      const optimizedResult = await testPrisma.user.findFirst({
        where: {
          email: testEmail,
          companyId: user!.companyId,
        },
        select: {
          id: true,
          email: true,
          themeMode: true,
          colorScheme: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              subscriptionStatus: true,
            },
          },
        },
      })

      const queryTime = performance.now() - queryStartTime

      // Then: Single query should be fast and complete
      expect(queryTime).toBeLessThan(30) // Database query should be fast
      expect(optimizedResult).toBeTruthy()
      expect(optimizedResult!.company).toBeTruthy()
      expect(optimizedResult!.company.subscriptionStatus).toBeTruthy()

      console.log(`🗄️ Optimized query time: ${queryTime.toFixed(2)}ms`)
    })

    it('should handle non-existent users efficiently', async () => {
      // Given: Non-existent user email
      const fakeEmail = '<EMAIL>'
      const fakeCompanyId = 'fake-company-id'

      // When: Querying for non-existent user
      const queryStartTime = performance.now()

      const result = await testPrisma.user.findFirst({
        where: {
          email: fakeEmail,
          companyId: fakeCompanyId,
        },
      })

      const queryTime = performance.now() - queryStartTime

      // Then: Should handle gracefully and quickly
      expect(queryTime).toBeLessThan(20) // Should fail fast
      expect(result).toBeNull()

      console.log(`❌ Non-existent user query: ${queryTime.toFixed(2)}ms`)
    })
  })

  describe('🔄 Error Handling & Retry Mechanisms', () => {
    it('should implement exponential backoff for retries', async () => {
      // Given: Retry configuration
      const maxRetries = 3
      const baseDelay = 10 // ms
      let attempts = 0

      const retryWithBackoff = async (operation: () => Promise<any>): Promise<any> => {
        while (attempts < maxRetries) {
          try {
            attempts++
            return await operation()
          } catch (error) {
            if (attempts >= maxRetries) throw error

            const delay = baseDelay * Math.pow(2, attempts - 1)
            await new Promise(resolve => setTimeout(resolve, delay))
          }
        }
      }

      // When: Testing retry mechanism
      const startTime = performance.now()

      const result = await retryWithBackoff(async () => {
        // Simulate operation that succeeds on second try
        if (attempts === 1) {
          throw new Error('Simulated failure')
        }
        return { success: true, attempt: attempts }
      })

      const totalTime = performance.now() - startTime

      // Then: Should retry and succeed
      expect(result.success).toBe(true)
      expect(result.attempt).toBe(2) // Should succeed on second attempt
      expect(totalTime).toBeLessThan(100) // Should not take too long

      console.log(`🔄 Retry mechanism: ${attempts} attempts in ${totalTime.toFixed(2)}ms`)
    })

    it('should handle database connection errors gracefully', async () => {
      // Given: Simulated database connection test
      let connectionAttempts = 0
      const maxConnectionAttempts = 3

      const testConnection = async (): Promise<boolean> => {
        connectionAttempts++

        try {
          // Test actual database connection
          await testPrisma.user.findFirst({
            where: { id: 'connection-test' },
          })
          return true
        } catch (error) {
          if (connectionAttempts < maxConnectionAttempts) {
            await new Promise(resolve => setTimeout(resolve, 10))
            return testConnection()
          }
          return false
        }
      }

      // When: Testing connection resilience
      const connectionStartTime = performance.now()
      const isConnected = await testConnection()
      const connectionTime = performance.now() - connectionStartTime

      // Then: Should handle connection gracefully
      expect(typeof isConnected).toBe('boolean')
      expect(connectionTime).toBeLessThan(200) // Should not hang
      expect(connectionAttempts).toBeGreaterThan(0)

      console.log(
        `🔌 Connection test: ${connectionAttempts} attempts in ${connectionTime.toFixed(2)}ms`
      )
    })
  })

  describe('📊 Performance Monitoring', () => {
    it('should track response time metrics accurately', async () => {
      // Given: Performance tracking setup
      const metrics: number[] = []
      const testRuns = 5

      // When: Running multiple performance tests
      for (let i = 0; i < testRuns; i++) {
        const startTime = performance.now()

        await testPrisma.user.findFirst({
          where: { email: TEST_USERS.employee.email },
          select: { id: true, email: true },
        })

        const responseTime = performance.now() - startTime
        metrics.push(responseTime)
      }

      // Then: Should collect meaningful metrics
      const avgResponseTime = metrics.reduce((sum, time) => sum + time, 0) / metrics.length
      const maxResponseTime = Math.max(...metrics)
      const minResponseTime = Math.min(...metrics)

      expect(metrics).toHaveLength(testRuns)
      expect(avgResponseTime).toBeLessThan(50) // Average should meet target
      expect(maxResponseTime).toBeLessThan(100) // Max should be reasonable
      expect(minResponseTime).toBeGreaterThan(0) // Sanity check

      console.log(
        `📊 Performance metrics: avg=${avgResponseTime.toFixed(2)}ms, min=${minResponseTime.toFixed(2)}ms, max=${maxResponseTime.toFixed(2)}ms`
      )
    })

    it('should validate performance targets consistently', async () => {
      // Given: Performance target validation
      const target25ms = 25
      const target50ms = 50
      const testIterations = 10

      let under25msCount = 0
      let under50msCount = 0

      // When: Running performance validation tests
      for (let i = 0; i < testIterations; i++) {
        const startTime = performance.now()

        // Simulate cache hit scenario
        await testRedis.get(`test_key_${i}`)

        const responseTime = performance.now() - startTime

        if (responseTime < target25ms) under25msCount++
        if (responseTime < target50ms) under50msCount++
      }

      // Then: Should meet performance targets consistently
      const under25msPercentage = (under25msCount / testIterations) * 100
      const under50msPercentage = (under50msCount / testIterations) * 100

      expect(under50msPercentage).toBeGreaterThan(80) // At least 80% under 50ms
      expect(under25msPercentage).toBeGreaterThan(50) // At least 50% under 25ms (cache hits)

      console.log(
        `🎯 Performance targets: ${under25msPercentage.toFixed(1)}% under 25ms, ${under50msPercentage.toFixed(1)}% under 50ms`
      )
    })
  })
})
