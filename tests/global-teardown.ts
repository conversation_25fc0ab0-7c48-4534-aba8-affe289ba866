import { prisma } from '../src/lib/prisma'
import { redis } from '../src/lib/redis'

async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...')
  try {
    await prisma.$disconnect()
    if (redis.quit) {
      await redis.quit()
    }
    console.log('✅ Cleanup complete')
  } catch (error) {
    console.warn('⚠️ Cleanup failed. Resources may not be fully released.', error)
  }
}

export default globalTeardown
