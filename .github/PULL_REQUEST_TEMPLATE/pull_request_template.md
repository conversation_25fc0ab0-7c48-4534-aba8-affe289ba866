# Pull Request

## 📋 Description

Please include a summary of the changes and the related issue. Include relevant motivation and context.

Fixes # (issue)

## 🎯 Type of Change

Please delete options that are not relevant:

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality) 
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🎨 Style/UI change
- [ ] ♻️ Code refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test updates

## 🧪 How Has This Been Tested?

Please describe the tests that you ran to verify your changes:

- [ ] Unit tests (Vitest)
- [ ] Integration tests
- [ ] E2E tests (Playwright)
- [ ] Manual testing
- [ ] Accessibility testing (axe-core/Lighthouse)
- [ ] Cross-browser testing
- [ ] Mobile responsiveness testing

**Test Configuration:**
- Node.js version:
- Browser(s):
- Device(s):

## ✅ Checklist

### Code Quality
- [ ] My code follows the Emynent coding standards
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings or errors

### Testing
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] E2E tests pass locally
- [ ] No regressions in existing functionality

### Architecture & Performance
- [ ] Changes follow the client-server architecture guidelines
- [ ] No duplicated components or logic introduced
- [ ] Performance impact has been considered
- [ ] Accessibility standards (WCAG 2.1 AA) maintained
- [ ] Responsive design verified across breakpoints

### Security & Dependencies
- [ ] Security implications have been considered
- [ ] No sensitive data exposed in logs or client-side code
- [ ] Dependencies are up to date and secure
- [ ] Rate limiting considerations addressed (if applicable)

## 📷 Screenshots (if appropriate)

### Before
<!-- Add screenshots showing the current state -->

### After  
<!-- Add screenshots showing the changes -->

## 📱 Mobile Screenshots (if UI changes)

<!-- Add mobile/tablet screenshots if UI was modified -->

## 🔗 Related Issues/PRs

- Closes #
- Related to #
- Depends on #

## 🚀 Deployment Notes

- [ ] No deployment changes required
- [ ] Environment variables updated (if applicable)
- [ ] Database migrations required (if applicable) 
- [ ] Third-party service configuration changes (if applicable)

## 📝 Additional Context

Add any other context about the pull request here, including:
- Migration considerations
- Breaking changes
- Performance implications
- Future considerations 