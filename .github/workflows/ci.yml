name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'

jobs:
  # Job 1: Code Quality & Security
  quality-gates:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Structure validation
        run: npm run structure:validate

      - name: Run linter
        run: npm run lint

      - name: Type checking
        run: npm run type-check

      - name: Security audit
        run: npm audit --audit-level=high
        continue-on-error: true

  # Job 2: Testing Suite
  test-suite:
    runs-on: ubuntu-latest
    needs: quality-gates

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:all:ci

      - name: WebSocket tests
        run: npm run test:websocket:ci

      - name: Test coverage report
        run: npm run test:coverage
        continue-on-error: true

  # Job 3: Build Verification
  build-verification:
    runs-on: ubuntu-latest
    needs: [quality-gates, test-suite]

    strategy:
      matrix:
        environment: [staging, production]

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build for ${{ matrix.environment }}
        run: npm run build
        env:
          SKIP_ENV_VALIDATION: true
          NODE_ENV: ${{ matrix.environment == 'production' && 'production' || 'staging' }}

      - name: Verify build artifacts
        run: |
          if [ ! -d ".next" ]; then
            echo "❌ Build failed - .next directory not found"
            exit 1
          fi

          if [ ! -f ".next/build-manifest.json" ]; then
            echo "❌ Build incomplete - manifest missing"
            exit 1
          fi

          echo "✅ Build successful for ${{ matrix.environment }}"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.environment }}-${{ github.sha }}
          path: .next/
          retention-days: 7

  # Job 4: Deployment Readiness (Staging)
  deploy-staging-ready:
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    runs-on: ubuntu-latest
    needs: [build-verification]
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Download staging artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-staging-${{ github.sha }}
          path: .next/

      - name: Staging deployment readiness
        run: |
          echo "🚀 Staging Environment Ready for Deployment"
          echo "Build SHA: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Artifacts verified and ready for deployment"

          # Future integration points:
          echo "📋 Integration Ready For:"
          echo "  - Vercel: vercel --prod --scope=staging"
          echo "  - GCP: gcloud app deploy --version=staging"
          echo "  - AWS: aws elasticbeanstalk create-application-version"

  # Job 5: Production Deployment Readiness
  deploy-production-ready:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    needs: [build-verification]
    environment: production

    steps:
      - uses: actions/checkout@v4

      - name: Download production artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-production-${{ github.sha }}
          path: .next/

      - name: Production deployment readiness
        run: |
          echo "🌟 Production Environment Ready for Deployment"
          echo "Build SHA: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Artifacts verified and ready for production deployment"

          # Future integration points:
          echo "📋 Integration Ready For:"
          echo "  - Vercel: vercel --prod"
          echo "  - GCP: gcloud app deploy --version=production"
          echo "  - AWS: aws elasticbeanstalk create-application-version"

  # Job 6: Release Notes (Production only)
  release-notes:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    needs: [deploy-production-ready]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate release notes
        run: |
          echo "📝 Release Notes for ${{ github.sha }}" > release-notes.md
          echo "Generated: $(date)" >> release-notes.md
          echo "" >> release-notes.md
          echo "## Changes in this release:" >> release-notes.md
          git log --oneline --since="1 day ago" >> release-notes.md
          cat release-notes.md

      - name: Upload release notes
        uses: actions/upload-artifact@v4
        with:
          name: release-notes-${{ github.sha }}
          path: release-notes.md
          retention-days: 30
