name: WebSocket Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/lib/websocket/**'
      - 'tests/websocket/**'
      - 'scripts/start-websocket-server.ts'
      - '.github/workflows/websocket-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/lib/websocket/**'
      - 'tests/websocket/**'
      - 'scripts/start-websocket-server.ts'
      - '.github/workflows/websocket-tests.yml'

jobs:
  websocket-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_USER: postgres
          POSTGRES_DB: emynent_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    env:
      DATABASE_URL: postgresql://postgres:password@localhost:5432/emynent_test
      DIRECT_URL: postgresql://postgres:password@localhost:5432/emynent_test
      NEXTAUTH_SECRET: test-secret-for-websocket-testing-only-not-for-production
      NEXTAUTH_URL: http://localhost:3001
      REDIS_URL: redis://localhost:6379/1
      NODE_ENV: test
      CI: true
      GITHUB_ACTIONS: true
      TEST_VERBOSE: false
      WEBSOCKET_PORT_RANGE_START: 8100
      WEBSOCKET_PORT_RANGE_END: 8200
      WEBSOCKET_TEST_TIMEOUT: 10000
      WEBSOCKET_CONNECTION_TIMEOUT: 5000

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Setup test database
      run: |
        npx prisma generate
        npm run db:test:setup

    - name: Run WebSocket tests
      run: npm run test:websocket:ci
      timeout-minutes: 10

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: websocket-test-results
        path: |
          coverage/
          test-results/
        retention-days: 7

    - name: Cleanup test database
      if: always()
      run: npm run db:test:reset 