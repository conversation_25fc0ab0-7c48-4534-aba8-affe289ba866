import react from '@vitejs/plugin-react'
import path from 'path'
import { defineConfig } from 'vitest/config'

export default defineConfig({
  plugins: [react()],
  test: {
    // Test environment and globals
    globals: true,
    environment: 'jsdom',

    // Setup files
    setupFiles: ['./tests/setup.ts'],

    // Enhanced test isolation for race condition prevention
    isolate: true,
    fileParallelism: false, // Disable file-level parallelism to prevent database races

    // Pool configuration - Controlled concurrency to prevent race conditions
    pool: 'threads',
    poolOptions: {
      threads: {
        maxThreads: 1, // Single thread to prevent race conditions
        minThreads: 1,
        singleThread: true, // Force single-threaded execution
      },
    },

    // Performance settings - Optimized for reliability over speed
    testTimeout: 30000, // Increased for database operations and complex tests
    hookTimeout: 15000,
    teardownTimeout: 15000,

    // Sequence configuration - Run tests in predictable order
    sequence: {
      concurrent: false, // Disable concurrent test execution
      shuffle: false, // Predictable test order
      hooks: 'stack', // LIFO cleanup order
    },

    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html'],
      exclude: [
        'node_modules/**',
        'tests/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/.next/**',
        '**/dist/**',
      ],
    },

    // File patterns - Simplified
    include: ['tests/**/*.test.{ts,tsx}', 'src/**/*.test.{ts,tsx}'],
    exclude: [
      'node_modules/**',
      'dist/**',
      '.next/**',
      'coverage/**',
      'tests/manual/**', // Exclude manual tests from automation
    ],

    // Server configuration for better import handling
    server: {
      deps: {
        external: ['react', 'react-dom', 'next', '@prisma/client', 'bcryptjs', 'zod'],
        // Inline dependencies for better test isolation
        inline: ['@testing-library/react', '@testing-library/jest-dom'],
      },
    },

    // Enhanced esbuild configuration
    esbuild: {
      sourcemap: false,
      target: 'node18',
      jsx: 'automatic', // Better React 19 support
    },

    // Reporter configuration with enhanced output
    reporter: process.env.CI ? ['verbose', 'github-actions', 'junit'] : ['verbose'],

    // Retry configuration - More retries for flaky database tests
    retry: process.env.CI ? 3 : 1,

    // Bail configuration - Stop on too many failures
    bail: process.env.CI ? 10 : 5,

    // Watch configuration
    watch: false,

    // Silent mode configuration
    silent: false,

    // Disable UI to prevent hanging
    ui: false,

    // Enhanced environment variables for test stability
    env: {
      NODE_ENV: 'test',
      DATABASE_URL: 'postgresql://postgres:postgres@localhost:5432/emynent_test',
      ANALYTICS_DATABASE_URL:
        'postgresql://postgres:postgres@localhost:5432/emynent_analytics_test',
      NEXTAUTH_SECRET: 'test-secret-key-for-testing-only-very-long-and-secure',
      NEXTAUTH_URL: 'http://localhost:3000',
      REDIS_URL: 'redis://localhost:6379',
      // Test-specific environment variables
      VITEST_POOL_ID: 'single', // Force single pool
      VITEST_SEGFAULT_RETRY: '0', // Disable segfault retry
      VITEST_MAX_THREADS: '1', // Force single thread
      // Disable external service calls during tests
      SKIP_EXTERNAL_SERVICES: 'true',
      // Enhanced logging for debugging
      VITEST_VERBOSE: process.env.VITEST_VERBOSE || 'false',
    },
  },

  // Enhanced path resolution for imports
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/app': path.resolve(__dirname, './src/app'),
      '@/lib': path.resolve(__dirname, './src/lib'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/controllers': path.resolve(__dirname, './src/controllers'),
      '@/tests': path.resolve(__dirname, './tests'),
      '@test': path.resolve(__dirname, './tests'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      // Test utilities
      '@/test-utils': path.resolve(__dirname, './tests/utils'),
      // Fix Next.js server module resolution
      'next/server': path.resolve(__dirname, './src/lib/next-server-compat.ts'),
    },
  },

  // Enhanced build configuration for test environment
  build: {
    // Faster builds for testing
    target: 'node18',
    minify: false,
    sourcemap: false,
  },
})
