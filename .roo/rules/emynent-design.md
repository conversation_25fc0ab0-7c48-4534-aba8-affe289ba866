---
description: 
globs: 
alwaysApply: true
---
# Design Preferences for Emynent Project

## Project Overview
- **Emynent**: A proprietary career and skills management platform tailored for growth-stage companies, initially piloted internally, with plans for full context-awareness across all entry points using Gen AI post-MVP.

## Vision & Goals
- Guide users seamlessly in managing career growth and skill progression with context-aware, personalized experiences.
- Deliver a polished, professional, consistent, and creatively engaging user experience with an elegant, modern aesthetic.
- Promote internal skill-sharing, resource discovery, and career advancement through adaptive, context-driven design.
- Enable organizations to personalize the platform, making it feel like an internal tool through versatile, brand-aligned theming that adapts to user context.
- Retain existing UI functionalities (e.g., Settings > Appearance panel with sidebar, preset/custom themes, real-time preview) during migration and context-awareness integration.
## Design System Approach and Vision
The Emynent design system is built to ensure consistency, elegance, and modernity while supporting the migration to a client-server architecture, enabling long-term personalization for organizations, and preparing for seamless, Gen AI-driven context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel).

- **Core Approach**:
  - **Consistency with shadcn/ui**: Uses shadcn/ui 0.0.4 as the foundation for reusable, accessible components, ensuring a unified design language across the platform, including the Appearance panel.
  - **Tailwind CSS for Styling**: Leverages Tailwind CSS 4.0.12 with utility classes to enforce consistent spacing, typography, and colors, registered in `tailwind.config.ts`.
  - **Theming with next-themes**: Utilizes next-themes 0.4.6 for light, dark, and system modes, supporting predefined and custom themes, preserving the Appearance panel’s theme mode toggle.
  - **Animations via Framer Motion**: Incorporates Framer Motion 12.5.0 for subtle, modern animations that enhance UX without compromising performance, guiding users through context-aware interactions.
- **Migration Safety**:
  - Ensures theme persistence across client-server transitions by storing theme state in the database and caching in Redis (1hr TTL), validated with Playwright visual regression tests, retaining the Appearance panel’s real-time preview.
  - Maintains layout consistency by inheriting from `app/(protected)/layout.tsx` and preserving global navigation (navbar, sidebar) across all routes, including the Settings sidebar.
  - Prevents regressions with snapshot testing for themes and layouts, integrated into CI pipelines (per Emynent Coding Rules, artifact_id: 844ed034-3539-445b-acdc-da23f3336a60).
- **Long-Term Vision**:
  - **Personalization for Organizations**: The design system enables versatile theming, allowing companies to customize colors and styles to align with their brand (e.g., Red Bull’s blue, red, yellow), with future context-aware theming based on user preferences and behavior.
  - **Scalability for White-Labeling**: Supports future white-labeling by allowing company-specific themes to be defined in `tailwind.config.ts`, with Superadmin controls for theme management in the Design System Manager (per scope, artifact_id: 2a65be92-45bc-4f2d-bdf6-3a410ba2b056).
  - **Enhanced User Engagement**: Encourages thoughtful UX enhancements (e.g., animations, micro-interactions) that align with the design language, improving engagement while maintaining consistency, especially for context-aware features like the Appearance panel.
  - **Seamless Context-Awareness**: Prepares for Gen AI-driven context-awareness by designing adaptive UI elements (e.g., dynamic dashboards, personalized Appearance suggestions) that respond to user context (e.g., role, preferences, recent actions), ensuring a fluid and intuitive experience.

## Design Philosophy
- **Consistency**: Strictly adhere to the established design language for a cohesive and predictable user experience, preserving the Appearance panel’s layout and functionality.
- **Elegance**: Prioritize clean, refined visuals and interactions that exude professionalism and sophistication.
- **Modernity**: Embrace a contemporary look and feel through sleek typography, minimalistic layouts, and subtle animations.
- **Creativity**: Allow thoughtful innovation to enhance UX/UI, provided it aligns with core design principles.
- **Seamless Context-Awareness**: Design interactions that adapt dynamically to user context, ensuring a fluid, intuitive experience with Gen AI-driven personalization.

## Design Language Rules
1. **Consistency First**: Maintain strict adherence to the current design language across all elements:
   - **Colors**: Use the existing color palette as the foundation. Introduce subtle, purposeful variations only to enhance UX, ensuring they blend seamlessly with the established scheme.
   - **Typography**: Use Inter as the primary font family, with weights 400 (regular), 500 (medium), and 600 (semibold). Font sizes: 16px (base), 14px (small), 20px (heading). Line height: 1.5. Prefer modern, legible typefaces that align with a professional tone.
   - **Iconography**: Use lucide-react 0.479.0 for consistent iconography, ensuring icons are sized uniformly (e.g., 24px for standard, 16px for small) and aligned with text or UI elements, as seen in the Appearance panel.
   - **Layout & Spacing**: Leverage Tailwind CSS 4.0.12 utility classes consistently (e.g., `p-4`, `m-2`, `gap-3`). Avoid arbitrary spacing or layout deviations; ensure alignment and rhythm in design, preserving the sidebar and section structure.
   - **Responsiveness**: Ensure responsiveness across all breakpoints (e.g., sm:640px, md:768px, lg:1024px) using Tailwind’s responsive prefixes, validated with Playwright visual regression tests.
2. **Elegance in Execution**: Opt for minimalistic, uncluttered designs with smooth transitions and refined details.
3. **Modern Aesthetic**: Incorporate flat design principles, subtle shadows, and clean lines to reflect a cutting-edge look.
4. **Accessibility**: Ensure WCAG 2.1 AA compliance across all UI elements:
   - Maintain a minimum contrast ratio of 4.5:1 for text and 3:1 for large text, validated with axe-core, Lighthouse, and manual NVDA/VoiceOver testing.
   - Include ARIA labels for interactive elements, visible focus states (e.g., 2px blue outline with `focus:ring-2 ring-blue-500`), and clear error messaging (e.g., red text with icon).
   - Test screen reader compatibility (e.g., NVDA, VoiceOver) for all components and themes, including the Appearance panel.

## Component Guidelines
1. **Default to shadcn/ui**: Use shadcn/ui 0.0.4 components as the primary building blocks for UI consistency and accessibility (WCAG 2.1 AA compliant).
2. **Thoughtful Innovation**: Introduce new or customized components sparingly, ensuring they:
   - Align with existing UI patterns and design language.
   - Avoid arbitrary drift or significant deviations from established standards.
   - Are checked for duplicates in `/src/components` and `/lib/design-system/components` (per awesome-cursorrules, react-components-creation-cursorrules-prompt-file).
   - Are prepared for context-aware rendering (e.g., Appearance panel suggesting themes based on user context), with placeholders gated behind `hasFeature(companyId, "contextAwareness")`.
3. **Testing Requirement**: Rigorously test all imported or newly designed components for consistency, usability, and visual harmony before integration, using Playwright screenshots and accessibility checks with axe-core, Lighthouse, and manual NVDA/VoiceOver testing for high-traffic or high-interaction flows (e.g., settings, dashboards).

## Creativity & Innovation Rules
1. **Encouraged but Controlled**: Thoughtful UX/UI enhancements are welcome, provided they:
   - Complement and elevate the existing design language without disrupting its integrity.
   - Are explicitly tested for consistency, usability, and elegance.
   - Demonstrably improve user experience, engagement, or accessibility.
   - Plan for context-aware enhancements (e.g., personalized tooltips, dynamic modals) that adapt to user context using Gen AI.
2. **Modern Enhancements**: Favor subtle animations (via Framer Motion 12.5.0), intuitive interactions, and visually appealing micro-interactions that align with a modern aesthetic and guide users through context-aware features.
3. **Approval Process**: Significant creative deviations from the design system, including context-aware enhancements, require review to ensure they uphold consistency and elegance.

## Prioritization
- **Consistency**: The cornerstone of the design system—never sacrifice it for creativity, especially for retaining the Appearance panel’s layout and functionality.
- **Elegance**: Aim for a sophisticated, polished feel in every interaction and visual element.
- **Modern Looking**: Ensure the platform feels fresh and forward-thinking, appealing to growth-stage companies.
- **Seamless Context-Awareness**: Prioritize adaptive, intuitive design that responds to user context, ensuring a fluid Gen AI-driven experience.

## Implementation Notes
- Use Tailwind CSS 4.0.12 classes to enforce spacing, typography, and color rules programmatically.
- Regularly audit the UI to eliminate inconsistencies and maintain a cohesive, elegant, and modern design.
- All UI routes must inherit from the main layout (`app/(protected)/layout.tsx`).
- Navbar and sidebar (global navigation) must persist across all authenticated pages, including the Settings sidebar, without override unless explicitly scoped.
- Design and page transitions must be validated using Playwright screenshots or other free, open-source visual regression tools (e.g., Cypress with visual plugins).
- **Design Token Management**:
  - Define semantic token names in `tailwind.config.ts` (e.g., `primary`, `secondary`, `accent`, `background`) to avoid hardcoding hex values, matching the Appearance panel’s theme elements.
  - Maintain a versioned design system (e.g., v1.0 for MVP), with changes tracked in `/docs/design-system-changelog.md`.
  - Update `tailwind.config.ts` via the Superadmin Panel’s Design System Manager for custom themes, ensuring WCAG compliance.
- **Performance Optimization**:
  - Ensure animations (via Framer Motion) have a max duration of 300ms and do not impact Web Vitals (e.g., CLS < 0.1).
  - Plan for context-aware rendering to maintain rendering times <300ms, using lazy-loading for dynamic UI elements like the real-time preview.
  - Optimize real-time preview rendering in the Appearance panel to maintain responsiveness (<100ms update).
- **State Persistence**:
  - Persist user selections (e.g., custom themes, gradient settings) in the database and cache in Redis (1hr TTL), ensuring seamless session continuity.
- **Collaboration**:
  - Encourage design reviews in PRs with feedback logged in `/docs/design-feedback.md`.
  - Require cross-team validation (at least one designer and one developer) for context-aware design implementations to ensure alignment with UX goals.

## Theming Rules
- **Theme Modes**:
  - Support **Light Mode**, **Dark Mode**, and **System Mode** (automatically adapts to the user’s system preferences), as implemented in the Appearance panel.
  - Store user theme preference in the database and cache it in Redis (1-hour TTL). If the preference cannot be loaded (e.g., Redis or DB failure), fallback to system mode and default to Emynent Default.
- **Pre-Defined Themes**:
  - Provide a set of pre-defined theme color palettes for each mode, selectable by users in the Appearance panel:
    - **Emynent Default**: Primary: #1E40AF, Secondary: #6B7280, Accent: #F59E0B.
    - **Slate**: Primary: #334155, Secondary: #64748B, Accent: #A5B4FC.
    - **Mint**: Primary: #10B981, Secondary: #6EE7B7, Accent: #FCD34D.
  - Ensure all pre-defined themes are visually distinct, WCAG 2.1 AA compliant, and brandable.
- **Custom Color Overrides**:
  - Allow users to customize the color palette within a selected mode (e.g., dark mode) to align with their brand, as shown in the Custom section.
  - Example: A Red Bull admin can set Primary: #003087 (blue), Secondary: #D4061F (red), Accent: #FFC107 (yellow), Background: #FFFFFF (white).
  - Extend `tailwind.config.ts` under `theme.extend.colors` to register custom theme tokens dynamically via the Superadmin Panel.
  - Ensure custom colors maintain WCAG 2.1 AA contrast ratios, validated with axe-core, Lighthouse, and manual NVDA/VoiceOver testing.
  - **Error Handling**: If custom colors fail accessibility checks, default to Emynent Default theme and notify the Superadmin with a validation error (e.g., “Custom color #FF0000 fails contrast ratio” via sonner 1.5.0 toast).
- **Context-Aware Theming (Placeholder)**:
  - Plan for Gen AI-driven theming that adapts to user context (e.g., suggesting themes based on user preferences or company branding), integrating into the Appearance panel post-MVP.
  - Use placeholders in `/src/components/context-aware` to prepare for dynamic theme adjustments.
- **Performance Optimization**:
  - Minimize CSS payload by using Tailwind CSS’s purge (content scanning) to remove unused styles and reduce bundle size. Validate output in production builds with Lighthouse.
  - Lazy-load custom theme styles for non-critical components (e.g., modals, charts) using dynamic imports.
- **Implementation**:
  - Use next-themes 0.4.6 for theme management, ensuring seamless mode switching as in the Appearance panel.
  - Do not hardcode hex values in components—always reference design tokens from `tailwind.config.ts`.
  - Validate theme persistence and visual consistency with Playwright snapshot tests after client-server transitions (per awesome-cursorrules, nextjs15-react19-vercelai-tailwind-cursorrules-prompt-file).
- **Future-Proofing**:
  - Design the theming system to support white-labeling, allowing company-specific themes to be defined and managed by Superadmins.
  - Ensure the system is extensible for adding new pre-defined themes or color overrides without breaking existing functionality, including the Appearance panel.

## Onboarding UX Expectations
- The onboarding flow must be preserved post-migration with all logic and routes intact.
- Onboarding should support progressive expansion: adding more pages or sections must not break existing logic.
- Full keyboard accessibility is required for onboarding flows. Navigation, form control, and confirmation must work without a mouse (WCAG 2.1 AA compliant), validated with axe-core, Lighthouse, and manual NVDA/VoiceOver testing for high-traffic or high-interaction flows.
- Roo Code AI must treat onboarding logic as protected and roo-aware, avoiding regressions.
- Future enhancements (additional onboarding steps, improved personalization, better keyboard navigation) must be designed to layer seamlessly.
- **Context-Aware Onboarding (Placeholder)**:
  - Plan for Gen AI-driven onboarding flows that adapt to user context (e.g., personalized steps based on role or preferences), ensuring a seamless and intuitive experience.
  - Use placeholders in `/src/components/context-aware` to prepare for dynamic onboarding content.

## Roo Code Design Responsibilities
- All page design will be handled in collaboration with Roo Code. Use direct implementation or reference screenshots where necessary.
- Roo Code must:
  - Validate changes against existing layout and design rules (per Emynent Coding Rules, artifact_id: 844ed034-3539-445b-acdc-da23f3336a60).
  - Adhere to layout consistency for settings, dashboards, onboarding, and landing pages, retaining the Appearance panel’s sidebar and section structure.
  - Maintain the public landing page UX (visual sections, call-to-action, brand tone).
  - Avoid regressions in layout, navigation, theme handling, or responsiveness, ensuring the Appearance panel’s functionality (e.g., preset selection, custom editing, preview) remains intact.
  - Ensure theme customization aligns with the design system, using tokens from `tailwind.config.ts`.
  - When a new custom theme is applied or created, log it in `/docs/design-system-changelog.md` and validate it through Playwright visual snapshots.
  - Design for context-awareness by preparing adaptive UI elements (e.g., dynamic dashboards, personalized Appearance suggestions) that respond to user context, using placeholders gated behind `hasFeature(companyId, "contextAwareness")`.

## Future Considerations (Non-MVP Scope)
- GitHub and other email providers for SSO are out of scope for MVP.
- If considered post-MVP, designs must reuse existing auth components (e.g., SignIn, SignUp) and maintain routing logic via `app/(auth)/layout.tsx` and `onboarding/page.tsx`, ensuring consistency with protected and public layouts.
- Additional pre-defined themes or advanced theming options (e.g., gradient support, pattern backgrounds) can be explored post-MVP.
- **Context-Aware Design Enhancements**:
  - Implement Gen AI-driven context-awareness (Q3-Q4 2025) to provide personalized UI experiences (e.g., dynamic dashboards, context-driven Appearance suggestions, adaptive onboarding flows).
  - Design for seamless integration of Gen AI insights, recommendations, and coaching, ensuring a fluid and intuitive user experience.

## Visual Testing & Layout Validation
- Use **Playwright screenshots** or **free open-source tools** (e.g., Cypress with visual plugins) to validate layout changes.
- Visual tests must be included when modifying:
  - Navbar or sidebar layouts, including the Settings sidebar.
  - Onboarding steps or page flows.
  - Theme-related appearance (light/dark/system modes, pre-defined/custom themes), including the Appearance panel.
  - Any route inheriting from `app/(protected)/layout.tsx`.
  - Context-aware UI components (placeholder) to ensure consistency.
- Snapshot test all themes (light, dark, system) and custom color overrides to ensure consistency post-migration, covering the Appearance panel’s preview.
- Include responsive breakpoint tests (sm, md, lg) to validate layout across screen sizes.
- Manual review is required if automated tools are unavailable.
- Avoid layout regressions or breakages post-deployment by running visual tests in CI pipelines.

---

This document governs all visual consistency and UI development for Emynent. It ensures Roo Code and developers preserve the look, feel, and behavior of the product—especially through architectural migrations—while preparing for a seamless, Gen AI-driven, context-aware experience across all entry points, including retaining the Appearance panel’s functionality.
