---
description: 
globs: 
alwaysApply: true
---
# Emynent MVP Tech Stack

## Framework
- **Next.js ^15.2.2**: Use Next.js with App Router for building scalable web applications, leveraging server-side rendering (SSR) and React Server Components (RSC) to prepare for full context-aware processing across all entry points post-MVP.
- **React ^19.0.0**: Leverage React for component-based UI development.
- **React DOM ^19.0.0**: Use React DOM for rendering React components.

## Language
- **TypeScript ^5.x**: Use TypeScript for static typing, enhancing type safety and maintainability.

## Styling
- **Tailwind CSS ^4.0.12**: Apply Tailwind CSS, a utility-first CSS framework, for styling.
- **shadcn/ui ^0.0.4**: Utilize shadcn/ui, CLI-driven reusable UI components built on Radix UI.
- **Radix UI Components ^1.1.x – ^2.2.x**: Incorporate Radix UI for accessible, modular UI elements.
- **Utilities**:
  - tailwind-merge ^3.0.2: Merge <PERSON>wind classes efficiently.
  - tailwindcss-animate ^1.0.7: Add animation utilities for Tailwind.
  - class-variance-authority ^0.7.1: Manage component variants.
  - clsx ^2.1.1: Conditionally join class names.
  - next-themes ^0.4.6: Support light/dark mode theming.

## Database
- **Prisma ORM ^6.5.0**: Use Prisma for type-safe database interactions, with placeholders for future context-aware models (e.g., expanded `UserContext` with `role String?, companyId Int?, preferences Json?, recentActions Json?, historicalData Json?`, `CoachingHistory` with `userId Int, recommendation String?, completed Boolean?`) to store comprehensive user context across all entry points.
- **PostgreSQL ^15.0**: Set up PostgreSQL as the primary database.
- **Redis ^7.0.0**: Use Redis for caching (e.g., feature flags, rate limiting), with plans to cache context-aware data (e.g., 1hr TTL) post-MVP.

## Authentication
- **NextAuth.js ^5.0.0-beta.25**: Implement NextAuth.js (beta) for authentication, noting potential stability risks.
- **@auth/core ^0.38.0**: Core authentication library for NextAuth.js.
- **@auth/prisma-adapter ^2.8.0**: Prisma adapter for NextAuth.js.
- **Google OAuth**: Configure Google OAuth as the primary authentication provider.
- **bcryptjs ^3.0.2**: Use for secure password hashing.

## Hosting
- **Vercel**: Deploy staging and production environments on Vercel, including Vercel Analytics and Storage.

## Form Management
- **React Hook Form ^7.54.2**: Use for efficient form state management.
- **Zod ^3.24.2**: Apply Zod for schema validation, ensuring data integrity.
- **@hookform/resolvers**: Integrate Zod with React Hook Form for form validation.

## State Management (Placeholder)
- **React Context or Zustand (TBD)**: Plan to integrate a state management solution to manage context-aware state across all entry points (e.g., user preferences, recent actions), with implementation deferred post-MVP.

## API Layer (Placeholder)
- **Context-Aware API (TBD)**: Plan to develop a RESTful API layer in `/controllers/context.ts` to process and serve dynamic context data across all entry points, with implementation deferred post-MVP.

## Testing
- **Vitest ^3.1.1**: Use for unit and integration tests, offering fast execution and co-location.
- **Playwright ^1.51.1**: Use for end-to-end (E2E) testing, ensuring cross-browser compatibility and context-aware workflows.
- **@testing-library/react ^14.3.1**: Test React components and DOM interactions.
- **Utilities**:
  - @testing-library/jest-dom: Custom Jest matchers for DOM testing.
  - user-event: Simulate user interactions.
  - jsdom ^26.0.0: JavaScript DOM environment for testing.
- **k6**: Perform basic load testing for critical API endpoints.

## Monitoring
- **Sentry ^7.0.0**: Use for error tracking and performance monitoring, with plans to monitor context-aware API latency and errors post-MVP.
- **LogRocket ^2.0.0**: Use for session replays to debug user issues.
- **Vercel Analytics**: Use for performance and usage analytics, including context-aware performance metrics.

## Additional Libraries
- **framer-motion ^12.5.0**: Integrate for animations and transitions.
- **lucide-react ^0.479.0**: Use as the icon library for consistent iconography.
- **sonner ^2.0.1**: Implement for toast notifications to enhance user feedback, with support for future context-aware alerts (e.g., “Unable to fetch context-aware recommendations”).
- **react-day-picker ^8.10.1**: Use for date selection in forms.
- **react-best-gradient-color-picker ^3.0.14**: Use for gradient color selection.
- **react-colorful ^5.6.1**: Use for lightweight color picking.
- **express-rate-limit ^7.5.0**: Apply rate limiting to API endpoints.
- **node-cron ^3.0.3**: Use for scheduled tasks (e.g., data cleanup, notifications).
- **Gen AI (Placeholder)**:
  - Plan to integrate a Gen AI API client (e.g., xAI API, TBD) post-MVP for full context-awareness across all entry points, providing personalized insights, recommendations, and coaching.

## Tooling and Configuration
- **npm**: Use npm as the package manager with locked dependencies.
- **pnpm**: Evaluate for future migration to improve performance.
- **ESLint ^9**: Enforce code quality with ESLint.
- **eslint-config-next ^15.2.2-canary.7**: Use Next.js-specific ESLint configuration.
- **tsx ^4.19.3**: Use for TypeScript execution in seeding and tooling scripts.
- **Husky ^9.1.7**: Manage Git hooks for pre-commit linting and testing.

## Guidelines
- **Version Alignment**: Ensure all components and configurations use the specified versions.
- **Consistency**: Adhere to this tech stack across the project to maintain uniformity.
- **Compatibility**: Verify new dependencies are compatible with listed technologies and document in `package.json`, including placeholders for context-aware libraries and Gen AI-related components post-MVP.
- **Package Management**: Use npm with locked dependencies; evaluate pnpm for future adoption.
- **Documentation**: Update `/docs/development.md`, `/docs/testing.md`, and `/docs/context-awareness-plan.md` for setup, testing configurations, and context-awareness placeholder integration.

## Security (Placeholder)
- **Context-Aware Data Privacy (TBD)**: Plan to implement anonymization of sensitive context data (e.g., preferences, recent actions, historical data) and require user consent for Gen AI-driven context-awareness in `/app/settings.tsx` post-MVP.

## Performance Optimization (Placeholder)
- **Context-Aware Performance (TBD)**: Plan to optimize context-aware processing to maintain API response times <300ms, using Redis caching (1hr TTL) and lazy-loading for context-aware UI components post-MVP.