---
description:
globs:
alwaysApply: false
---
# 🏢 Emynent Project Implementation Rules

**Project-Specific Standards for AI-Native Career & Skills Progression Platform**

## 📋 Project Overview
Emynent is an AI-native organizational intelligence platform for growth-stage companies, built with Next.js 15+, React 19, Prisma 6.5.0, and Tailwind CSS 4.0.12. The Enhanced MVP validates core functionalities using TDD, first principles thinking, and user-centric design with hyper-personalization.

## 🎯 MVP Scope & Constraints

### **In Scope (MVP)**
- **Career Visibility**: Clear, personalized career progression paths
- **Skill Management**: Context-aware tracking and development
- **RBAC Security**: Role-based access control with audit logging
- **Multi-tenancy**: Company-level data isolation
- **Client-Server Architecture**: Modular, scalable architecture
- **Behavioral Intelligence**: Real-time user behavior tracking
- **Context-Awareness Foundation**: Placeholders for Gen AI integration

### **Out of Scope (MVP)**
- ❌ No microservices or multi-repo conversion
- ❌ No RBAC schema changes unless scoped
- ❌ No UX redesigns or new navigation patterns
- ❌ No HR tool integrations
- ❌ No new design systems or forks
- ❌ No internationalization (i18n) or localization
- ❌ No active Gen AI implementation (placeholders only)

### **User Roles & Permissions**
| Role       | Inherits From | Pages Accessed                                  |
|------------|---------------|------------------------------------------------|
| Employee   | —             | Dashboard, Settings                             |
| Manager    | Employee      | Team dashboard                                  |
| Director   | Manager       | Department dashboard                            |
| Admin      | Director      | Org settings, User management                   |
| Superadmin | Admin         | All + Superadmin Panel + Developer Persona Mode |

## 🏗️ Architecture Implementation Standards

### **Client-Server Architecture**
- **Controllers**: Handle HTTP requests, validation, error responses
- **Services**: Stateless business logic, no side effects
- **RESTful APIs**: Feature-based organization with OpenAPI documentation
- **Zod Validation**: Shared schemas in `/src/lib/validators/`
- **Error Handling**: Structured responses with sonner 1.5.0 toast alerts

### **Database & Caching Strategy**
- **Prisma ORM 6.5.0**: Type-safe database interactions
- **PostgreSQL**: Primary database with company-scoped operations
- **Redis Caching**: Feature flags (10min TTL), roles (1hr TTL)
- **Context Models**: Placeholder `UserContext` and `CoachingHistory` for future Gen AI

## 🛡️ Security & Compliance

### **Authentication & Authorization**
- **NextAuth.js 5.0.0-beta.25**: Google OAuth + credentials
- **Role Validation**: All protected routes validate via session/authGuard
- **Middleware**: Authentication, role preloading, security headers
- **Multi-tenancy**: Company-scoped operations throughout codebase

### **Security Standards**
- **Input Sanitization**: Prevent XSS, CSRF, SQL injection
- **Rate Limiting**: 100 requests/min per user, Superadmin limits
- **Secure Logging**: No sensitive data (JWTs, emails, IPs) in Sentry/LogRocket
- **API Security**: JWT rotation (24hrs), strict CSP, encrypted PII (AES-256)

### **Audit Logging Requirements**
- Log all Superadmin actions (timestamp, actor, action, target)
- Store in database with 90-day retention
- Queryable by timestamp/actor/action
- Include company edits, feature toggles, impersonation

## 🧪 Testing Standards (See @emynent-testing.md)

### **Coverage Requirements**
- **Unit Tests**: 80%+ coverage for `/services`, `/controllers` (Vitest 3.1.1)
- **Integration Tests**: All API endpoints with real database operations
- **E2E Tests**: Complete user workflows with Playwright 1.51.1
- **Accessibility**: WCAG 2.1 AA compliance via axe-core, Lighthouse
- **Visual Regression**: Playwright snapshots for themes and layouts

### **TDD Mandatory Practices**
- **Red-Green-Refactor**: All production code follows TDD cycle
- **No Mocking**: Test against real services, database, APIs
- **Real Data Testing**: Use actual Prisma operations and API calls
- **100% Functional**: When tests pass, features work in production

### **Test Organization**
```
/tests
  /integration     → API and service integration tests
  /e2e            → Playwright end-to-end workflows
  /a11y           → Accessibility compliance tests
  /theme-snapshots → Visual regression for themes
  /critical       → Critical path user journeys
  /context-aware  → Placeholder tests for future Gen AI features
```

## 🎨 Design System & UX Standards (See @emynent-design.md)

### **Design Philosophy**
- **Consistency**: Strict adherence to established design language
- **Elegance**: Clean, refined visuals and interactions
- **Modernity**: Contemporary look with sleek typography, minimalistic layouts
- **Accessibility**: WCAG 2.1 AA compliance as baseline

### **Component Standards**
- **shadcn/ui 0.0.4**: Primary building blocks for consistency
- **Tailwind CSS 4.0.12**: Utility classes for spacing, typography, colors
- **next-themes 0.4.6**: Light/dark/system mode support
- **Framer Motion 12.5.0**: Subtle animations for enhanced UX

### **Theme Management**
- **Database Storage**: User preferences with Redis caching (1hr TTL)
- **Fallback Logic**: System mode + Emynent Default on failure
- **Pre-defined Themes**: Emynent Default, Slate, Mint with WCAG compliance
- **Custom Overrides**: Company-specific branding (e.g., Red Bull colors)

### **Layout Preservation**
- **Persistent Layout**: `app/(protected)/layout.tsx` with navbar/sidebar
- **Settings Panel**: Appearance section with real-time preview
- **Responsive Design**: Validated across breakpoints (sm, md, lg)

## ⚡ Performance & Optimization

### **Performance Targets**
- **API Response**: <100ms for critical endpoints
- **UI Interactions**: <200ms for all user-facing operations
- **Page Load**: <2.5s first load, <300ms navigation
- **Bundle Size**: <500KB production builds
- **Web Vitals**: LCP <2.5s, FCP <1.5s, CLS <0.1

### **Optimization Strategies**
- **Lazy Loading**: Charts, modals, non-critical components
- **Redis Caching**: Feature flags, roles, session data
- **Image Optimization**: WebP format with lazy-loading
- **Bundle Analysis**: Regular monitoring and optimization

## 🤖 Gen AI & Context-Awareness Preparation

### **Placeholder Implementation**
- **Feature Flags**: `hasFeature(companyId, "contextAwareness")` throughout codebase
- **Controller Placeholders**: `/src/controllers/gen-ai.ts`, `/src/controllers/context.ts`
- **Service Placeholders**: `/src/services/ai-recommendations.ts`, `/src/services/context-service.ts`
- **UI Components**: `/src/components/context-aware/` for future adaptive interfaces

### **Data Models (Placeholder)**
```typescript
// Future context-aware models in Prisma schema
model UserContext {
  id           Int    @id @default(autoincrement())
  role         String?
  companyId    Int?
  preferences  Json?
  recentActions Json?
  historicalData Json?
}

model CoachingHistory {
  id           Int     @id @default(autoincrement())
  userId       Int
  recommendation String?
  completed    Boolean?
}
```

### **Privacy & Security Planning**
- **Data Anonymization**: Plan for sensitive context data anonymization
- **User Consent**: Required for Gen AI-driven features
- **Rate Limiting**: 10 requests/min per user for Gen AI APIs
- **Context Processing**: Target <300ms for context-aware rendering

## 📚 Tech Stack Requirements (See @emynent-tech-stack.md)

### **Core Dependencies**
```json
{
  "next": "^15.2.2",
  "react": "^19.0.0",
  "typescript": "^5.x",
  "tailwindcss": "^4.0.12",
  "prisma": "^6.5.0",
  "next-auth": "^5.0.0-beta.25",
  "vitest": "^3.1.1",
  "playwright": "^1.51.1"
}
```

### **Development Tools**
- **ESLint**: Code quality enforcement
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit validation
- **TypeScript**: Strict mode enabled
- **Zod**: Schema validation

### **Monitoring & Analytics**
- **Sentry 7.0.0**: Error tracking and performance monitoring
- **LogRocket 2.0.0**: Session replays for debugging
- **Vercel Analytics**: Performance and usage metrics

## 🚀 Development Workflow Integration

### **Code Generation Rules**
- **Search Before Create**: Check `/src/components/`, `/src/lib/`, `/src/services/` for existing solutions
- **Prevent Duplication**: Log reused components in PR descriptions
- **Folder Placement**: Follow established architecture patterns
- **Feature Flags**: Gate experimental features behind `hasFeature()` checks

### **Migration & Refactoring**
- **Preserve Functionality**: Validate against production behavior with E2E tests
- **Document Changes**: Log migrations in `/docs/migration-log.md`
- **No Parallel Implementations**: Replace old patterns completely
- **Clean Deprecated Code**: Remove unused components and dead code

### **Error Handling Protocol**
- **Graceful Degradation**: Fallback UI for component-level errors
- **User-Friendly Messages**: Clear error notifications via sonner
- **Structured Logging**: Use Sentry for sanitized error tracking
- **Try-Catch**: Implement for all async operations

## 📋 Quality Gates & Validation

### **Pre-Merge Requirements**
- [ ] All ESLint and TypeScript checks pass
- [ ] 80% test coverage for services/controllers
- [ ] E2E tests validate critical user paths
- [ ] Visual regression tests updated for UI changes
- [ ] Accessibility compliance verified (axe-core, Lighthouse)
- [ ] Performance targets met (API <100ms, UI <200ms)

### **PR Standards**
- **Detailed Descriptions**: Summary, affected areas, reused components
- **Test Documentation**: Coverage summary and validation steps
- **Migration Impact**: Document architectural changes
- **Cross-Reference**: Link to related tasks and tickets

### **Deployment Validation**
- **Staging Tests**: Full test suite in staging environment
- **Performance Monitoring**: Vercel Analytics validation
- **Error Tracking**: Sentry monitoring for production issues
- **Rollback Plan**: Documented recovery procedures

## 🔄 Continuous Improvement

### **Pattern Recognition**
- **Monitor Duplicates**: Track repeated implementations for abstraction
- **Error Patterns**: Identify common issues for prevention rules
- **Performance Trends**: Regular monitoring and optimization
- **User Feedback**: Integrate into development priorities

### **Documentation Maintenance**
- **Code Examples**: Keep aligned with actual implementation
- **Architecture Updates**: Reflect current system state
- **Migration Logs**: Document all architectural changes
- **Test Coverage**: Maintain current status and gaps

---

**This document governs all Emynent-specific implementation requirements. All development must align with these standards while preparing for future AI-driven context-awareness capabilities.**
