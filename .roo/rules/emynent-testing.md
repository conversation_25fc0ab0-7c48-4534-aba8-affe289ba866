---
description:
globs:
alwaysApply: false
---
# 🧪 Emynent Testing Standards & Requirements

**Comprehensive testing framework ensuring zero regressions, AI-ready architecture, and production confidence**

## 📝 Related Documentation
- **Development Rules**: See @ai-agent-development-rules.md for TDD methodology
- **Architecture**: See @emynent-architecture.md for system design patterns
- **Folder Structure**: See @emynent-folder-structure.md for test organization

---

## 🎯 Testing Philosophy

### **Core Principles**
- **Prevent Regressions**: Tests must catch any visual, behavioral, or structural regressions
- **Test-Driven Development**: Write tests first, implement to make them pass
- **Real Data Testing**: No mocking - test with actual data, services, and conditions
- **AI-Ready Testing**: Prepare for context-aware features and intelligent adaptations
- **Accessibility First**: WCAG 2.1 AA compliance validation in all tests

### **Quality Gates**
- **100% Functional Coverage**: When tests pass, features work in production
- **Zero Tolerance**: No broken tests, no skipped tests, no workarounds
- **Performance Validated**: All interactions meet speed requirements
- **Multi-Role Validation**: Every feature tested across all user roles

---

## 📊 Coverage Requirements

| Test Type | Tool Stack | Coverage Target | Requirement |
|-----------|------------|-----------------|-------------|
| **Unit Tests** | Vitest 3.1.1 | 90%+ on services/controllers | ✓ Business logic validation |
| **Integration Tests** | Vitest + Real DB | 100% API endpoints | ✓ API + Database + Validation |
| **End-to-End Tests** | Playwright 1.51.1 | All user workflows | ✓ Complete user journeys |
| **Accessibility Tests** | axe-core + Lighthouse | WCAG 2.1 AA compliance | ✓ Screen reader + keyboard nav |
| **Visual Tests** | Playwright Snapshots | All UI states | ✓ Layout + theme + responsive |
| **Performance Tests** | k6 + Lighthouse | Speed requirements | ✓ <100ms API, <200ms UI |

---

## 👥 Multi-Role Testing Matrix

### **Required Test Roles**
Test every feature and route with each role:

| Role | Access Level | Test Focus |
|------|-------------|------------|
| **Employee** | Basic access | Personal dashboard, goal setting, feedback |
| **Manager** | Team management | Team dashboard, member oversight, reporting |
| **Director** | Department oversight | Department analytics, strategic planning |
| **Admin** | Organization management | User management, system configuration |
| **Superadmin** | Full system access | All features + developer tools + impersonation |

---

## 🎨 Theme & Visual Testing

### **Theme Test Matrix**
All UI must work with every theme combination:

| Mode | Theme Variations | Test Requirements |
|------|------------------|-------------------|
| **Light Mode** | Emynent Default, Slate, Mint | Visual snapshots + accessibility |
| **Dark Mode** | Emynent Default, Slate, Mint | Visual snapshots + accessibility |
| **System Mode** | Auto-switch based on OS | Transition behavior testing |
| **Custom Themes** | Company-specific overrides | Color validation + WCAG compliance |

---

## 🔧 Testing Commands & Workflow

### **Local Development Commands**
```bash
# Core testing commands
npm test                           # Run all unit tests
npm run test:watch                 # Watch mode for development
npm run test:integration          # Run integration tests
npm run test:e2e                  # Run end-to-end tests
npm run test:visual               # Run visual regression tests
npm run test:a11y                 # Run accessibility tests
npm run test:performance          # Run performance tests

# Coverage and reporting
npm run test:coverage             # Generate coverage report
npm run test:coverage:open        # Open coverage report in browser

# Quality gates
npm run test:ci                   # Full CI test suite
npm run test:pre-commit           # Pre-commit validation
```

### **Continuous Integration Requirements**

All PRs must pass:
- ✅ Unit test suite (90%+ coverage)
- ✅ Integration test suite (100% API coverage)
- ✅ E2E test suite (critical user flows)
- ✅ Visual regression tests (theme/layout consistency)
- ✅ Accessibility audit (WCAG 2.1 AA compliance)
- ✅ Performance benchmarks (speed requirements met)

---

## 📋 Implementation Checklists

### **Before Writing Any Code**
- [ ] Write failing test that describes expected behavior
- [ ] Plan test data and scenarios needed
- [ ] Consider multi-role testing requirements
- [ ] Plan accessibility testing approach

### **During Development**
- [ ] Keep tests green while implementing
- [ ] Add integration tests for API changes
- [ ] Update visual snapshots for UI changes
- [ ] Test error scenarios and edge cases

### **Before PR Submission**
- [ ] All tests pass locally
- [ ] Coverage requirements met (90%+ for units, 100% for integration)
- [ ] Visual regression tests updated if UI changed
- [ ] Accessibility compliance verified
- [ ] Performance benchmarks met

---

## 🎯 Success Metrics

- **Zero Production Bugs**: Tests catch all issues before deployment
- **Fast Feedback Loop**: Test results available within 5 minutes
- **High Confidence Deployment**: Deploy without manual testing
- **Accessibility Compliance**: 100% WCAG 2.1 AA compliance
- **Performance Assurance**: All speed requirements consistently met

---

*This document represents the gold standard for testing practices in AI-native applications. Follow these guidelines to ensure robust, reliable, and user-friendly software that maintains quality at scale.*
