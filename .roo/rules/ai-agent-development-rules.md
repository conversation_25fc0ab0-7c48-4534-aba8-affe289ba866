---
description: Comprehensive AI-native development rules with TDD, first principles thinking, and user-centric design for building intelligent applications
globs: src/**/*.ts, src/**/*.tsx, tests/**/*.ts, tests/**/*.tsx, *.md
alwaysApply: true
---

# 🤖 AI Agent Development Rules & Engineering Guidelines (2025)

**The Definitive Guide for Building Agentic AI Applications with Excellence**

> This document establishes the fundamental principles, practices, and engineering rules for developing AI-native applications. Every decision should serve both immediate functionality and future AI capabilities.

## 📝 Related Documentation
- **Task Master Workflow**: See @dev_workflow.md for development processes
- **Testing Standards**: See @emynent-testing.md for comprehensive testing requirements  
- **Code Quality**: See @emynent-coding-rules.md for Emynent-specific standards
- **Architecture**: See @emynent-architecture.md for system design principles
- **Folder Structure**: See @emynent-folder-structure.md for organization guidelines

---

## 📋 Table of Contents

1. [Core Principles](#core-principles)
2. [AI-First Development Philosophy](#ai-first-development-philosophy)
3. [Testing Excellence](#testing-excellence)
4. [Engineering Standards](#engineering-standards)
5. [Architecture & Design](#architecture--design)
6. [Development Workflow](#development-workflow)
7. [Quality Assurance](#quality-assurance)
8. [Security & Performance](#security--performance)
9. [Implementation Guidelines](#implementation-guidelines)
10. [Tool Usage Rules](#tool-usage-rules)

---

## 🎯 Core Principles

### First Principles Thinking
> **Start with fundamental truths, not assumptions**

- Question every existing pattern and requirement
- Build from base principles rather than copying existing solutions
- Validate assumptions through real-world testing, not speculation
- Design for the problem at hand, not the problem you think exists

### User-Centric Design
> **Every feature must solve a real user problem**

- User needs drive all architectural decisions
- Features must be validated against actual user behavior
- Design for accessibility and inclusivity from day one
- Prioritize user value over technical elegance

### AI-Native Architecture
> **Intelligence built in, not bolted on**

- Every feature considers AI enhancement from conception
- Design data structures for ML from the start
- Build for adaptive, contextual user experiences
- Create extensible foundations for future AI capabilities

### No Technical Debt Tolerance
> **Quality is non-negotiable**

- Test-driven development is mandatory, not optional
- No shortcuts, workarounds, or "temporary" solutions
- 100% functional test coverage requirement
- Real data testing only - no mocking allowed

---

## 🧠 AI-First Development Philosophy

### Intelligence-First Design Pattern

```typescript
// ❌ Static, basic implementation
function createDashboard() {
  return <div>Static dashboard content</div>
}

// ✅ AI-ready, intelligence-first approach
function createDashboard({ userContext, behaviorPatterns, organizationData }) {
  const insights = generateInsights(userContext, behaviorPatterns)
  const adaptiveContent = personalizeContent(userContext)
  const predictions = forecastUserNeeds(behaviorPatterns)
  
  return (
    <Dashboard>
      <AdaptiveHeader context={userContext} />
      <PersonalizedContent content={adaptiveContent} />
      <InsightsPanel insights={insights} />
      <PredictiveRecommendations predictions={predictions} />
      <BehaviorTracker onAction={trackUserBehavior} />
    </Dashboard>
  )
}
```

### Behavioral Data Collection by Design

Every user interaction must be instrumented for future AI training:

```typescript
// ❌ Missing behavioral intelligence
function handleUserAction(action) {
  executeAction(action)
}

// ✅ Intelligence-aware interaction tracking
function handleUserAction(action, context) {
  // Capture rich behavioral data
  const behaviorData = {
    action: action.type,
    timestamp: Date.now(),
    userContext: {
      role: context.user.role,
      experience: context.user.experienceLevel,
      currentGoals: context.user.activeGoals,
      recentActions: context.user.recentActionHistory
    },
    environmentContext: {
      timeOfDay: new Date().getHours(),
      deviceType: context.device.type,
      sessionDuration: context.session.duration,
      pageContext: context.page.currentSection
    },
    organizationContext: {
      teamSize: context.organization.teamSize,
      industry: context.organization.industry,
      cultureProfile: context.organization.cultureProfile
    }
  }
  
  // Execute the action
  const result = executeAction(action)
  
  // Track outcome for learning
  trackBehaviorWithOutcome(behaviorData, result)
  
  // Generate contextual follow-up suggestions
  suggestNextActions(behaviorData, result)
  
  return result
}
```

### Smart Defaults and Contextual Intelligence

```typescript
// ❌ Static, one-size-fits-all approach
<input placeholder="Enter goal title" />

// ✅ Intelligent, context-aware forms
<SmartInput 
  placeholder={generateSmartPlaceholder(userRole, recentGoals, organizationContext)}
  defaultValue={suggestGoalTitle(userContext, teamPatterns)}
  onFocus={() => showContextualHints(userExperience, situationalContext)}
  onChange={(value) => provideLiveValidation(value, organizationStandards)}
  suggestions={generateSmartSuggestions(userHistory, peerBehavior)}
/>
```

### Agentic System Integration

```typescript
// Design for autonomous agent collaboration
interface AgentCapableComponent {
  // Allow AI agents to interact programmatically
  executeAgentAction: (action: AgentAction) => Promise<AgentResult>
  
  // Provide agent-readable state
  getAgentContext: () => AgentContext
  
  // Support agent-driven personalization
  applyAgentRecommendations: (recommendations: Recommendation[]) => void
  
  // Enable agent learning from interactions
  reportInteractionOutcome: (outcome: InteractionOutcome) => void
}
```

---

## 🧪 Testing Excellence

### Test-Driven Development (TDD) - Mandatory

> **No code without tests. No exceptions.**

#### The TDD Cycle (Red-Green-Refactor)

1. **Write a failing test** - Define expected behavior clearly
2. **Write minimal code** - Make the test pass with simplest solution
3. **Refactor ruthlessly** - Improve code quality while keeping tests green
4. **Repeat** - Continue until feature is complete

```typescript
// Example TDD implementation
describe('IntelligentGoalSuggestion', () => {
  it.todo('suggests goals based on user role and team patterns')
  it.todo('adapts suggestions based on user feedback over time')
  it.todo('provides fallback suggestions when ML model is unavailable')
  it.todo('filters suggestions based on organization constraints')
  
  // Implement first test to establish patterns
  it('suggests goals based on user role and team patterns', async () => {
    // Arrange
    const userContext = {
      role: 'engineering_manager',
      teamSize: 8,
      experienceLevel: 'senior'
    }
    
    const teamPatterns = {
      commonGoalTypes: ['technical_debt', 'team_growth', 'delivery_speed'],
      successfulGoalAttributes: ['specific', 'measurable', 'time_bound']
    }
    
    // Act
    const suggestions = await generateGoalSuggestions(userContext, teamPatterns)
    
    // Assert
    expect(suggestions).toHaveLength(3)
    expect(suggestions[0]).toMatchObject({
      type: expect.stringMatching(/technical_debt|team_growth|delivery_speed/),
      title: expect.stringContaining('engineering'),
      confidence: expect.numberMatching(n => n >= 0.7)
    })
  })
})
```

### No Mocking Policy

> **Test with real data, real scenarios, real conditions**

```typescript
// ❌ Mocked, unrealistic testing
const mockUserService = {
  getUser: jest.fn().mockReturnValue({ id: 1, name: 'Test User' })
}

// ✅ Real data, real scenarios
describe('UserDashboard Integration', () => {
  it('displays real user data with proper error handling', async () => {
    // Use real test database with real user data
    const testUser = await createTestUser({
      role: 'manager',
      teamMembers: 5,
      hasActiveGoals: true
    })
    
    // Test real API calls
    const dashboard = await loadUserDashboard(testUser.id)
    
    // Verify real data integration
    expect(dashboard.userInfo.name).toBe(testUser.name)
    expect(dashboard.goals).toHaveLength(testUser.activeGoals.length)
    
    // Test real error scenarios
    await expect(loadUserDashboard('invalid-id')).rejects.toThrow('User not found')
  })
})
```

### 100% Functional Test Coverage

Every feature must have comprehensive test coverage:

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test component interactions
- **End-to-End Tests**: Test complete user workflows
- **AI Behavior Tests**: Test intelligent features and adaptations
- **Performance Tests**: Verify response times and resource usage
- **Accessibility Tests**: Ensure WCAG 2.1 AA compliance

---

## ⚙️ Engineering Standards

### Code Quality Standards

#### Function Design
```typescript
// ❌ Poor function design
function doStuff(x, y, z) {
  // Multiple responsibilities
  const result1 = calculateSomething(x)
  const result2 = validateSomething(y)
  const result3 = formatSomething(z)
  return [result1, result2, result3]
}

// ✅ Single Responsibility Principle
function calculateUserEngagementScore(userActions: UserAction[]): EngagementScore {
  validateUserActions(userActions)
  
  const engagementMetrics = analyzeUserBehavior(userActions)
  const score = computeEngagementScore(engagementMetrics)
  
  return {
    score,
    factors: engagementMetrics,
    confidence: calculateConfidence(engagementMetrics),
    timestamp: Date.now()
  }
}
```

#### Variable Naming Standards
```typescript
// ❌ Unclear, abbreviated names
const usr = getCurrentUser()
const calcMPScore = (data) => data.reduce((acc, val) => acc + val.score, 0)

// ✅ Descriptive, intention-revealing names
const currentUser = getCurrentUser()
const calculateMarketPerformanceScore = (performanceData: PerformanceMetric[]) => 
  performanceData.reduce((totalScore, metric) => totalScore + metric.score, 0)
```

### Error Handling Excellence

```typescript
// ❌ Poor error handling
async function loadUserData(userId) {
  const user = await userApi.getUser(userId)
  return user
}

// ✅ Comprehensive error handling
async function loadUserData(userId: string): Promise<UserData> {
  try {
    validateUserId(userId)
    
    const user = await userApi.getUser(userId)
    
    if (!user) {
      throw new UserNotFoundError(`User ${userId} not found`)
    }
    
    await trackUserDataAccess(userId)
    
    return enrichUserData(user)
    
  } catch (error) {
    if (error instanceof UserNotFoundError) {
      logger.warn('User lookup failed', { userId, error: error.message })
      throw error
    }
    
    if (error instanceof NetworkError) {
      logger.error('Network error during user data load', { userId, error })
      throw new ServiceUnavailableError('User service temporarily unavailable')
    }
    
    logger.error('Unexpected error loading user data', { userId, error })
    throw new InternalServerError('Failed to load user data')
  }
}
```

### Performance Standards

#### Response Time Requirements
- **API Responses**: < 100ms for simple queries, < 500ms for complex operations
- **UI Interactions**: < 200ms for all user actions
- **Page Load Times**: < 1.5s for initial load, < 500ms for navigation
- **AI Model Inference**: < 2s for real-time suggestions, < 5s for complex analysis

#### Code Optimization Patterns
```typescript
// ✅ Optimized React component with proper memoization
const SmartDashboard = memo(({ userId, filters }: DashboardProps) => {
  // Memoize expensive calculations
  const dashboardData = useMemo(() => 
    computeDashboardMetrics(userId, filters), [userId, filters]
  )
  
  // Debounce user inputs
  const debouncedSearch = useMemo(() => 
    debounce((query: string) => performSearch(query), 300), []
  )
  
  // Lazy load heavy components
  const AdvancedAnalytics = lazy(() => import('./AdvancedAnalytics'))
  
  return (
    <div className="dashboard">
      <BasicMetrics data={dashboardData.basic} />
      <Suspense fallback={<AnalyticsPlaceholder />}>
        <AdvancedAnalytics data={dashboardData.advanced} />
      </Suspense>
    </div>
  )
})
```

### **Configuration Management**
- **Environment Variables**: Store in `.env.local` for development, never commit `.env` files
- **Feature Flags**: Use `hasFeature(companyId, featureKey)` pattern (see @emynent-coding-rules.md)
- **Database Configuration**: Follow Prisma best practices (see @emynent-tech-stack.md)
- **API Configuration**: RESTful design with OpenAPI documentation

### **Performance Standards**
- **API Response Time**: <100ms for critical endpoints (validated with k6 load testing)
- **UI Interactions**: <200ms for all user interactions
- **Database Queries**: Optimize for <50ms response time
- **Bundle Size**: <500KB for production builds
- **Web Vitals**: LCP <2.5s, FCP <1.5s, CLS <0.1 (see @emynent-testing.md)

---

## 🏗️ Architecture & Design

### Multi-Tenant Architecture

```typescript
// ✅ Tenant-aware data access
interface TenantAwareService {
  tenantId: string
  
  async getData<T>(query: Query): Promise<T[]> {
    // Automatic tenant isolation
    const tenantQuery = this.addTenantConstraint(query)
    return this.dataAccess.query(tenantQuery)
  }
  
  private addTenantConstraint(query: Query): Query {
    return {
      ...query,
      where: {
        ...query.where,
        tenantId: this.tenantId
      }
    }
  }
}
```

### AI-Ready Data Architecture

```typescript
// ✅ ML-optimized data structures
interface UserBehaviorEvent {
  // Core event data
  id: string
  userId: string
  tenantId: string
  timestamp: number
  eventType: string
  
  // AI feature data
  context: {
    userRole: string
    experienceLevel: number
    sessionContext: SessionContext
    organizationContext: OrganizationContext
  }
  
  // ML training labels
  outcome: {
    successful: boolean
    userSatisfaction: number
    taskCompletion: boolean
    timeToComplete: number
  }
  
  // Feature vectors for ML
  features: {
    numerical: Record<string, number>
    categorical: Record<string, string>
    embeddings: Record<string, number[]>
  }
}
```

### Component Design Standards

```typescript
// ✅ Intelligent, reusable component design
interface SmartComponentProps {
  // Core functionality
  data: ComponentData
  
  // AI enhancement hooks
  userContext?: UserContext
  onBehaviorTrack?: (event: BehaviorEvent) => void
  intelligenceLevel?: 'basic' | 'adaptive' | 'predictive'
  
  // Accessibility
  accessibilityLabel: string
  ariaDescribedBy?: string
  
  // Testing
  testId?: string
}

const SmartGoalInput: React.FC<SmartComponentProps> = ({
  userContext,
  onBehaviorTrack,
  intelligenceLevel = 'adaptive',
  accessibilityLabel,
  testId
}) => {
  // AI-powered features based on intelligence level
  const suggestions = useSmartSuggestions(userContext, intelligenceLevel)
  const validation = useIntelligentValidation(userContext)
  
  return (
    <div data-testid={testId}>
      <label>{accessibilityLabel}</label>
      <Input
        suggestions={suggestions}
        validation={validation}
        onInput={(value) => onBehaviorTrack?.({
          type: 'goal_input',
          value,
          context: userContext
        })}
        aria-label={accessibilityLabel}
      />
    </div>
  )
}
```

### **System Architecture**
- **Client-Server Separation**: Follow controller-service pattern (see @emynent-architecture.md)
- **Database Design**: Use Prisma with PostgreSQL (see @emynent-tech-stack.md)
- **API Design**: RESTful with feature-based organization
- **State Management**: Context + custom hooks pattern
- **Error Handling**: Comprehensive try-catch with user-friendly messages

---

## 🔄 Development Workflow

### Git Workflow Standards

```bash
# Branch naming convention
feature/TASK-123-intelligent-goal-suggestions
bugfix/TASK-456-dashboard-performance-issue
hotfix/TASK-789-critical-security-patch

# Commit message format
feat(goals): implement AI-powered goal suggestions

- Add ML-based goal recommendation engine
- Include user behavior analysis for personalization
- Implement fallback suggestions for new users
- Add comprehensive test coverage for all scenarios

Tests: ✅ Unit, Integration, E2E
Performance: ✅ <200ms response time
Security: ✅ OWASP compliance verified
Accessibility: ✅ WCAG 2.1 AA compliant

Co-authored-by: AI-Agent <<EMAIL>>
```

### Code Review Standards

Every pull request must include:

1. **Comprehensive Tests**: Unit, integration, and E2E tests
2. **Performance Validation**: Response time measurements
3. **Security Review**: OWASP compliance check
4. **Accessibility Audit**: WCAG 2.1 AA compliance verification
5. **AI Enhancement Plan**: How the feature will evolve with AI
6. **Documentation Update**: API docs, user guides, architecture notes

### Continuous Integration Pipeline

```yaml
# .github/workflows/quality-gate.yml
name: Quality Gate

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Run Tests
        run: |
          npm run test:unit
          npm run test:integration
          npm run test:e2e
      
      - name: Coverage Check
        run: |
          npm run test:coverage
          # Fail if coverage < 100%
          
      - name: Performance Testing
        run: |
          npm run test:performance
          # Fail if any endpoint > 500ms
          
      - name: Security Scan
        run: |
          npm run security:scan
          npm run security:dependencies
          
      - name: Accessibility Testing
        run: |
          npm run test:a11y
          # Fail if WCAG 2.1 AA violations found
```

---

## 🛡️ Security & Performance

### Security-First Development

```typescript
// ✅ Secure data handling
class SecureUserService {
  async updateUserData(
    userId: string, 
    data: Partial<UserData>, 
    requestingUser: User
  ): Promise<UserData> {
    // Authentication verification
    await this.verifyAuthentication(requestingUser)
    
    // Authorization check
    await this.verifyPermissions(requestingUser, 'user:update', userId)
    
    // Input validation and sanitization
    const sanitizedData = this.sanitizeInput(data)
    const validatedData = await this.validateUserData(sanitizedData)
    
    // Audit logging
    await this.auditLog({
      action: 'user_update',
      actor: requestingUser.id,
      target: userId,
      changes: validatedData,
      timestamp: Date.now()
    })
    
    // Secure update with encryption
    const encryptedData = await this.encryptSensitiveFields(validatedData)
    const updatedUser = await this.userRepository.update(userId, encryptedData)
    
    // Return sanitized response (no sensitive data)
    return this.sanitizeResponse(updatedUser)
  }
}
```

### Performance Optimization

```typescript
// ✅ Performance-optimized data loading
class PerformantDataService {
  private cache = new Map<string, CacheEntry>()
  
  async loadDashboardData(userId: string): Promise<DashboardData> {
    // Check cache first
    const cached = this.getCachedData(userId)
    if (cached && !this.isExpired(cached)) {
      return cached.data
    }
    
    // Parallel data loading
    const [user, goals, metrics, insights] = await Promise.all([
      this.loadUser(userId),
      this.loadUserGoals(userId),
      this.loadMetrics(userId),
      this.generateInsights(userId)
    ])
    
    // Combine and cache
    const dashboardData = this.combineDashboardData({
      user, goals, metrics, insights
    })
    
    this.cacheData(userId, dashboardData)
    
    return dashboardData
  }
  
  private async generateInsights(userId: string): Promise<Insight[]> {
    // Use streaming for AI-generated content
    const insightStream = await this.aiService.generateInsightsStream(userId)
    
    // Return immediately with placeholder, update via WebSocket
    this.streamInsightsToClient(userId, insightStream)
    
    return [] // Placeholder insights
  }
}
```

---

## 🛠️ Tool Usage Rules

### IDE and AI Assistant Integration

#### Roo Code/VS Code/PyCharm/etc. Rules
- Use consistent formatting across all IDEs (Prettier, ESLint)
- Enable AI assistance for code completion, not code generation
- Maintain consistent shortcuts and configurations
- Use IDE-agnostic file structures and naming conventions

#### AI Pair Programming Protocol
```typescript
// When working with AI assistants:

// 1. Always write tests first
describe('FeatureName', () => {
  it.todo('should behave as expected in scenario A')
  it.todo('should handle edge case B correctly')
  it.todo('should fail gracefully when C occurs')
})

// 2. Implement one test to establish patterns
it('should behave as expected in scenario A', () => {
  // Provide clear example for AI to follow
})

// 3. Let AI generate remaining tests based on pattern
// 4. Review and refine AI-generated code
// 5. Use AI for implementation after tests are solid
```

### Continuous Learning Integration

```typescript
// Document patterns for AI learning
interface DevelopmentPattern {
  name: string
  description: string
  example: CodeExample
  antiPattern: CodeExample
  applicableScenarios: string[]
  measuredOutcomes: {
    performanceImprovement: number
    maintainabilityScore: number
    bugReduction: number
  }
}

// Track successful patterns
const PROVEN_PATTERNS: DevelopmentPattern[] = [
  {
    name: 'IntelligentFormComponent',
    description: 'Form components with built-in AI enhancement hooks',
    example: smartFormExample,
    antiPattern: staticFormExample,
    applicableScenarios: ['user input', 'data collection', 'feedback forms'],
    measuredOutcomes: {
      performanceImprovement: 0.15,
      maintainabilityScore: 0.25,
      bugReduction: 0.40
    }
  }
]
```

### **Folder Structure Requirements**
**Follow established patterns** (see @emynent-folder-structure.md for complete structure):

**Key Directories**:
- `/src/app/` → Next.js App Router (routes and layouts)
- `/src/components/` → Reusable UI components organized by domain
- `/src/controllers/` → API request handlers (see /src/controllers/auth-controller.ts)
- `/src/services/` → Business logic (see /src/services/auth-service.ts)  
- `/src/lib/` → Utilities and shared logic (see /src/lib/auth/)
- `/src/types/` → TypeScript definitions
- `/tests/` → Test files (see @emynent-testing.md)
- `/domains/` → Domain-driven feature modules
- `/packages/` → Shared libraries

### **Component Development**
- **Use Design System**: Source from `/src/components/ui` and `/lib/design-system`
- **Check for Duplicates**: Search existing components before creating new ones

### **API Development**
- **Controller Pattern**: Follow `/src/controllers/auth-controller.ts` example
- **Service Layer**: Reference `/src/services/auth-service.ts` for business logic patterns
- **Validation**: Use Zod schemas from `/src/lib/validators/`
- **Database**: Follow Prisma patterns in `/prisma/schema.prisma`

---

## 📏 Quality Measurement

### Success Metrics

#### Code Quality Metrics
- **Test Coverage**: 100% functional coverage required
- **Performance**: All interactions < 200ms, APIs < 100ms
- **Accessibility**: WCAG 2.1 AA compliance verified
- **Security**: Zero high/critical vulnerabilities
- **Maintainability**: Complexity score < 10 per function

#### AI Enhancement Metrics
- **User Engagement**: Measure interaction depth and frequency
- **Personalization Effectiveness**: A/B test AI vs. static features
- **Prediction Accuracy**: Track AI suggestion acceptance rates
- **Adaptation Speed**: Measure time to learn user preferences

#### Business Impact Metrics
- **Feature Adoption**: Track usage of intelligent features
- **User Satisfaction**: Measure NPS improvement with AI features
- **Development Velocity**: Compare delivery speed with/without AI
- **Technical Debt**: Track accumulation and resolution rates

### Continuous Improvement Process

```typescript
// Automated quality assessment
interface QualityGate {
  checkTestCoverage(): Promise<CoverageReport>
  measurePerformance(): Promise<PerformanceReport>
  scanSecurity(): Promise<SecurityReport>
  auditAccessibility(): Promise<AccessibilityReport>
  evaluateAIFeatures(): Promise<AIQualityReport>
}

// Quality gates that block deployment
const qualityGate = new QualityGate()

async function validateDeployment(): Promise<boolean> {
  const results = await Promise.all([
    qualityGate.checkTestCoverage(),
    qualityGate.measurePerformance(),
    qualityGate.scanSecurity(),
    qualityGate.auditAccessibility(),
    qualityGate.evaluateAIFeatures()
  ])
  
  return results.every(report => report.passed)
}
```

---

## 🎯 Implementation Checklist

### Before Starting Any Feature

- [ ] Understand the user problem being solved
- [ ] Design data structures for future AI enhancement
- [ ] Plan behavioral data collection strategy
- [ ] Define success metrics and measurement approach
- [ ] Write comprehensive test scenarios
- [ ] Consider accessibility requirements
- [ ] Plan for multi-tenant architecture
- [ ] Design for performance and scalability

### During Development

- [ ] Write tests before implementation (TDD)
- [ ] Use real data, never mock in tests
- [ ] Implement proper error handling
- [ ] Add behavioral tracking to all interactions
- [ ] Follow naming conventions and code standards
- [ ] Optimize for performance requirements
- [ ] Ensure security best practices
- [ ] Validate accessibility compliance

### Before Deployment

- [ ] 100% test coverage achieved
- [ ] Performance requirements met
- [ ] Security scan passed
- [ ] Accessibility audit completed
- [ ] AI enhancement hooks implemented
- [ ] Documentation updated
- [ ] Code review completed
- [ ] Quality gates passed

---

## 🔗 Related Documents

- **Task Master Development Workflow**: See `dev_workflow.md`
- **Testing Guidelines**: See `tests.md`
- **Architecture Decisions**: See project architecture documentation
- **Security Standards**: See security compliance documentation
- **AI Model Integration**: See AI services documentation

---

## 📝 Document Maintenance

This document is updated quarterly or when significant patterns emerge. All team members are responsible for contributing improvements based on real-world development experiences.

**Last Updated**: January 2025  
**Next Review**: April 2025  
**Maintainers**: Development Team, AI Engineering Team

---

*This document represents the collective wisdom and proven practices of building AI-native applications. Follow these guidelines to create software that is not only functional today but ready for the intelligent future.* 

## ✅ **Implementation Checklists**

### **Before Starting Any Feature**
- [ ] Review @emynent-scope.md to ensure feature is in scope
- [ ] Check @emynent-folder-structure.md for correct file placement
- [ ] Search `/src/components/`, `/src/services/`, `/src/lib/` for existing solutions
- [ ] Review related tests in `/tests/` directory
- [ ] Verify API key configuration in `.env.example`

### **Component Development Checklist**
- [ ] Component placed in correct `/src/components/` subdirectory
- [ ] Uses design tokens from `/src/lib/design-system/`
- [ ] Implements accessibility (see @emynent-testing.md for a11y requirements)
- [ ] Includes TypeScript interfaces in `/src/types/`
- [ ] Has corresponding test file in `/tests/` or co-located `.test.tsx`
- [ ] Follows theming patterns from `/src/components/theme/`

### **API Development Checklist**
- [ ] Controller created in `/src/controllers/` following auth-controller.ts pattern
- [ ] Service logic separated in `/src/services/` following auth-service.ts pattern
- [ ] Validation schema added to `/src/lib/validators/`
- [ ] Database models updated in `/prisma/schema.prisma`
- [ ] Integration tests created in `/tests/integration/`
- [ ] Error handling follows patterns in existing controllers

### **Testing Implementation Checklist**
- [ ] Unit tests achieve 80%+ coverage (see @emynent-testing.md)
- [ ] Integration tests cover API endpoints
- [ ] E2E tests cover user workflows using Playwright
- [ ] Visual regression tests for UI components
- [ ] Accessibility tests with axe-core validation
- [ ] Performance tests meet requirements (<100ms API, <200ms UI)

### **Before Code Review**
- [ ] All tests pass (no exceptions per @ai-agent-development-rules.md)
- [ ] Code follows patterns in @emynent-coding-rules.md
- [ ] Documentation updated in relevant `/docs/` files
- [ ] No console.log statements in production code
- [ ] TypeScript strict mode compliance
- [ ] No duplicate code (search_files existing codebase first)

---

## 🔄 **Rule Maintenance & Updates**

### **When to Update This Document**
- **New Patterns Emerge**: When 3+ files use the same pattern, codify it as a rule
- **Codebase Evolution**: When architecture changes (update references to actual files)
- **Tool Updates**: When dependencies in package.json change significantly
- **Testing Improvements**: When @emynent-testing.md standards evolve

### **Cross-Reference Maintenance**
- **Keep File References Current**: Update @filename references when files move
- **Sync with Related Rules**: Ensure consistency with @emynent-coding-rules.md
- **Update Examples**: Replace theoretical examples with actual codebase examples
- **Remove Outdated Patterns**: Deprecate rules that no longer apply to current tech stack

### **Contributing to Rules**
- **Submit Changes**: Update based on real development challenges
- **Provide Examples**: Use actual code from `/src/` directory in examples  
- **Test Rule Changes**: Ensure new rules work with existing @emynent-testing.md standards
- **Document Impact**: Explain how changes affect @emynent-architecture.md compliance

--- 