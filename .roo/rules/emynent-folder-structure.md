---
description: 
globs: 
alwaysApply: true
---
# 🧠 Emynent Project Folder Structure Guide (AI-First, Clean, and Scalable)

This document defines the **current**, **AI-native**, **domain-driven**, and **scalable** folder structure for the Emynent project, optimized for a single-repository client-server architecture. It supports **multi-IDE environments** (Roo Code, PyCharm, VSCode) and **coding assistants**, ensuring seamless understanding of the codebase.

**Status: IMPLEMENTED ✅** - This reflects the actual current structure as of January 2025.

---

## 🌳 Root Directory Layout

```bash
/emynent
├── src/                 # Core application source (client-server split)
├── domains/             # Business and product domains  
├── packages/            # Shared design, types, utils, AI logic
├── infra/               # Infra-as-code, CI/CD, containerization
├── scripts/             # Project automation, dev tools, AI helpers
├── tests/               # Global test configurations and mocks
├── archive/             # Archived legacy modules
├── docs/                # Project documentation
│   ├── adr/                  # Architecture Decision Records
│   ├── security/             # Security documentation
│   └── workflows/            # Cross-domain workflows
├── config/              # Configuration management
│   ├── environments/         # Environment configurations
│   ├── testing/              # Test configurations
│   ├── typescript/           # TypeScript configurations
│   └── linting/             # ESLint configurations  
├── public/              # Static assets (Next.js)
├── prisma/              # Database schema and migrations
├── tasks/               # Task Master files
├── .github/             # GitHub Actions workflows
├── .roo/             # Roo Code AI rules and configuration
├── package.json         # Dependencies and scripts (npm)
├── tsconfig.json        # TypeScript configuration
├── next.config.js       # Next.js configuration
├── tailwind.config.ts   # Tailwind CSS configuration
├── vitest.config.ts     # Vitest testing configuration
├── middleware.ts        # Next.js middleware
└── README.md            # Project overview and setup
```

🧠 **AI Agents Start Here:** Use context-map.json in each domain folder for grounding. Avoid /archive/ unless instructed. Log changes in docs/.

---

## 🧩 /src — Core Application (Client-Server Split)

```bash
/src
├── app/                 # Next.js App Router (routes and layouts)
│   ├── (protected)/
│   │   ├── layout.tsx         # Persistent layout (navbar, sidebar, theming)
│   │   ├── dashboard/         # Dashboard pages
│   │   ├── settings/          # Settings panel with appearance theming
│   │   ├── superadmin/        # Superadmin panel
│   │   ├── admin/             # Admin dashboard
│   │   └── employee/          # Employee dashboard
│   ├── (admin)/               # Admin-only routes
│   ├── api/                   # Server-side API routes (feature-based)
│   │   ├── auth/              # Authentication endpoints
│   │   ├── analytics/         # Analytics and tracking
│   │   ├── design-system/     # Design system APIs
│   │   ├── intelligence/      # AI and intelligence APIs  
│   │   ├── collaboration/     # Real-time collaboration
│   │   ├── personalization/   # User personalization
│   │   ├── settings/          # Settings management
│   │   ├── users/             # User management
│   │   ├── companies/         # Company management
│   │   ├── onboarding/        # Onboarding flow APIs
│   │   └── websocket/         # WebSocket endpoints
│   ├── auth/                  # Auth pages (signin, signup)
│   ├── onboarding/            # Onboarding flow pages
│   ├── error/                 # Error pages
│   ├── providers/             # Context providers
│   └── layout.tsx             # Root layout
├── components/          # Reusable UI components
│   ├── context-aware/         # Context-aware UI components
│   ├── onboarding/            # Onboarding components
│   ├── auth/                  # Authentication UI
│   ├── shared/                # Generic shared components
│   ├── ai/                    # AI-driven UI components
│   ├── analytics/             # Analytics components
│   ├── collaboration/         # Collaboration UI
│   ├── design-system/         # Design system components
│   ├── intelligence/          # Intelligence UI
│   ├── layout/                # Layout components
│   ├── personalization/       # Personalization UI
│   ├── settings/              # Settings components
│   ├── superadmin/            # Superadmin UI
│   ├── theme/                 # Theme-related components
│   ├── ui/                    # Base UI components
│   └── websocket/             # WebSocket components
├── controllers/         # API request handlers
│   ├── auth-controller.ts     # Authentication logic
│   ├── user-controller.ts     # User management
│   ├── theme-controller.ts    # Theme management
│   ├── context.ts             # Context-aware handlers
│   ├── gen-ai.ts              # Gen AI controllers
│   ├── ai-recommendations.ts  # AI recommendation logic
│   └── behavioral-analytics.ts # Analytics controllers
├── services/            # Stateless business logic
│   ├── auth-service.ts        # Authentication services
│   ├── user-service.ts        # User services
│   ├── theme-service.ts       # Theme services
│   ├── onboarding-service.ts  # Onboarding logic
│   ├── settings-service.ts    # Settings management
│   ├── rbac-service.ts        # Role-based access control
│   ├── redis-service.ts       # Redis caching
│   ├── session-service.ts     # Session management
│   ├── context-service.ts     # Context-aware services
│   ├── ai-recommendations.ts  # AI recommendation services
│   ├── behavioral-analytics.ts # Analytics services
│   └── intelligence/          # Intelligence services
├── lib/                 # Utilities and shared logic
│   ├── auth/                  # Authentication utilities
│   ├── design-system/         # Design system utilities
│   ├── analytics/             # Analytics utilities
│   ├── collaboration/         # Collaboration utilities
│   ├── context/               # Context utilities
│   ├── hooks/                 # Custom React hooks
│   ├── intelligence/          # Intelligence utilities
│   ├── personalization/       # Personalization utilities
│   ├── services/              # Service utilities
│   ├── utils/                 # General utilities
│   ├── validators/            # Zod validation schemas
│   └── websocket/             # WebSocket utilities
├── hooks/               # Custom React hooks
├── types/               # TypeScript type definitions
├── tests/               # Component and unit tests
└── README.md            # App structure overview
```

---

## 🧠 /domains — Domain-Driven Feature Modules

```bash
/domains
├── auth/
│   ├── client/                # UI: LoginForm.tsx, SignupForm.tsx
│   ├── server/                # API logic: login.handler.ts
│   ├── shared/                # Types: AuthRole.ts, AuthTypes.ts
│   ├── __tests__/             # Domain-specific tests
│   │   ├── client/
│   │   ├── server/
│   │   └── integration/
│   ├── journeys.md            # Login, logout, registration flows
│   ├── context-map.json       # AI entrypoints and navigation
│   └── README.md              # Domain documentation
├── settings/
│   ├── client/                # UI: AppearancePanel.tsx, SettingsNav.tsx  
│   ├── server/                # Logic: theme.handler.ts, settings.handler.ts
│   ├── shared/                # Types: ThemeConfig.ts, SettingsTypes.ts
│   ├── __tests__/             # Domain-specific tests
│   │   ├── client/
│   │   ├── server/
│   │   └── integration/
│   ├── journeys.md            # Settings flows, theme selection
│   ├── context-map.json       # AI entrypoints and navigation
│   └── README.md              # Domain documentation
└── [Additional domains as needed]
```

---

## 📦 /packages — Shared Libraries

```bash
/packages
├── design-system/
│   ├── src/
│   │   ├── components/        # Shared UI: Button.tsx, Modal.tsx
│   │   ├── design-system/     # Core design system logic
│   │   ├── themes/            # Theme configurations
│   │   └── utils/             # Design system utilities
│   └── README.md
├── ai-agents/
│   ├── src/                   # AI agent logic and utilities
│   └── README.md
├── telemetry/
│   ├── src/
│   │   ├── metrics/           # Performance metrics
│   │   └── logging/           # Centralized logging
│   └── README.md
├── reusables/
│   ├── src/                   # Pre-built component templates
│   └── README.md
├── utils/
│   ├── src/                   # Shared utility functions
│   └── README.md
├── types/
│   ├── src/                   # Global TypeScript interfaces
│   └── README.md
└── README.md
```

---

## ⚙️ /infra — Infrastructure

```bash
/infra
├── ci/                        # GitHub Actions workflows
├── config/                    # Infrastructure configurations
├── docker/                    # Dockerfiles and containers
└── README.md
```

---

## 🧪 /tests — Global Testing Configuration

```bash
/tests
├── a11y/                      # Accessibility tests
├── behavioral-analytics/      # Analytics testing
├── config/                    # Test configurations
├── critical/                  # Critical path tests
├── design-system/             # Design system tests
├── fixtures/                  # Test data and fixtures
├── integration/               # Integration tests
├── intelligence/              # AI/Intelligence tests
├── mocks/                     # Mock data and services
├── phase2/                    # Phase-specific tests
├── phase3/                    # Phase-specific tests
├── phase4/                    # Phase-specific tests
├── theme-snapshots/           # Visual regression tests
├── utils/                     # Test utilities
├── websocket/                 # WebSocket tests
├── global-setup.ts            # Global test setup
├── setup.ts                   # Test environment setup
└── README.md
```

---

## 🧹 /scripts — Automation and Tooling

```bash
/scripts
├── [Various automation scripts]
└── README.md
```

---

## 📦 /archive — Legacy Code

```bash
/archive
├── legacy_monolith/           # Pre-migration code
├── unused_endpoints/          # Deprecated API endpoints
└── README.md
```

---

## ✨ AI + Developer Journey Design

Each domain includes:
- **README.md**: Purpose, key files, and documentation
- **context-map.json**: AI entrypoints and navigation (e.g., `/api/auth/login`)
- **journeys.md**: User flows and business logic (e.g., login → dashboard)

**AI Agent Instructions:**
- Prioritize `context-map.json` for navigation
- Avoid `/archive/` unless explicitly instructed
- Log changes in `/docs/`

---

## 🧭 Navigation Guide (Human + AI)

| Task | Current File Path |
|------|-------------------|
| Fix login error | `/src/lib/auth/config.ts` |
| Debug API login | `/src/app/api/auth/login/route.ts` |
| Debug theming | `/src/app/(protected)/layout.tsx` |
| Fix Appearance panel | `/domains/settings/client/` or `/src/components/settings/` |
| Restore onboarding | `/src/components/onboarding/` |
| Debug onboarding API | `/src/app/api/onboarding/` |
| Review auth flow | `/domains/auth/journeys.md` |
| Update controllers | `/src/controllers/` |
| Update services | `/src/services/` |
| Design system work | `/packages/design-system/` |
| AI/Intelligence | `/src/components/ai/` or `/src/services/intelligence/` |
| Analytics | `/src/components/analytics/` |
| WebSocket features | `/src/components/websocket/` |
| Test files | `/tests/` |
| Configuration | `/config/` |

---

## ✅ Current Status Summary

This structure is:

- **✅ Client-Server Ready**: Fully migrated architecture
- **✅ Design System Compliant**: Supports consistency, theming, accessibility
- **✅ AI-Friendly**: Optimized for Roo Code and AI assistants
- **✅ Domain-Driven**: Clear separation of business domains
- **✅ Battle-Tested**: Proven in production environment
- **✅ Feature-Complete**: All major functionality implemented

**Key Strengths:**
- Feature-based API organization (more maintainable than versioned)
- Comprehensive component organization by domain
- AI-friendly context mapping and documentation
- Proper separation of concerns across layers
- Extensive testing infrastructure

**This represents the current, working implementation as of January 2025.**
