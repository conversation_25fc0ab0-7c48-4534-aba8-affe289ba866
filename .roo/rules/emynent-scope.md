---
description: 
globs: 
alwaysApply: true
---
# Emynent Enhanced MVP Scope and Development Rules

## Project Overview
Emynent is an **AI-native organizational intelligence platform** designed to transform how growth-stage companies understand and empower their people. This Enhanced MVP validates core functionalities using **Test-Driven Development (TDD)**, **first principles thinking**, and **user-centric design** with **hyper-personalization** at its core. Built with Next.js 15+ (App Router), Prisma, NextAuth.js, and Tailwind + shadcn/ui design system, featuring intelligent behavioral analytics and context-aware placeholders for future Gen AI integration.

## Business Purpose & Vision
Emynent enables structured career frameworks and skills progression through intelligent, personalized experiences:
- **Hyper-Personalized Self-Assessment**: Adaptive skill development paths based on individual behavior patterns and career aspirations
- **Intelligent Manager Oversight**: AI-enhanced insights for training needs identification and team development
- **Predictive Strategic Visibility**: Data-driven visibility for department leads with behavioral trend analysis

## Enhanced MVP Goals & Objectives
- **Career Visibility**: Clear, personalized career progression paths with intelligent recommendations
- **Skill Management**: Context-aware tracking and development with behavioral analytics
- **RBAC Security**: Role-based access control with audit logging and compliance
- **User-Centric UX/UI**: Adaptive interface based on user behavior and preferences  
- **Multi-tenancy**: Company-level data isolation with cross-tenant intelligence (anonymized)
- **Client-Server Architecture**: Modular, scalable architecture with intelligent caching
- **Behavioral Intelligence**: Real-time user behavior tracking and pattern analysis
- **Hyper-Personalization Engine**: Dynamic UI adaptation based on user context and patterns
- **Context-Awareness Foundation**: Comprehensive placeholders for Gen AI integration across all entry points

## Development Philosophy & Principles

### **Test-Driven Development (TDD)**
- **Red-Green-Refactor Cycle**: Write failing tests first, implement minimal code to pass, then refactor
- **100% Functional Tests**: When tests pass, real functionality works - no mocking or workarounds
- **Real Data Testing**: All tests use actual database operations, API calls, and UI interactions
- **No Mock Dependencies**: Test against real services to ensure production readiness

### **First Principles Thinking**
- **Question Assumptions**: Challenge conventional HR platform patterns
- **Build from Fundamentals**: Design each feature from core user needs, not industry standards
- **Minimize Complexity**: Simplest solution that delivers maximum user value
- **Evidence-Based Decisions**: All features validated through user research and behavioral data

### **User-Centric Design**
- **Behavioral Science Integration**: Apply psychology principles to drive engagement
- **Continuous User Feedback**: Real-time feedback collection and iteration
- **Accessibility First**: WCAG 2.1 AA compliance as baseline, not afterthought
- **Performance as UX**: Sub-300ms interactions for all user-facing operations

### **Hyper-Personalization**
- **Individual Context Awareness**: Adapt to each user's role, preferences, and behavior patterns
- **Dynamic Interface Evolution**: UI that learns and adapts to user interaction patterns
- **Behavioral Trigger Points**: Smart prompts and suggestions at optimal moments
- **Cross-Company Intelligence**: Anonymous behavioral insights to improve platform-wide UX

## MVP Exit Criteria
- No regressions; all features fully functional
- Superadmin access to dashboard and panel via Settings
- Authentication and tenant isolation enforced in production
- All scoped documentation complete and accessible
- No critical QA or usability bugs
- WCAG 2.1 AA accessibility verified via Lighthouse/axe
- All E2E tests pass in CI

## Non-Goals (MVP)
- No microservices or multi-repo conversion
- No RBAC schema changes unless scoped
- No UX redesigns or new navigation patterns
- No HR tool integrations
- No new design systems or forks
- No internationalization (i18n) or localization
- No active Gen AI implementation for context-awareness, insights, recommendations, or coaching, though placeholders are included for future integration across all entry points (e.g., expanded `UserContext` schema, context-aware controllers).

## User Roles and Permissions
| Role       | Inherits From | Pages Accessed                                  |
|------------|---------------|-------------------------------------------------|
| Employee   | —             | Dashboard, Settings                             |
| Manager    | Employee      | Team dashboard                                  |
| Director   | Manager       | Department dashboard                            |
| Admin      | Director      | Org settings, User management                   |
| Superadmin | Admin         | All + Superadmin Panel + Developer Persona Mode |

## Superadmin Panel Scope
### Access
- **Dashboard**: Superadmins land on the standard dashboard
- **Panel Access**: Available only via `/settings/superadmin` (not in top nav)
- **Layout**: Reuses global sidebar, theme settings, and design fidelity
- **Developer Mode**: Switch between role views (Employee, Manager, Director, Admin)
- **Persona Switching**: View-only impersonation of users, with developer-mode banner

### Panel Modules
- **Design System Manager**:
  - Live-editable component library (charts, inputs, forms, typography)
  - Updates global usage via shared imports
  - Supports previews and AI enhancements (e.g., AI-generated design suggestions post-MVP)
  - Versioned changes with rollback, stored in database
- **Company Management**:
  - Add/edit/delete companies
  - Assign admins, set subscription limits, update plan status
- **Email Validation**:
  - Whitelist domains, resend verification, override logic
- **Feature Flags**:
  - Toggle features per company (e.g., AI, modules)
  - Stored in database, cached in Redis
  - Evaluated via `hasFeature(companyId, featureKey)`
  - Include placeholder for `contextAwareness` flag to enable future full context-awareness across all entry points

### Audit Logging
- Log all Superadmin actions (timestamp, actor, action, target)
- Actions include company edits, feature toggles, impersonation, design system changes
- Stored in database, accessible via Superadmin Panel (Superadmin-only)
- 90-day retention, exportable, queryable by timestamp/actor/action
## Engineering Requirements
- Enforce access via middleware and client guards
- Show fallback UI for component-level errors
- Use client-server architecture with RESTful APIs (OpenAPI-documented)
- Reuse global sidebar, layout, and theme system for Superadmin panel
- Structure logic in `/lib/superadmin` and `/features/superadmin`
- **Database Schema**:
  - Tables: `User`, `Company`, `Role`, `FeatureFlag`, `AuditLog`
  - Prisma migrations in `/prisma/migrations`
  - Seed data for local development
  - Placeholder models: Expanded `UserContext` (e.g., `model UserContext { id Int @id @default(autoincrement()) /* role String?, companyId Int?, preferences Json?, recentActions Json?, historicalData Json? */ }`) and `CoachingHistory` (e.g., `model CoachingHistory { id Int @id @default(autoincrement()) /* userId Int, recommendation String?, completed Boolean? */ }`) for future Gen AI-driven context-awareness across all entry points
- **Local Development**:
  - Docker Compose for PostgreSQL and Redis
  - `.env.example` for environment variables
  - Setup documented in `/docs/development.md`
  - Seed data maintained via `/prisma/seed.ts`
- **Data Migration**:
  - Confirm with stakeholders if existing user data needs migration
  - If none, use fresh database
  - If needed, develop `/prisma/migrate-data.ts`, validate integrity, minimize downtime (<1 hour)
  - Document in `/docs/migration.md`
- **Scalability**:
  - Support 1,000 concurrent users per company
  - Optimize queries for <100ms response
  - Cache data (e.g., feature flags, roles) in Redis
- **Offline Behavior**:
  - Show cached dashboard with offline warning
  - Disable write operations when offline
- **Analytics**:
  - Track feature usage (e.g., dashboard views, skill updates) per company
  - Store anonymized metrics in database, accessible via Superadmin Panel
- **Scheduled Jobs**:
  - Use node-cron for tasks (e.g., data cleanup, notifications)
  - Document in `/docs/jobs.md`
- **Additional Libraries**:
  - Framer Motion: Animations and transitions
  - Lucide React: Icon library
  - Sonner: Toast notifications
  - React Hook Form: Form state management
  - React Day Picker: Date selection
  - React Best Gradient Color Picker, React Colorful: Color selection

## Security
- Protect against XSS, CSRF, SQL injection
- Encrypt PII at rest (AES-256)
- Follow OWASP Top 10 guidelines
- Avoid hardcoding secrets; use environment variables
- **Rate Limiting**:
  - API endpoints: 100 requests/min per user (express-rate-limit)
  - Superadmin actions: 10 deletions/hour, 50 impersonations/hour (Redis)
- **API Tokens**: JWT authentication, rotated every 24 hours
- **Data Privacy**: GDPR/CCPA-compliant PII handling, export/deletion options
- **Content Security Policy**: Strict CSP in Next.js middleware
- **Session Management**: 30-minute timeouts, manual logout in Settings
- **Authentication**: NextAuth.js with Google OAuth, bcryptjs for credentials
- **Context-Awareness Data Privacy (Placeholder)**:
  - Plan to anonymize sensitive user data (e.g., preferences, recent actions, historical data) and require user consent for Gen AI-driven context-awareness in `/app/settings.tsx` post-MVP

## Error Handling & Monitoring
- Use Sentry for error tracking, LogRocket for session replays
- Log API errors centrally
- Implement `try/catch` with clear error messages
- **Fallbacks**:
  - Cache Sentry/LogRocket data locally if services fail
  - Use Vercel Analytics as backup

## Dependencies
Pin all dependencies in `package.json`:
- **Core**: Next.js ^15.2.2, React ^19.0.0, React DOM ^19.0.0, TypeScript ^5.x
- **Frontend**:
  - Radix UI ^1.1.x – ^2.2.x
  - shadcn/ui ^0.0.4
  - Tailwind CSS ^4.0.12
  - tailwind-merge ^3.0.2
  - tailwindcss-animate ^1.0.7
  - class-variance-authority ^0.7.1
  - clsx ^2.1.1
  - next-themes ^0.4.6
  - lucide-react ^0.479.0
  - framer-motion ^12.5.0
  - sonner ^2.0.1
  - react-hook-form ^7.54.2
  - react-day-picker ^8.10.1
  - react-best-gradient-color-picker ^3.0.14
  - react-colorful ^5.6.1
- **Backend**:
  - next-auth ^5.0.0-beta.25
  - @auth/core ^0.38.0
  - @auth/prisma-adapter ^2.8.0
  - Prisma ^6.5.0
  - Zod ^3.24.2
  - bcryptjs ^3.0.2
  - express-rate-limit ^7.5.0
  - node-cron ^3.0.3
- **Testing**:
  - Vitest ^3.1.1
  - Playwright ^1.51.1
  - @testing-library/react ^14.3.1
  - @testing-library/jest-dom, user-event, jsdom ^26.0.0
- **Tooling**:
  - ESLint ^9, eslint-config-next ^15.2.2-canary.7
  - tsx ^4.19.3
  - Husky ^9.1.7
- **Monitoring**:
  - Sentry ^7.0.0
  - LogRocket ^2.0.0
  - Vercel Analytics
- Use npm (locked); evaluate pnpm for future migration

## UX/UI Requirements (User-Centric & Hyper-Personalized)

### **Core Design Philosophy**
- **User-Centric by Default**: Every design decision validated through user research
- **Hyper-Personalization Engine**: Interface adapts to individual user behavior patterns
- **Behavioral Science Integration**: Apply psychology principles to maximize engagement
- **Accessibility First**: WCAG 2.1 AA compliance as baseline, not afterthought
- **Performance as UX**: All interactions complete in <300ms for fluid experience

### **Intelligent Interface Design**
- **Adaptive UI Components**: Interface elements that learn from user preferences
- **Context-Aware Interactions**: Smart suggestions and prompts at optimal moments
- **Behavioral Trigger Points**: Notifications and guidance based on user patterns
- **Progressive Disclosure**: Information architecture that adapts to user expertise level
- **Predictive Navigation**: Interface anticipates user needs based on behavior history

### **Personalization Features**
- **Dynamic Dashboard Layouts**: Widget placement and content adapted to user role and usage
- **Intelligent Onboarding**: Personalized flow based on user background and goals
- **Adaptive Theme Selection**: Color schemes and layouts optimized for user preferences
- **Smart Content Curation**: Resources and recommendations tailored to career path
- **Contextual Help System**: Support content adapted to user skill level and current task

### **Behavioral Analytics Integration**
- **Real-Time Adaptation**: UI changes based on immediate user behavior patterns
- **Cross-Session Learning**: Interface improvements based on historical usage data
- **A/B Testing Framework**: Continuous optimization of user experience elements
- **Engagement Optimization**: Features designed to maximize meaningful user interactions
- **Privacy-Compliant Tracking**: Behavioral analytics with full user consent and transparency

### **Enhanced Onboarding Experience**
- **Personalized Welcome Flow**: Adaptive introduction based on user role and company context
- **Progressive Skill Assessment**: Intelligent evaluation that adapts to user responses
- **Dynamic Tutorial System**: Interactive guidance that responds to user learning pace
- **Context-Aware Tooltips**: Smart help system that appears at optimal moments
- **Behavioral Pattern Recognition**: System learns user preferences during onboarding

### **Feedback & Continuous Improvement**
- **Real-Time Feedback Collection**: Unobtrusive feedback gathering integrated into workflows
- **Behavioral Sentiment Analysis**: Understand user satisfaction through interaction patterns
- **Continuous Iteration Cycles**: Regular UX improvements based on user data and feedback
- **User Research Integration**: Systematic user testing and research informing design decisions

## Engineering Principles

### **Code Quality & Architecture**
- **No Duplicated Logic**: DRY principle with intelligent abstraction layers
- **Shared Zod Schemas**: Type-safe validation across frontend and backend
- **Modular Architecture**: Clean separation of concerns with clear interfaces
- **First Principles Design**: Challenge assumptions, build from user needs
- **Performance by Design**: Every feature optimized for <300ms user interactions

### **Folder Structure (Enhanced)**
```
/app                     → Next.js routes and layouts
/features               → Feature-specific business logic
/lib                    → Shared utilities and design system
  /design-system        → Component library and theming
  /auth                 → Authentication and session management  
  /analytics            → User behavior tracking and analysis
  /personalization      → Hyper-personalization engine
  /validators           → Shared Zod schemas
/controllers            → API request/response handling
/services               → Pure business logic (no side effects)
/types                  → Shared TypeScript definitions
/tests                  → TDD test suite with real data
```

### **Development Standards**
- **TDD Mandatory**: No production code without tests written first
- **User-Centric Validation**: Every feature validated through user research
- **Behavioral Data Integration**: All interactions tracked for personalization
- **Context-Aware Design**: Placeholders for future AI integration in all features
- **Security by Design**: OWASP compliance integrated from conception
- **Accessibility First**: WCAG 2.1 AA compliance as baseline requirement

### **Data & Personalization Architecture**
- **Behavioral Analytics Pipeline**: Real-time user interaction tracking
- **Context-Aware Data Models**: Rich user context storage for personalization
- **Cross-Company Intelligence**: Anonymous behavioral insights (privacy-compliant)
- **Predictive Data Structures**: Schema designed for future ML/AI integration
- **Privacy by Design**: GDPR/CCPA compliance built into data architecture

### **Component & Service Design**
- **Role-Based Rendering**: UI adapts to user permissions and context
- **Backend Controller-Service Pattern**: Clean separation of concerns
- **Shared Component Library**: Consistent UI with behavioral tracking
- **Context-Aware Components**: UI elements that adapt to user behavior
- **Intelligent Defaults**: Smart configuration based on user patterns

## Roo Code AI Rules
### Code Generation
- **Generate code in** `/lib` or `/features` for reusability
- **Avoid modifying** `node_modules` or auto-generated files
- **Adhere to tech stack**:
  - Use Next.js 15.2.2, React 19.0.0, TypeScript 5.x
  - Use shadcn/ui, Tailwind CSS 4.0.12, Radix UI components
  - Use Prisma 6.5.0, Zod 3.24.2, next-auth 5.0.0-beta.25
- **Validate code**:
  - Run ESLint and TypeScript checks
  - Ensure OWASP Top 10 compliance
  - Include JSDoc for complex/reusable code
- **Follow structure**:
  - Controller-service pattern for backend
  - Shared components in `lib/design-system/components`
  - RESTful APIs with OpenAPI documentation
- **Optimize** for performance and WCAG 2.1 AA accessibility

### Testing
- **Generate tests**:
  - Vitest for unit/integration (80% coverage for services/controllers)
  - Playwright for E2E (cover Superadmin navigation, role redirects, etc.)
- **Provide stubs** for new components/services
- **Validate edge cases** (e.g., network failures, invalid inputs)
- **Document** setup in `/docs/testing.md`
- **Context-Awareness Testing (Placeholder)**:
  - Plan E2E tests for future context-awareness across all entry points (e.g., personalized dashboard rendering) using mocked responses, validating failure scenarios

### Debugging
- **Analyze** Sentry logs and suggest fixes
- **Provide** debug snippets for common issues (e.g., Prisma query failures)
- **Ensure** error messages are clear and actionable

### Code Review
- **Generate** PR descriptions summarizing changes
- **Respond** to reviewer comments with explanations or adjustments
- **Validate** changes against MVP scope

### Scope Adherence
- **Reject** tasks in Non-Goals (e.g., microservices, i18n, active context-awareness implementation)
- **Prompt** for clarification if input is ambiguous
- **Explain** refactoring benefits and scope
- **Avoid** regressions or unapproved dependency changes

### Error Handling
- If code fails validation, suggest alternatives or request clarification
- Ensure `try/catch` and fallback UI for all generated code

## Testing Requirements (TDD Approach)

### **Core Testing Philosophy**
- **TDD Red-Green-Refactor**: All code follows strict TDD cycle
- **100% Functional Verification**: Tests validate real functionality, not mocks
- **Production-Reality Testing**: Tests mirror production environment exactly
- **No Workarounds**: If tests pass, features work - no exceptions
- No mocking, hacking or workarounds - when the test passes, the actual feature works as expected.

### **Test Coverage Requirements**
- **Unit Tests**: 100% coverage for services/controllers (Vitest) - Higher than typical due to TDD
- **Integration Tests**: 100% coverage for API endpoints and Prisma queries (Vitest)
- **E2E Tests (Playwright)** - **Comprehensive Coverage**:
  - All user workflows from authentication to feature completion
  - Superadmin navigation and persona switching
  - Theme selection, persistence, and fallback scenarios
  - Role-based redirects and dashboard access verification
  - Component fallback and error state validation
  - Mobile responsiveness (iPhone SE, Pixel 6, iPad Air) with touch interactions
  - Network failure scenarios and offline behavior
  - Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
  - Hyper-personalization behavior verification
  - Context-aware UI adaptation testing

### **Real-World Testing Standards**
- **Accessibility Tests**: 
  - Lighthouse automated scanning (100% pass rate)
  - axe-core integration tests (zero violations)
  - Manual screen reader testing (NVDA/VoiceOver)
  - Keyboard-only navigation verification
- **Performance Testing**:
  - Web Vitals validation (LCP < 2.5s, FCP < 1.5s, CLS < 0.1)
  - API response time verification (<100ms for critical endpoints)
  - User interaction responsiveness (<300ms for all UI updates)
- **Load Testing**:
  - k6 scripts for 1,000 concurrent users per company
  - Database performance under load
  - Redis caching effectiveness validation
- **Security Testing**:
  - Penetration testing for XSS, CSRF, SQL injection
  - Authentication flow security verification
  - Rate limiting effectiveness testing
  - Data privacy compliance validation

### **Behavioral & Personalization Testing**
- **User Journey Analytics**: Test tracking accuracy and behavioral pattern detection
- **Personalization Engine**: Verify adaptive UI changes based on user behavior
- **Context Awareness**: Test placeholder functionality and future-proofing
- **Cross-Company Intelligence**: Validate anonymization and insight generation

### **Testing Infrastructure**
- **No Mock Strategy**: All tests run against real database, APIs, and external services
- **Test Data Management**: Realistic seed data that reflects production scenarios
- **Continuous Testing**: All tests run on every commit, PR, and deployment
- **Test Environment Parity**: Testing environment mirrors production exactly

## Performance Expectations
- First load <2.5s on 4G mobile
- Lazy-load non-critical dashboard/Superadmin sections
- No blocking loads for charts/async data
- Superadmin operations (e.g., impersonation) <1s
- **Benchmarking**: Web Vitals (LCP, FCP, CLS) via Vercel Analytics
- **Context-Awareness Performance (Placeholder)**:
  - Plan for future context-awareness to maintain API response times <300ms, using Redis caching (1hr TTL) for context data

## Documentation Required
| Area                       | Owner         | Format            | Location                     |
|----------------------------|---------------|-------------------|------------------------------|
| RBAC & Roles               | Security Team | Markdown          | `/docs/rbac.md`              |
| API Endpoints & Schema     | Backend Team  | Markdown + OpenAPI| `/docs/api.md`               |
| Multi-tenancy              | Architecture  | Diagram + MD      | `/docs/tenancy.md`           |
| Design System Architecture | Design Team   | MD + Preview      | `/docs/design.md`            |
| Superadmin Panel Spec      | Platform Lead | Notion            | Notion > Features            |
| Migration Plan             | Engineering   | Markdown          | `/docs/migration.md`         |
| Local Development Setup    | Engineering   | Markdown          | `/docs/development.md`       |
| Backup & Recovery          | Engineering   | Markdown          | `/docs/recovery.md`          |
| Testing Framework          | Engineering   | Markdown          | `/docs/testing.md`           |
| Scheduled Jobs             | Engineering   | Markdown          | `/docs/jobs.md`              |

- All documentation versioned (Git/Notion), RBAC-controlled
- Migration Plan includes legacy code deprecation and data migration (if needed)

## Code Quality & Naming Conventions
- Enforce ESLint, Prettier, TypeScript strict mode
- Naming:
  - Pages: kebab-case (e.g., `dashboard-overview.tsx`)
  - Components: PascalCase (e.g., `ChartPreview.tsx`)
  - Folders: lowercase-hyphen (e.g., `feature-flags/`)
  - Types/interfaces: CamelCase (e.g., `CompanyPlan`)
- **Documentation**:
  - JSDoc for public functions/complex logic
  - Inline comments for non-obvious code
  - API endpoints with request/response schemas

## Deployment Requirements
- **CI/CD**: GitHub Actions for testing, linting, deployment
- **Environments**: Staging, Production on Vercel
- **Monitoring**: Vercel Analytics, Sentry, LogRocket
- **Branching**:
  - `main`: Production
  - `develop`: Integration
  - `feature/*`: Feature work
  - PRs require 1 reviewer, passing CI
- **Backup & Recovery**:
  - Daily PostgreSQL backups to Vercel Storage
  - 30-day retention
  - Document in `/docs/recovery.md`
- **Environment Variables**:
  - Manage via Vercel dashboard (staging/production)
  - Use `.env.local`, synced with `.env.example`
- **Support & Incident Management**:
  - GitHub Issues for bugs
  - Notion board for incident assignment
  - SLA: Critical bugs resolved in 24 hours
- **Deployment Rollback**:
  - Vercel native rollback for failed deployments
  - Document in `/docs/recovery.md`

## Glossary
| Term            | Description                                     |
|-----------------|-------------------------------------------------|
| RBAC            | Role-Based Access Control                       |
| Zod             | Schema validation for frontend & backend        |
| Prisma          | ORM for PostgreSQL                              |
| SSR             | Server-Side Rendering (React Server Components) |
| Feature Flags   | Controlled feature toggles by company           |
| Middleware      | Route protection logic (auth, tenant)           |
| Roo Code AI       | AI assistant supporting development             |
| Design System   | Central component library (shadcn/ui-based)     |
| Gen AI          | Generative AI for context-aware insights, recommendations, and coaching |
| UserContext     | Placeholder model for storing comprehensive user context (e.g., role, preferences, recent actions, historical data) |
| Context-Awareness | Dynamic adaptation of the app across all entry points based on user context, leveraging Gen AI post-MVP |

## Future Roadmap (Post-MVP)
- Advanced permission groups and toggles
- Public API with scopes/tokens
- HRIS integrations (Leapsome, BambooHR)
- Mobile apps (iOS/Android)
- Recognition and engagement tools
- BI dashboards and analytics
- LMS and performance review modules
- Org chart, directory, birthday/anniversary alerts
- i18n support for global locales (e.g., en, fr, de)
- **Gen AI Integration (Q3-Q4 2025)**:
  - Full context-awareness across all entry points (e.g., onboarding, settings, dashboards, Superadmin Panel), adapting dynamically to user context (e.g., role, company, preferences, recent actions, historical data)
  - Personalized insights, recommendations, and coaching using Gen AI APIs (e.g., xAI)

## Technical Debt & Implementation Tracking

### **Current Implementation Status**
- ✅ **Client-Server Architecture**: Completed and operational
- ✅ **Context-Aware Infrastructure**: Placeholders implemented and feature-flagged
- ✅ **Behavioral Analytics Framework**: Basic tracking operational
- 🔄 **Audit Logging System**: Partially implemented - needs completion
- 🔄 **Hyper-Personalization Engine**: Foundation built, algorithm refinement needed
- ⏳ **Advanced Behavioral Analytics**: Planned for next sprint

### **Technical Debt Priorities**
1. **High Priority (Production Blockers)**:
   - Complete audit logging system with 90-day retention
   - Implement comprehensive error tracking and recovery
   - Finalize rate limiting implementation across all endpoints

2. **Medium Priority (Enhanced MVP)**:
   - Optimize personalization algorithm performance
   - Enhance behavioral analytics data processing
   - Implement advanced caching strategies for user context

3. **Low Priority (Future Optimization)**:
   - Database query optimization for large datasets
   - Advanced security hardening beyond OWASP Top 10
   - Performance optimization for 10,000+ concurrent users

### **Implementation Guidelines for AI Agents**
- **No Shortcuts**: Implement complete solutions, not temporary workarounds
- **TDD Compliance**: All new features must follow red-green-refactor cycle
- **Real Data Testing**: No mocking - test against actual services and databases
- **User Impact Focus**: Prioritize features that directly improve user experience
- **Technical Debt Tracking**: Document any architectural decisions that create future work

---

**Note**: This document defines the Emynent Enhanced MVP scope and development rules. All layouts, workflows, and UX must be preserved and enhanced unless explicitly scoped. Technical debt must be addressed systematically to maintain code quality and user experience standards.
